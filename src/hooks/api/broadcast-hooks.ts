/**
 * Broadcast Links API Hooks
 * Hooks for broadcast links management operations
 */

'use client';

import { queryKeys } from '@/lib/query-client';
import { BroadcastQueries } from '@/lib/query-types';
import { PaginatedResponse } from '@/lib/query-utils';
import { 
  useBaseQuery, 
  usePaginatedQuery,
  useBaseMutation,
  useOptimisticMutation,
  useApiHookUtils 
} from './base-hooks';

/**
 * Hook for getting broadcast links
 */
export function useBroadcastLinks(params?: BroadcastQueries.BroadcastLinkQueryParams) {
  const queryParams = new URLSearchParams();
  if (params?.page) queryParams.set('page', params.page.toString());
  if (params?.limit) queryParams.set('limit', params.limit.toString());
  if (params?.fixtureId) queryParams.set('fixtureId', params.fixtureId);
  if (params?.quality) queryParams.set('quality', params.quality);
  if (params?.language) queryParams.set('language', params.language);
  if (params?.isActive !== undefined) queryParams.set('isActive', params.isActive.toString());
  if (params?.query) queryParams.set('query', params.query);

  return usePaginatedQuery(
    [...queryKeys.broadcast.links(), params],
    async (): Promise<PaginatedResponse<BroadcastQueries.BroadcastLink>> => {
      const response = await fetch(`/api/broadcast-links?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch broadcast links: ${response.statusText}`);
      }

      return response.json();
    },
    {
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
}

/**
 * Hook for getting specific broadcast link
 */
export function useBroadcastLink(linkId: string) {
  return useBaseQuery(
    queryKeys.broadcast.link(linkId),
    async (): Promise<BroadcastQueries.BroadcastLink> => {
      const response = await fetch(`/api/broadcast-links/${linkId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch broadcast link: ${response.statusText}`);
      }

      return response.json();
    },
    {
      enabled: !!linkId,
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
}

/**
 * Hook for getting broadcast links for a specific fixture
 */
export function useFixtureBroadcastLinks(fixtureId: string) {
  return useBaseQuery(
    queryKeys.broadcast.fixture(fixtureId),
    async (): Promise<BroadcastQueries.BroadcastLink[]> => {
      const response = await fetch(`/api/broadcast-links/fixture/${fixtureId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch fixture broadcast links: ${response.statusText}`);
      }

      return response.json();
    },
    {
      enabled: !!fixtureId,
      staleTime: 1 * 60 * 1000, // 1 minute - links for live fixtures change frequently
    }
  );
}

/**
 * Hook for creating broadcast link
 */
export function useCreateBroadcastLink() {
  const { invalidateQueries } = useApiHookUtils();

  return useOptimisticMutation<BroadcastQueries.BroadcastLink, BroadcastQueries.CreateBroadcastLinkRequest>(
    async (data) => {
      const response = await fetch('/api/broadcast-links', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to create broadcast link: ${response.statusText}`);
      }

      return response.json();
    },
    {
      onSuccess: (data) => {
        // Invalidate broadcast links queries
        invalidateQueries(queryKeys.broadcast.links());
        invalidateQueries(queryKeys.broadcast.fixture(data.fixtureId));
        console.log('✅ Broadcast link created successfully');
      },
      onError: (error) => {
        console.error('❌ Failed to create broadcast link:', error);
      },
    }
  );
}

/**
 * Hook for updating broadcast link
 */
export function useUpdateBroadcastLink() {
  const { invalidateQueries } = useApiHookUtils();

  return useOptimisticMutation<BroadcastQueries.BroadcastLink, { id: string; data: BroadcastQueries.UpdateBroadcastLinkRequest }>(
    async ({ id, data }) => {
      const response = await fetch(`/api/broadcast-links/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to update broadcast link: ${response.statusText}`);
      }

      return response.json();
    },
    {
      onSuccess: (data) => {
        // Invalidate specific link and related queries
        invalidateQueries(queryKeys.broadcast.link(data.id));
        invalidateQueries(queryKeys.broadcast.links());
        invalidateQueries(queryKeys.broadcast.fixture(data.fixtureId));
        console.log('✅ Broadcast link updated successfully');
      },
      onError: (error) => {
        console.error('❌ Failed to update broadcast link:', error);
      },
    }
  );
}

/**
 * Hook for deleting broadcast link
 */
export function useDeleteBroadcastLink() {
  const { invalidateQueries } = useApiHookUtils();

  return useBaseMutation<{ message: string }, string>(
    async (linkId) => {
      const response = await fetch(`/api/broadcast-links/${linkId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete broadcast link: ${response.statusText}`);
      }

      return response.json();
    },
    {
      onSuccess: (_, linkId) => {
        // Invalidate broadcast links queries
        invalidateQueries(queryKeys.broadcast.links());
        invalidateQueries(queryKeys.broadcast.link(linkId));
        console.log('✅ Broadcast link deleted successfully');
      },
      onError: (error) => {
        console.error('❌ Failed to delete broadcast link:', error);
      },
    }
  );
}

/**
 * Hook for toggling broadcast link active status
 */
export function useToggleBroadcastLinkStatus() {
  const { invalidateQueries, updateQueryData } = useApiHookUtils();

  return useOptimisticMutation<BroadcastQueries.BroadcastLink, { id: string; isActive: boolean }>(
    async ({ id, isActive }) => {
      const response = await fetch(`/api/broadcast-links/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive }),
      });

      if (!response.ok) {
        throw new Error(`Failed to toggle broadcast link status: ${response.statusText}`);
      }

      return response.json();
    },
    {
      onMutate: async ({ id, isActive }) => {
        // Optimistically update the link status
        const linkQueryKey = queryKeys.broadcast.link(id);
        const previousLink = updateQueryData<BroadcastQueries.BroadcastLink>(
          linkQueryKey,
          (old) => old ? { ...old, isActive } : old
        );

        return { previousLink, linkQueryKey };
      },
      onError: (error, variables, context) => {
        // Revert optimistic update on error
        if (context?.previousLink && context?.linkQueryKey) {
          updateQueryData(context.linkQueryKey, () => context.previousLink);
        }
        console.error('❌ Failed to toggle broadcast link status:', error);
      },
      onSuccess: (data) => {
        // Invalidate related queries
        invalidateQueries(queryKeys.broadcast.links());
        invalidateQueries(queryKeys.broadcast.fixture(data.fixtureId));
        console.log('✅ Broadcast link status toggled successfully');
      },
    }
  );
}

/**
 * Composite hook for broadcast links operations
 */
export function useBroadcastLinksManager() {
  const createLink = useCreateBroadcastLink();
  const updateLink = useUpdateBroadcastLink();
  const deleteLink = useDeleteBroadcastLink();
  const toggleStatus = useToggleBroadcastLinkStatus();

  return {
    // Mutations
    createLink,
    updateLink,
    deleteLink,
    toggleStatus,
    
    // Actions
    createBroadcastLink: createLink.mutate,
    updateBroadcastLink: updateLink.mutate,
    deleteBroadcastLink: deleteLink.mutate,
    toggleLinkStatus: toggleStatus.mutate,
    
    // State
    isCreating: createLink.isPending,
    isUpdating: updateLink.isPending,
    isDeleting: deleteLink.isPending,
    isToggling: toggleStatus.isPending,
    isLoading: createLink.isPending || updateLink.isPending || deleteLink.isPending || toggleStatus.isPending,
    
    // Errors
    createError: createLink.error,
    updateError: updateLink.error,
    deleteError: deleteLink.error,
    toggleError: toggleStatus.error,
  };
}

/**
 * Hook for broadcast links by quality
 */
export function useBroadcastLinksByQuality(quality: 'HD' | 'SD' | 'Mobile') {
  return useBroadcastLinks({ quality, isActive: true });
}

/**
 * Hook for broadcast links by language
 */
export function useBroadcastLinksByLanguage(language: string) {
  return useBroadcastLinks({ language, isActive: true });
}

/**
 * Hook for active broadcast links
 */
export function useActiveBroadcastLinks() {
  return useBroadcastLinks({ isActive: true });
}
