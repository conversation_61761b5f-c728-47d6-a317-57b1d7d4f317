/**
 * Base API Hooks
 * Foundation hooks for API operations with TanStack Query
 */

'use client';

import { 
  useQuery, 
  useMutation, 
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions 
} from '@tanstack/react-query';
import { 
  queryOptionsBuilder, 
  mutationOptionsBuilder,
  ApiResponse,
  PaginatedResponse,
  BaseQueryOptions,
  BaseMutationOptions
} from '@/lib/query-utils';
import { ApiError, isApiError, errorUtils } from '@/lib/query-error-handler';

/**
 * Base query hook with error handling and type safety
 */
export function useBaseQuery<TData>(
  queryKey: readonly unknown[],
  queryFn: () => Promise<TData>,
  options?: BaseQueryOptions<TData>
) {
  return useQuery({
    queryKey,
    queryFn,
    ...options,
    onError: (error: unknown) => {
      console.error(`[Query Error] ${queryKey.join(' → ')}:`, error);
      if (options?.onError) {
        options.onError(error as ApiError);
      }
    },
  });
}

/**
 * Base mutation hook with error handling and type safety
 */
export function useBaseMutation<TData, TVariables>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: BaseMutationOptions<TData, TVariables>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn,
    ...options,
    onError: (error: unknown, variables: TVariables, context: unknown) => {
      console.error('[Mutation Error]:', error);
      if (options?.onError) {
        options.onError(error as ApiError, variables, context);
      }
    },
    onSuccess: (data: TData, variables: TVariables, context: unknown) => {
      if (options?.onSuccess) {
        options.onSuccess(data, variables, context);
      }
    },
  });
}

/**
 * Paginated query hook for list endpoints
 */
export function usePaginatedQuery<TData>(
  queryKey: readonly unknown[],
  queryFn: () => Promise<PaginatedResponse<TData>>,
  options?: BaseQueryOptions<PaginatedResponse<TData>>
) {
  return useBaseQuery(queryKey, queryFn, {
    ...queryOptionsBuilder.userSpecific(),
    ...options,
  });
}

/**
 * Real-time query hook for frequently updated data
 */
export function useRealTimeQuery<TData>(
  queryKey: readonly unknown[],
  queryFn: () => Promise<TData>,
  options?: BaseQueryOptions<TData>
) {
  return useBaseQuery(queryKey, queryFn, {
    ...queryOptionsBuilder.realTime(),
    ...options,
  });
}

/**
 * Static query hook for rarely changing data
 */
export function useStaticQuery<TData>(
  queryKey: readonly unknown[],
  queryFn: () => Promise<TData>,
  options?: BaseQueryOptions<TData>
) {
  return useBaseQuery(queryKey, queryFn, {
    ...queryOptionsBuilder.static(),
    ...options,
  });
}

/**
 * Background sync query hook for data that updates in background
 */
export function useBackgroundSyncQuery<TData>(
  queryKey: readonly unknown[],
  queryFn: () => Promise<TData>,
  options?: BaseQueryOptions<TData>
) {
  return useBaseQuery(queryKey, queryFn, {
    ...queryOptionsBuilder.backgroundSync(),
    ...options,
  });
}

/**
 * Optimistic mutation hook for immediate UI updates
 */
export function useOptimisticMutation<TData, TVariables>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: BaseMutationOptions<TData, TVariables>
) {
  return useBaseMutation(mutationFn, {
    ...mutationOptionsBuilder.optimistic(),
    ...options,
  });
}

/**
 * Critical mutation hook for important operations with retries
 */
export function useCriticalMutation<TData, TVariables>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: BaseMutationOptions<TData, TVariables>
) {
  return useBaseMutation(mutationFn, {
    ...mutationOptionsBuilder.critical(),
    ...options,
  });
}

/**
 * Background mutation hook for non-critical operations
 */
export function useBackgroundMutation<TData, TVariables>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: BaseMutationOptions<TData, TVariables>
) {
  return useBaseMutation(mutationFn, {
    ...mutationOptionsBuilder.background(),
    ...options,
  });
}

/**
 * Hook utilities for common operations
 */
export const useApiHookUtils = () => {
  const queryClient = useQueryClient();

  return {
    /**
     * Invalidate queries by pattern
     */
    invalidateQueries: (queryKey: readonly unknown[]) => {
      return queryClient.invalidateQueries({ queryKey });
    },

    /**
     * Remove queries from cache
     */
    removeQueries: (queryKey: readonly unknown[]) => {
      return queryClient.removeQueries({ queryKey });
    },

    /**
     * Update query data optimistically
     */
    updateQueryData: <T>(queryKey: readonly unknown[], updater: (oldData: T | undefined) => T) => {
      queryClient.setQueryData(queryKey, updater);
    },

    /**
     * Get cached query data
     */
    getQueryData: <T>(queryKey: readonly unknown[]): T | undefined => {
      return queryClient.getQueryData(queryKey);
    },

    /**
     * Prefetch query data
     */
    prefetchQuery: <T>(queryKey: readonly unknown[], queryFn: () => Promise<T>) => {
      return queryClient.prefetchQuery({ queryKey, queryFn });
    },

    /**
     * Check if query is loading
     */
    isQueryLoading: (queryKey: readonly unknown[]): boolean => {
      const query = queryClient.getQueryState(queryKey);
      return query?.fetchStatus === 'fetching';
    },

    /**
     * Get query error
     */
    getQueryError: (queryKey: readonly unknown[]): ApiError | null => {
      const query = queryClient.getQueryState(queryKey);
      return isApiError(query?.error) ? query.error : null;
    },

    /**
     * Handle API error with user feedback
     */
    handleApiError: (error: unknown, context?: string): string => {
      return errorUtils.getUserMessage(error);
    },
  };
};

/**
 * Hook for API status monitoring
 */
export const useApiStatus = () => {
  const queryClient = useQueryClient();

  return {
    /**
     * Get overall API status
     */
    getApiStatus: () => {
      const queries = queryClient.getQueryCache().getAll();
      const totalQueries = queries.length;
      const loadingQueries = queries.filter(q => q.state.fetchStatus === 'fetching').length;
      const errorQueries = queries.filter(q => q.state.status === 'error').length;
      const successQueries = queries.filter(q => q.state.status === 'success').length;

      return {
        total: totalQueries,
        loading: loadingQueries,
        error: errorQueries,
        success: successQueries,
        isLoading: loadingQueries > 0,
        hasErrors: errorQueries > 0,
        healthScore: totalQueries > 0 ? (successQueries / totalQueries) * 100 : 100,
      };
    },

    /**
     * Get queries by status
     */
    getQueriesByStatus: (status: 'loading' | 'error' | 'success' | 'idle') => {
      const queries = queryClient.getQueryCache().getAll();
      
      switch (status) {
        case 'loading':
          return queries.filter(q => q.state.fetchStatus === 'fetching');
        case 'error':
          return queries.filter(q => q.state.status === 'error');
        case 'success':
          return queries.filter(q => q.state.status === 'success');
        case 'idle':
          return queries.filter(q => q.state.fetchStatus === 'idle');
        default:
          return [];
      }
    },

    /**
     * Clear all errors
     */
    clearAllErrors: () => {
      const errorQueries = queryClient.getQueryCache().getAll()
        .filter(q => q.state.status === 'error');
      
      errorQueries.forEach(query => {
        queryClient.resetQueries({ queryKey: query.queryKey });
      });
    },
  };
};
