/**
 * Authentication API Hooks
 * Hooks for system authentication operations
 */

'use client';

import { queryKeys } from '@/lib/query-client';
import { AuthQueries } from '@/lib/query-types';
import { useBaseQuery, useBaseMutation, useApiHookUtils } from './base-hooks';

/**
 * Hook for user login
 */
export function useLogin() {
  const { invalidateQueries } = useApiHookUtils();

  return useBaseMutation<AuthQueries.LoginResponse, AuthQueries.LoginRequest>(
    async (credentials) => {
      const response = await fetch('/api/system-auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        throw new Error(`Login failed: ${response.statusText}`);
      }

      return response.json();
    },
    {
      onSuccess: (data) => {
        // Invalidate auth queries on successful login
        invalidateQueries(queryKeys.auth.all);
        console.log('✅ Login successful:', data.user.username);
      },
      onError: (error) => {
        console.error('❌ Login failed:', error);
      },
    }
  );
}

/**
 * Hook for user logout
 */
export function useLogout() {
  const { invalidateQueries } = useApiHookUtils();

  return useBaseMutation<{ message: string }, void>(
    async () => {
      const response = await fetch('/api/system-auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Logout failed: ${response.statusText}`);
      }

      return response.json();
    },
    {
      onSuccess: () => {
        // Clear all auth-related queries on logout
        invalidateQueries(queryKeys.auth.all);
        console.log('✅ Logout successful');
      },
      onError: (error) => {
        console.error('❌ Logout failed:', error);
      },
    }
  );
}

/**
 * Hook for logout from all devices
 */
export function useLogoutAll() {
  const { invalidateQueries } = useApiHookUtils();

  return useBaseMutation<{ message: string }, void>(
    async () => {
      const response = await fetch('/api/system-auth/logout-all', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Logout all failed: ${response.statusText}`);
      }

      return response.json();
    },
    {
      onSuccess: () => {
        // Clear all auth-related queries on logout all
        invalidateQueries(queryKeys.auth.all);
        console.log('✅ Logout all successful');
      },
      onError: (error) => {
        console.error('❌ Logout all failed:', error);
      },
    }
  );
}

/**
 * Hook for getting user profile
 */
export function useProfile() {
  return useBaseQuery(
    queryKeys.auth.profile(),
    async (): Promise<AuthQueries.ProfileResponse> => {
      const response = await fetch('/api/system-auth/profile');

      if (!response.ok) {
        throw new Error(`Failed to fetch profile: ${response.statusText}`);
      }

      return response.json();
    },
    {
      enabled: false, // Disable by default, enable when user is authenticated
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: (failureCount, error: any) => {
        // Don't retry on 401 (unauthorized)
        if (error?.status === 401) return false;
        return failureCount < 2;
      },
    }
  );
}

/**
 * Hook for updating user profile
 */
export function useUpdateProfile() {
  const { invalidateQueries } = useApiHookUtils();

  return useBaseMutation<AuthQueries.ProfileResponse, AuthQueries.UpdateProfileRequest>(
    async (data) => {
      const response = await fetch('/api/system-auth/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to update profile: ${response.statusText}`);
      }

      return response.json();
    },
    {
      onSuccess: () => {
        // Invalidate profile query to refetch updated data
        invalidateQueries(queryKeys.auth.profile());
        console.log('✅ Profile updated successfully');
      },
      onError: (error) => {
        console.error('❌ Profile update failed:', error);
      },
    }
  );
}

/**
 * Hook for changing password
 */
export function useChangePassword() {
  return useBaseMutation<{ message: string }, AuthQueries.ChangePasswordRequest>(
    async (data) => {
      const response = await fetch('/api/system-auth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to change password: ${response.statusText}`);
      }

      return response.json();
    },
    {
      onSuccess: () => {
        console.log('✅ Password changed successfully');
      },
      onError: (error) => {
        console.error('❌ Password change failed:', error);
      },
    }
  );
}

/**
 * Hook for creating new system user (Admin only)
 */
export function useCreateUser() {
  const { invalidateQueries } = useApiHookUtils();

  return useBaseMutation<AuthQueries.ProfileResponse, AuthQueries.CreateUserRequest>(
    async (data) => {
      const response = await fetch('/api/system-auth/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to create user: ${response.statusText}`);
      }

      return response.json();
    },
    {
      onSuccess: () => {
        // Invalidate users list to show new user
        invalidateQueries(queryKeys.auth.users());
        console.log('✅ User created successfully');
      },
      onError: (error) => {
        console.error('❌ User creation failed:', error);
      },
    }
  );
}

/**
 * Hook for getting system users list (Admin only)
 */
export function useSystemUsers() {
  return useBaseQuery(
    queryKeys.auth.users(),
    async (): Promise<AuthQueries.ProfileResponse[]> => {
      const response = await fetch('/api/system-auth/users');

      if (!response.ok) {
        throw new Error(`Failed to fetch users: ${response.statusText}`);
      }

      return response.json();
    },
    {
      enabled: false, // Enable only for Admin users
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
}

/**
 * Hook for getting specific system user (Admin only)
 */
export function useSystemUser(userId: string) {
  return useBaseQuery(
    queryKeys.auth.user(userId),
    async (): Promise<AuthQueries.ProfileResponse> => {
      const response = await fetch(`/api/system-auth/users/${userId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch user: ${response.statusText}`);
      }

      return response.json();
    },
    {
      enabled: !!userId, // Only fetch if userId is provided
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

/**
 * Composite hook for authentication state and actions
 */
export function useAuth() {
  const login = useLogin();
  const logout = useLogout();
  const logoutAll = useLogoutAll();
  const profile = useProfile();
  const updateProfile = useUpdateProfile();
  const changePassword = useChangePassword();
  const createUser = useCreateUser();

  return {
    // Queries
    profile,
    
    // Mutations
    login,
    logout,
    logoutAll,
    updateProfile,
    changePassword,
    createUser,
    
    // Computed state
    isAuthenticated: !!profile.data,
    user: profile.data,
    isLoading: profile.isLoading || login.isPending || logout.isPending,
    error: profile.error || login.error || logout.error,
    
    // Actions
    loginUser: login.mutate,
    logoutUser: logout.mutate,
    logoutAllDevices: logoutAll.mutate,
    updateUserProfile: updateProfile.mutate,
    changeUserPassword: changePassword.mutate,
    createNewUser: createUser.mutate,
  };
}
