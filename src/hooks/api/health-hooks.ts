/**
 * Health Check API Hooks
 * Hooks for API health monitoring operations
 */

'use client';

import { queryKeys } from '@/lib/query-client';
import { HealthQueries } from '@/lib/query-types';
import { useBackgroundSyncQuery, useBaseQuery } from './base-hooks';

/**
 * Hook for API health check
 */
export function useApiHealth() {
  return useBackgroundSyncQuery(
    queryKeys.health.api(),
    async (): Promise<HealthQueries.HealthResponse> => {
      const response = await fetch('/api/health');

      if (!response.ok) {
        throw new Error(`Health check failed: ${response.statusText}`);
      }

      return response.json();
    },
    {
      staleTime: 30 * 1000, // 30 seconds
      refetchInterval: 60 * 1000, // Refetch every minute
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    }
  );
}

/**
 * Hook for database health check
 */
export function useDatabaseHealth() {
  return useBaseQuery(
    [...queryKeys.health.all, 'database'],
    async (): Promise<{ status: 'up' | 'down'; responseTime: number }> => {
      const startTime = performance.now();
      const response = await fetch('/api/health/database');
      const endTime = performance.now();

      if (!response.ok) {
        throw new Error(`Database health check failed: ${response.statusText}`);
      }

      const data = await response.json();
      return {
        ...data,
        responseTime: endTime - startTime,
      };
    },
    {
      staleTime: 30 * 1000, // 30 seconds
      retry: 2,
    }
  );
}

/**
 * Hook for external API health check
 */
export function useExternalApiHealth() {
  return useBaseQuery(
    [...queryKeys.health.all, 'external-api'],
    async (): Promise<{ status: 'up' | 'down'; responseTime: number }> => {
      const startTime = performance.now();
      const response = await fetch('/api/health/external-api');
      const endTime = performance.now();

      if (!response.ok) {
        throw new Error(`External API health check failed: ${response.statusText}`);
      }

      const data = await response.json();
      return {
        ...data,
        responseTime: endTime - startTime,
      };
    },
    {
      staleTime: 60 * 1000, // 1 minute
      retry: 2,
    }
  );
}

/**
 * Hook for comprehensive system health
 */
export function useSystemHealth() {
  const apiHealth = useApiHealth();
  const dbHealth = useDatabaseHealth();
  const externalApiHealth = useExternalApiHealth();

  const isLoading = apiHealth.isLoading || dbHealth.isLoading || externalApiHealth.isLoading;
  const hasErrors = apiHealth.isError || dbHealth.isError || externalApiHealth.isError;

  // Calculate overall health status
  const getOverallStatus = (): 'healthy' | 'degraded' | 'unhealthy' => {
    if (hasErrors) return 'unhealthy';
    
    const apiStatus = apiHealth.data?.status;
    const dbStatus = dbHealth.data?.status;
    const externalStatus = externalApiHealth.data?.status;

    if (apiStatus === 'healthy' && dbStatus === 'up' && externalStatus === 'up') {
      return 'healthy';
    }
    
    if (apiStatus === 'unhealthy' || dbStatus === 'down') {
      return 'unhealthy';
    }
    
    return 'degraded';
  };

  // Calculate average response time
  const getAverageResponseTime = (): number => {
    const times = [
      dbHealth.data?.responseTime,
      externalApiHealth.data?.responseTime,
    ].filter((time): time is number => typeof time === 'number');

    if (times.length === 0) return 0;
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  };

  return {
    // Individual health checks
    api: apiHealth,
    database: dbHealth,
    externalApi: externalApiHealth,
    
    // Overall status
    isLoading,
    hasErrors,
    overallStatus: getOverallStatus(),
    averageResponseTime: getAverageResponseTime(),
    
    // Health data
    healthData: {
      api: apiHealth.data,
      database: dbHealth.data,
      externalApi: externalApiHealth.data,
    },
    
    // Error information
    errors: {
      api: apiHealth.error,
      database: dbHealth.error,
      externalApi: externalApiHealth.error,
    },
    
    // Refetch functions
    refetchAll: () => {
      apiHealth.refetch();
      dbHealth.refetch();
      externalApiHealth.refetch();
    },
  };
}

/**
 * Hook for monitoring API performance
 */
export function useApiPerformance() {
  const systemHealth = useSystemHealth();

  const getPerformanceMetrics = () => {
    const { healthData } = systemHealth;
    
    return {
      uptime: healthData.api?.uptime || 0,
      responseTime: systemHealth.averageResponseTime,
      status: systemHealth.overallStatus,
      services: {
        database: healthData.database?.status || 'unknown',
        externalApi: healthData.externalApi?.status || 'unknown',
      },
      lastCheck: new Date().toISOString(),
    };
  };

  return {
    ...systemHealth,
    performanceMetrics: getPerformanceMetrics(),
    
    // Performance indicators
    isPerformanceGood: systemHealth.averageResponseTime < 1000, // Less than 1 second
    isPerformanceFair: systemHealth.averageResponseTime < 3000, // Less than 3 seconds
    isPerformancePoor: systemHealth.averageResponseTime >= 3000, // 3+ seconds
  };
}

/**
 * Hook for health monitoring dashboard
 */
export function useHealthDashboard() {
  const performance = useApiPerformance();
  
  const getDashboardData = () => {
    const { healthData, overallStatus, averageResponseTime } = performance;
    
    return {
      status: overallStatus,
      uptime: healthData.api?.uptime || 0,
      version: healthData.api?.version || 'unknown',
      responseTime: averageResponseTime,
      services: [
        {
          name: 'Database',
          status: healthData.database?.status || 'unknown',
          responseTime: healthData.database?.responseTime || 0,
        },
        {
          name: 'External API',
          status: healthData.externalApi?.status || 'unknown',
          responseTime: healthData.externalApi?.responseTime || 0,
        },
      ],
      lastUpdated: new Date().toISOString(),
    };
  };

  return {
    ...performance,
    dashboardData: getDashboardData(),
    
    // Dashboard actions
    refreshDashboard: performance.refetchAll,
    
    // Status indicators
    statusColor: {
      healthy: '#10b981', // green
      degraded: '#f59e0b', // yellow
      unhealthy: '#ef4444', // red
    }[performance.overallStatus],
    
    statusIcon: {
      healthy: '✅',
      degraded: '⚠️',
      unhealthy: '❌',
    }[performance.overallStatus],
  };
}
