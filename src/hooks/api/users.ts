/**
 * User API Hooks
 * TanStack Query hooks for SystemUser management
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import {
  SystemUser,
  CreateUserRequest,
  UpdateUserRequest,
  ChangePasswordRequest,
  UserListParams,
  UserListResponse,
  UserStatistics,
  UserActivity,
  UserSession,
  DEFAULT_USER_PARAMS,
} from '@/types/user';

/**
 * API endpoints
 */
const API_ENDPOINTS = {
  users: '/api/system-auth/users',
  userById: (id: string) => `/api/system-auth/users/${id}`,
  userStats: '/api/system-auth/users/statistics',
  userActivity: (id: string) => `/api/system-auth/users/${id}/activity`,
  userSessions: (id: string) => `/api/system-auth/users/${id}/sessions`,
  changePassword: (id: string) => `/api/system-auth/users/${id}/change-password`,
  resetPassword: (id: string) => `/api/system-auth/users/${id}/reset-password`,
};

/**
 * Query keys
 */
export const userQueryKeys = {
  all: ['users'] as const,
  lists: () => [...userQueryKeys.all, 'list'] as const,
  list: (params: UserListParams) => [...userQueryKeys.lists(), params] as const,
  details: () => [...userQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...userQueryKeys.details(), id] as const,
  statistics: () => [...userQueryKeys.all, 'statistics'] as const,
  activity: (id: string) => [...userQueryKeys.all, 'activity', id] as const,
  sessions: (id: string) => [...userQueryKeys.all, 'sessions', id] as const,
};

/**
 * Mock data for development
 */
const mockUsers: SystemUser[] = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    firstName: 'System',
    lastName: 'Administrator',
    role: 'admin',
    status: 'active',
    lastLogin: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(), // 30 days ago
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
  },
  {
    id: '2',
    username: 'editor1',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Editor',
    role: 'editor',
    status: 'active',
    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15).toISOString(), // 15 days ago
    updatedAt: new Date().toISOString(),
    createdBy: '1',
  },
  {
    id: '3',
    username: 'moderator1',
    email: '<EMAIL>',
    firstName: 'Jane',
    lastName: 'Moderator',
    role: 'moderator',
    status: 'active',
    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(), // 7 days ago
    updatedAt: new Date().toISOString(),
    createdBy: '1',
  },
  {
    id: '4',
    username: 'inactive_user',
    email: '<EMAIL>',
    firstName: 'Inactive',
    lastName: 'User',
    role: 'editor',
    status: 'inactive',
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 60).toISOString(), // 60 days ago
    updatedAt: new Date().toISOString(),
    createdBy: '1',
  },
];

/**
 * Mock API functions
 */
const mockAPI = {
  getUsers: async (params: UserListParams): Promise<UserListResponse> => {
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
    
    let filteredUsers = [...mockUsers];
    
    // Apply filters
    if (params.search) {
      const search = params.search.toLowerCase();
      filteredUsers = filteredUsers.filter(user =>
        user.username.toLowerCase().includes(search) ||
        user.email.toLowerCase().includes(search) ||
        user.firstName?.toLowerCase().includes(search) ||
        user.lastName?.toLowerCase().includes(search)
      );
    }
    
    if (params.role) {
      filteredUsers = filteredUsers.filter(user => user.role === params.role);
    }
    
    if (params.status) {
      filteredUsers = filteredUsers.filter(user => user.status === params.status);
    }
    
    // Apply sorting
    if (params.sortBy) {
      filteredUsers.sort((a, b) => {
        const aValue = a[params.sortBy!] || '';
        const bValue = b[params.sortBy!] || '';
        const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        return params.sortOrder === 'desc' ? -comparison : comparison;
      });
    }
    
    // Apply pagination
    const page = params.page || 1;
    const limit = params.limit || 20;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
    
    return {
      users: paginatedUsers,
      total: filteredUsers.length,
      page,
      limit,
      totalPages: Math.ceil(filteredUsers.length / limit),
    };
  },

  getUser: async (id: string): Promise<SystemUser> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const user = mockUsers.find(u => u.id === id);
    if (!user) throw new Error('User not found');
    return user;
  },

  createUser: async (data: CreateUserRequest): Promise<SystemUser> => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    const newUser: SystemUser = {
      id: String(mockUsers.length + 1),
      ...data,
      status: data.status || 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: '1', // Current user
    };
    mockUsers.push(newUser);
    return newUser;
  },

  updateUser: async (id: string, data: UpdateUserRequest): Promise<SystemUser> => {
    await new Promise(resolve => setTimeout(resolve, 800));
    const userIndex = mockUsers.findIndex(u => u.id === id);
    if (userIndex === -1) throw new Error('User not found');
    
    mockUsers[userIndex] = {
      ...mockUsers[userIndex],
      ...data,
      updatedAt: new Date().toISOString(),
    };
    
    return mockUsers[userIndex];
  },

  deleteUser: async (id: string): Promise<void> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const userIndex = mockUsers.findIndex(u => u.id === id);
    if (userIndex === -1) throw new Error('User not found');
    mockUsers.splice(userIndex, 1);
  },

  getStatistics: async (): Promise<UserStatistics> => {
    await new Promise(resolve => setTimeout(resolve, 400));
    
    const total = mockUsers.length;
    const active = mockUsers.filter(u => u.status === 'active').length;
    const inactive = mockUsers.filter(u => u.status === 'inactive').length;
    const suspended = mockUsers.filter(u => u.status === 'suspended').length;
    
    const byRole = {
      admin: mockUsers.filter(u => u.role === 'admin').length,
      editor: mockUsers.filter(u => u.role === 'editor').length,
      moderator: mockUsers.filter(u => u.role === 'moderator').length,
    };
    
    const recentLogins = mockUsers.filter(u => {
      if (!u.lastLogin) return false;
      const lastLogin = new Date(u.lastLogin);
      const dayAgo = new Date(Date.now() - 1000 * 60 * 60 * 24);
      return lastLogin > dayAgo;
    }).length;
    
    const monthAgo = new Date(Date.now() - 1000 * 60 * 60 * 24 * 30);
    const newThisMonth = mockUsers.filter(u => {
      const created = new Date(u.createdAt);
      return created > monthAgo;
    }).length;
    
    return {
      total,
      active,
      inactive,
      suspended,
      byRole,
      recentLogins,
      newThisMonth,
    };
  },
};

/**
 * Get users list
 */
export function useUsers(params: UserListParams = DEFAULT_USER_PARAMS) {
  return useQuery({
    queryKey: userQueryKeys.list(params),
    queryFn: () => mockAPI.getUsers(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get user by ID
 */
export function useUser(id: string) {
  return useQuery({
    queryKey: userQueryKeys.detail(id),
    queryFn: () => mockAPI.getUser(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get user statistics
 */
export function useUserStatistics() {
  return useQuery({
    queryKey: userQueryKeys.statistics(),
    queryFn: () => mockAPI.getStatistics(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Create user mutation
 */
export function useCreateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateUserRequest) => mockAPI.createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userQueryKeys.statistics() });
      message.success('User created successfully');
    },
    onError: (error: Error) => {
      message.error(`Failed to create user: ${error.message}`);
    },
  });
}

/**
 * Update user mutation
 */
export function useUpdateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateUserRequest }) => 
      mockAPI.updateUser(id, data),
    onSuccess: (updatedUser) => {
      queryClient.invalidateQueries({ queryKey: userQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userQueryKeys.detail(updatedUser.id) });
      queryClient.invalidateQueries({ queryKey: userQueryKeys.statistics() });
      message.success('User updated successfully');
    },
    onError: (error: Error) => {
      message.error(`Failed to update user: ${error.message}`);
    },
  });
}

/**
 * Delete user mutation
 */
export function useDeleteUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => mockAPI.deleteUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userQueryKeys.statistics() });
      message.success('User deleted successfully');
    },
    onError: (error: Error) => {
      message.error(`Failed to delete user: ${error.message}`);
    },
  });
}
