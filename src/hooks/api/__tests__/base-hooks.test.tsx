/**
 * Base Hooks Tests
 * Tests for base API hooks functionality
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { 
  useBaseQuery, 
  useBaseMutation, 
  usePaginatedQuery,
  useApiHookUtils,
  useApiStatus 
} from '../base-hooks';

// Create test QueryClient
const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

// Test wrapper component
function TestWrapper({ children }: { children: React.ReactNode }) {
  const queryClient = createTestQueryClient();
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}

// Test component for useBaseQuery
function TestBaseQuery() {
  const { data, isLoading, error } = useBaseQuery(
    ['test-query'],
    async () => {
      return { message: 'Hello from base query!' };
    }
  );

  return (
    <div>
      <div data-testid="loading">{isLoading ? 'true' : 'false'}</div>
      <div data-testid="error">{error ? String(error) : 'none'}</div>
      <div data-testid="data">{data ? JSON.stringify(data) : 'none'}</div>
    </div>
  );
}

// Test component for useBaseMutation
function TestBaseMutation() {
  const mutation = useBaseMutation(
    async (data: { message: string }) => {
      return { result: `Processed: ${data.message}` };
    }
  );

  return (
    <div>
      <div data-testid="mutation-loading">{mutation.isPending ? 'true' : 'false'}</div>
      <div data-testid="mutation-error">{mutation.error ? String(mutation.error) : 'none'}</div>
      <div data-testid="mutation-data">{mutation.data ? JSON.stringify(mutation.data) : 'none'}</div>
      <button 
        data-testid="mutation-trigger"
        onClick={() => mutation.mutate({ message: 'test' })}
      >
        Trigger Mutation
      </button>
    </div>
  );
}

// Test component for usePaginatedQuery
function TestPaginatedQuery() {
  const { data, isLoading, error } = usePaginatedQuery(
    ['test-paginated'],
    async () => {
      return {
        data: [{ id: 1, name: 'Item 1' }, { id: 2, name: 'Item 2' }],
        pagination: {
          page: 1,
          limit: 10,
          total: 2,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
        success: true,
        timestamp: new Date().toISOString(),
      };
    }
  );

  return (
    <div>
      <div data-testid="paginated-loading">{isLoading ? 'true' : 'false'}</div>
      <div data-testid="paginated-error">{error ? String(error) : 'none'}</div>
      <div data-testid="paginated-data">{data ? JSON.stringify(data) : 'none'}</div>
    </div>
  );
}

// Test component for useApiHookUtils
function TestApiHookUtils() {
  const utils = useApiHookUtils();

  return (
    <div>
      <div data-testid="utils-available">{utils ? 'true' : 'false'}</div>
      <button 
        data-testid="invalidate-queries"
        onClick={() => utils.invalidateQueries(['test'])}
      >
        Invalidate Queries
      </button>
    </div>
  );
}

// Test component for useApiStatus
function TestApiStatus() {
  const status = useApiStatus();
  const apiStatus = status.getApiStatus();

  return (
    <div>
      <div data-testid="api-status-total">{apiStatus.total}</div>
      <div data-testid="api-status-loading">{apiStatus.loading}</div>
      <div data-testid="api-status-error">{apiStatus.error}</div>
      <div data-testid="api-status-success">{apiStatus.success}</div>
      <div data-testid="api-status-health">{apiStatus.healthScore}</div>
    </div>
  );
}

describe('Base Hooks', () => {
  describe('useBaseQuery', () => {
    it('should execute query and return data', async () => {
      render(
        <TestWrapper>
          <TestBaseQuery />
        </TestWrapper>
      );

      // Initially loading
      expect(screen.getByTestId('loading')).toHaveTextContent('true');
      expect(screen.getByTestId('data')).toHaveTextContent('none');

      // Wait for query to complete
      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('false');
      });

      // Check results
      expect(screen.getByTestId('error')).toHaveTextContent('none');
      expect(screen.getByTestId('data')).toHaveTextContent('{"message":"Hello from base query!"}');
    });
  });

  describe('useBaseMutation', () => {
    it('should execute mutation when triggered', async () => {
      render(
        <TestWrapper>
          <TestBaseMutation />
        </TestWrapper>
      );

      // Initially not loading
      expect(screen.getByTestId('mutation-loading')).toHaveTextContent('false');
      expect(screen.getByTestId('mutation-data')).toHaveTextContent('none');

      // Trigger mutation
      const button = screen.getByTestId('mutation-trigger');
      button.click();

      // Wait for mutation to complete
      await waitFor(() => {
        expect(screen.getByTestId('mutation-loading')).toHaveTextContent('false');
      });

      // Check results
      expect(screen.getByTestId('mutation-error')).toHaveTextContent('none');
      expect(screen.getByTestId('mutation-data')).toHaveTextContent('{"result":"Processed: test"}');
    });
  });

  describe('usePaginatedQuery', () => {
    it('should execute paginated query and return paginated data', async () => {
      render(
        <TestWrapper>
          <TestPaginatedQuery />
        </TestWrapper>
      );

      // Wait for query to complete
      await waitFor(() => {
        expect(screen.getByTestId('paginated-loading')).toHaveTextContent('false');
      });

      // Check results
      expect(screen.getByTestId('paginated-error')).toHaveTextContent('none');
      
      const dataElement = screen.getByTestId('paginated-data');
      const dataText = dataElement.textContent;
      expect(dataText).toContain('Item 1');
      expect(dataText).toContain('Item 2');
      expect(dataText).toContain('pagination');
    });
  });

  describe('useApiHookUtils', () => {
    it('should provide utility functions', () => {
      render(
        <TestWrapper>
          <TestApiHookUtils />
        </TestWrapper>
      );

      expect(screen.getByTestId('utils-available')).toHaveTextContent('true');
      
      // Should not throw when clicking invalidate button
      const button = screen.getByTestId('invalidate-queries');
      expect(() => button.click()).not.toThrow();
    });
  });

  describe('useApiStatus', () => {
    it('should provide API status information', async () => {
      render(
        <TestWrapper>
          <TestApiStatus />
        </TestWrapper>
      );

      // Check that status values are numbers
      const total = screen.getByTestId('api-status-total').textContent;
      const loading = screen.getByTestId('api-status-loading').textContent;
      const error = screen.getByTestId('api-status-error').textContent;
      const success = screen.getByTestId('api-status-success').textContent;
      const health = screen.getByTestId('api-status-health').textContent;

      expect(total).toMatch(/^\d+$/);
      expect(loading).toMatch(/^\d+$/);
      expect(error).toMatch(/^\d+$/);
      expect(success).toMatch(/^\d+$/);
      expect(health).toMatch(/^\d+(\.\d+)?$/);
    });
  });
});
