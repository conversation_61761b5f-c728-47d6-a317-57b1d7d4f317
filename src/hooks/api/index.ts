/**
 * API Hooks Index
 * Central export for all API hooks
 */

// Base hooks and utilities
export * from './base-hooks';

// Domain-specific hooks
export * from './auth-hooks';
export * from './football-hooks';
export * from './users';
export * from './broadcast-hooks';
export * from './health-hooks';

// Re-export TanStack Query hooks for convenience
export {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery,
} from '@tanstack/react-query';

/**
 * API hooks library metadata
 */
export const API_HOOKS_VERSION = '1.0.0';
export const API_HOOKS_NAME = 'APISportsGame API Hooks';

/**
 * Quick setup function for API hooks
 */
export function setupApiHooks() {
  console.log(`${API_HOOKS_NAME} v${API_HOOKS_VERSION} initialized`);
}
