/**
 * Store Types and Interfaces
 * Defines TypeScript types for all store modules
 */

// ============================================================================
// Base Store Types
// ============================================================================

/**
 * Base store state interface
 * All stores should extend this interface
 */
export interface BaseStoreState {
  // Hydration state for SSR compatibility
  _hasHydrated: boolean;
  setHasHydrated: (hasHydrated: boolean) => void;
}

/**
 * Store action interface
 * Defines the structure for store actions
 */
export interface StoreAction<T = any> {
  type: string;
  payload?: T;
}

/**
 * Store slice interface
 * For modular store composition
 */
export interface StoreSlice<T> {
  (...args: any[]): T;
}

// ============================================================================
// User and Authentication Types
// ============================================================================

/**
 * System user roles
 */
export type SystemUserRole = 'Admin' | 'Editor' | 'Moderator';

/**
 * System user interface
 */
export interface SystemUser {
  id: string;
  email: string;
  name: string;
  role: SystemUserRole;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
}

/**
 * Authentication tokens
 */
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number; // Unix timestamp
}

/**
 * Authentication state
 */
export interface AuthState {
  // User data
  user: SystemUser | null;
  tokens: AuthTokens | null;
  
  // Authentication status
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // Error handling
  error: string | null;
  
  // Session management
  lastActivity: number; // Unix timestamp
  sessionTimeout: number; // Minutes
}

/**
 * Authentication actions
 */
export interface AuthActions {
  // Login/logout
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  logoutAll: () => Promise<void>;
  
  // User management
  updateProfile: (data: Partial<SystemUser>) => Promise<void>;
  refreshTokens: () => Promise<void>;
  
  // State management
  setUser: (user: SystemUser | null) => void;
  setTokens: (tokens: AuthTokens | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Session management
  updateLastActivity: () => void;
  checkSession: () => boolean;
  
  // Hydration
  hydrate: () => void;
}

// ============================================================================
// Application State Types
// ============================================================================

/**
 * Theme configuration
 */
export interface ThemeConfig {
  mode: 'light' | 'dark';
  primaryColor: string;
  borderRadius: number;
  compactMode: boolean;
}

/**
 * Navigation state
 */
export interface NavigationState {
  currentPath: string;
  breadcrumbs: Array<{
    title: string;
    path: string;
  }>;
  sidebarCollapsed: boolean;
  activeMenuKey: string;
}

/**
 * Global UI state
 */
export interface UIState {
  // Loading states
  globalLoading: boolean;
  loadingMessage: string;
  
  // Error states
  globalError: string | null;
  errorDetails: any;
  
  // Notification state
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message: string;
    duration?: number;
    timestamp: number;
  }>;
  
  // Modal state
  modals: Record<string, {
    visible: boolean;
    data?: any;
  }>;
}

/**
 * Application settings
 */
export interface AppSettings {
  // Language and localization
  language: 'en' | 'vi';
  timezone: string;
  dateFormat: string;
  
  // Data preferences
  pageSize: number;
  autoRefresh: boolean;
  refreshInterval: number; // Seconds
  
  // Feature flags
  features: {
    darkMode: boolean;
    notifications: boolean;
    autoSave: boolean;
    advancedFilters: boolean;
  };
}

/**
 * Application state
 */
export interface AppState {
  // Configuration
  theme: ThemeConfig;
  settings: AppSettings;
  
  // Navigation
  navigation: NavigationState;
  
  // UI state
  ui: UIState;
  
  // System info
  version: string;
  buildTime: string;
  environment: 'development' | 'staging' | 'production';
}

/**
 * Application actions
 */
export interface AppActions {
  // Theme management
  setTheme: (theme: Partial<ThemeConfig>) => void;
  toggleTheme: () => void;
  
  // Settings management
  updateSettings: (settings: Partial<AppSettings>) => void;
  resetSettings: () => void;
  
  // Navigation
  setCurrentPath: (path: string) => void;
  setBreadcrumbs: (breadcrumbs: NavigationState['breadcrumbs']) => void;
  toggleSidebar: () => void;
  setActiveMenu: (key: string) => void;
  
  // UI state management
  setGlobalLoading: (loading: boolean, message?: string) => void;
  setGlobalError: (error: string | null, details?: any) => void;
  clearGlobalError: () => void;
  
  // Notifications
  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  
  // Modals
  showModal: (key: string, data?: any) => void;
  hideModal: (key: string) => void;
  hideAllModals: () => void;
}

// ============================================================================
// Combined Store Types
// ============================================================================

/**
 * Complete authentication store
 */
export interface AuthStore extends BaseStoreState, AuthState, AuthActions {}

/**
 * Complete application store
 */
export interface AppStore extends BaseStoreState, AppState, AppActions {}

// ============================================================================
// Store Configuration Types
// ============================================================================

/**
 * Store persistence configuration
 */
export interface StorePersistConfig {
  name: string;
  version: number;
  partialize?: (state: any) => any;
  skipHydration?: boolean;
}

/**
 * Store devtools configuration
 */
export interface StoreDevtoolsConfig {
  name: string;
  enabled: boolean;
}

/**
 * Store configuration
 */
export interface StoreConfig {
  persist?: StorePersistConfig;
  devtools?: StoreDevtoolsConfig;
}
