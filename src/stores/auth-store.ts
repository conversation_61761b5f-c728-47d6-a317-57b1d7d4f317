/**
 * Authentication Store
 * Manages user authentication state, tokens, and session
 */

import { create } from 'zustand';
import {
  AuthStore,
  SystemUser,
  AuthTokens,
  SystemUserRole
} from './types';
import {
  createStoreWithMiddleware,
  createBaseStoreActions,
  extractErrorMessage,
  isTokenExpired,
  getTokenExpiration,
  clearStoredTokens,
  isSessionValid,
  logStoreAction
} from './utils';
import {
  STORE_NAMES,
  STORAGE_KEYS,
  SESSION_TIMEOUT,
  TOKEN_REFRESH_THRESHOLD,
  STORE_API_ENDPOINTS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  STORE_VERSIONS
} from './constants';

// ============================================================================
// Initial State
// ============================================================================

const initialAuthState = {
  // Base store state
  _hasHydrated: false,

  // User data
  user: null,
  tokens: null,

  // Authentication status
  isAuthenticated: false,
  isLoading: false,

  // Error handling
  error: null,

  // Session management
  lastActivity: Date.now(),
  sessionTimeout: SESSION_TIMEOUT,
};

// ============================================================================
// Store Implementation
// ============================================================================

/**
 * Authentication Store Creator
 */
const createAuthStore = () => {
  return create<AuthStore>()(
    createStoreWithMiddleware<AuthStore>(
      (set, get) => ({
        ...initialAuthState,

        // Base store actions
        ...createBaseStoreActions<AuthStore>(set),

        // ========================================================================
        // Authentication Actions
        // ========================================================================

        /**
         * Login user with email and password
         */
        login: async (email: string, password: string) => {
          logStoreAction(STORE_NAMES.AUTH, 'login', { email });

          set({ isLoading: true, error: null });

          try {
            const response = await fetch(STORE_API_ENDPOINTS.AUTH.LOGIN, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ email, password }),
            });

            if (!response.ok) {
              const errorData = await response.json().catch(() => ({}));
              throw new Error(errorData.message || ERROR_MESSAGES.AUTH.LOGIN_FAILED);
            }

            const data = await response.json();

            if (data.success && data.data) {
              const { user, accessToken, refreshToken } = data.data;

              // Create tokens object
              const tokens: AuthTokens = {
                accessToken,
                refreshToken,
                expiresAt: getTokenExpiration(accessToken),
              };

              // Update store state
              set({
                user,
                tokens,
                isAuthenticated: true,
                isLoading: false,
                error: null,
                lastActivity: Date.now(),
              });

              logStoreAction(STORE_NAMES.AUTH, 'login_success', { userId: user.id });
            } else {
              throw new Error(data.message || ERROR_MESSAGES.AUTH.LOGIN_FAILED);
            }
          } catch (error) {
            const errorMessage = extractErrorMessage(error);
            logStoreAction(STORE_NAMES.AUTH, 'login_error', { error: errorMessage });

            set({
              isLoading: false,
              error: errorMessage,
              isAuthenticated: false,
              user: null,
              tokens: null,
            });

            throw error;
          }
        },

        /**
         * Logout user from current session
         */
        logout: async () => {
          logStoreAction(STORE_NAMES.AUTH, 'logout');

          const { tokens } = get();

          try {
            // Call logout API if we have tokens
            if (tokens?.accessToken) {
              await fetch(STORE_API_ENDPOINTS.AUTH.LOGOUT, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${tokens.accessToken}`,
                },
              });
            }
          } catch (error) {
            // Log error but don't prevent logout
            logStoreAction(STORE_NAMES.AUTH, 'logout_api_error', { error: extractErrorMessage(error) });
          }

          // Clear local state regardless of API call result
          clearStoredTokens();
          set({
            user: null,
            tokens: null,
            isAuthenticated: false,
            error: null,
            lastActivity: Date.now(),
          });

          logStoreAction(STORE_NAMES.AUTH, 'logout_success');
        },

        /**
         * Logout user from all devices
         */
        logoutAll: async () => {
          logStoreAction(STORE_NAMES.AUTH, 'logout_all');

          const { tokens } = get();

          try {
            if (tokens?.accessToken) {
              await fetch(STORE_API_ENDPOINTS.AUTH.LOGOUT_ALL, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${tokens.accessToken}`,
                },
              });
            }
          } catch (error) {
            logStoreAction(STORE_NAMES.AUTH, 'logout_all_api_error', { error: extractErrorMessage(error) });
          }

          // Clear local state
          clearStoredTokens();
          set({
            user: null,
            tokens: null,
            isAuthenticated: false,
            error: null,
            lastActivity: Date.now(),
          });

          logStoreAction(STORE_NAMES.AUTH, 'logout_all_success');
        },

        // ========================================================================
        // User Profile Actions
        // ========================================================================

        /**
         * Update user profile
         */
        updateProfile: async (data: Partial<SystemUser>) => {
          logStoreAction(STORE_NAMES.AUTH, 'update_profile', data);

          const { tokens, user } = get();

          if (!tokens?.accessToken || !user) {
            throw new Error(ERROR_MESSAGES.AUTH.UNAUTHORIZED);
          }

          set({ isLoading: true, error: null });

          try {
            const response = await fetch(STORE_API_ENDPOINTS.AUTH.PROFILE, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${tokens.accessToken}`,
              },
              body: JSON.stringify(data),
            });

            if (!response.ok) {
              const errorData = await response.json().catch(() => ({}));
              throw new Error(errorData.message || ERROR_MESSAGES.AUTH.PROFILE_UPDATE_FAILED);
            }

            const responseData = await response.json();

            if (responseData.success && responseData.data) {
              set({
                user: { ...user, ...responseData.data },
                isLoading: false,
                error: null,
                lastActivity: Date.now(),
              });

              logStoreAction(STORE_NAMES.AUTH, 'update_profile_success');
            } else {
              throw new Error(responseData.message || ERROR_MESSAGES.AUTH.PROFILE_UPDATE_FAILED);
            }
          } catch (error) {
            const errorMessage = extractErrorMessage(error);
            logStoreAction(STORE_NAMES.AUTH, 'update_profile_error', { error: errorMessage });

            set({
              isLoading: false,
              error: errorMessage,
            });

            throw error;
          }
        },

        /**
         * Refresh authentication tokens
         */
        refreshTokens: async () => {
          logStoreAction(STORE_NAMES.AUTH, 'refresh_tokens');

          const { tokens } = get();

          if (!tokens?.refreshToken) {
            throw new Error(ERROR_MESSAGES.AUTH.TOKEN_REFRESH_FAILED);
          }

          try {
            const response = await fetch(STORE_API_ENDPOINTS.AUTH.REFRESH, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ refreshToken: tokens.refreshToken }),
            });

            if (!response.ok) {
              throw new Error(ERROR_MESSAGES.AUTH.TOKEN_REFRESH_FAILED);
            }

            const data = await response.json();

            if (data.success && data.data) {
              const { accessToken, refreshToken } = data.data;

              const newTokens: AuthTokens = {
                accessToken,
                refreshToken,
                expiresAt: getTokenExpiration(accessToken),
              };

              set({
                tokens: newTokens,
                lastActivity: Date.now(),
              });

              logStoreAction(STORE_NAMES.AUTH, 'refresh_tokens_success');
            } else {
              throw new Error(data.message || ERROR_MESSAGES.AUTH.TOKEN_REFRESH_FAILED);
            }
          } catch (error) {
            const errorMessage = extractErrorMessage(error);
            logStoreAction(STORE_NAMES.AUTH, 'refresh_tokens_error', { error: errorMessage });

            // If refresh fails, logout user
            get().logout();
            throw error;
          }
        },

        // ========================================================================
        // State Management Actions
        // ========================================================================

        /**
         * Set user data
         */
        setUser: (user: SystemUser | null) => {
          set({ user, isAuthenticated: !!user });
          logStoreAction(STORE_NAMES.AUTH, 'set_user', { userId: user?.id });
        },

        /**
         * Set authentication tokens
         */
        setTokens: (tokens: AuthTokens | null) => {
          set({ tokens });
          logStoreAction(STORE_NAMES.AUTH, 'set_tokens', { hasTokens: !!tokens });
        },

        /**
         * Set loading state
         */
        setLoading: (loading: boolean) => {
          set({ isLoading: loading });
        },

        /**
         * Set error message
         */
        setError: (error: string | null) => {
          set({ error });
          if (error) {
            logStoreAction(STORE_NAMES.AUTH, 'set_error', { error });
          }
        },

        /**
         * Clear error message
         */
        clearError: () => {
          set({ error: null });
        },

        // ========================================================================
        // Session Management Actions
        // ========================================================================

        /**
         * Update last activity timestamp
         */
        updateLastActivity: () => {
          set({ lastActivity: Date.now() });
        },

        /**
         * Check if current session is valid
         */
        checkSession: () => {
          const { lastActivity, sessionTimeout, tokens } = get();

          // Check session timeout
          if (!isSessionValid(lastActivity, sessionTimeout)) {
            logStoreAction(STORE_NAMES.AUTH, 'session_expired');
            get().logout();
            return false;
          }

          // Check token expiration
          if (tokens && isTokenExpired(tokens.expiresAt)) {
            logStoreAction(STORE_NAMES.AUTH, 'token_expired');

            // Try to refresh tokens
            get().refreshTokens().catch(() => {
              // If refresh fails, logout will be called automatically
            });

            return false;
          }

          return true;
        },

        /**
         * Hydrate store from persisted state
         */
        hydrate: () => {
          const state = get();

          // Validate persisted session
          if (state.isAuthenticated && state.user && state.tokens) {
            const isValid = state.checkSession();
            if (!isValid) {
              // Session is invalid, clear state
              set({
                user: null,
                tokens: null,
                isAuthenticated: false,
                error: null,
              });
            }
          }

          set({ _hasHydrated: true });
          logStoreAction(STORE_NAMES.AUTH, 'hydrated');
        },
      }),
      {
        persist: {
          name: STORAGE_KEYS.AUTH,
          version: STORE_VERSIONS.AUTH,
          partialize: (state) => ({
            user: state.user,
            tokens: state.tokens,
            isAuthenticated: state.isAuthenticated,
            lastActivity: state.lastActivity,
            sessionTimeout: state.sessionTimeout,
          }),
        },
        devtools: {
          name: STORE_NAMES.AUTH,
          enabled: process.env.NODE_ENV === 'development',
        },
      }
    )
  );
};

// ============================================================================
// Export Store
// ============================================================================

export const useAuthStore = createAuthStore();

// Export store for testing and advanced usage
export { createAuthStore };
