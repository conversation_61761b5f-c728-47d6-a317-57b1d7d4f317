/**
 * Provider Hooks - Custom hooks for using stores with providers
 * Provides convenient access to stores through context
 */

'use client';

import { useCallback, useMemo } from 'react';
import {
  useStoreContext,
  useAuthStoreContext,
  useAppStoreContext,
  useIsStoreContextAvailable
} from './store-context';
import type { SystemUser, AuthState, AppState } from './types';

/**
 * Hook to use auth store with provider context
 * Provides auth state and actions
 */
export function useAuthProvider() {
  const authStore = useAuthStoreContext();

  const login = useCallback(async (credentials: { username: string; password: string }) => {
    return authStore.login(credentials);
  }, [authStore]);

  const logout = useCallback(async () => {
    return authStore.logout();
  }, [authStore]);

  const logoutAll = useCallback(async () => {
    return authStore.logoutAll();
  }, [authStore]);

  const refreshToken = useCallback(async () => {
    return authStore.refreshToken();
  }, [authStore]);

  const updateProfile = useCallback(async (data: Partial<SystemUser>) => {
    return authStore.updateProfile(data);
  }, [authStore]);

  const changePassword = useCallback(async (data: { currentPassword: string; newPassword: string }) => {
    return authStore.changePassword(data);
  }, [authStore]);

  return useMemo(() => ({
    // State
    user: authStore.user,
    token: authStore.token,
    refreshToken: authStore.refreshToken,
    isAuthenticated: authStore.isAuthenticated,
    isLoading: authStore.isLoading,
    error: authStore.error,
    isInitialized: authStore.isInitialized,

    // Actions
    login,
    logout,
    logoutAll,
    refreshToken: refreshToken,
    updateProfile,
    changePassword,
    clearError: authStore.clearError,
    reset: authStore.reset,
  }), [
    authStore.user,
    authStore.token,
    authStore.refreshToken,
    authStore.isAuthenticated,
    authStore.isLoading,
    authStore.error,
    authStore.isInitialized,
    login,
    logout,
    logoutAll,
    refreshToken,
    updateProfile,
    changePassword,
    authStore.clearError,
    authStore.reset,
  ]);
}

/**
 * Hook to use app store with provider context
 * Provides app state and actions
 */
export function useAppProvider() {
  const appStore = useAppStoreContext();

  const setTheme = useCallback((theme: 'light' | 'dark') => {
    appStore.setTheme(theme);
  }, [appStore]);

  const toggleTheme = useCallback(() => {
    appStore.toggleTheme();
  }, [appStore]);

  const setLanguage = useCallback((language: string) => {
    appStore.setLanguage(language);
  }, [appStore]);

  const setSidebarCollapsed = useCallback((collapsed: boolean) => {
    appStore.setSidebarCollapsed(collapsed);
  }, [appStore]);

  const toggleSidebar = useCallback(() => {
    appStore.toggleSidebar();
  }, [appStore]);

  const setLoading = useCallback((loading: boolean) => {
    appStore.setLoading(loading);
  }, [appStore]);

  const showNotification = useCallback((notification: { type: 'success' | 'error' | 'warning' | 'info'; message: string; description?: string }) => {
    appStore.showNotification(notification);
  }, [appStore]);

  const hideNotification = useCallback(() => {
    appStore.hideNotification();
  }, [appStore]);

  return useMemo(() => ({
    // State
    theme: appStore.theme,
    language: appStore.language,
    sidebarCollapsed: appStore.sidebarCollapsed,
    isLoading: appStore.isLoading,
    notification: appStore.notification,
    isInitialized: appStore.isInitialized,

    // Actions
    setTheme,
    toggleTheme,
    setLanguage,
    setSidebarCollapsed,
    toggleSidebar,
    setLoading,
    showNotification,
    hideNotification,
    reset: appStore.reset,
  }), [
    appStore.theme,
    appStore.language,
    appStore.sidebarCollapsed,
    appStore.isLoading,
    appStore.notification,
    appStore.isInitialized,
    setTheme,
    toggleTheme,
    setLanguage,
    setSidebarCollapsed,
    toggleSidebar,
    setLoading,
    showNotification,
    hideNotification,
    appStore.reset,
  ]);
}

/**
 * Hook to check if stores are available
 */
export function useStoreAvailability() {
  const isAvailable = useIsStoreContextAvailable();

  return useMemo(() => ({
    isAvailable,
    isStoreReady: isAvailable,
  }), [isAvailable]);
}

/**
 * Hook to get all stores
 */
export function useStores() {
  const context = useStoreContext();

  return useMemo(() => ({
    authStore: context.authStore,
    appStore: context.appStore,
  }), [context.authStore, context.appStore]);
}

/**
 * Hook for development/debugging purposes
 */
export function useStoreDebug() {
  const { authStore, appStore } = useStores();

  return useMemo(() => ({
    authState: {
      user: authStore.user,
      isAuthenticated: authStore.isAuthenticated,
      isLoading: authStore.isLoading,
      error: authStore.error,
      isInitialized: authStore.isInitialized,
    } as AuthState,
    appState: {
      theme: appStore.theme,
      language: appStore.language,
      sidebarCollapsed: appStore.sidebarCollapsed,
      isLoading: appStore.isLoading,
      notification: appStore.notification,
      isInitialized: appStore.isInitialized,
    } as AppState,
    actions: {
      resetAuth: authStore.reset,
      resetApp: appStore.reset,
      clearAuthError: authStore.clearError,
      hideNotification: appStore.hideNotification,
    },
  }), [authStore, appStore]);
}
