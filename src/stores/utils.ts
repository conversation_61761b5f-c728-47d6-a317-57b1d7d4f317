/**
 * Store Utilities
 * Helper functions and utilities for store management
 */

import { StateCreator } from 'zustand';
import { persist, devtools, createJSONStorage } from 'zustand/middleware';
import type {
  BaseStoreState,
  StoreConfig,
  StorePersistConfig,
  StoreDevtoolsConfig
} from './types';

// ============================================================================
// Store Creation Utilities
// ============================================================================

/**
 * Create a store with middleware support
 * Provides a consistent way to create stores with persistence and devtools
 */
export function createStoreWithMiddleware<T extends BaseStoreState>(
  storeCreator: StateCreator<T>,
  config: StoreConfig
): StateCreator<T> {
  let store: any = storeCreator;

  // Apply persistence middleware if configured
  if (config.persist) {
    store = persist(
      store,
      {
        name: config.persist.name,
        version: config.persist.version,
        storage: createJSONStorage(() => localStorage),
        partialize: config.persist.partialize || ((state) => state),
        skipHydration: config.persist.skipHydration || false,
        onRehydrateStorage: () => (state: any) => {
          if (state) {
            state.setHasHydrated(true);
          }
        },
      }
    );
  }

  // Apply devtools middleware if configured
  if (config.devtools) {
    store = devtools(
      store,
      {
        name: config.devtools.name,
        enabled: config.devtools.enabled && process.env.NODE_ENV === 'development',
      }
    );
  }

  return store as StateCreator<T>;
}

// ============================================================================
// Base Store State Utilities
// ============================================================================

/**
 * Create base store state
 * Provides common state properties for all stores
 */
export function createBaseStoreState(): BaseStoreState {
  return {
    _hasHydrated: false,
    setHasHydrated: (hasHydrated: boolean) => {
      // This will be implemented by the actual store
    },
  };
}

/**
 * Create base store actions
 * Provides common actions for all stores
 */
export function createBaseStoreActions<T extends BaseStoreState>(
  set: (partial: Partial<T>) => void
): Pick<BaseStoreState, 'setHasHydrated'> {
  return {
    setHasHydrated: (hasHydrated: boolean) => {
      set({ _hasHydrated: hasHydrated } as Partial<T>);
    },
  };
}

// ============================================================================
// Token Management Utilities
// ============================================================================

/**
 * Check if token is expired
 */
export function isTokenExpired(expiresAt: number): boolean {
  return Date.now() >= expiresAt;
}

/**
 * Get token expiration time
 * Calculates expiration time from JWT token or sets default
 */
export function getTokenExpiration(token: string, defaultMinutes: number = 60): number {
  try {
    // Try to decode JWT token to get expiration
    const payload = JSON.parse(atob(token.split('.')[1]));
    if (payload.exp) {
      return payload.exp * 1000; // Convert to milliseconds
    }
  } catch (error) {
    // If JWT parsing fails, use default expiration
  }

  // Default expiration: current time + defaultMinutes
  return Date.now() + (defaultMinutes * 60 * 1000);
}

/**
 * Clear all stored tokens
 */
export function clearStoredTokens(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth-storage');
    sessionStorage.removeItem('auth-storage');
  }
}

// ============================================================================
// Session Management Utilities
// ============================================================================

/**
 * Check if session is valid
 */
export function isSessionValid(
  lastActivity: number,
  sessionTimeout: number
): boolean {
  const now = Date.now();
  const timeoutMs = sessionTimeout * 60 * 1000; // Convert minutes to milliseconds
  return (now - lastActivity) < timeoutMs;
}

/**
 * Get session remaining time in minutes
 */
export function getSessionRemainingTime(
  lastActivity: number,
  sessionTimeout: number
): number {
  const now = Date.now();
  const timeoutMs = sessionTimeout * 60 * 1000;
  const elapsed = now - lastActivity;
  const remaining = timeoutMs - elapsed;
  return Math.max(0, Math.floor(remaining / (60 * 1000)));
}

// ============================================================================
// Error Handling Utilities
// ============================================================================

/**
 * Extract error message from various error types
 */
export function extractErrorMessage(error: any): string {
  if (typeof error === 'string') {
    return error;
  }

  if (error?.response?.data?.message) {
    return error.response.data.message;
  }

  if (error?.message) {
    return error.message;
  }

  if (error?.error) {
    return error.error;
  }

  return 'An unexpected error occurred';
}

/**
 * Create error object with details
 */
export function createErrorObject(
  message: string,
  details?: any
): { message: string; details: any } {
  return {
    message,
    details: details || null,
  };
}

// ============================================================================
// Notification Utilities
// ============================================================================

/**
 * Generate unique notification ID
 */
export function generateNotificationId(): string {
  return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Get default notification duration based on type
 */
export function getDefaultNotificationDuration(
  type: 'success' | 'error' | 'warning' | 'info'
): number {
  switch (type) {
    case 'success':
      return 3000; // 3 seconds
    case 'error':
      return 5000; // 5 seconds
    case 'warning':
      return 4000; // 4 seconds
    case 'info':
      return 3000; // 3 seconds
    default:
      return 3000;
  }
}

// ============================================================================
// Local Storage Utilities
// ============================================================================

/**
 * Safe localStorage operations
 */
export const storage = {
  get: (key: string): any => {
    if (typeof window === 'undefined') return null;

    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.warn(`Error reading from localStorage key "${key}":`, error);
      return null;
    }
  },

  set: (key: string, value: any): void => {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.warn(`Error writing to localStorage key "${key}":`, error);
    }
  },

  remove: (key: string): void => {
    if (typeof window === 'undefined') return;

    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error);
    }
  },

  clear: (): void => {
    if (typeof window === 'undefined') return;

    try {
      localStorage.clear();
    } catch (error) {
      console.warn('Error clearing localStorage:', error);
    }
  },
};

// ============================================================================
// Development Utilities
// ============================================================================

/**
 * Log store action for debugging
 */
export function logStoreAction(
  storeName: string,
  actionName: string,
  payload?: any
): void {
  if (process.env.NODE_ENV === 'development') {
    console.group(`🐻 [${storeName}] ${actionName}`);
    if (payload !== undefined) {
      console.log('Payload:', payload);
    }
    console.log('Timestamp:', new Date().toISOString());
    console.groupEnd();
  }
}

/**
 * Validate store state structure
 */
export function validateStoreState<T extends BaseStoreState>(
  state: T,
  requiredKeys: (keyof T)[]
): boolean {
  for (const key of requiredKeys) {
    if (!(key in state)) {
      console.error(`Missing required store state key: ${String(key)}`);
      return false;
    }
  }
  return true;
}
