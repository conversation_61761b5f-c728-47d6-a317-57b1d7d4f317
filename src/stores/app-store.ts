/**
 * Application Store
 * Manages application state, theme, settings, navigation, and UI state
 */

import { create } from 'zustand';
import { 
  AppStore, 
  ThemeConfig,
  AppSettings,
  NavigationState,
  UIState
} from './types';
import { 
  createStoreWithMiddleware,
  createBaseStoreActions,
  generateNotificationId,
  getDefaultNotificationDuration,
  logStoreAction
} from './utils';
import { 
  STORE_NAMES,
  STORAGE_KEYS,
  DEFAULT_THEME,
  DEFAULT_APP_SETTINGS,
  DEFAULT_NAVIGATION,
  DEFAULT_UI_STATE,
  SUCCESS_MESSAGES,
  ERROR_MESSAGES,
  STORE_VERSIONS
} from './constants';

// ============================================================================
// Initial State
// ============================================================================

const initialAppState = {
  // Base store state
  _hasHydrated: false,
  
  // Configuration
  theme: DEFAULT_THEME,
  settings: DEFAULT_APP_SETTINGS,
  
  // Navigation
  navigation: DEFAULT_NAVIGATION,
  
  // UI state
  ui: DEFAULT_UI_STATE,
  
  // System info
  version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
  buildTime: process.env.NEXT_PUBLIC_BUILD_TIME || new Date().toISOString(),
  environment: (process.env.NODE_ENV as 'development' | 'staging' | 'production') || 'development',
};

// ============================================================================
// Store Implementation
// ============================================================================

/**
 * Application Store Creator
 */
const createAppStore = () => {
  return create<AppStore>()(
    createStoreWithMiddleware<AppStore>(
      (set, get) => ({
        ...initialAppState,
        
        // Base store actions
        ...createBaseStoreActions<AppStore>(set),
        
        // ========================================================================
        // Theme Management Actions
        // ========================================================================
        
        /**
         * Set theme configuration
         */
        setTheme: (themeUpdate: Partial<ThemeConfig>) => {
          const currentTheme = get().theme;
          const newTheme = { ...currentTheme, ...themeUpdate };
          
          set({ theme: newTheme });
          logStoreAction(STORE_NAMES.APP, 'set_theme', themeUpdate);
        },
        
        /**
         * Toggle between light and dark mode
         */
        toggleTheme: () => {
          const currentMode = get().theme.mode;
          const newMode = currentMode === 'light' ? 'dark' : 'light';
          
          get().setTheme({ mode: newMode });
          logStoreAction(STORE_NAMES.APP, 'toggle_theme', { newMode });
        },
        
        // ========================================================================
        // Settings Management Actions
        // ========================================================================
        
        /**
         * Update application settings
         */
        updateSettings: (settingsUpdate: Partial<AppSettings>) => {
          const currentSettings = get().settings;
          const newSettings = { ...currentSettings, ...settingsUpdate };
          
          set({ settings: newSettings });
          logStoreAction(STORE_NAMES.APP, 'update_settings', settingsUpdate);
        },
        
        /**
         * Reset settings to default values
         */
        resetSettings: () => {
          set({ settings: DEFAULT_APP_SETTINGS });
          logStoreAction(STORE_NAMES.APP, 'reset_settings');
        },
        
        // ========================================================================
        // Navigation Actions
        // ========================================================================
        
        /**
         * Set current path
         */
        setCurrentPath: (path: string) => {
          const currentNavigation = get().navigation;
          const newNavigation = { ...currentNavigation, currentPath: path };
          
          set({ navigation: newNavigation });
          logStoreAction(STORE_NAMES.APP, 'set_current_path', { path });
        },
        
        /**
         * Set breadcrumbs
         */
        setBreadcrumbs: (breadcrumbs: NavigationState['breadcrumbs']) => {
          const currentNavigation = get().navigation;
          const newNavigation = { ...currentNavigation, breadcrumbs };
          
          set({ navigation: newNavigation });
          logStoreAction(STORE_NAMES.APP, 'set_breadcrumbs', { count: breadcrumbs.length });
        },
        
        /**
         * Toggle sidebar collapsed state
         */
        toggleSidebar: () => {
          const currentNavigation = get().navigation;
          const newCollapsed = !currentNavigation.sidebarCollapsed;
          const newNavigation = { ...currentNavigation, sidebarCollapsed: newCollapsed };
          
          set({ navigation: newNavigation });
          logStoreAction(STORE_NAMES.APP, 'toggle_sidebar', { collapsed: newCollapsed });
        },
        
        /**
         * Set active menu key
         */
        setActiveMenu: (key: string) => {
          const currentNavigation = get().navigation;
          const newNavigation = { ...currentNavigation, activeMenuKey: key };
          
          set({ navigation: newNavigation });
          logStoreAction(STORE_NAMES.APP, 'set_active_menu', { key });
        },
        
        // ========================================================================
        // UI State Management Actions
        // ========================================================================
        
        /**
         * Set global loading state
         */
        setGlobalLoading: (loading: boolean, message?: string) => {
          const currentUI = get().ui;
          const newUI = { 
            ...currentUI, 
            globalLoading: loading,
            loadingMessage: message || ''
          };
          
          set({ ui: newUI });
          logStoreAction(STORE_NAMES.APP, 'set_global_loading', { loading, message });
        },
        
        /**
         * Set global error
         */
        setGlobalError: (error: string | null, details?: any) => {
          const currentUI = get().ui;
          const newUI = { 
            ...currentUI, 
            globalError: error,
            errorDetails: details || null
          };
          
          set({ ui: newUI });
          logStoreAction(STORE_NAMES.APP, 'set_global_error', { error, hasDetails: !!details });
        },
        
        /**
         * Clear global error
         */
        clearGlobalError: () => {
          const currentUI = get().ui;
          const newUI = { 
            ...currentUI, 
            globalError: null,
            errorDetails: null
          };
          
          set({ ui: newUI });
          logStoreAction(STORE_NAMES.APP, 'clear_global_error');
        },
        
        // ========================================================================
        // Notifications Actions
        // ========================================================================
        
        /**
         * Add notification
         */
        addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => {
          const id = generateNotificationId();
          const timestamp = Date.now();
          const duration = notification.duration || getDefaultNotificationDuration(notification.type);
          
          const newNotification = {
            ...notification,
            id,
            timestamp,
            duration,
          };
          
          const currentUI = get().ui;
          const newNotifications = [...currentUI.notifications, newNotification];
          const newUI = { ...currentUI, notifications: newNotifications };
          
          set({ ui: newUI });
          logStoreAction(STORE_NAMES.APP, 'add_notification', { type: notification.type, id });
          
          // Auto-remove notification after duration
          if (duration > 0) {
            setTimeout(() => {
              get().removeNotification(id);
            }, duration);
          }
        },
        
        /**
         * Remove notification
         */
        removeNotification: (id: string) => {
          const currentUI = get().ui;
          const newNotifications = currentUI.notifications.filter(n => n.id !== id);
          const newUI = { ...currentUI, notifications: newNotifications };
          
          set({ ui: newUI });
          logStoreAction(STORE_NAMES.APP, 'remove_notification', { id });
        },
        
        /**
         * Clear all notifications
         */
        clearNotifications: () => {
          const currentUI = get().ui;
          const newUI = { ...currentUI, notifications: [] };
          
          set({ ui: newUI });
          logStoreAction(STORE_NAMES.APP, 'clear_notifications');
        },
        
        // ========================================================================
        // Modals Actions
        // ========================================================================
        
        /**
         * Show modal
         */
        showModal: (key: string, data?: any) => {
          const currentUI = get().ui;
          const newModals = { 
            ...currentUI.modals, 
            [key]: { visible: true, data } 
          };
          const newUI = { ...currentUI, modals: newModals };
          
          set({ ui: newUI });
          logStoreAction(STORE_NAMES.APP, 'show_modal', { key, hasData: !!data });
        },
        
        /**
         * Hide modal
         */
        hideModal: (key: string) => {
          const currentUI = get().ui;
          const newModals = { 
            ...currentUI.modals, 
            [key]: { visible: false, data: undefined } 
          };
          const newUI = { ...currentUI, modals: newModals };
          
          set({ ui: newUI });
          logStoreAction(STORE_NAMES.APP, 'hide_modal', { key });
        },
        
        /**
         * Hide all modals
         */
        hideAllModals: () => {
          const currentUI = get().ui;
          const newModals: Record<string, { visible: boolean; data?: any }> = {};
          
          // Set all modals to hidden
          Object.keys(currentUI.modals).forEach(key => {
            newModals[key] = { visible: false, data: undefined };
          });
          
          const newUI = { ...currentUI, modals: newModals };
          
          set({ ui: newUI });
          logStoreAction(STORE_NAMES.APP, 'hide_all_modals');
        },
      }),
      {
        persist: {
          name: STORAGE_KEYS.APP,
          version: STORE_VERSIONS.APP,
          partialize: (state) => ({
            theme: state.theme,
            settings: state.settings,
            navigation: {
              sidebarCollapsed: state.navigation.sidebarCollapsed,
              activeMenuKey: state.navigation.activeMenuKey,
            },
          }),
        },
        devtools: {
          name: STORE_NAMES.APP,
          enabled: process.env.NODE_ENV === 'development',
        },
      }
    )
  );
};

// ============================================================================
// Export Store
// ============================================================================

export const useAppStore = createAppStore();

// Export store for testing and advanced usage
export { createAppStore };
