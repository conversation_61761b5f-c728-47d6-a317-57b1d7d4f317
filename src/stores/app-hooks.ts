/**
 * Application Hooks
 * Custom hooks for easy application state access
 */

'use client';

import { useCallback, useEffect } from 'react';
import { useAppStore } from './app-store';
import type { ThemeConfig, AppSettings, NavigationState, UIState } from './types';

// ============================================================================
// Theme Hooks
// ============================================================================

/**
 * Hook to get current theme configuration
 */
export const useTheme = () => {
  return useAppStore((state) => state.theme);
};

/**
 * Hook to get current theme mode
 */
export const useThemeMode = () => {
  return useAppStore((state) => state.theme.mode);
};

/**
 * Hook to check if dark mode is active
 */
export const useIsDarkMode = () => {
  return useAppStore((state) => state.theme.mode === 'dark');
};

/**
 * Hook to get theme actions
 */
export const useThemeActions = () => {
  const setTheme = useAppStore((state) => state.setTheme);
  const toggleTheme = useAppStore((state) => state.toggleTheme);

  return {
    setTheme,
    toggleTheme,
  };
};

// ============================================================================
// Settings Hooks
// ============================================================================

/**
 * Hook to get application settings
 */
export const useAppSettings = () => {
  return useAppStore((state) => state.settings);
};

/**
 * Hook to get settings actions
 */
export const useSettingsActions = () => {
  const updateSettings = useAppStore((state) => state.updateSettings);
  const resetSettings = useAppStore((state) => state.resetSettings);

  return {
    updateSettings,
    resetSettings,
  };
};

/**
 * Hook to get specific setting value
 */
export const useSetting = <K extends keyof AppSettings>(key: K) => {
  return useAppStore((state) => state.settings[key]);
};

// ============================================================================
// Navigation Hooks
// ============================================================================

/**
 * Hook to get navigation state
 */
export const useNavigation = () => {
  return useAppStore((state) => state.navigation);
};

/**
 * Hook to get current path
 */
export const useCurrentPath = () => {
  return useAppStore((state) => state.navigation.currentPath);
};

/**
 * Hook to get breadcrumbs
 */
export const useBreadcrumbs = () => {
  return useAppStore((state) => state.navigation.breadcrumbs);
};

/**
 * Hook to get sidebar state
 */
export const useSidebarState = () => {
  const collapsed = useAppStore((state) => state.navigation.sidebarCollapsed);
  const toggleSidebar = useAppStore((state) => state.toggleSidebar);

  return {
    collapsed,
    toggleSidebar,
  };
};

/**
 * Hook to get active menu key
 */
export const useActiveMenu = () => {
  return useAppStore((state) => state.navigation.activeMenuKey);
};

/**
 * Hook to get navigation actions
 */
export const useNavigationActions = () => {
  const setCurrentPath = useAppStore((state) => state.setCurrentPath);
  const setBreadcrumbs = useAppStore((state) => state.setBreadcrumbs);
  const toggleSidebar = useAppStore((state) => state.toggleSidebar);
  const setActiveMenu = useAppStore((state) => state.setActiveMenu);

  return {
    setCurrentPath,
    setBreadcrumbs,
    toggleSidebar,
    setActiveMenu,
  };
};

// ============================================================================
// UI State Hooks
// ============================================================================

/**
 * Hook to get UI state
 */
export const useUIState = () => {
  return useAppStore((state) => state.ui);
};

/**
 * Hook to get global loading state
 */
export const useGlobalLoading = () => {
  const loading = useAppStore((state) => state.ui.globalLoading);
  const message = useAppStore((state) => state.ui.loadingMessage);

  return { loading, message };
};

/**
 * Hook to get global error state
 */
export const useGlobalError = () => {
  const error = useAppStore((state) => state.ui.globalError);
  const details = useAppStore((state) => state.ui.errorDetails);

  return { error, details };
};

/**
 * Hook to get UI actions
 */
export const useUIActions = () => {
  const setGlobalLoading = useAppStore((state) => state.setGlobalLoading);
  const setGlobalError = useAppStore((state) => state.setGlobalError);
  const clearGlobalError = useAppStore((state) => state.clearGlobalError);

  return {
    setGlobalLoading,
    setGlobalError,
    clearGlobalError,
  };
};

// ============================================================================
// Notifications Hooks
// ============================================================================

/**
 * Hook to get notifications
 */
export const useNotifications = () => {
  return useAppStore((state) => state.ui.notifications);
};

/**
 * Hook to get notification actions
 */
export const useNotificationActions = () => {
  const addNotification = useAppStore((state) => state.addNotification);
  const removeNotification = useAppStore((state) => state.removeNotification);
  const clearNotifications = useAppStore((state) => state.clearNotifications);

  return {
    addNotification,
    removeNotification,
    clearNotifications,
  };
};

/**
 * Hook for easy notification creation
 */
export const useNotify = () => {
  const addNotification = useAppStore((state) => state.addNotification);

  const notify = useCallback(() => ({
    success: (message: string, title?: string) => {
      addNotification({
        type: 'success',
        title: title || 'Success',
        message,
      });
    },
    error: (message: string, title?: string) => {
      addNotification({
        type: 'error',
        title: title || 'Error',
        message,
      });
    },
    warning: (message: string, title?: string) => {
      addNotification({
        type: 'warning',
        title: title || 'Warning',
        message,
      });
    },
    info: (message: string, title?: string) => {
      addNotification({
        type: 'info',
        title: title || 'Info',
        message,
      });
    },
  }), [addNotification]);

  return notify();
};

// ============================================================================
// Modals Hooks
// ============================================================================

/**
 * Hook to get modals state
 */
export const useModals = () => {
  return useAppStore((state) => state.ui.modals);
};

/**
 * Hook to get specific modal state
 */
export const useModal = (key: string) => {
  const modal = useAppStore((state) => state.ui.modals[key]);
  const showModal = useAppStore((state) => state.showModal);
  const hideModal = useAppStore((state) => state.hideModal);

  const show = useCallback((data?: any) => {
    showModal(key, data);
  }, [showModal, key]);

  const hide = useCallback(() => {
    hideModal(key);
  }, [hideModal, key]);

  return {
    visible: modal?.visible || false,
    data: modal?.data,
    show,
    hide,
  };
};

/**
 * Hook to get modal actions
 */
export const useModalActions = () => {
  const showModal = useAppStore((state) => state.showModal);
  const hideModal = useAppStore((state) => state.hideModal);
  const hideAllModals = useAppStore((state) => state.hideAllModals);

  return {
    showModal,
    hideModal,
    hideAllModals,
  };
};

// ============================================================================
// System Info Hooks
// ============================================================================

/**
 * Hook to get application version
 */
export const useAppVersion = () => {
  return useAppStore((state) => state.version);
};

/**
 * Hook to get build time
 */
export const useBuildTime = () => {
  return useAppStore((state) => state.buildTime);
};

/**
 * Hook to get environment
 */
export const useEnvironment = () => {
  return useAppStore((state) => state.environment);
};

/**
 * Hook to get system info
 */
export const useSystemInfo = () => {
  const version = useAppVersion();
  const buildTime = useBuildTime();
  const environment = useEnvironment();

  return {
    version,
    buildTime,
    environment,
    isDevelopment: environment === 'development',
    isProduction: environment === 'production',
  };
};

// ============================================================================
// Composite Hooks
// ============================================================================

/**
 * Hook to get complete application state
 */
export const useApp = () => {
  const theme = useTheme();
  const settings = useAppSettings();
  const navigation = useNavigation();
  const ui = useUIState();
  const systemInfo = useSystemInfo();

  const themeActions = useThemeActions();
  const settingsActions = useSettingsActions();
  const navigationActions = useNavigationActions();
  const uiActions = useUIActions();
  const notificationActions = useNotificationActions();
  const modalActions = useModalActions();

  return {
    // State
    theme,
    settings,
    navigation,
    ui,
    systemInfo,

    // Actions
    ...themeActions,
    ...settingsActions,
    ...navigationActions,
    ...uiActions,
    ...notificationActions,
    ...modalActions,
  };
};

/**
 * Hook for responsive design utilities
 */
export const useResponsive = () => {
  const sidebarCollapsed = useAppStore((state) => state.navigation.sidebarCollapsed);
  const toggleSidebar = useAppStore((state) => state.toggleSidebar);

  // Auto-collapse sidebar on mobile
  useEffect(() => {
    const handleResize = () => {
      const isMobile = window.innerWidth < 768;
      if (isMobile && !sidebarCollapsed) {
        toggleSidebar();
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Check on mount

    return () => window.removeEventListener('resize', handleResize);
  }, [sidebarCollapsed, toggleSidebar]);

  return {
    isMobile: typeof window !== 'undefined' ? window.innerWidth < 768 : false,
    isTablet: typeof window !== 'undefined' ? window.innerWidth >= 768 && window.innerWidth < 1024 : false,
    isDesktop: typeof window !== 'undefined' ? window.innerWidth >= 1024 : false,
  };
};
