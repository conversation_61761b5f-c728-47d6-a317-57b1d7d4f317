/**
 * Application Utilities
 * Helper functions for application operations
 */

import type { ThemeConfig, AppSettings, NavigationState, UIState } from './types';
import { useAppStore } from './app-store';

// ============================================================================
// Theme Utilities
// ============================================================================

/**
 * Get current theme configuration
 */
export const getCurrentTheme = (): ThemeConfig => {
  return useAppStore.getState().theme;
};

/**
 * Get current theme mode
 */
export const getCurrentThemeMode = (): 'light' | 'dark' => {
  return useAppStore.getState().theme.mode;
};

/**
 * Check if dark mode is active
 */
export const isDarkMode = (): boolean => {
  return getCurrentThemeMode() === 'dark';
};

/**
 * Toggle theme mode
 */
export const toggleTheme = (): void => {
  useAppStore.getState().toggleTheme();
};

/**
 * Set theme mode
 */
export const setThemeMode = (mode: 'light' | 'dark'): void => {
  useAppStore.getState().setTheme({ mode });
};

/**
 * Apply theme to document
 */
export const applyThemeToDocument = (): void => {
  if (typeof document === 'undefined') return;

  const theme = getCurrentTheme();
  const { mode, primaryColor, borderRadius } = theme;

  // Apply theme class to body
  document.body.className = document.body.className.replace(/theme-\w+/g, '');
  document.body.classList.add(`theme-${mode}`);

  // Apply CSS custom properties
  const root = document.documentElement;
  root.style.setProperty('--primary-color', primaryColor);
  root.style.setProperty('--border-radius', `${borderRadius}px`);
};

// ============================================================================
// Settings Utilities
// ============================================================================

/**
 * Get current application settings
 */
export const getCurrentSettings = (): AppSettings => {
  return useAppStore.getState().settings;
};

/**
 * Get specific setting value
 */
export const getSetting = <K extends keyof AppSettings>(key: K): AppSettings[K] => {
  return useAppStore.getState().settings[key];
};

/**
 * Update application settings
 */
export const updateSettings = (settings: Partial<AppSettings>): void => {
  useAppStore.getState().updateSettings(settings);
};

/**
 * Reset settings to default
 */
export const resetSettings = (): void => {
  useAppStore.getState().resetSettings();
};

// ============================================================================
// Navigation Utilities
// ============================================================================

/**
 * Get current navigation state
 */
export const getCurrentNavigation = (): NavigationState => {
  return useAppStore.getState().navigation;
};

/**
 * Get current path
 */
export const getCurrentPath = (): string => {
  return useAppStore.getState().navigation.currentPath;
};

/**
 * Set current path
 */
export const setCurrentPath = (path: string): void => {
  useAppStore.getState().setCurrentPath(path);
};

/**
 * Get breadcrumbs
 */
export const getBreadcrumbs = (): NavigationState['breadcrumbs'] => {
  return useAppStore.getState().navigation.breadcrumbs;
};

/**
 * Set breadcrumbs
 */
export const setBreadcrumbs = (breadcrumbs: NavigationState['breadcrumbs']): void => {
  useAppStore.getState().setBreadcrumbs(breadcrumbs);
};

/**
 * Check if sidebar is collapsed
 */
export const isSidebarCollapsed = (): boolean => {
  return useAppStore.getState().navigation.sidebarCollapsed;
};

/**
 * Toggle sidebar
 */
export const toggleSidebar = (): void => {
  useAppStore.getState().toggleSidebar();
};

/**
 * Get active menu key
 */
export const getActiveMenuKey = (): string => {
  return useAppStore.getState().navigation.activeMenuKey;
};

/**
 * Set active menu key
 */
export const setActiveMenu = (key: string): void => {
  useAppStore.getState().setActiveMenu(key);
};

// ============================================================================
// UI State Utilities
// ============================================================================

/**
 * Get current UI state
 */
export const getCurrentUIState = (): UIState => {
  return useAppStore.getState().ui;
};

/**
 * Check if global loading is active
 */
export const isGlobalLoading = (): boolean => {
  return useAppStore.getState().ui.globalLoading;
};

/**
 * Set global loading state
 */
export const setGlobalLoading = (loading: boolean, message?: string): void => {
  useAppStore.getState().setGlobalLoading(loading, message);
};

/**
 * Get global error
 */
export const getGlobalError = (): string | null => {
  return useAppStore.getState().ui.globalError;
};

/**
 * Set global error
 */
export const setGlobalError = (error: string | null, details?: any): void => {
  useAppStore.getState().setGlobalError(error, details);
};

/**
 * Clear global error
 */
export const clearGlobalError = (): void => {
  useAppStore.getState().clearGlobalError();
};

// ============================================================================
// Notifications Utilities
// ============================================================================

/**
 * Get current notifications
 */
export const getNotifications = (): UIState['notifications'] => {
  return useAppStore.getState().ui.notifications;
};

/**
 * Add notification
 */
export const addNotification = (
  notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>
): void => {
  useAppStore.getState().addNotification(notification);
};

/**
 * Remove notification
 */
export const removeNotification = (id: string): void => {
  useAppStore.getState().removeNotification(id);
};

/**
 * Clear all notifications
 */
export const clearNotifications = (): void => {
  useAppStore.getState().clearNotifications();
};

/**
 * Show success notification
 */
export const notifySuccess = (message: string, title?: string): void => {
  addNotification({
    type: 'success',
    title: title || 'Success',
    message,
  });
};

/**
 * Show error notification
 */
export const notifyError = (message: string, title?: string): void => {
  addNotification({
    type: 'error',
    title: title || 'Error',
    message,
  });
};

/**
 * Show warning notification
 */
export const notifyWarning = (message: string, title?: string): void => {
  addNotification({
    type: 'warning',
    title: title || 'Warning',
    message,
  });
};

/**
 * Show info notification
 */
export const notifyInfo = (message: string, title?: string): void => {
  addNotification({
    type: 'info',
    title: title || 'Info',
    message,
  });
};

// ============================================================================
// Modals Utilities
// ============================================================================

/**
 * Get modals state
 */
export const getModalsState = (): UIState['modals'] => {
  return useAppStore.getState().ui.modals;
};

/**
 * Check if modal is visible
 */
export const isModalVisible = (key: string): boolean => {
  const modal = useAppStore.getState().ui.modals[key];
  return modal?.visible || false;
};

/**
 * Get modal data
 */
export const getModalData = (key: string): any => {
  const modal = useAppStore.getState().ui.modals[key];
  return modal?.data;
};

/**
 * Show modal
 */
export const showModal = (key: string, data?: any): void => {
  useAppStore.getState().showModal(key, data);
};

/**
 * Hide modal
 */
export const hideModal = (key: string): void => {
  useAppStore.getState().hideModal(key);
};

/**
 * Hide all modals
 */
export const hideAllModals = (): void => {
  useAppStore.getState().hideAllModals();
};

// ============================================================================
// System Info Utilities
// ============================================================================

/**
 * Get application version
 */
export const getAppVersion = (): string => {
  return useAppStore.getState().version;
};

/**
 * Get build time
 */
export const getBuildTime = (): string => {
  return useAppStore.getState().buildTime;
};

/**
 * Get environment
 */
export const getEnvironment = (): 'development' | 'staging' | 'production' => {
  return useAppStore.getState().environment;
};

/**
 * Check if development environment
 */
export const isDevelopment = (): boolean => {
  return getEnvironment() === 'development';
};

/**
 * Check if production environment
 */
export const isProduction = (): boolean => {
  return getEnvironment() === 'production';
};

// ============================================================================
// Responsive Utilities
// ============================================================================

/**
 * Check if current viewport is mobile
 */
export const isMobile = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth < 768;
};

/**
 * Check if current viewport is tablet
 */
export const isTablet = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth >= 768 && window.innerWidth < 1024;
};

/**
 * Check if current viewport is desktop
 */
export const isDesktop = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth >= 1024;
};

/**
 * Get current breakpoint
 */
export const getCurrentBreakpoint = (): 'mobile' | 'tablet' | 'desktop' => {
  if (isMobile()) return 'mobile';
  if (isTablet()) return 'tablet';
  return 'desktop';
};

// ============================================================================
// Development Utilities
// ============================================================================

/**
 * Get full application state (development only)
 */
export const getAppState = () => {
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return useAppStore.getState();
};

/**
 * Reset application state (development/testing only)
 */
export const resetAppState = (): void => {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  const store = useAppStore.getState();
  store.resetSettings();
  store.clearNotifications();
  store.hideAllModals();
  store.clearGlobalError();
  store.setGlobalLoading(false);
};
