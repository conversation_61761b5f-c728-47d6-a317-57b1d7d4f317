/**
 * Authentication Hooks
 * Custom hooks for easy authentication state access
 */

'use client';

import { useCallback, useEffect } from 'react';
import { useAuthStore } from './auth-store';
import type { SystemUser, SystemUserRole } from './types';
import { ACTIVITY_TRACKING_INTERVAL } from './constants';

// ============================================================================
// Basic Authentication Hooks
// ============================================================================

/**
 * Hook to get current user
 */
export const useUser = () => {
  return useAuthStore((state) => state.user);
};

/**
 * Hook to get authentication status
 */
export const useIsAuthenticated = () => {
  return useAuthStore((state) => state.isAuthenticated);
};

/**
 * Hook to get authentication loading state
 */
export const useAuthLoading = () => {
  return useAuthStore((state) => state.isLoading);
};

/**
 * Hook to get authentication error
 */
export const useAuthError = () => {
  return useAuthStore((state) => state.error);
};

/**
 * Hook to get authentication tokens
 */
export const useAuthTokens = () => {
  return useAuthStore((state) => state.tokens);
};

// ============================================================================
// Authentication Action Hooks
// ============================================================================

/**
 * Hook to get login function
 */
export const useLogin = () => {
  return useAuthStore((state) => state.login);
};

/**
 * Hook to get logout function
 */
export const useLogout = () => {
  return useAuthStore((state) => state.logout);
};

/**
 * Hook to get logout all function
 */
export const useLogoutAll = () => {
  return useAuthStore((state) => state.logoutAll);
};

/**
 * Hook to get update profile function
 */
export const useUpdateProfile = () => {
  return useAuthStore((state) => state.updateProfile);
};

/**
 * Hook to get refresh tokens function
 */
export const useRefreshTokens = () => {
  return useAuthStore((state) => state.refreshTokens);
};

// ============================================================================
// Utility Hooks
// ============================================================================

/**
 * Hook to clear authentication error
 */
export const useClearAuthError = () => {
  return useAuthStore((state) => state.clearError);
};

/**
 * Hook to check session validity
 */
export const useCheckSession = () => {
  return useAuthStore((state) => state.checkSession);
};

/**
 * Hook to update last activity
 */
export const useUpdateActivity = () => {
  return useAuthStore((state) => state.updateLastActivity);
};

// ============================================================================
// Role-based Hooks
// ============================================================================

/**
 * Hook to check if user has specific role
 */
export const useHasRole = (role: SystemUserRole) => {
  const user = useUser();
  return user?.role === role;
};

/**
 * Hook to check if user is admin
 */
export const useIsAdmin = () => {
  return useHasRole('Admin');
};

/**
 * Hook to check if user is editor
 */
export const useIsEditor = () => {
  return useHasRole('Editor');
};

/**
 * Hook to check if user is moderator
 */
export const useIsModerator = () => {
  return useHasRole('Moderator');
};

/**
 * Hook to check if user has admin or editor role
 */
export const useCanEdit = () => {
  const user = useUser();
  return user?.role === 'Admin' || user?.role === 'Editor';
};

/**
 * Hook to check if user can perform admin actions
 */
export const useCanAdmin = () => {
  return useIsAdmin();
};

// ============================================================================
// Composite Hooks
// ============================================================================

/**
 * Hook to get complete authentication state
 */
export const useAuth = () => {
  const user = useUser();
  const isAuthenticated = useIsAuthenticated();
  const isLoading = useAuthLoading();
  const error = useAuthError();
  const tokens = useAuthTokens();

  const login = useLogin();
  const logout = useLogout();
  const logoutAll = useLogoutAll();
  const updateProfile = useUpdateProfile();
  const clearError = useClearAuthError();

  return {
    // State
    user,
    isAuthenticated,
    isLoading,
    error,
    tokens,

    // Actions
    login,
    logout,
    logoutAll,
    updateProfile,
    clearError,

    // Role checks
    isAdmin: useIsAdmin(),
    isEditor: useIsEditor(),
    isModerator: useIsModerator(),
    canEdit: useCanEdit(),
    canAdmin: useCanAdmin(),
  };
};

/**
 * Hook for authentication with automatic session management
 */
export const useAuthWithSession = () => {
  const auth = useAuth();
  const checkSession = useCheckSession();
  const updateActivity = useUpdateActivity();

  // Auto-check session validity
  useEffect(() => {
    if (auth.isAuthenticated) {
      const interval = setInterval(() => {
        checkSession();
      }, ACTIVITY_TRACKING_INTERVAL);

      return () => clearInterval(interval);
    }
  }, [auth.isAuthenticated, checkSession]);

  // Update activity on user interaction
  const handleUserActivity = useCallback(() => {
    if (auth.isAuthenticated) {
      updateActivity();
    }
  }, [auth.isAuthenticated, updateActivity]);

  // Auto-update activity on mouse/keyboard events
  useEffect(() => {
    if (auth.isAuthenticated) {
      const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];

      events.forEach(event => {
        document.addEventListener(event, handleUserActivity, true);
      });

      return () => {
        events.forEach(event => {
          document.removeEventListener(event, handleUserActivity, true);
        });
      };
    }
  }, [auth.isAuthenticated, handleUserActivity]);

  return {
    ...auth,
    checkSession,
    updateActivity,
  };
};

// ============================================================================
// Permission Hooks
// ============================================================================

/**
 * Hook to check multiple permissions
 */
export const usePermissions = (requiredRoles: SystemUserRole[]) => {
  const user = useUser();

  const hasPermission = useCallback((roles: SystemUserRole[]) => {
    if (!user) return false;
    return roles.includes(user.role);
  }, [user]);

  const hasAnyPermission = hasPermission(requiredRoles);

  return {
    hasPermission: hasAnyPermission,
    userRole: user?.role,
    checkRole: hasPermission,
  };
};

/**
 * Hook for route protection
 */
export const useRouteProtection = (requiredRoles?: SystemUserRole[]) => {
  const isAuthenticated = useIsAuthenticated();
  const user = useUser();
  const isLoading = useAuthLoading();

  const hasAccess = useCallback(() => {
    if (!isAuthenticated) return false;
    if (!requiredRoles || requiredRoles.length === 0) return true;
    if (!user) return false;

    return requiredRoles.includes(user.role);
  }, [isAuthenticated, user, requiredRoles]);

  return {
    isAuthenticated,
    hasAccess: hasAccess(),
    isLoading,
    user,
    shouldRedirect: !isLoading && !isAuthenticated,
    shouldShowUnauthorized: !isLoading && isAuthenticated && !hasAccess(),
  };
};

// ============================================================================
// Development Hooks
// ============================================================================

/**
 * Hook for development/debugging authentication state
 */
export const useAuthDebug = () => {
  const state = useAuthStore((state) => state);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return {
    fullState: state,
    hasHydrated: state._hasHydrated,
    lastActivity: new Date(state.lastActivity).toISOString(),
    sessionTimeout: state.sessionTimeout,
    tokenExpiry: state.tokens ? new Date(state.tokens.expiresAt).toISOString() : null,
  };
};
