/**
 * Authentication Utilities
 * Helper functions for authentication operations
 */

import type { SystemUser, SystemUserRole, AuthTokens } from './types';
import { useAuthStore } from './auth-store';

// ============================================================================
// Token Utilities
// ============================================================================

/**
 * Get current access token
 */
export const getAccessToken = (): string | null => {
  const tokens = useAuthStore.getState().tokens;
  return tokens?.accessToken || null;
};

/**
 * Get current refresh token
 */
export const getRefreshToken = (): string | null => {
  const tokens = useAuthStore.getState().tokens;
  return tokens?.refreshToken || null;
};

/**
 * Get authorization header for API requests
 */
export const getAuthHeader = (): Record<string, string> => {
  const token = getAccessToken();
  return token ? { Authorization: `Bearer ${token}` } : {};
};

/**
 * Check if user is currently authenticated
 */
export const isAuthenticated = (): boolean => {
  return useAuthStore.getState().isAuthenticated;
};

/**
 * Get current user
 */
export const getCurrentUser = (): SystemUser | null => {
  return useAuthStore.getState().user;
};

/**
 * Get current user role
 */
export const getCurrentUserRole = (): SystemUserRole | null => {
  const user = getCurrentUser();
  return user?.role || null;
};

// ============================================================================
// Permission Utilities
// ============================================================================

/**
 * Check if current user has specific role
 */
export const hasRole = (role: SystemUserRole): boolean => {
  const currentRole = getCurrentUserRole();
  return currentRole === role;
};

/**
 * Check if current user has any of the specified roles
 */
export const hasAnyRole = (roles: SystemUserRole[]): boolean => {
  const currentRole = getCurrentUserRole();
  return currentRole ? roles.includes(currentRole) : false;
};

/**
 * Check if current user is admin
 */
export const isAdmin = (): boolean => {
  return hasRole('Admin');
};

/**
 * Check if current user is editor
 */
export const isEditor = (): boolean => {
  return hasRole('Editor');
};

/**
 * Check if current user is moderator
 */
export const isModerator = (): boolean => {
  return hasRole('Moderator');
};

/**
 * Check if current user can edit content
 */
export const canEdit = (): boolean => {
  return hasAnyRole(['Admin', 'Editor']);
};

/**
 * Check if current user can perform admin actions
 */
export const canAdmin = (): boolean => {
  return isAdmin();
};

/**
 * Check if current user can moderate content
 */
export const canModerate = (): boolean => {
  return hasAnyRole(['Admin', 'Editor', 'Moderator']);
};

// ============================================================================
// Session Utilities
// ============================================================================

/**
 * Force session check
 */
export const checkSession = (): boolean => {
  return useAuthStore.getState().checkSession();
};

/**
 * Update user activity timestamp
 */
export const updateActivity = (): void => {
  useAuthStore.getState().updateLastActivity();
};

/**
 * Get session remaining time in minutes
 */
export const getAuthSessionRemainingTime = (): number => {
  const { lastActivity, sessionTimeout } = useAuthStore.getState();
  const now = Date.now();
  const timeoutMs = sessionTimeout * 60 * 1000;
  const elapsed = now - lastActivity;
  const remaining = timeoutMs - elapsed;
  return Math.max(0, Math.floor(remaining / (60 * 1000)));
};

/**
 * Check if session will expire soon (within 5 minutes)
 */
export const isSessionExpiringSoon = (): boolean => {
  return getAuthSessionRemainingTime() <= 5;
};

// ============================================================================
// Authentication Actions
// ============================================================================

/**
 * Login with credentials
 */
export const login = async (email: string, password: string): Promise<void> => {
  return useAuthStore.getState().login(email, password);
};

/**
 * Logout current user
 */
export const logout = async (): Promise<void> => {
  return useAuthStore.getState().logout();
};

/**
 * Logout from all devices
 */
export const logoutAll = async (): Promise<void> => {
  return useAuthStore.getState().logoutAll();
};

/**
 * Update user profile
 */
export const updateProfile = async (data: Partial<SystemUser>): Promise<void> => {
  return useAuthStore.getState().updateProfile(data);
};

/**
 * Refresh authentication tokens
 */
export const refreshTokens = async (): Promise<void> => {
  return useAuthStore.getState().refreshTokens();
};

// ============================================================================
// Error Handling Utilities
// ============================================================================

/**
 * Get current authentication error
 */
export const getAuthError = (): string | null => {
  return useAuthStore.getState().error;
};

/**
 * Clear authentication error
 */
export const clearAuthError = (): void => {
  useAuthStore.getState().clearError();
};

/**
 * Check if there's an authentication error
 */
export const hasAuthError = (): boolean => {
  return !!getAuthError();
};

// ============================================================================
// API Request Utilities
// ============================================================================

/**
 * Create authenticated fetch request
 */
export const authenticatedFetch = async (
  url: string,
  options: RequestInit = {}
): Promise<Response> => {
  const authHeaders = getAuthHeader();

  const config: RequestInit = {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders,
      ...options.headers,
    },
  };

  const response = await fetch(url, config);

  // Handle token expiration
  if (response.status === 401) {
    const isAuth = isAuthenticated();
    if (isAuth) {
      // Try to refresh tokens
      try {
        await refreshTokens();
        // Retry the request with new token
        const newAuthHeaders = getAuthHeader();
        const retryConfig: RequestInit = {
          ...config,
          headers: {
            ...config.headers,
            ...newAuthHeaders,
          },
        };
        return fetch(url, retryConfig);
      } catch (error) {
        // Refresh failed, logout user
        await logout();
        throw new Error('Authentication expired. Please log in again.');
      }
    }
  }

  return response;
};

/**
 * Create authenticated API request with JSON response
 */
export const authenticatedApiRequest = async <T = any>(
  url: string,
  options: RequestInit = {}
): Promise<T> => {
  const response = await authenticatedFetch(url, options);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Request failed with status ${response.status}`);
  }

  return response.json();
};

// ============================================================================
// Route Protection Utilities
// ============================================================================

/**
 * Check if user can access route with required roles
 */
export const canAccessRoute = (requiredRoles?: SystemUserRole[]): boolean => {
  if (!isAuthenticated()) return false;
  if (!requiredRoles || requiredRoles.length === 0) return true;

  return hasAnyRole(requiredRoles);
};

/**
 * Get redirect path for unauthenticated users
 */
export const getLoginRedirectPath = (currentPath?: string): string => {
  const loginPath = '/login';
  if (currentPath && currentPath !== '/') {
    return `${loginPath}?redirect=${encodeURIComponent(currentPath)}`;
  }
  return loginPath;
};

/**
 * Get redirect path after successful login
 */
export const getPostLoginRedirectPath = (searchParams?: URLSearchParams): string => {
  const redirectParam = searchParams?.get('redirect');
  return redirectParam || '/dashboard';
};

// ============================================================================
// Development Utilities
// ============================================================================

/**
 * Get full authentication state (development only)
 */
export const getAuthState = () => {
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return useAuthStore.getState();
};

/**
 * Mock login for development/testing
 */
export const mockLogin = (user: SystemUser, tokens: AuthTokens): void => {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  useAuthStore.getState().setUser(user);
  useAuthStore.getState().setTokens(tokens);
};

/**
 * Reset authentication state (development/testing only)
 */
export const resetAuthState = (): void => {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  useAuthStore.getState().logout();
};
