/**
 * Store Constants
 * Defines constants used across all stores
 */

// ============================================================================
// Store Names
// ============================================================================

export const STORE_NAMES = {
  AUTH: 'auth-store',
  APP: 'app-store',
} as const;

// ============================================================================
// Storage Keys
// ============================================================================

export const STORAGE_KEYS = {
  AUTH: 'auth-storage',
  APP: 'app-storage',
  THEME: 'theme-storage',
  SETTINGS: 'settings-storage',
} as const;

// ============================================================================
// Default Values
// ============================================================================

/**
 * Default theme configuration
 */
export const DEFAULT_THEME = {
  mode: 'light' as const,
  primaryColor: '#1890ff',
  borderRadius: 6,
  compactMode: false,
};

/**
 * Default application settings
 */
export const DEFAULT_APP_SETTINGS = {
  language: 'en' as const,
  timezone: 'UTC',
  dateFormat: 'YYYY-MM-DD',
  pageSize: 20,
  autoRefresh: true,
  refreshInterval: 30, // seconds
  features: {
    darkMode: true,
    notifications: true,
    autoSave: true,
    advancedFilters: true,
  },
};

/**
 * Default navigation state
 */
export const DEFAULT_NAVIGATION = {
  currentPath: '/',
  breadcrumbs: [],
  sidebarCollapsed: false,
  activeMenuKey: 'dashboard',
};

/**
 * Default UI state
 */
export const DEFAULT_UI_STATE = {
  globalLoading: false,
  loadingMessage: '',
  globalError: null,
  errorDetails: null,
  notifications: [],
  modals: {},
};

// ============================================================================
// Session Configuration
// ============================================================================

/**
 * Session timeout in minutes
 */
export const SESSION_TIMEOUT = 60; // 1 hour

/**
 * Token refresh threshold in minutes
 * Refresh token when it expires in less than this time
 */
export const TOKEN_REFRESH_THRESHOLD = 5; // 5 minutes

/**
 * Activity tracking interval in milliseconds
 */
export const ACTIVITY_TRACKING_INTERVAL = 60000; // 1 minute

// ============================================================================
// API Configuration
// ============================================================================

/**
 * API endpoints for store operations
 */
export const STORE_API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/system-auth/login',
    LOGOUT: '/api/system-auth/logout',
    LOGOUT_ALL: '/api/system-auth/logout-all',
    PROFILE: '/api/system-auth/profile',
    REFRESH: '/api/system-auth/refresh',
  },
} as const;

// ============================================================================
// Error Messages
// ============================================================================

export const ERROR_MESSAGES = {
  AUTH: {
    LOGIN_FAILED: 'Login failed. Please check your credentials.',
    LOGOUT_FAILED: 'Logout failed. Please try again.',
    SESSION_EXPIRED: 'Your session has expired. Please log in again.',
    TOKEN_REFRESH_FAILED: 'Failed to refresh authentication token.',
    PROFILE_UPDATE_FAILED: 'Failed to update profile. Please try again.',
    UNAUTHORIZED: 'You are not authorized to perform this action.',
  },
  APP: {
    SETTINGS_SAVE_FAILED: 'Failed to save settings. Please try again.',
    THEME_LOAD_FAILED: 'Failed to load theme configuration.',
    NETWORK_ERROR: 'Network error. Please check your connection.',
    UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',
  },
} as const;

// ============================================================================
// Success Messages
// ============================================================================

export const SUCCESS_MESSAGES = {
  AUTH: {
    LOGIN_SUCCESS: 'Successfully logged in.',
    LOGOUT_SUCCESS: 'Successfully logged out.',
    PROFILE_UPDATED: 'Profile updated successfully.',
  },
  APP: {
    SETTINGS_SAVED: 'Settings saved successfully.',
    THEME_UPDATED: 'Theme updated successfully.',
  },
} as const;

// ============================================================================
// Store Versions
// ============================================================================

/**
 * Store versions for migration support
 */
export const STORE_VERSIONS = {
  AUTH: 1,
  APP: 1,
} as const;

// ============================================================================
// Development Configuration
// ============================================================================

/**
 * Development mode configuration
 */
export const DEV_CONFIG = {
  ENABLE_DEVTOOLS: process.env.NODE_ENV === 'development',
  ENABLE_LOGGING: process.env.NODE_ENV === 'development',
  MOCK_API_DELAY: 1000, // milliseconds
} as const;

// ============================================================================
// Feature Flags
// ============================================================================

/**
 * Feature flags for conditional functionality
 */
export const FEATURE_FLAGS = {
  ENABLE_DARK_MODE: true,
  ENABLE_NOTIFICATIONS: true,
  ENABLE_AUTO_SAVE: true,
  ENABLE_ADVANCED_FILTERS: true,
  ENABLE_REAL_TIME_UPDATES: false, // Future feature
  ENABLE_OFFLINE_MODE: false, // Future feature
} as const;

// ============================================================================
// Validation Rules
// ============================================================================

/**
 * Validation rules for store data
 */
export const VALIDATION_RULES = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD_MIN_LENGTH: 8,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  PAGE_SIZE_MIN: 5,
  PAGE_SIZE_MAX: 100,
  REFRESH_INTERVAL_MIN: 10, // seconds
  REFRESH_INTERVAL_MAX: 300, // seconds
} as const;
