/**
 * Store Provider Tests
 * Tests for store provider functionality
 */

import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import { StoreProvider, StoreProviderUtils } from '../store-provider';
import { useAuthProvider, useAppProvider, useStoreAvailability } from '../provider-hooks';

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Test component that uses store providers
function TestComponent() {
  const auth = useAuthProvider();
  const app = useAppProvider();
  const { isAvailable } = useStoreAvailability();

  return (
    <div>
      <div data-testid="store-available">{isAvailable ? 'true' : 'false'}</div>
      <div data-testid="auth-initialized">{auth.isInitialized ? 'true' : 'false'}</div>
      <div data-testid="app-initialized">{app.isInitialized ? 'true' : 'false'}</div>
      <div data-testid="auth-authenticated">{auth.isAuthenticated ? 'true' : 'false'}</div>
      <div data-testid="app-theme">{app.theme}</div>
      <div data-testid="app-language">{app.language}</div>
      <div data-testid="sidebar-collapsed">{app.sidebarCollapsed ? 'true' : 'false'}</div>
    </div>
  );
}

describe('StoreProvider', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  it('should provide stores to child components', async () => {
    render(
      <StoreProvider>
        <TestComponent />
      </StoreProvider>
    );

    // Check if stores are available
    expect(screen.getByTestId('store-available')).toHaveTextContent('true');
    
    // Wait for initialization
    await waitFor(() => {
      expect(screen.getByTestId('auth-initialized')).toHaveTextContent('true');
      expect(screen.getByTestId('app-initialized')).toHaveTextContent('true');
    });
  });

  it('should initialize stores with default values', async () => {
    render(
      <StoreProvider>
        <TestComponent />
      </StoreProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('auth-authenticated')).toHaveTextContent('false');
      expect(screen.getByTestId('app-theme')).toHaveTextContent('light');
      expect(screen.getByTestId('app-language')).toHaveTextContent('en');
      expect(screen.getByTestId('sidebar-collapsed')).toHaveTextContent('false');
    });
  });

  it('should load persisted data from localStorage', async () => {
    mockLocalStorage.getItem.mockImplementation((key) => {
      switch (key) {
        case 'apisportsgame_theme':
          return 'dark';
        case 'apisportsgame_language':
          return 'vi';
        case 'apisportsgame_sidebar_collapsed':
          return 'true';
        default:
          return null;
      }
    });

    render(
      <StoreProvider>
        <TestComponent />
      </StoreProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('app-theme')).toHaveTextContent('dark');
      expect(screen.getByTestId('app-language')).toHaveTextContent('vi');
      expect(screen.getByTestId('sidebar-collapsed')).toHaveTextContent('true');
    });
  });

  it('should handle initialization errors gracefully', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    // Mock localStorage to throw error
    mockLocalStorage.getItem.mockImplementation(() => {
      throw new Error('localStorage error');
    });

    render(
      <StoreProvider>
        <TestComponent />
      </StoreProvider>
    );

    // Should still render without crashing
    expect(screen.getByTestId('store-available')).toHaveTextContent('true');
    
    consoleSpy.mockRestore();
  });
});

describe('StoreProviderUtils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should check store initialization', () => {
    const result = StoreProviderUtils.checkStoreInitialization();
    
    expect(result).toHaveProperty('auth');
    expect(result).toHaveProperty('app');
    expect(result).toHaveProperty('all');
    expect(typeof result.auth).toBe('boolean');
    expect(typeof result.app).toBe('boolean');
    expect(typeof result.all).toBe('boolean');
  });

  it('should reset all stores', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    
    StoreProviderUtils.resetAllStores();
    
    expect(consoleSpy).toHaveBeenCalledWith('✅ All stores reset successfully');
    
    consoleSpy.mockRestore();
  });

  it('should clear persisted data', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    
    StoreProviderUtils.clearPersistedData();
    
    expect(mockLocalStorage.removeItem).toHaveBeenCalledTimes(6); // Number of storage keys
    expect(consoleSpy).toHaveBeenCalledWith('✅ All persisted store data cleared');
    
    consoleSpy.mockRestore();
  });

  it('should handle errors when clearing persisted data', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    mockLocalStorage.removeItem.mockImplementation(() => {
      throw new Error('localStorage error');
    });
    
    StoreProviderUtils.clearPersistedData();
    
    expect(consoleSpy).toHaveBeenCalledWith('❌ Failed to clear persisted data:', expect.any(Error));
    
    consoleSpy.mockRestore();
  });
});
