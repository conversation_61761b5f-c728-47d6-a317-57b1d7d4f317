/**
 * Authentication Store Tests
 * Tests for authentication store functionality
 */

import { createAuthStore } from '../auth-store';
import type { SystemUser, AuthTokens } from '../types';

// Mock fetch for testing
global.fetch = jest.fn();

describe('Authentication Store', () => {
  let store: ReturnType<typeof createAuthStore>;

  beforeEach(() => {
    store = createAuthStore();
    jest.clearAllMocks();
  });

  describe('Initial State', () => {
    test('should have correct initial state', () => {
      const state = store.getState();
      
      expect(state.user).toBeNull();
      expect(state.tokens).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
      expect(state._hasHydrated).toBe(false);
    });
  });

  describe('State Management Actions', () => {
    test('should set user correctly', () => {
      const mockUser: SystemUser = {
        id: 'user-1',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'Admin',
        isActive: true,
        createdAt: '2024-05-25T00:00:00Z',
        updatedAt: '2024-05-25T00:00:00Z',
      };

      store.getState().setUser(mockUser);
      
      const state = store.getState();
      expect(state.user).toEqual(mockUser);
      expect(state.isAuthenticated).toBe(true);
    });

    test('should set tokens correctly', () => {
      const mockTokens: AuthTokens = {
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() + 3600000, // 1 hour from now
      };

      store.getState().setTokens(mockTokens);
      
      const state = store.getState();
      expect(state.tokens).toEqual(mockTokens);
    });

    test('should set loading state', () => {
      store.getState().setLoading(true);
      expect(store.getState().isLoading).toBe(true);

      store.getState().setLoading(false);
      expect(store.getState().isLoading).toBe(false);
    });

    test('should set and clear error', () => {
      const errorMessage = 'Test error';
      
      store.getState().setError(errorMessage);
      expect(store.getState().error).toBe(errorMessage);

      store.getState().clearError();
      expect(store.getState().error).toBeNull();
    });
  });

  describe('Session Management', () => {
    test('should update last activity', () => {
      const initialActivity = store.getState().lastActivity;
      
      // Wait a bit to ensure timestamp difference
      setTimeout(() => {
        store.getState().updateLastActivity();
        const newActivity = store.getState().lastActivity;
        expect(newActivity).toBeGreaterThan(initialActivity);
      }, 10);
    });

    test('should check session validity', () => {
      // Set up a valid session
      const mockUser: SystemUser = {
        id: 'user-1',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'Admin',
        isActive: true,
        createdAt: '2024-05-25T00:00:00Z',
        updatedAt: '2024-05-25T00:00:00Z',
      };

      const mockTokens: AuthTokens = {
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() + 3600000, // 1 hour from now
      };

      store.getState().setUser(mockUser);
      store.getState().setTokens(mockTokens);
      
      const isValid = store.getState().checkSession();
      expect(isValid).toBe(true);
    });
  });

  describe('Hydration', () => {
    test('should set hydration state', () => {
      store.getState().setHasHydrated(true);
      expect(store.getState()._hasHydrated).toBe(true);
    });

    test('should hydrate store', () => {
      store.getState().hydrate();
      expect(store.getState()._hasHydrated).toBe(true);
    });
  });

  describe('Login Flow', () => {
    test('should handle successful login', async () => {
      const mockResponse = {
        success: true,
        data: {
          user: {
            id: 'user-1',
            email: '<EMAIL>',
            name: 'Test User',
            role: 'Admin',
            isActive: true,
            createdAt: '2024-05-25T00:00:00Z',
            updatedAt: '2024-05-25T00:00:00Z',
          },
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
        },
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      await store.getState().login('<EMAIL>', 'password');

      const state = store.getState();
      expect(state.isAuthenticated).toBe(true);
      expect(state.user).toEqual(mockResponse.data.user);
      expect(state.tokens?.accessToken).toBe(mockResponse.data.accessToken);
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
    });

    test('should handle login failure', async () => {
      const mockErrorResponse = {
        success: false,
        message: 'Invalid credentials',
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        json: async () => mockErrorResponse,
      });

      await expect(
        store.getState().login('<EMAIL>', 'wrong-password')
      ).rejects.toThrow('Invalid credentials');

      const state = store.getState();
      expect(state.isAuthenticated).toBe(false);
      expect(state.user).toBeNull();
      expect(state.tokens).toBeNull();
      expect(state.isLoading).toBe(false);
      expect(state.error).toBe('Invalid credentials');
    });
  });

  describe('Logout Flow', () => {
    test('should handle logout', async () => {
      // Set up authenticated state
      const mockUser: SystemUser = {
        id: 'user-1',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'Admin',
        isActive: true,
        createdAt: '2024-05-25T00:00:00Z',
        updatedAt: '2024-05-25T00:00:00Z',
      };

      store.getState().setUser(mockUser);
      store.getState().setTokens({
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() + 3600000,
      });

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      });

      await store.getState().logout();

      const state = store.getState();
      expect(state.isAuthenticated).toBe(false);
      expect(state.user).toBeNull();
      expect(state.tokens).toBeNull();
      expect(state.error).toBeNull();
    });
  });
});
