/**
 * Application Store Tests
 * Tests for application store functionality
 */

import { createAppStore } from '../app-store';
import type { ThemeConfig, AppSettings } from '../types';

describe('Application Store', () => {
  let store: ReturnType<typeof createAppStore>;

  beforeEach(() => {
    store = createAppStore();
    jest.clearAllMocks();
  });

  describe('Initial State', () => {
    test('should have correct initial state', () => {
      const state = store.getState();
      
      expect(state.theme.mode).toBe('light');
      expect(state.settings.language).toBe('en');
      expect(state.navigation.currentPath).toBe('/');
      expect(state.ui.globalLoading).toBe(false);
      expect(state.ui.notifications).toEqual([]);
      expect(state._hasHydrated).toBe(false);
    });
  });

  describe('Theme Management', () => {
    test('should set theme correctly', () => {
      const themeUpdate: Partial<ThemeConfig> = {
        mode: 'dark',
        primaryColor: '#ff0000',
      };

      store.getState().setTheme(themeUpdate);
      
      const state = store.getState();
      expect(state.theme.mode).toBe('dark');
      expect(state.theme.primaryColor).toBe('#ff0000');
    });

    test('should toggle theme mode', () => {
      // Initial mode is light
      expect(store.getState().theme.mode).toBe('light');

      store.getState().toggleTheme();
      expect(store.getState().theme.mode).toBe('dark');

      store.getState().toggleTheme();
      expect(store.getState().theme.mode).toBe('light');
    });
  });

  describe('Settings Management', () => {
    test('should update settings correctly', () => {
      const settingsUpdate: Partial<AppSettings> = {
        language: 'vi',
        pageSize: 50,
      };

      store.getState().updateSettings(settingsUpdate);
      
      const state = store.getState();
      expect(state.settings.language).toBe('vi');
      expect(state.settings.pageSize).toBe(50);
    });

    test('should reset settings to default', () => {
      // First update settings
      store.getState().updateSettings({ language: 'vi', pageSize: 50 });
      
      // Then reset
      store.getState().resetSettings();
      
      const state = store.getState();
      expect(state.settings.language).toBe('en');
      expect(state.settings.pageSize).toBe(20);
    });
  });

  describe('Navigation Management', () => {
    test('should set current path', () => {
      store.getState().setCurrentPath('/dashboard');
      expect(store.getState().navigation.currentPath).toBe('/dashboard');
    });

    test('should set breadcrumbs', () => {
      const breadcrumbs = [
        { title: 'Home', path: '/' },
        { title: 'Dashboard', path: '/dashboard' },
      ];

      store.getState().setBreadcrumbs(breadcrumbs);
      expect(store.getState().navigation.breadcrumbs).toEqual(breadcrumbs);
    });

    test('should toggle sidebar', () => {
      const initialCollapsed = store.getState().navigation.sidebarCollapsed;
      
      store.getState().toggleSidebar();
      expect(store.getState().navigation.sidebarCollapsed).toBe(!initialCollapsed);
      
      store.getState().toggleSidebar();
      expect(store.getState().navigation.sidebarCollapsed).toBe(initialCollapsed);
    });

    test('should set active menu', () => {
      store.getState().setActiveMenu('dashboard');
      expect(store.getState().navigation.activeMenuKey).toBe('dashboard');
    });
  });

  describe('UI State Management', () => {
    test('should set global loading state', () => {
      store.getState().setGlobalLoading(true, 'Loading data...');
      
      const state = store.getState();
      expect(state.ui.globalLoading).toBe(true);
      expect(state.ui.loadingMessage).toBe('Loading data...');
    });

    test('should set and clear global error', () => {
      const errorMessage = 'Something went wrong';
      const errorDetails = { code: 500 };
      
      store.getState().setGlobalError(errorMessage, errorDetails);
      
      let state = store.getState();
      expect(state.ui.globalError).toBe(errorMessage);
      expect(state.ui.errorDetails).toEqual(errorDetails);
      
      store.getState().clearGlobalError();
      
      state = store.getState();
      expect(state.ui.globalError).toBeNull();
      expect(state.ui.errorDetails).toBeNull();
    });
  });

  describe('Notifications Management', () => {
    test('should add notification', () => {
      const notification = {
        type: 'success' as const,
        title: 'Success',
        message: 'Operation completed',
      };

      store.getState().addNotification(notification);
      
      const state = store.getState();
      expect(state.ui.notifications).toHaveLength(1);
      expect(state.ui.notifications[0].type).toBe('success');
      expect(state.ui.notifications[0].title).toBe('Success');
      expect(state.ui.notifications[0].message).toBe('Operation completed');
      expect(state.ui.notifications[0].id).toBeDefined();
      expect(state.ui.notifications[0].timestamp).toBeDefined();
    });

    test('should remove notification', () => {
      // Add notification first
      store.getState().addNotification({
        type: 'info',
        title: 'Info',
        message: 'Test message',
      });
      
      const notificationId = store.getState().ui.notifications[0].id;
      
      // Remove notification
      store.getState().removeNotification(notificationId);
      
      expect(store.getState().ui.notifications).toHaveLength(0);
    });

    test('should clear all notifications', () => {
      // Add multiple notifications
      store.getState().addNotification({
        type: 'success',
        title: 'Success',
        message: 'Message 1',
      });
      store.getState().addNotification({
        type: 'error',
        title: 'Error',
        message: 'Message 2',
      });
      
      expect(store.getState().ui.notifications).toHaveLength(2);
      
      // Clear all
      store.getState().clearNotifications();
      
      expect(store.getState().ui.notifications).toHaveLength(0);
    });
  });

  describe('Modals Management', () => {
    test('should show modal', () => {
      const modalData = { userId: '123' };
      
      store.getState().showModal('userModal', modalData);
      
      const state = store.getState();
      expect(state.ui.modals.userModal.visible).toBe(true);
      expect(state.ui.modals.userModal.data).toEqual(modalData);
    });

    test('should hide modal', () => {
      // Show modal first
      store.getState().showModal('userModal', { userId: '123' });
      
      // Hide modal
      store.getState().hideModal('userModal');
      
      const state = store.getState();
      expect(state.ui.modals.userModal.visible).toBe(false);
      expect(state.ui.modals.userModal.data).toBeUndefined();
    });

    test('should hide all modals', () => {
      // Show multiple modals
      store.getState().showModal('modal1', { data: 'test1' });
      store.getState().showModal('modal2', { data: 'test2' });
      
      // Hide all modals
      store.getState().hideAllModals();
      
      const state = store.getState();
      expect(state.ui.modals.modal1.visible).toBe(false);
      expect(state.ui.modals.modal2.visible).toBe(false);
    });
  });

  describe('System Info', () => {
    test('should have system information', () => {
      const state = store.getState();
      
      expect(state.version).toBeDefined();
      expect(state.buildTime).toBeDefined();
      expect(state.environment).toBeDefined();
      expect(['development', 'staging', 'production']).toContain(state.environment);
    });
  });
});
