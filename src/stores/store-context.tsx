/**
 * Store Context - React context for accessing stores
 * Provides centralized access to all Zustand stores
 */

'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useAuth } from './auth-hooks';
import { useApp } from './app-hooks';
import type { AuthStore, AppStore } from './types';

/**
 * Store context interface
 */
interface StoreContextType {
  authStore: AuthStore;
  appStore: AppStore;
}

/**
 * Store context
 */
const StoreContext = createContext<StoreContextType | null>(null);

/**
 * Store context provider props
 */
interface StoreContextProviderProps {
  children: ReactNode;
}

/**
 * Store context provider component
 * Provides all stores to child components
 */
export function StoreContextProvider({ children }: StoreContextProviderProps) {
  // Get store instances
  const authStore = useAuth();
  const appStore = useApp();

  const contextValue: StoreContextType = {
    authStore,
    appStore,
  };

  return (
    <StoreContext.Provider value={contextValue}>
      {children}
    </StoreContext.Provider>
  );
}

/**
 * Hook to access store context
 * Throws error if used outside provider
 */
export function useStoreContext(): StoreContextType {
  const context = useContext(StoreContext);

  if (!context) {
    throw new Error(
      'useStoreContext must be used within a StoreContextProvider'
    );
  }

  return context;
}

/**
 * Hook to access auth store from context
 */
export function useAuthStoreContext() {
  const { authStore } = useStoreContext();
  return authStore;
}

/**
 * Hook to access app store from context
 */
export function useAppStoreContext() {
  const { appStore } = useStoreContext();
  return appStore;
}

/**
 * Hook to check if store context is available
 */
export function useIsStoreContextAvailable(): boolean {
  const context = useContext(StoreContext);
  return context !== null;
}
