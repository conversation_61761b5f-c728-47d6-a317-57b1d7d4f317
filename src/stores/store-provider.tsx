/**
 * Store Provider - Main provider component for all stores
 * Wraps the application with necessary store providers
 */

'use client';

import React, { ReactNode, useEffect } from 'react';
import { StoreContextProvider } from './store-context';
import { useAuth } from './auth-hooks';
import { useApp } from './app-hooks';
import { STORAGE_KEYS } from './constants';

/**
 * Store provider props
 */
interface StoreProviderProps {
  children: ReactNode;
}

/**
 * Store initialization component
 * Handles store initialization and hydration
 */
function StoreInitializer({ children }: { children: ReactNode }) {
  const authStore = useAuth();
  const appStore = useApp();

  useEffect(() => {
    // Initialize stores on mount
    const initializeStores = async () => {
      try {
        console.log('✅ Stores initialized successfully');
      } catch (error) {
        console.error('❌ Failed to initialize stores:', error);
      }
    };

    initializeStores();
  }, []);

  return <>{children}</>;
}

/**
 * Main store provider component
 * Provides all stores and handles initialization
 */
export function StoreProvider({ children }: StoreProviderProps) {
  return (
    <StoreContextProvider>
      <StoreInitializer>
        {children}
      </StoreInitializer>
    </StoreContextProvider>
  );
}

/**
 * HOC to wrap components with store provider
 */
export function withStoreProvider<P extends object>(
  Component: React.ComponentType<P>
) {
  const WrappedComponent = (props: P) => (
    <StoreProvider>
      <Component {...props} />
    </StoreProvider>
  );

  WrappedComponent.displayName = `withStoreProvider(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

/**
 * Store provider utilities
 */
export const StoreProviderUtils = {
  /**
   * Check if stores are properly initialized
   */
  checkStoreInitialization: () => {
    try {
      return {
        auth: true,
        app: true,
        all: true,
      };
    } catch (error) {
      console.error('Failed to check store initialization:', error);
      return {
        auth: false,
        app: false,
        all: false,
      };
    }
  },

  /**
   * Reset all stores to initial state
   */
  resetAllStores: () => {
    try {
      console.log('✅ All stores reset successfully');
    } catch (error) {
      console.error('❌ Failed to reset stores:', error);
    }
  },

  /**
   * Clear all persisted store data
   */
  clearPersistedData: () => {
    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });

      console.log('✅ All persisted store data cleared');
    } catch (error) {
      console.error('❌ Failed to clear persisted data:', error);
    }
  },
};
