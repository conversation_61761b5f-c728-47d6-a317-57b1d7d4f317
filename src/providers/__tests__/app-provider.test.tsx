/**
 * App Provider Tests
 * Tests for App Provider functionality
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { AppProvider, AppProviderErrorBoundary } from '../app-provider';
import { ProviderUtils } from '../index';

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Test component
function TestComponent() {
  return (
    <div>
      <div data-testid="test-content">App Provider Test</div>
    </div>
  );
}

// Error component
function ErrorComponent() {
  throw new Error('Test app error');
}

describe('AppProvider', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  it('should render children successfully', async () => {
    render(
      <AppProvider>
        <TestComponent />
      </AppProvider>
    );

    expect(screen.getByTestId('test-content')).toHaveTextContent('App Provider Test');
  });

  it('should initialize app on mount', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    
    render(
      <AppProvider>
        <TestComponent />
      </AppProvider>
    );

    // Wait for initialization
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('🚀 APISportsGame CMS initializing...');
      expect(consoleSpy).toHaveBeenCalledWith('✅ APISportsGame CMS initialized successfully');
    });

    consoleSpy.mockRestore();
  });

  it('should initialize theme system', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    
    render(
      <AppProvider>
        <TestComponent />
      </AppProvider>
    );

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('🎨 Theme system initialized');
    });

    consoleSpy.mockRestore();
  });

  it('should apply saved theme from localStorage', async () => {
    mockLocalStorage.getItem.mockImplementation((key) => {
      if (key === 'apisportsgame_theme') return 'dark';
      return null;
    });

    const setAttributeSpy = jest.spyOn(document.documentElement, 'setAttribute');

    render(
      <AppProvider>
        <TestComponent />
      </AppProvider>
    );

    await waitFor(() => {
      expect(setAttributeSpy).toHaveBeenCalledWith('data-theme', 'dark');
    });

    setAttributeSpy.mockRestore();
  });

  it('should detect system theme preference when no saved theme', async () => {
    // Mock dark mode preference
    window.matchMedia = jest.fn().mockImplementation(query => ({
      matches: query === '(prefers-color-scheme: dark)',
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    }));

    const setAttributeSpy = jest.spyOn(document.documentElement, 'setAttribute');

    render(
      <AppProvider>
        <TestComponent />
      </AppProvider>
    );

    await waitFor(() => {
      expect(setAttributeSpy).toHaveBeenCalledWith('data-theme', 'dark');
    });

    setAttributeSpy.mockRestore();
  });

  it('should initialize error tracking in production', async () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';
    
    const addEventListenerSpy = jest.spyOn(window, 'addEventListener');
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

    render(
      <AppProvider>
        <TestComponent />
      </AppProvider>
    );

    await waitFor(() => {
      expect(addEventListenerSpy).toHaveBeenCalledWith('error', expect.any(Function));
      expect(addEventListenerSpy).toHaveBeenCalledWith('unhandledrejection', expect.any(Function));
      expect(consoleSpy).toHaveBeenCalledWith('🔍 Error tracking initialized');
    });

    process.env.NODE_ENV = originalEnv;
    addEventListenerSpy.mockRestore();
    consoleSpy.mockRestore();
  });

  it('should initialize performance monitoring in development', async () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';
    
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

    render(
      <AppProvider>
        <TestComponent />
      </AppProvider>
    );

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('📊 Performance monitoring initialized');
    });

    process.env.NODE_ENV = originalEnv;
    consoleSpy.mockRestore();
  });
});

describe('AppProviderErrorBoundary', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Suppress console.error for error boundary tests
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should render children when no error occurs', () => {
    render(
      <AppProviderErrorBoundary>
        <TestComponent />
      </AppProviderErrorBoundary>
    );

    expect(screen.getByTestId('test-content')).toHaveTextContent('App Provider Test');
  });

  it('should catch and display error fallback when error occurs', () => {
    render(
      <AppProviderErrorBoundary>
        <ErrorComponent />
      </AppProviderErrorBoundary>
    );

    expect(screen.getByText('APISportsGame CMS Error')).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
    expect(screen.getByText('Refresh Page')).toBeInTheDocument();
  });

  it('should show error details in development mode', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    render(
      <AppProviderErrorBoundary>
        <ErrorComponent />
      </AppProviderErrorBoundary>
    );

    expect(screen.getByText('Error Details (Development)')).toBeInTheDocument();

    process.env.NODE_ENV = originalEnv;
  });
});

describe('ProviderUtils', () => {
  it('should check provider status', () => {
    const status = ProviderUtils.checkProviderStatus();
    
    expect(status).toHaveProperty('store');
    expect(status).toHaveProperty('query');
    expect(status).toHaveProperty('all');
    expect(typeof status.store).toBe('boolean');
    expect(typeof status.query).toBe('boolean');
    expect(typeof status.all).toBe('boolean');
  });

  it('should reset all providers in development mode', () => {
    const originalEnv = process.env.NODE_ENV;
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    
    process.env.NODE_ENV = 'development';
    
    ProviderUtils.resetAllProviders();
    
    expect(consoleSpy).toHaveBeenCalledWith('[Dev] All providers reset');
    
    process.env.NODE_ENV = originalEnv;
    consoleSpy.mockRestore();
  });

  it('should not reset providers in production mode', () => {
    const originalEnv = process.env.NODE_ENV;
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    
    process.env.NODE_ENV = 'production';
    
    ProviderUtils.resetAllProviders();
    
    expect(consoleSpy).not.toHaveBeenCalledWith('[Dev] All providers reset');
    
    process.env.NODE_ENV = originalEnv;
    consoleSpy.mockRestore();
  });
});
