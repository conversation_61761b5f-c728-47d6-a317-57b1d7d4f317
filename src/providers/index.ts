/**
 * Providers Index
 * Central export for all provider components
 */

// App provider (combined)
export * from './app-provider';

// Individual providers (re-exported for convenience)
export { StoreProvider, StoreProviderUtils } from '@/stores';
export {
  QueryProvider,
  QueryProviderWithErrorBoundary,
  QueryProviderUtils
} from '@/lib/query-provider';

// Import for internal use
import { QueryProviderUtils as QProviderUtils } from '@/lib/query-provider';

/**
 * Provider utilities
 */
export const ProviderUtils = {
  /**
   * Check if all providers are properly initialized
   */
  checkProviderStatus: () => {
    const storeAvailable = true; // StoreProvider is always available when rendered
    const queryAvailable = QProviderUtils.isQueryClientAvailable();

    return {
      store: storeAvailable,
      query: queryAvailable,
      all: storeAvailable && queryAvailable,
    };
  },

  /**
   * Reset all providers (development only)
   */
  resetAllProviders: () => {
    if (process.env.NODE_ENV === 'development') {
      QProviderUtils.resetQueryClient();
      console.log('[Dev] All providers reset');
    }
  },
} as const;
