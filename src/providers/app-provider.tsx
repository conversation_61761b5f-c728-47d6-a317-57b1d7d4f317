/**
 * App Provider - Combined provider for all application contexts
 * Integrates Store Provider and Query Provider
 */

'use client';

import React, { ReactNode } from 'react';
import { StoreProvider } from '@/stores';
import { QueryProviderWithErrorBoundary } from '@/lib/query-provider';

/**
 * App provider props
 */
interface AppProviderProps {
  children: ReactNode;
}

/**
 * Combined app provider component
 * Provides all necessary contexts for the application
 */
export function AppProvider({ children }: AppProviderProps) {
  return (
    <QueryProviderWithErrorBoundary>
      <StoreProvider>
        <AppInitializer>
          {children}
        </AppInitializer>
      </StoreProvider>
    </QueryProviderWithErrorBoundary>
  );
}

/**
 * App initializer component
 * Handles app-wide initialization logic
 */
function AppInitializer({ children }: { children: ReactNode }) {
  React.useEffect(() => {
    // Initialize app-wide settings
    initializeApp();
  }, []);

  return <>{children}</>;
}

/**
 * Initialize application
 */
async function initializeApp() {
  try {
    console.log('🚀 APISportsGame CMS initializing...');
    
    // Initialize theme
    initializeTheme();
    
    // Initialize error tracking (production only)
    if (process.env.NODE_ENV === 'production') {
      initializeErrorTracking();
    }
    
    // Initialize performance monitoring (development only)
    if (process.env.NODE_ENV === 'development') {
      initializePerformanceMonitoring();
    }
    
    console.log('✅ APISportsGame CMS initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize APISportsGame CMS:', error);
  }
}

/**
 * Initialize theme system
 */
function initializeTheme() {
  try {
    // Get saved theme from localStorage
    const savedTheme = localStorage.getItem('apisportsgame_theme');
    
    if (savedTheme) {
      // Apply saved theme
      document.documentElement.setAttribute('data-theme', savedTheme);
    } else {
      // Detect system theme preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      const defaultTheme = prefersDark ? 'dark' : 'light';
      document.documentElement.setAttribute('data-theme', defaultTheme);
    }
    
    console.log('🎨 Theme system initialized');
  } catch (error) {
    console.warn('⚠️ Failed to initialize theme system:', error);
  }
}

/**
 * Initialize error tracking (production only)
 */
function initializeErrorTracking() {
  try {
    // Global error handler
    window.addEventListener('error', (event) => {
      console.error('[Global Error]', event.error);
      // TODO: Send to error tracking service
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      console.error('[Unhandled Promise Rejection]', event.reason);
      // TODO: Send to error tracking service
    });
    
    console.log('🔍 Error tracking initialized');
  } catch (error) {
    console.warn('⚠️ Failed to initialize error tracking:', error);
  }
}

/**
 * Initialize performance monitoring (development only)
 */
function initializePerformanceMonitoring() {
  try {
    // Performance observer for navigation timing
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            console.log('[Performance] Navigation timing:', entry);
          }
        }
      });
      
      observer.observe({ entryTypes: ['navigation'] });
    }
    
    // Log initial performance metrics
    setTimeout(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        console.log('[Performance] Page load metrics:', {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          totalTime: navigation.loadEventEnd - navigation.fetchStart,
        });
      }
    }, 1000);
    
    console.log('📊 Performance monitoring initialized');
  } catch (error) {
    console.warn('⚠️ Failed to initialize performance monitoring:', error);
  }
}

/**
 * App provider error boundary
 */
export class AppProviderErrorBoundary extends React.Component<
  { children: ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): { hasError: boolean; error: Error } {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('[AppProvider Error]', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <AppErrorFallback 
          error={this.state.error}
          onRetry={() => this.setState({ hasError: false, error: undefined })}
        />
      );
    }

    return this.props.children;
  }
}

/**
 * App error fallback component
 */
function AppErrorFallback({ 
  error, 
  onRetry 
}: { 
  error?: Error; 
  onRetry: () => void; 
}) {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      padding: '20px',
      textAlign: 'center',
      fontFamily: 'system-ui, sans-serif',
      backgroundColor: '#f9fafb',
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '40px',
        borderRadius: '12px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        maxWidth: '600px',
        width: '100%',
      }}>
        <h1 style={{ 
          color: '#dc2626', 
          marginBottom: '16px',
          fontSize: '24px',
          fontWeight: 'bold',
        }}>
          APISportsGame CMS Error
        </h1>
        <p style={{ 
          color: '#6b7280', 
          marginBottom: '24px',
          lineHeight: '1.6',
        }}>
          An unexpected error occurred while loading the application. 
          Please try refreshing the page or contact support if the problem persists.
        </p>
        
        {error && process.env.NODE_ENV === 'development' && (
          <details style={{ 
            marginBottom: '24px', 
            padding: '16px', 
            backgroundColor: '#f3f4f6',
            borderRadius: '8px',
            textAlign: 'left',
          }}>
            <summary style={{ 
              cursor: 'pointer', 
              fontWeight: 'bold',
              marginBottom: '12px',
            }}>
              Error Details (Development)
            </summary>
            <pre style={{ 
              fontSize: '12px', 
              overflow: 'auto',
              whiteSpace: 'pre-wrap',
              margin: 0,
            }}>
              {error.message}
              {error.stack && `\n\n${error.stack}`}
            </pre>
          </details>
        )}
        
        <div style={{ display: 'flex', gap: '12px', justifyContent: 'center' }}>
          <button
            onClick={onRetry}
            style={{
              padding: '12px 24px',
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '16px',
              fontWeight: '500',
            }}
          >
            Try Again
          </button>
          <button
            onClick={() => window.location.reload()}
            style={{
              padding: '12px 24px',
              backgroundColor: '#6b7280',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '16px',
              fontWeight: '500',
            }}
          >
            Refresh Page
          </button>
        </div>
      </div>
    </div>
  );
}

/**
 * HOC to wrap components with App Provider
 */
export function withAppProvider<P extends object>(
  Component: React.ComponentType<P>
) {
  const WrappedComponent = (props: P) => (
    <AppProvider>
      <Component {...props} />
    </AppProvider>
  );
  
  WrappedComponent.displayName = `withAppProvider(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}
