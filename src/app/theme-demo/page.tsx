/**
 * Theme Demo Page - Test page for Theme System functionality
 * Demonstrates usage of theme system with Ant Design components
 */

'use client';

import React from 'react';
import {
  Button,
  Card,
  Input,
  Select,
  Switch,
  Space,
  Typography,
  Divider,
  Row,
  Col,
  Tag,
  Alert,
  Progress,
  Avatar,
  Badge,
  Tooltip
} from 'antd';
import {
  SunOutlined,
  MoonOutlined,
  SettingOutlined,
  UserOutlined,
  BellOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { useTheme, useThemeStyles, useSystemTheme, useThemeDebug } from '@/theme';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

export default function ThemeDemoPage() {
  const { theme, isDark, toggleTheme, setTheme, colors } = useTheme();
  const { systemTheme } = useSystemTheme();
  const themeStyles = useThemeStyles();
  const themeDebug = useThemeDebug();

  const handleThemeChange = (value: string) => {
    setTheme(value as 'light' | 'dark');
  };

  return (
    <div style={{ padding: '24px', minHeight: '100vh', backgroundColor: colors.background.layout }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* Header */}
        <Card style={{ marginBottom: '24px' }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={2} style={{ margin: 0 }}>
                🎨 Theme System Demo
              </Title>
              <Text type="secondary">
                APISportsGame CMS Theme Configuration
              </Text>
            </Col>
            <Col>
              <Space>
                <Select
                  value={theme}
                  onChange={handleThemeChange}
                  style={{ width: 120 }}
                >
                  <Option value="light">
                    <SunOutlined /> Light
                  </Option>
                  <Option value="dark">
                    <MoonOutlined /> Dark
                  </Option>
                </Select>
                <Button
                  type="primary"
                  icon={isDark ? <SunOutlined /> : <MoonOutlined />}
                  onClick={toggleTheme}
                >
                  Toggle Theme
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* Theme Information */}
        <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
          <Col xs={24} md={8}>
            <Card title="Theme Status" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>Current Theme: </Text>
                  <Tag color={isDark ? 'purple' : 'orange'}>
                    {theme}
                  </Tag>
                </div>
                <div>
                  <Text strong>System Theme: </Text>
                  <Tag color="blue">{systemTheme}</Tag>
                </div>
                <div>
                  <Text strong>Is Dark Mode: </Text>
                  <Switch checked={isDark} disabled />
                </div>
              </Space>
            </Card>
          </Col>

          <Col xs={24} md={8}>
            <Card title="Theme Colors" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <div style={{
                    width: '20px',
                    height: '20px',
                    backgroundColor: colors.primary,
                    borderRadius: '4px',
                    border: '1px solid #ccc'
                  }} />
                  <Text>Primary: {colors.primary}</Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <div style={{
                    width: '20px',
                    height: '20px',
                    backgroundColor: colors.success,
                    borderRadius: '4px',
                    border: '1px solid #ccc'
                  }} />
                  <Text>Success: {colors.success}</Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <div style={{
                    width: '20px',
                    height: '20px',
                    backgroundColor: colors.warning,
                    borderRadius: '4px',
                    border: '1px solid #ccc'
                  }} />
                  <Text>Warning: {colors.warning}</Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <div style={{
                    width: '20px',
                    height: '20px',
                    backgroundColor: colors.error,
                    borderRadius: '4px',
                    border: '1px solid #ccc'
                  }} />
                  <Text>Error: {colors.error}</Text>
                </div>
              </Space>
            </Card>
          </Col>

          <Col xs={24} md={8}>
            <Card title="Background Colors" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <div style={{
                    width: '20px',
                    height: '20px',
                    backgroundColor: colors.background.container,
                    borderRadius: '4px',
                    border: '1px solid #ccc'
                  }} />
                  <Text>Container</Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <div style={{
                    width: '20px',
                    height: '20px',
                    backgroundColor: colors.background.layout,
                    borderRadius: '4px',
                    border: '1px solid #ccc'
                  }} />
                  <Text>Layout</Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <div style={{
                    width: '20px',
                    height: '20px',
                    backgroundColor: colors.background.elevated,
                    borderRadius: '4px',
                    border: '1px solid #ccc'
                  }} />
                  <Text>Elevated</Text>
                </div>
              </Space>
            </Card>
          </Col>
        </Row>

        {/* Component Showcase */}
        <Card title="Component Showcase" style={{ marginBottom: '24px' }}>
          <Row gutter={[24, 24]}>
            {/* Buttons */}
            <Col xs={24} md={12}>
              <Title level={4}>Buttons</Title>
              <Space wrap>
                <Button type="primary">Primary</Button>
                <Button>Default</Button>
                <Button type="dashed">Dashed</Button>
                <Button type="text">Text</Button>
                <Button type="link">Link</Button>
                <Button danger>Danger</Button>
              </Space>
            </Col>

            {/* Inputs */}
            <Col xs={24} md={12}>
              <Title level={4}>Inputs</Title>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Input placeholder="Basic input" />
                <Input.Search placeholder="Search input" enterButton />
                <Select defaultValue="option1" style={{ width: '100%' }}>
                  <Option value="option1">Option 1</Option>
                  <Option value="option2">Option 2</Option>
                </Select>
              </Space>
            </Col>

            {/* Alerts */}
            <Col xs={24}>
              <Title level={4}>Alerts</Title>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Alert message="Success message" type="success" showIcon />
                <Alert message="Info message" type="info" showIcon />
                <Alert message="Warning message" type="warning" showIcon />
                <Alert message="Error message" type="error" showIcon />
              </Space>
            </Col>

            {/* Progress */}
            <Col xs={24} md={12}>
              <Title level={4}>Progress</Title>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Progress percent={30} />
                <Progress percent={50} status="active" />
                <Progress percent={70} status="exception" />
                <Progress percent={100} />
              </Space>
            </Col>

            {/* Avatars and Badges */}
            <Col xs={24} md={12}>
              <Title level={4}>Avatars & Badges</Title>
              <Space>
                <Avatar icon={<UserOutlined />} />
                <Avatar style={{ backgroundColor: colors.primary }}>U</Avatar>
                <Badge count={5}>
                  <Avatar icon={<UserOutlined />} />
                </Badge>
                <Badge dot>
                  <Avatar icon={<BellOutlined />} />
                </Badge>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* Theme Styles Demo */}
        <Card title="Theme Styles Demo" style={{ marginBottom: '24px' }}>
          <Row gutter={[24, 24]}>
            <Col xs={24} md={8}>
              <div style={{ ...themeStyles.containerStyle, padding: '16px' }} className="theme-card">
                <Title level={5}>Container Style</Title>
                <Text>This uses theme container style with proper background and text colors.</Text>
              </div>
            </Col>

            <Col xs={24} md={8}>
              <div style={{ ...themeStyles.cardStyle, padding: '16px' }} className="theme-card">
                <Title level={5}>Card Style</Title>
                <Text>This uses theme card style with elevated background and borders.</Text>
              </div>
            </Col>

            <Col xs={24} md={8}>
              <div style={{ ...themeStyles.headerStyle, padding: '16px' }} className="theme-header">
                <Title level={5}>Header Style</Title>
                <Text>This uses theme header style with bottom border.</Text>
              </div>
            </Col>
          </Row>
        </Card>

        {/* Development Debug Info */}
        {process.env.NODE_ENV === 'development' && themeDebug && (
          <Card title="Debug Information (Development)" style={{ marginBottom: '24px' }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button onClick={themeDebug.logThemeInfo} icon={<SettingOutlined />}>
                Log Theme Info to Console
              </Button>
              <details>
                <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
                  Theme Debug Data
                </summary>
                <pre style={{
                  background: colors.background.elevated,
                  padding: '12px',
                  borderRadius: '6px',
                  overflow: 'auto',
                  fontSize: '12px',
                  marginTop: '8px'
                }}>
                  {JSON.stringify(themeDebug.debugInfo, null, 2)}
                </pre>
              </details>
            </Space>
          </Card>
        )}

        {/* Navigation */}
        <Card>
          <Title level={4}>Navigation</Title>
          <Paragraph>
            This demo page shows the Theme System functionality with Ant Design integration.
            The theme automatically applies to all Ant Design components and provides CSS variables
            for custom styling.
          </Paragraph>
          <Space>
            <Button type="link" href="/">← Back to Home</Button>
            <Button type="link" href="/api-hooks-demo">API Hooks Demo</Button>
            <Button type="link" href="/simple-query-demo">Simple Query Demo</Button>
          </Space>
        </Card>
      </div>
    </div>
  );
}
