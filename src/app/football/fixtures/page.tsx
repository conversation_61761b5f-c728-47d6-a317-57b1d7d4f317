/**
 * Football Fixtures Management Page
 * Comprehensive fixtures management with live updates and filtering
 */

'use client';

import React, { useState } from 'react';
import {
  AppLayout,
  PageHeader,
  Container,
  DataTable,
  Card,
  StatCard,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Avatar,
  Dropdown,
  Typography,
  Alert,

  Badge,
  DatePicker,
} from '@/components';
import {
  CalendarOutlined,
  SearchOutlined,
  EyeOutlined,
  MoreOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  SyncOutlined,
  FilterOutlined,
  TeamOutlined,
  TrophyOutlined,
  VideoCameraOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons';
import { useFixtures, useLeagues } from '@/hooks/api';
import { useRouter, useSearchParams } from 'next/navigation';
import dayjs from 'dayjs';

const { Text } = Typography;
const { RangePicker } = DatePicker;

// Types for fixtures
interface Fixture {
  id: number;
  externalId: string;
  date: string;
  timestamp: number;
  status: {
    long: string;
    short: string;
    elapsed?: number;
  };
  league: {
    id: number;
    name: string;
    country: string;
    logo?: string;
    season: number;
    round: string;
  };
  teams: {
    home: {
      id: number;
      name: string;
      logo?: string;
      winner?: boolean;
    };
    away: {
      id: number;
      name: string;
      logo?: string;
      winner?: boolean;
    };
  };
  goals: {
    home?: number;
    away?: number;
  };
  venue?: {
    id?: number;
    name?: string;
    city?: string;
  };
}

interface FixtureListParams {
  page?: number;
  limit?: number;
  search?: string;
  league?: number;
  team?: number;
  status?: string;
  date?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: 'date' | 'league' | 'status';
  sortOrder?: 'asc' | 'desc';
}

const DEFAULT_PARAMS: FixtureListParams = {
  page: 1,
  limit: 20,
  sortBy: 'date',
  sortOrder: 'desc',
};

export default function FixturesPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const teamParam = searchParams.get('team');
  const leagueParam = searchParams.get('league');

  const [params, setParams] = useState<FixtureListParams>({
    ...DEFAULT_PARAMS,
    team: teamParam ? parseInt(teamParam) : undefined,
    league: leagueParam ? parseInt(leagueParam) : undefined,
  });

  // API hooks
  const { data: fixturesData, isLoading, error } = useFixtures(params);
  const { data: leaguesData } = useLeagues({ limit: 100 });

  // Mock statistics
  const statistics = {
    total: 2450,
    live: 12,
    today: 45,
    upcoming: 180,
    finished: 2213,
  };

  // Handle search
  const handleSearch = (value: string) => {
    setParams(prev => ({ ...prev, search: value, page: 1 }));
  };

  // Handle filter change
  const handleFilterChange = (key: keyof FixtureListParams, value: any) => {
    setParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // Handle date range change
  const handleDateRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      setParams(prev => ({
        ...prev,
        dateFrom: dates[0].format('YYYY-MM-DD'),
        dateTo: dates[1].format('YYYY-MM-DD'),
        page: 1,
      }));
    } else {
      setParams(prev => ({
        ...prev,
        dateFrom: undefined,
        dateTo: undefined,
        page: 1,
      }));
    }
  };

  // Handle table change
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    if (pagination) {
      setParams(prev => ({
        ...prev,
        page: pagination.current,
        limit: pagination.pageSize
      }));
    }

    if (sorter && sorter.field) {
      setParams(prev => ({
        ...prev,
        sortBy: sorter.field as any,
        sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc'
      }));
    }
  };

  // Get status color and icon
  const getStatusDisplay = (status: Fixture['status']) => {
    const statusMap = {
      'NS': { color: 'default', icon: <ClockCircleOutlined />, text: 'Not Started' },
      'LIVE': { color: 'red', icon: <VideoCameraOutlined />, text: 'Live' },
      '1H': { color: 'orange', icon: <PlayCircleOutlined />, text: '1st Half' },
      'HT': { color: 'blue', icon: <PauseCircleOutlined />, text: 'Half Time' },
      '2H': { color: 'orange', icon: <PlayCircleOutlined />, text: '2nd Half' },
      'FT': { color: 'green', icon: <CheckCircleOutlined />, text: 'Full Time' },
      'AET': { color: 'purple', icon: <CheckCircleOutlined />, text: 'After Extra Time' },
      'PEN': { color: 'purple', icon: <CheckCircleOutlined />, text: 'Penalties' },
      'CANC': { color: 'red', icon: <CloseCircleOutlined />, text: 'Cancelled' },
      'POSTP': { color: 'orange', icon: <ClockCircleOutlined />, text: 'Postponed' },
    };

    const display = statusMap[status.short as keyof typeof statusMap] || {
      color: 'default',
      icon: <ClockCircleOutlined />,
      text: status.long,
    };

    return (
      <Tag icon={display.icon} color={display.color}>
        {display.text}
        {status.elapsed && ` (${status.elapsed}')`}
      </Tag>
    );
  };

  // Table columns
  const columns = [
    {
      title: 'Date & Time',
      dataIndex: 'date',
      key: 'date',
      sorter: true,
      width: 140,
      render: (date: string) => (
        <div>
          <div style={{ fontWeight: 'bold', fontSize: '12px' }}>
            {dayjs(date).format('MMM DD')}
          </div>
          <div style={{ fontSize: '11px', color: '#666' }}>
            {dayjs(date).format('HH:mm')}
          </div>
        </div>
      ),
    },
    {
      title: 'Match',
      key: 'match',
      render: (_, fixture: Fixture) => (
        <div style={{ minWidth: '250px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
            {fixture.teams.home.logo && (
              <img
                src={fixture.teams.home.logo}
                alt={fixture.teams.home.name}
                width={20}
                height={20}
                style={{ objectFit: 'contain' }}
              />
            )}
            <span style={{
              fontWeight: fixture.teams.home.winner ? 'bold' : 'normal',
              color: fixture.teams.home.winner ? '#52c41a' : 'inherit'
            }}>
              {fixture.teams.home.name}
            </span>
            {fixture.goals.home !== undefined && (
              <Badge count={fixture.goals.home} style={{ backgroundColor: '#52c41a' }} />
            )}
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {fixture.teams.away.logo && (
              <img
                src={fixture.teams.away.logo}
                alt={fixture.teams.away.name}
                width={20}
                height={20}
                style={{ objectFit: 'contain' }}
              />
            )}
            <span style={{
              fontWeight: fixture.teams.away.winner ? 'bold' : 'normal',
              color: fixture.teams.away.winner ? '#52c41a' : 'inherit'
            }}>
              {fixture.teams.away.name}
            </span>
            {fixture.goals.away !== undefined && (
              <Badge count={fixture.goals.away} style={{ backgroundColor: '#52c41a' }} />
            )}
          </div>
        </div>
      ),
    },
    {
      title: 'League',
      dataIndex: 'league',
      key: 'league',
      render: (league: Fixture['league']) => (
        <div style={{ minWidth: '150px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {league.logo && (
              <img
                src={league.logo}
                alt={league.name}
                width={16}
                height={16}
                style={{ objectFit: 'contain' }}
              />
            )}
            <span style={{ fontWeight: 'bold', fontSize: '12px' }}>
              {league.name}
            </span>
          </div>
          <div style={{ fontSize: '11px', color: '#666' }}>
            {league.round}
          </div>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: Fixture['status']) => getStatusDisplay(status),
    },
    {
      title: 'Venue',
      dataIndex: 'venue',
      key: 'venue',
      render: (venue?: Fixture['venue']) => (
        venue?.name ? (
          <div style={{ fontSize: '12px' }}>
            <div style={{ fontWeight: 'bold' }}>{venue.name}</div>
            {venue.city && (
              <div style={{ color: '#666' }}>{venue.city}</div>
            )}
          </div>
        ) : (
          <Text type="secondary">TBD</Text>
        )
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, fixture: Fixture) => {
        const menuItems = [
          {
            key: 'view',
            icon: <EyeOutlined />,
            label: 'View Details',
            onClick: () => router.push(`/football/fixtures/${fixture.id}`),
          },
          {
            key: 'broadcast',
            icon: <PlayCircleOutlined />,
            label: 'Broadcast Links',
            onClick: () => router.push(`/broadcast/links?fixture=${fixture.id}`),
          },
          {
            key: 'teams',
            icon: <TeamOutlined />,
            label: 'View Teams',
            onClick: () => router.push(`/football/teams?fixture=${fixture.id}`),
          },
        ];

        return (
          <Dropdown menu={{ items: menuItems }} trigger={['click']}>
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        );
      },
    },
  ];

  return (
    <AppLayout>
      <PageHeader
        title="Football Fixtures"
        subtitle="Manage football fixtures and match schedules"
        breadcrumbs={[
          { title: 'Home', href: '/' },
          { title: 'Football', href: '/football' },
          { title: 'Fixtures' },
        ]}
        actions={[
          <Button
            key="live"
            icon={<VideoCameraOutlined />}
            onClick={() => router.push('/football/fixtures/live')}
            type="primary"
            danger
          >
            Live Fixtures
          </Button>,
          <Button
            key="sync"
            icon={<SyncOutlined />}
            onClick={() => router.push('/football/sync')}
          >
            Sync Data
          </Button>,
        ]}
      />

      <Container>
        {/* Statistics Cards */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px',
          marginBottom: '24px'
        }}>
          <StatCard
            title="Total Fixtures"
            value={statistics.total}
            subtitle="All fixtures"
            icon={<CalendarOutlined />}
          />

          <StatCard
            title="Live Now"
            value={statistics.live}
            subtitle="Currently playing"
            icon={<VideoCameraOutlined />}
            trend={{ value: statistics.live, isPositive: statistics.live > 0 }}
          />

          <StatCard
            title="Today"
            value={statistics.today}
            subtitle="Fixtures today"
            icon={<ClockCircleOutlined />}
          />

          <StatCard
            title="Upcoming"
            value={statistics.upcoming}
            subtitle="Future fixtures"
            icon={<CalendarOutlined />}
          />
        </div>

        {/* Filters and Search */}
        <Card style={{ marginBottom: '24px' }}>
          <Space size="middle" wrap>
            <Input
              placeholder="Search fixtures..."
              prefix={<SearchOutlined />}
              value={params.search}
              onChange={(e) => handleSearch(e.target.value)}
              style={{ width: '250px' }}
              allowClear
            />

            <Select
              placeholder="Filter by league"
              value={params.league}
              onChange={(value) => handleFilterChange('league', value)}
              style={{ width: '180px' }}
              allowClear
            >
              {leaguesData?.data?.map((league: any) => (
                <Select.Option key={league.id} value={league.id}>
                  {league.name}
                </Select.Option>
              ))}
            </Select>

            <Select
              placeholder="Filter by status"
              value={params.status}
              onChange={(value) => handleFilterChange('status', value)}
              style={{ width: '140px' }}
              allowClear
            >
              <Select.Option value="NS">Not Started</Select.Option>
              <Select.Option value="LIVE">Live</Select.Option>
              <Select.Option value="FT">Finished</Select.Option>
              <Select.Option value="POSTP">Postponed</Select.Option>
              <Select.Option value="CANC">Cancelled</Select.Option>
            </Select>

            <RangePicker
              placeholder={['From Date', 'To Date']}
              onChange={handleDateRangeChange}
              style={{ width: '240px' }}
              format="YYYY-MM-DD"
            />

            <Button
              icon={<FilterOutlined />}
              onClick={() => {
                setParams(DEFAULT_PARAMS);
                router.push('/football/fixtures');
              }}
            >
              Clear Filters
            </Button>
          </Space>
        </Card>

        {/* Error Alert */}
        {error && (
          <Alert
            message="Error Loading Fixtures"
            description="Failed to load fixtures data. Please try again."
            type="error"
            showIcon
            style={{ marginBottom: '24px' }}
          />
        )}

        {/* Fixtures Table */}
        <DataTable
          columns={columns}
          dataSource={fixturesData?.data || []}
          loading={isLoading}
          pagination={{
            current: params.page,
            pageSize: params.limit,
            total: fixturesData?.meta?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} fixtures`,
          }}
          onChange={handleTableChange}
          rowKey="id"
          scroll={{ x: 1200 }}
        />
      </Container>
    </AppLayout>
  );
}
