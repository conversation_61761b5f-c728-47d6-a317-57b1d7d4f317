/**
 * Football Management Hub Page
 * Central navigation for all football-related features
 */

'use client';

import React from 'react';
import {
  AppLayout,
  PageHeader,
  Container,
  Card,
  StatCard,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Alert,
  Tag,
  Divider,
} from '@/components';
import {
  TrophyOutlined,
  TeamOutlined,
  CalendarOutlined,
  SyncOutlined,
  VideoCameraOutlined,
  Bar<PERSON><PERSON>Outlined,
  SettingOutlined,
  PlayCircleOutlined,
  ClockCircleOutlined,
  GlobalOutlined,
  RightOutlined,
  DashboardOutlined,
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { useSyncStatus } from '@/hooks/api';

const { Title, Text, Paragraph } = Typography;

export default function FootballPage() {
  const router = useRouter();
  const { data: syncStatus } = useSyncStatus();

  // Mock statistics
  const statistics = {
    leagues: 45,
    teams: 520,
    fixtures: 2450,
    liveMatches: 12,
    todayMatches: 45,
    upcomingMatches: 180,
  };

  // Feature cards data
  const features = [
    {
      title: 'Leagues Management',
      description: 'Manage football leagues and competitions',
      icon: <TrophyOutlined style={{ fontSize: '32px', color: '#1890ff' }} />,
      path: '/football/leagues',
      stats: `${statistics.leagues} leagues`,
      color: '#1890ff',
    },
    {
      title: 'Teams Management',
      description: 'View and manage football teams',
      icon: <TeamOutlined style={{ fontSize: '32px', color: '#52c41a' }} />,
      path: '/football/teams',
      stats: `${statistics.teams} teams`,
      color: '#52c41a',
    },
    {
      title: 'Fixtures Management',
      description: 'Manage match schedules and results',
      icon: <CalendarOutlined style={{ fontSize: '32px', color: '#faad14' }} />,
      path: '/football/fixtures',
      stats: `${statistics.fixtures} fixtures`,
      color: '#faad14',
    },
    {
      title: 'Live Fixtures',
      description: 'Monitor live matches in real-time',
      icon: <VideoCameraOutlined style={{ fontSize: '32px', color: '#ff4d4f' }} />,
      path: '/football/fixtures/live',
      stats: `${statistics.liveMatches} live now`,
      color: '#ff4d4f',
      highlight: statistics.liveMatches > 0,
    },
    {
      title: 'Data Synchronization',
      description: 'Sync football data from external sources',
      icon: <SyncOutlined style={{ fontSize: '32px', color: '#722ed1' }} />,
      path: '/football/sync',
      stats: 'Sync management',
      color: '#722ed1',
    },
    {
      title: 'Analytics & Reports',
      description: 'View football data analytics and reports',
      icon: <BarChartOutlined style={{ fontSize: '32px', color: '#13c2c2' }} />,
      path: '/football/analytics',
      stats: 'Coming soon',
      color: '#13c2c2',
      disabled: true,
    },
  ];

  // Quick actions
  const quickActions = [
    {
      title: 'Start Manual Sync',
      description: 'Sync all football data now',
      icon: <SyncOutlined />,
      action: () => router.push('/football/sync'),
      type: 'primary' as const,
    },
    {
      title: 'View Live Matches',
      description: 'See what\'s playing now',
      icon: <VideoCameraOutlined />,
      action: () => router.push('/football/fixtures/live'),
      type: 'default' as const,
      danger: true,
    },
    {
      title: 'Today\'s Fixtures',
      description: 'Check today\'s schedule',
      icon: <ClockCircleOutlined />,
      action: () => router.push('/football/fixtures?date=today'),
      type: 'default' as const,
    },
    {
      title: 'Add New League',
      description: 'Create a new league',
      icon: <TrophyOutlined />,
      action: () => router.push('/football/leagues/create'),
      type: 'default' as const,
    },
  ];

  return (
    <AppLayout>
      <PageHeader
        title="Football Management"
        subtitle="Comprehensive football data management system"
        breadcrumbs={[
          { title: 'Home', href: '/' },
          { title: 'Football' },
        ]}
        actions={[
          <Button
            key="dashboard"
            icon={<DashboardOutlined />}
            onClick={() => router.push('/')}
          >
            Dashboard
          </Button>,
          <Button
            key="settings"
            icon={<SettingOutlined />}
            onClick={() => router.push('/football/settings')}
          >
            Settings
          </Button>,
        ]}
      />

      <Container>
        {/* Sync Status Alert */}
        {syncStatus?.isRunning && (
          <Alert
            message="Data Synchronization in Progress"
            description="Football data is currently being synchronized. Some information may be temporarily outdated."
            type="info"
            showIcon
            style={{ marginBottom: '24px' }}
            action={
              <Button
                size="small"
                onClick={() => router.push('/football/sync')}
              >
                View Progress
              </Button>
            }
          />
        )}

        {/* Statistics Overview */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px',
          marginBottom: '32px'
        }}>
          <StatCard
            title="Total Leagues"
            value={statistics.leagues}
            subtitle="Active competitions"
            icon={<TrophyOutlined />}
          />

          <StatCard
            title="Total Teams"
            value={statistics.teams}
            subtitle="Registered teams"
            icon={<TeamOutlined />}
          />

          <StatCard
            title="Total Fixtures"
            value={statistics.fixtures}
            subtitle="All matches"
            icon={<CalendarOutlined />}
          />

          <StatCard
            title="Live Matches"
            value={statistics.liveMatches}
            subtitle="Currently playing"
            icon={<VideoCameraOutlined />}
            trend={{ value: statistics.liveMatches, isPositive: statistics.liveMatches > 0 }}
          />

          <StatCard
            title="Today's Matches"
            value={statistics.todayMatches}
            subtitle="Scheduled today"
            icon={<ClockCircleOutlined />}
          />

          <StatCard
            title="Upcoming Matches"
            value={statistics.upcomingMatches}
            subtitle="Future fixtures"
            icon={<CalendarOutlined />}
          />
        </div>

        {/* Feature Cards */}
        <Title level={3} style={{ marginBottom: '24px' }}>
          Football Management Features
        </Title>

        <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
          {features.map((feature, index) => (
            <Col xs={24} sm={12} lg={8} key={index}>
              <Card
                hoverable={!feature.disabled}
                style={{
                  height: '100%',
                  opacity: feature.disabled ? 0.6 : 1,
                  border: feature.highlight ? `2px solid ${feature.color}` : undefined,
                  boxShadow: feature.highlight ? `0 4px 12px ${feature.color}20` : undefined,
                }}
                bodyStyle={{ padding: '24px' }}
                onClick={() => !feature.disabled && router.push(feature.path)}
              >
                <div style={{ textAlign: 'center' }}>
                  <div style={{ marginBottom: '16px' }}>
                    {feature.icon}
                  </div>

                  <Title level={4} style={{ marginBottom: '8px' }}>
                    {feature.title}
                    {feature.highlight && (
                      <Tag color="red" style={{ marginLeft: '8px' }}>
                        LIVE
                      </Tag>
                    )}
                  </Title>

                  <Paragraph
                    type="secondary"
                    style={{ marginBottom: '16px', minHeight: '44px' }}
                  >
                    {feature.description}
                  </Paragraph>

                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Tag color={feature.color}>{feature.stats}</Tag>
                    {!feature.disabled && <RightOutlined style={{ color: feature.color }} />}
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>

        {/* Quick Actions */}
        <Title level={3} style={{ marginBottom: '24px' }}>
          Quick Actions
        </Title>

        <Row gutter={[16, 16]} style={{ marginBottom: '32px' }}>
          {quickActions.map((action, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card
                hoverable
                bodyStyle={{ padding: '20px', textAlign: 'center' }}
                onClick={action.action}
              >
                <Space direction="vertical" size="small">
                  <Button
                    type={action.type}
                    danger={action.danger}
                    icon={action.icon}
                    size="large"
                    style={{ marginBottom: '8px' }}
                  />
                  <Text strong>{action.title}</Text>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {action.description}
                  </Text>
                </Space>
              </Card>
            </Col>
          ))}
        </Row>

        {/* System Information */}
        <Card title="System Information" style={{ marginBottom: '24px' }}>
          <Row gutter={[24, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <div>
                <Text type="secondary">Data Source</Text>
                <div style={{ fontWeight: 'bold' }}>API-Football</div>
              </div>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <div>
                <Text type="secondary">Last Sync</Text>
                <div style={{ fontWeight: 'bold' }}>
                  {syncStatus?.lastSync ?
                    new Date(syncStatus.lastSync.timestamp).toLocaleString() :
                    'Never'
                  }
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <div>
                <Text type="secondary">Sync Status</Text>
                <div>
                  <Tag color={syncStatus?.isRunning ? 'orange' : 'green'}>
                    {syncStatus?.isRunning ? 'Running' : 'Idle'}
                  </Tag>
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <div>
                <Text type="secondary">Coverage</Text>
                <div style={{ fontWeight: 'bold' }}>
                  <GlobalOutlined /> Global
                </div>
              </div>
            </Col>
          </Row>
        </Card>

        {/* Help and Documentation */}
        <Card title="Help & Documentation">
          <Paragraph>
            The Football Management system provides comprehensive tools for managing football data including leagues, teams, fixtures, and live match monitoring.
          </Paragraph>
          <Space wrap>
            <Button type="link" onClick={() => router.push('/help/football')}>
              Football Guide
            </Button>
            <Button type="link" onClick={() => router.push('/help/sync')}>
              Sync Documentation
            </Button>
            <Button type="link" onClick={() => router.push('/help/api')}>
              API Reference
            </Button>
            <Button type="link" onClick={() => router.push('/support')}>
              Contact Support
            </Button>
          </Space>
        </Card>
      </Container>
    </AppLayout>
  );
}
