'use client';

import React from 'react';
import {
  AppLayout,
  PageHeader,
  Container,
  TwoColumnLayout,
  Card,
  StatCard,
  Button,
  Space,
  Typography,
  Alert,
} from '@/components';
import {
  DashboardOutlined,
  UserOutlined,
  TrophyOutlined,
  CalendarOutlined,
  <PERSON>Outlined,
  Bar<PERSON><PERSON>Outlined,
  ApiOutlined,
  SettingOutlined,
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

export default function Home() {
  return (
    <AppLayout>
      <PageHeader
        title="Dashboard"
        subtitle="Welcome to APISportsGame CMS - Your central hub for managing football data and broadcast links"
        actions={[
          <Button key="settings" icon={<SettingOutlined />}>
            Settings
          </Button>,
        ]}
      />

      <Container>
        {/* Welcome Alert */}
        <Alert
          message="Welcome to APISportsGame CMS!"
          description="This is your central dashboard for managing football leagues, teams, fixtures, broadcast links, and system users. Navigate using the sidebar menu to access different sections."
          type="success"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        {/* Stats Overview */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '16px',
          marginBottom: '24px'
        }}>
          <StatCard
            title="Total Leagues"
            value="25"
            subtitle="Active football leagues"
            icon={<TrophyOutlined />}
            trend={{ value: 8, isPositive: true }}
          />

          <StatCard
            title="Teams"
            value="500+"
            subtitle="Registered teams"
            icon={<UserOutlined />}
            trend={{ value: 12, isPositive: true }}
          />

          <StatCard
            title="Fixtures"
            value="1,250"
            subtitle="Total fixtures"
            icon={<CalendarOutlined />}
            trend={{ value: 5, isPositive: true }}
          />

          <StatCard
            title="Broadcast Links"
            value="850"
            subtitle="Active links"
            icon={<LinkOutlined />}
            trend={{ value: 15, isPositive: true }}
          />
        </div>

        {/* Main Content */}
        <TwoColumnLayout
          leftContent={
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              <Card title="Quick Actions">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button type="primary" icon={<CalendarOutlined />} block>
                    Sync Latest Fixtures
                  </Button>
                  <Button icon={<LinkOutlined />} block>
                    Add Broadcast Link
                  </Button>
                  <Button icon={<UserOutlined />} block>
                    Create System User
                  </Button>
                  <Button icon={<BarChartOutlined />} block>
                    View Reports
                  </Button>
                </Space>
              </Card>

              <Card title="System Overview">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text strong>API Status:</Text>
                    <Text style={{ color: '#52c41a', marginLeft: '8px' }}>● Online</Text>
                  </div>
                  <div>
                    <Text strong>Database:</Text>
                    <Text style={{ color: '#52c41a', marginLeft: '8px' }}>● Connected</Text>
                  </div>
                  <div>
                    <Text strong>External API:</Text>
                    <Text style={{ color: '#52c41a', marginLeft: '8px' }}>● Syncing</Text>
                  </div>
                  <div>
                    <Text strong>Last Sync:</Text>
                    <Text style={{ marginLeft: '8px' }}>2 minutes ago</Text>
                  </div>
                </Space>
              </Card>
            </Space>
          }
          rightContent={
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              <Card title="Recent Activity">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text strong>Fixture sync completed</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      2 minutes ago
                    </Text>
                  </div>
                  <div>
                    <Text strong>New broadcast link added</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      5 minutes ago
                    </Text>
                  </div>
                  <div>
                    <Text strong>User John Doe logged in</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      10 minutes ago
                    </Text>
                  </div>
                  <div>
                    <Text strong>System backup completed</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      1 hour ago
                    </Text>
                  </div>
                </Space>
              </Card>

              <Card title="Demo Pages">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button type="link" href="/layout-demo" icon={<DashboardOutlined />}>
                    Layout Demo
                  </Button>
                  <Button type="link" href="/components-demo" icon={<BarChartOutlined />}>
                    Components Demo
                  </Button>
                  <Button type="link" href="/theme-demo" icon={<SettingOutlined />}>
                    Theme Demo
                  </Button>
                  <Button type="link" href="/api-hooks-demo" icon={<ApiOutlined />}>
                    API Hooks Demo
                  </Button>
                </Space>
              </Card>
            </Space>
          }
        />

        {/* Getting Started */}
        <Card title="Getting Started" style={{ marginTop: '24px' }}>
          <Paragraph>
            Welcome to the APISportsGame CMS! This dashboard provides you with a comprehensive overview
            of your football data management system. Here's what you can do:
          </Paragraph>

          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>
            <div>
              <Title level={5}>
                <TrophyOutlined /> Football Data Management
              </Title>
              <Text>
                Manage leagues, teams, and fixtures. Sync data from external APIs and
                keep your football database up to date.
              </Text>
            </div>

            <div>
              <Title level={5}>
                <LinkOutlined /> Broadcast Links
              </Title>
              <Text>
                Add and manage broadcast links for fixtures. Control quality settings
                and ensure reliable streaming sources.
              </Text>
            </div>

            <div>
              <Title level={5}>
                <UserOutlined /> User System
              </Title>
              <Text>
                Manage system users, roles, and permissions. Control access to different
                parts of the CMS based on user roles.
              </Text>
            </div>

            <div>
              <Title level={5}>
                <BarChartOutlined /> System Monitoring
              </Title>
              <Text>
                Monitor API health, view system logs, and track performance metrics
                to ensure optimal system operation.
              </Text>
            </div>
          </div>
        </Card>
      </Container>
    </AppLayout>
  );
}
alt = "Window icon"
width = { 16}
height = { 16}
  />
  Examples
        </a >
  <a
    className="flex items-center gap-2 hover:underline hover:underline-offset-4"
    href="https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
    target="_blank"
    rel="noopener noreferrer"
  >
    <Image
      aria-hidden
      src="/globe.svg"
      alt="Globe icon"
      width={16}
      height={16}
    />
    Go to nextjs.org →
  </a>
      </footer >
    </div >
  );
}
