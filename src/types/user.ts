/**
 * User Types & Interfaces
 * SystemUser management types for APISportsGame CMS
 */

/**
 * System user roles
 */
export type SystemUserRole = 'admin' | 'editor' | 'moderator';

/**
 * User status
 */
export type UserStatus = 'active' | 'inactive' | 'suspended';

/**
 * System user interface
 */
export interface SystemUser {
  id: string;
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: SystemUserRole;
  status: UserStatus;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  avatar?: string;
  permissions?: string[];
}

/**
 * Create user request
 */
export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  role: SystemUserRole;
  status?: UserStatus;
}

/**
 * Update user request
 */
export interface UpdateUserRequest {
  email?: string;
  firstName?: string;
  lastName?: string;
  role?: SystemUserRole;
  status?: UserStatus;
  avatar?: string;
}

/**
 * Change password request
 */
export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * User list query parameters
 */
export interface UserListParams {
  page?: number;
  limit?: number;
  search?: string;
  role?: SystemUserRole;
  status?: UserStatus;
  sortBy?: 'username' | 'email' | 'role' | 'status' | 'createdAt' | 'lastLogin';
  sortOrder?: 'asc' | 'desc';
}

/**
 * User list response
 */
export interface UserListResponse {
  users: SystemUser[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * User statistics
 */
export interface UserStatistics {
  total: number;
  active: number;
  inactive: number;
  suspended: number;
  byRole: {
    admin: number;
    editor: number;
    moderator: number;
  };
  recentLogins: number;
  newThisMonth: number;
}

/**
 * User activity log
 */
export interface UserActivity {
  id: string;
  userId: string;
  action: string;
  description: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
}

/**
 * User session
 */
export interface UserSession {
  id: string;
  userId: string;
  deviceInfo: string;
  ipAddress: string;
  lastActivity: string;
  createdAt: string;
  isActive: boolean;
}

/**
 * Role permissions mapping
 */
export const ROLE_PERMISSIONS: Record<SystemUserRole, string[]> = {
  admin: [
    'users.create',
    'users.read',
    'users.update',
    'users.delete',
    'users.manage_roles',
    'football.create',
    'football.read',
    'football.update',
    'football.delete',
    'football.sync',
    'broadcast.create',
    'broadcast.read',
    'broadcast.update',
    'broadcast.delete',
    'system.settings',
    'system.logs',
    'system.health',
  ],
  editor: [
    'users.read',
    'football.create',
    'football.read',
    'football.update',
    'football.sync',
    'broadcast.create',
    'broadcast.read',
    'broadcast.update',
    'broadcast.delete',
  ],
  moderator: [
    'users.read',
    'football.read',
    'broadcast.read',
    'broadcast.update',
  ],
};

/**
 * Role display names
 */
export const ROLE_LABELS: Record<SystemUserRole, string> = {
  admin: 'Administrator',
  editor: 'Editor',
  moderator: 'Moderator',
};

/**
 * Status display names
 */
export const STATUS_LABELS: Record<UserStatus, string> = {
  active: 'Active',
  inactive: 'Inactive',
  suspended: 'Suspended',
};

/**
 * Role colors for UI
 */
export const ROLE_COLORS: Record<SystemUserRole, string> = {
  admin: '#ff4d4f',
  editor: '#1890ff',
  moderator: '#52c41a',
};

/**
 * Status colors for UI
 */
export const STATUS_COLORS: Record<UserStatus, string> = {
  active: '#52c41a',
  inactive: '#d9d9d9',
  suspended: '#ff4d4f',
};

/**
 * User form validation rules
 */
export const USER_VALIDATION = {
  username: {
    min: 3,
    max: 50,
    pattern: /^[a-zA-Z0-9_-]+$/,
    message: 'Username must be 3-50 characters and contain only letters, numbers, hyphens, and underscores',
  },
  email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: 'Please enter a valid email address',
  },
  password: {
    min: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    message: 'Password must be at least 8 characters with uppercase, lowercase, number, and special character',
  },
  firstName: {
    max: 50,
    message: 'First name must not exceed 50 characters',
  },
  lastName: {
    max: 50,
    message: 'Last name must not exceed 50 characters',
  },
};

/**
 * Default user list params
 */
export const DEFAULT_USER_PARAMS: UserListParams = {
  page: 1,
  limit: 20,
  sortBy: 'createdAt',
  sortOrder: 'desc',
};

/**
 * User helper functions
 */
export const userHelpers = {
  /**
   * Get user full name
   */
  getFullName: (user: SystemUser): string => {
    if (user.firstName && user.lastName) {
      return `${user.firstName} ${user.lastName}`;
    }
    if (user.firstName) {
      return user.firstName;
    }
    if (user.lastName) {
      return user.lastName;
    }
    return user.username;
  },

  /**
   * Get user display name
   */
  getDisplayName: (user: SystemUser): string => {
    const fullName = userHelpers.getFullName(user);
    return fullName !== user.username ? `${fullName} (${user.username})` : user.username;
  },

  /**
   * Check if user has permission
   */
  hasPermission: (user: SystemUser, permission: string): boolean => {
    const rolePermissions = ROLE_PERMISSIONS[user.role] || [];
    return rolePermissions.includes(permission);
  },

  /**
   * Check if user is active
   */
  isActive: (user: SystemUser): boolean => {
    return user.status === 'active';
  },

  /**
   * Get user avatar URL or initials
   */
  getAvatarDisplay: (user: SystemUser): { type: 'url' | 'initials'; value: string } => {
    if (user.avatar) {
      return { type: 'url', value: user.avatar };
    }
    
    const fullName = userHelpers.getFullName(user);
    const initials = fullName
      .split(' ')
      .map(name => name.charAt(0).toUpperCase())
      .slice(0, 2)
      .join('');
    
    return { type: 'initials', value: initials || user.username.charAt(0).toUpperCase() };
  },

  /**
   * Format last login time
   */
  formatLastLogin: (lastLogin?: string): string => {
    if (!lastLogin) return 'Never';
    
    const date = new Date(lastLogin);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    
    return `${Math.floor(diffDays / 365)} years ago`;
  },
};
