/**
 * User Form Components
 * Forms for creating and editing SystemUser accounts
 */

'use client';

import React, { useEffect } from 'react';
import { Form, Input, Select, Switch, Button, Space, Alert, Typography, Divider } from 'antd';
import { UserOutlined, MailOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import { 
  SystemUser, 
  CreateUserRequest, 
  UpdateUserRequest,
  SystemUserRole,
  UserStatus,
  ROLE_LABELS,
  STATUS_LABELS,
  USER_VALIDATION,
} from '@/types/user';
import { useThemeStyles } from '@/theme';

const { Text } = Typography;

/**
 * User form props
 */
export interface UserFormProps {
  user?: SystemUser;
  onSubmit: (data: CreateUserRequest | UpdateUserRequest) => void;
  loading?: boolean;
  mode: 'create' | 'edit';
  className?: string;
  style?: React.CSSProperties;
}

/**
 * User Form component
 */
export function UserForm({
  user,
  onSubmit,
  loading = false,
  mode,
  className,
  style,
}: UserFormProps) {
  const [form] = Form.useForm();
  const themeStyles = useThemeStyles();

  // Initialize form with user data for edit mode
  useEffect(() => {
    if (mode === 'edit' && user) {
      form.setFieldsValue({
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        status: user.status,
        isActive: user.status === 'active',
      });
    }
  }, [form, mode, user]);

  // Handle form submission
  const handleSubmit = (values: any) => {
    const formData = {
      ...values,
      status: values.isActive ? 'active' : 'inactive',
    };
    delete formData.isActive;
    delete formData.confirmPassword;
    
    onSubmit(formData);
  };

  // Handle form validation failure
  const handleSubmitFailed = (errorInfo: any) => {
    console.log('Form validation failed:', errorInfo);
  };

  return (
    <div className={className} style={style}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        onFinishFailed={handleSubmitFailed}
        autoComplete="off"
        requiredMark={false}
      >
        {/* Basic Information */}
        <div style={{ marginBottom: '24px' }}>
          <Text strong style={{ fontSize: '16px', color: themeStyles.getTextColor('primary') }}>
            Basic Information
          </Text>
          <Divider style={{ margin: '12px 0 24px 0' }} />

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            {/* Username */}
            <Form.Item
              name="username"
              label="Username"
              rules={[
                { required: true, message: 'Please enter username' },
                { min: USER_VALIDATION.username.min, message: USER_VALIDATION.username.message },
                { max: USER_VALIDATION.username.max, message: USER_VALIDATION.username.message },
                { pattern: USER_VALIDATION.username.pattern, message: USER_VALIDATION.username.message },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="Enter username"
                disabled={mode === 'edit'} // Username cannot be changed
              />
            </Form.Item>

            {/* Email */}
            <Form.Item
              name="email"
              label="Email Address"
              rules={[
                { required: true, message: 'Please enter email address' },
                { type: 'email', message: USER_VALIDATION.email.message },
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="Enter email address"
              />
            </Form.Item>

            {/* First Name */}
            <Form.Item
              name="firstName"
              label="First Name"
              rules={[
                { max: USER_VALIDATION.firstName.max, message: USER_VALIDATION.firstName.message },
              ]}
            >
              <Input placeholder="Enter first name" />
            </Form.Item>

            {/* Last Name */}
            <Form.Item
              name="lastName"
              label="Last Name"
              rules={[
                { max: USER_VALIDATION.lastName.max, message: USER_VALIDATION.lastName.message },
              ]}
            >
              <Input placeholder="Enter last name" />
            </Form.Item>
          </div>
        </div>

        {/* Password Section (Create mode only) */}
        {mode === 'create' && (
          <div style={{ marginBottom: '24px' }}>
            <Text strong style={{ fontSize: '16px', color: themeStyles.getTextColor('primary') }}>
              Password
            </Text>
            <Divider style={{ margin: '12px 0 24px 0' }} />

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
              {/* Password */}
              <Form.Item
                name="password"
                label="Password"
                rules={[
                  { required: true, message: 'Please enter password' },
                  { min: USER_VALIDATION.password.min, message: USER_VALIDATION.password.message },
                  { pattern: USER_VALIDATION.password.pattern, message: USER_VALIDATION.password.message },
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="Enter password"
                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                />
              </Form.Item>

              {/* Confirm Password */}
              <Form.Item
                name="confirmPassword"
                label="Confirm Password"
                dependencies={['password']}
                rules={[
                  { required: true, message: 'Please confirm password' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('Passwords do not match'));
                    },
                  }),
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="Confirm password"
                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                />
              </Form.Item>
            </div>

            <Alert
              message="Password Requirements"
              description="Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character."
              type="info"
              showIcon
              style={{ marginTop: '16px' }}
            />
          </div>
        )}

        {/* Role and Status */}
        <div style={{ marginBottom: '24px' }}>
          <Text strong style={{ fontSize: '16px', color: themeStyles.getTextColor('primary') }}>
            Role & Status
          </Text>
          <Divider style={{ margin: '12px 0 24px 0' }} />

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            {/* Role */}
            <Form.Item
              name="role"
              label="Role"
              rules={[
                { required: true, message: 'Please select a role' },
              ]}
            >
              <Select placeholder="Select role">
                <Select.Option value="admin">{ROLE_LABELS.admin}</Select.Option>
                <Select.Option value="editor">{ROLE_LABELS.editor}</Select.Option>
                <Select.Option value="moderator">{ROLE_LABELS.moderator}</Select.Option>
              </Select>
            </Form.Item>

            {/* Status */}
            <Form.Item
              name="isActive"
              label="Status"
              valuePropName="checked"
              initialValue={true}
            >
              <Switch
                checkedChildren="Active"
                unCheckedChildren="Inactive"
              />
            </Form.Item>
          </div>

          {/* Role Descriptions */}
          <div style={{ marginTop: '16px' }}>
            <Alert
              message="Role Permissions"
              description={
                <div>
                  <div><strong>Administrator:</strong> Full access to all features including user management, system settings, and logs.</div>
                  <div><strong>Editor:</strong> Can manage football data, broadcast links, and view users.</div>
                  <div><strong>Moderator:</strong> Read-only access to most features with limited broadcast link management.</div>
                </div>
              }
              type="info"
              showIcon
            />
          </div>
        </div>

        {/* Form Actions */}
        <Form.Item style={{ marginBottom: 0 }}>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              size="large"
            >
              {mode === 'create' ? 'Create User' : 'Update User'}
            </Button>
            
            <Button
              size="large"
              onClick={() => form.resetFields()}
            >
              Reset
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </div>
  );
}

/**
 * Quick user form for modal/drawer usage
 */
export interface QuickUserFormProps {
  onSubmit: (data: CreateUserRequest) => void;
  loading?: boolean;
  onCancel?: () => void;
}

export function QuickUserForm({ onSubmit, loading = false, onCancel }: QuickUserFormProps) {
  const [form] = Form.useForm();

  const handleSubmit = (values: any) => {
    const formData = {
      ...values,
      status: 'active' as UserStatus,
    };
    delete formData.confirmPassword;
    onSubmit(formData);
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      autoComplete="off"
      requiredMark={false}
    >
      {/* Username */}
      <Form.Item
        name="username"
        label="Username"
        rules={[
          { required: true, message: 'Please enter username' },
          { min: USER_VALIDATION.username.min, message: USER_VALIDATION.username.message },
          { max: USER_VALIDATION.username.max, message: USER_VALIDATION.username.message },
          { pattern: USER_VALIDATION.username.pattern, message: USER_VALIDATION.username.message },
        ]}
      >
        <Input prefix={<UserOutlined />} placeholder="Enter username" />
      </Form.Item>

      {/* Email */}
      <Form.Item
        name="email"
        label="Email Address"
        rules={[
          { required: true, message: 'Please enter email address' },
          { type: 'email', message: USER_VALIDATION.email.message },
        ]}
      >
        <Input prefix={<MailOutlined />} placeholder="Enter email address" />
      </Form.Item>

      {/* Password */}
      <Form.Item
        name="password"
        label="Password"
        rules={[
          { required: true, message: 'Please enter password' },
          { min: USER_VALIDATION.password.min, message: USER_VALIDATION.password.message },
        ]}
      >
        <Input.Password prefix={<LockOutlined />} placeholder="Enter password" />
      </Form.Item>

      {/* Role */}
      <Form.Item
        name="role"
        label="Role"
        rules={[{ required: true, message: 'Please select a role' }]}
      >
        <Select placeholder="Select role">
          <Select.Option value="admin">{ROLE_LABELS.admin}</Select.Option>
          <Select.Option value="editor">{ROLE_LABELS.editor}</Select.Option>
          <Select.Option value="moderator">{ROLE_LABELS.moderator}</Select.Option>
        </Select>
      </Form.Item>

      {/* Actions */}
      <Form.Item style={{ marginBottom: 0, marginTop: '24px' }}>
        <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
          {onCancel && (
            <Button onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="primary" htmlType="submit" loading={loading}>
            Create User
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );
}
