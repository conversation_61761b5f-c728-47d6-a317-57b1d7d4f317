/**
 * Main App Layout Component
 * Primary layout structure for the APISportsGame CMS
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Layout } from 'antd';
import { useThemeStyles } from '@/theme';
import { useAppProvider } from '@/stores';
import { AppHeader } from './app-header';
import { AppSidebar } from './app-sidebar';
import { AppFooter } from './app-footer';

const { Content } = Layout;

/**
 * App layout props
 */
export interface AppLayoutProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * Main App Layout component
 */
export function AppLayout({ children, className, style }: AppLayoutProps) {
  const themeStyles = useThemeStyles();
  const app = useAppProvider();
  
  // Layout state
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      
      // Auto-collapse sidebar on mobile
      if (mobile && !sidebarCollapsed) {
        setSidebarCollapsed(true);
      }
    };

    // Initial check
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, [sidebarCollapsed]);

  // Handle sidebar toggle
  const handleSidebarToggle = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // Handle mobile sidebar overlay
  const handleMobileOverlayClick = () => {
    if (isMobile && !sidebarCollapsed) {
      setSidebarCollapsed(true);
    }
  };

  const layoutStyle: React.CSSProperties = {
    minHeight: '100vh',
    backgroundColor: themeStyles.getBackgroundColor('layout'),
    ...style,
  };

  const contentStyle: React.CSSProperties = {
    marginLeft: isMobile ? 0 : (sidebarCollapsed ? '80px' : '250px'),
    transition: 'margin-left 0.2s ease',
    minHeight: 'calc(100vh - 64px)', // Header height
    backgroundColor: themeStyles.getBackgroundColor('layout'),
  };

  return (
    <Layout className={className} style={layoutStyle}>
      {/* Header */}
      <AppHeader
        sidebarCollapsed={sidebarCollapsed}
        onSidebarToggle={handleSidebarToggle}
        isMobile={isMobile}
      />

      {/* Sidebar */}
      <AppSidebar
        collapsed={sidebarCollapsed}
        isMobile={isMobile}
        onCollapse={setSidebarCollapsed}
      />

      {/* Mobile overlay */}
      {isMobile && !sidebarCollapsed && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 999,
          }}
          onClick={handleMobileOverlayClick}
        />
      )}

      {/* Main content */}
      <Layout style={contentStyle}>
        <Content
          style={{
            padding: '24px',
            backgroundColor: themeStyles.getBackgroundColor('layout'),
            overflow: 'auto',
          }}
        >
          {children}
        </Content>

        {/* Footer */}
        <AppFooter />
      </Layout>
    </Layout>
  );
}

/**
 * Layout provider for layout state management
 */
export interface LayoutContextType {
  sidebarCollapsed: boolean;
  setSidebarCollapsed: (collapsed: boolean) => void;
  isMobile: boolean;
  toggleSidebar: () => void;
}

const LayoutContext = React.createContext<LayoutContextType | undefined>(undefined);

export function LayoutProvider({ children }: { children: React.ReactNode }) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const value: LayoutContextType = {
    sidebarCollapsed,
    setSidebarCollapsed,
    isMobile,
    toggleSidebar,
  };

  return (
    <LayoutContext.Provider value={value}>
      {children}
    </LayoutContext.Provider>
  );
}

/**
 * Hook to use layout context
 */
export function useLayout() {
  const context = React.useContext(LayoutContext);
  if (context === undefined) {
    throw new Error('useLayout must be used within a LayoutProvider');
  }
  return context;
}

/**
 * Simple layout for pages that don't need sidebar
 */
export interface SimpleLayoutProps {
  children: React.ReactNode;
  showHeader?: boolean;
  showFooter?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export function SimpleLayout({
  children,
  showHeader = true,
  showFooter = true,
  className,
  style,
}: SimpleLayoutProps) {
  const themeStyles = useThemeStyles();

  const layoutStyle: React.CSSProperties = {
    minHeight: '100vh',
    backgroundColor: themeStyles.getBackgroundColor('layout'),
    display: 'flex',
    flexDirection: 'column',
    ...style,
  };

  return (
    <Layout className={className} style={layoutStyle}>
      {showHeader && (
        <AppHeader
          sidebarCollapsed={true}
          onSidebarToggle={() => {}}
          isMobile={false}
          showSidebarToggle={false}
        />
      )}

      <Content
        style={{
          flex: 1,
          padding: '24px',
          backgroundColor: themeStyles.getBackgroundColor('layout'),
        }}
      >
        {children}
      </Content>

      {showFooter && <AppFooter />}
    </Layout>
  );
}
