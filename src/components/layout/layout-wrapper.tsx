/**
 * Layout Wrapper Component
 * Determines which layout to use based on route and configuration
 */

'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import { AppLayout, SimpleLayout } from './app-layout';

/**
 * Layout wrapper props
 */
export interface LayoutWrapperProps {
  children: React.ReactNode;
}

/**
 * Routes that should use simple layout (no sidebar)
 */
const SIMPLE_LAYOUT_ROUTES = [
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password',
  '/unauthorized',
  '/404',
  '/500',
  '/maintenance',
];

/**
 * Routes that should use no layout wrapper (custom layout)
 */
const NO_LAYOUT_ROUTES = [
  '/api-hooks-demo',
  '/simple-theme-demo',
  '/simple-query-demo',
  '/store-demo',
];

/**
 * Layout Wrapper component
 */
export function LayoutWrapper({ children }: LayoutWrapperProps) {
  const pathname = usePathname();

  // Check if current route should use no layout
  const shouldUseNoLayout = NO_LAYOUT_ROUTES.some(route =>
    pathname === route || pathname.startsWith(route + '/')
  );

  if (shouldUseNoLayout) {
    return <>{children}</>;
  }

  // Check if current route should use simple layout
  const shouldUseSimpleLayout = SIMPLE_LAYOUT_ROUTES.some(route =>
    pathname === route || pathname.startsWith(route + '/')
  );

  if (shouldUseSimpleLayout) {
    return (
      <SimpleLayout>
        {children}
      </SimpleLayout>
    );
  }

  // Use main app layout for all other routes
  return (
    <AppLayout>
      {children}
    </AppLayout>
  );
}

/**
 * HOC to wrap pages with layout
 */
export function withLayout<P extends object>(
  Component: React.ComponentType<P>,
  layoutType?: 'app' | 'simple' | 'none'
) {
  const WrappedComponent = (props: P) => {
    switch (layoutType) {
      case 'simple':
        return (
          <SimpleLayout>
            <Component {...props} />
          </SimpleLayout>
        );
      case 'none':
        return <Component {...props} />;
      case 'app':
      default:
        return (
          <AppLayout>
            <Component {...props} />
          </AppLayout>
        );
    }
  };

  WrappedComponent.displayName = `withLayout(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

/**
 * Layout configuration hook
 */
export function useLayoutConfig() {
  const pathname = usePathname();

  const getLayoutType = (): 'app' | 'simple' | 'none' => {
    if (NO_LAYOUT_ROUTES.some(route => pathname === route || pathname.startsWith(route + '/'))) {
      return 'none';
    }

    if (SIMPLE_LAYOUT_ROUTES.some(route => pathname === route || pathname.startsWith(route + '/'))) {
      return 'simple';
    }

    return 'app';
  };

  return {
    layoutType: getLayoutType(),
    isAppLayout: getLayoutType() === 'app',
    isSimpleLayout: getLayoutType() === 'simple',
    isNoLayout: getLayoutType() === 'none',
  };
}
