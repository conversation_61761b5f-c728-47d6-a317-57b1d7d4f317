/**
 * App Footer Component
 * Footer for the APISportsGame CMS
 */

'use client';

import React from 'react';
import { Layout, Space, Typography, Divider } from 'antd';
import { 
  GithubOutlined, 
  TwitterOutlined, 
  LinkedinOutlined,
  HeartFilled,
  ApiOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import { useThemeStyles } from '@/theme';

const { Footer } = Layout;
const { Text, Link } = Typography;

/**
 * App footer props
 */
export interface AppFooterProps {
  className?: string;
  style?: React.CSSProperties;
  compact?: boolean;
}

/**
 * App Footer component
 */
export function AppFooter({ className, style, compact = false }: AppFooterProps) {
  const themeStyles = useThemeStyles();

  const footerStyle: React.CSSProperties = {
    backgroundColor: themeStyles.getBackgroundColor('container'),
    borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,
    padding: compact ? '12px 24px' : '24px',
    textAlign: 'center',
    ...style,
  };

  const currentYear = new Date().getFullYear();

  if (compact) {
    return (
      <Footer className={className} style={footerStyle}>
        <Text
          style={{
            fontSize: '12px',
            color: themeStyles.getTextColor('tertiary'),
          }}
        >
          © {currentYear} APISportsGame CMS. Built with{' '}
          <HeartFilled style={{ color: themeStyles.getColor('error') }} /> by Augment Code
        </Text>
      </Footer>
    );
  }

  return (
    <Footer className={className} style={footerStyle}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* Main footer content */}
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '32px',
            marginBottom: '24px',
            textAlign: 'left',
          }}
        >
          {/* About section */}
          <div>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                marginBottom: '12px',
              }}
            >
              <div
                style={{
                  width: '24px',
                  height: '24px',
                  backgroundColor: themeStyles.getColor('primary'),
                  borderRadius: '4px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: 'bold',
                }}
              >
                ⚽
              </div>
              <Text
                style={{
                  fontSize: '16px',
                  fontWeight: 'bold',
                  color: themeStyles.getTextColor('primary'),
                }}
              >
                APISportsGame
              </Text>
            </div>
            <Text
              style={{
                fontSize: '14px',
                color: themeStyles.getTextColor('secondary'),
                lineHeight: 1.6,
              }}
            >
              A comprehensive CMS for managing football data, broadcast links, and user systems.
              Built with modern technologies for optimal performance.
            </Text>
          </div>

          {/* Quick links */}
          <div>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: themeStyles.getTextColor('primary'),
                marginBottom: '12px',
                display: 'block',
              }}
            >
              Quick Links
            </Text>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              <Link
                href="/"
                style={{
                  fontSize: '13px',
                  color: themeStyles.getTextColor('secondary'),
                }}
              >
                Dashboard
              </Link>
              <Link
                href="/football/fixtures"
                style={{
                  fontSize: '13px',
                  color: themeStyles.getTextColor('secondary'),
                }}
              >
                Fixtures
              </Link>
              <Link
                href="/broadcast/links"
                style={{
                  fontSize: '13px',
                  color: themeStyles.getTextColor('secondary'),
                }}
              >
                Broadcast Links
              </Link>
              <Link
                href="/system/health"
                style={{
                  fontSize: '13px',
                  color: themeStyles.getTextColor('secondary'),
                }}
              >
                System Health
              </Link>
            </div>
          </div>

          {/* Resources */}
          <div>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: themeStyles.getTextColor('primary'),
                marginBottom: '12px',
                display: 'block',
              }}
            >
              Resources
            </Text>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              <Link
                href="/system/api-docs"
                style={{
                  fontSize: '13px',
                  color: themeStyles.getTextColor('secondary'),
                }}
              >
                <ApiOutlined style={{ marginRight: '4px' }} />
                API Documentation
              </Link>
              <Link
                href="/components-demo"
                style={{
                  fontSize: '13px',
                  color: themeStyles.getTextColor('secondary'),
                }}
              >
                Component Library
              </Link>
              <Link
                href="/theme-demo"
                style={{
                  fontSize: '13px',
                  color: themeStyles.getTextColor('secondary'),
                }}
              >
                Theme System
              </Link>
              <Link
                href="https://github.com/apisportsgame"
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  fontSize: '13px',
                  color: themeStyles.getTextColor('secondary'),
                }}
              >
                <GithubOutlined style={{ marginRight: '4px' }} />
                GitHub Repository
              </Link>
            </div>
          </div>

          {/* Contact */}
          <div>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: themeStyles.getTextColor('primary'),
                marginBottom: '12px',
                display: 'block',
              }}
            >
              Connect
            </Text>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              <Link
                href="https://github.com/apisportsgame"
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  fontSize: '13px',
                  color: themeStyles.getTextColor('secondary'),
                }}
              >
                <GithubOutlined style={{ marginRight: '4px' }} />
                GitHub
              </Link>
              <Link
                href="https://twitter.com/apisportsgame"
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  fontSize: '13px',
                  color: themeStyles.getTextColor('secondary'),
                }}
              >
                <TwitterOutlined style={{ marginRight: '4px' }} />
                Twitter
              </Link>
              <Link
                href="https://linkedin.com/company/apisportsgame"
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  fontSize: '13px',
                  color: themeStyles.getTextColor('secondary'),
                }}
              >
                <LinkedinOutlined style={{ marginRight: '4px' }} />
                LinkedIn
              </Link>
              <Link
                href="https://apisportsgame.com"
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  fontSize: '13px',
                  color: themeStyles.getTextColor('secondary'),
                }}
              >
                <GlobalOutlined style={{ marginRight: '4px' }} />
                Website
              </Link>
            </div>
          </div>
        </div>

        <Divider style={{ margin: '24px 0 16px 0' }} />

        {/* Bottom footer */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            flexWrap: 'wrap',
            gap: '16px',
          }}
        >
          <Text
            style={{
              fontSize: '13px',
              color: themeStyles.getTextColor('tertiary'),
            }}
          >
            © {currentYear} APISportsGame CMS. All rights reserved. Built with{' '}
            <HeartFilled style={{ color: themeStyles.getColor('error') }} /> by Augment Code
          </Text>

          <Space size="middle">
            <Link
              href="/privacy"
              style={{
                fontSize: '13px',
                color: themeStyles.getTextColor('tertiary'),
              }}
            >
              Privacy Policy
            </Link>
            <Link
              href="/terms"
              style={{
                fontSize: '13px',
                color: themeStyles.getTextColor('tertiary'),
              }}
            >
              Terms of Service
            </Link>
            <Text
              style={{
                fontSize: '13px',
                color: themeStyles.getTextColor('tertiary'),
              }}
            >
              v1.0.0
            </Text>
          </Space>
        </div>
      </div>
    </Footer>
  );
}

/**
 * Simple footer for minimal layouts
 */
export interface SimpleFooterProps {
  className?: string;
  style?: React.CSSProperties;
}

export function SimpleFooter({ className, style }: SimpleFooterProps) {
  const themeStyles = useThemeStyles();
  const currentYear = new Date().getFullYear();

  return (
    <div
      className={className}
      style={{
        padding: '16px 24px',
        textAlign: 'center',
        backgroundColor: themeStyles.getBackgroundColor('container'),
        borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,
        ...style,
      }}
    >
      <Text
        style={{
          fontSize: '12px',
          color: themeStyles.getTextColor('tertiary'),
        }}
      >
        © {currentYear} APISportsGame CMS. Built with{' '}
        <HeartFilled style={{ color: themeStyles.getColor('error') }} /> by Augment Code
      </Text>
    </div>
  );
}
