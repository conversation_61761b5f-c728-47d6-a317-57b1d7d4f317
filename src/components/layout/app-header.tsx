/**
 * App Header Component
 * Main header for the APISportsGame CMS
 */

'use client';

import React from 'react';
import { Layout, Space, Button, Dropdown, Avatar, Badge, Tooltip, Typography } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  SunOutlined,
  MoonOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import { useTheme, useThemeStyles } from '@/theme';
import { useAuth } from '@/hooks/api';

const { Header } = Layout;
const { Text } = Typography;

/**
 * App header props
 */
export interface AppHeaderProps {
  sidebarCollapsed: boolean;
  onSidebarToggle: () => void;
  isMobile: boolean;
  showSidebarToggle?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * App Header component
 */
export function AppHeader({
  sidebarCollapsed,
  onSidebarToggle,
  isMobile,
  showSidebarToggle = true,
  className,
  style,
}: AppHeaderProps) {
  const { theme, toggleTheme, isDark } = useTheme();
  const themeStyles = useThemeStyles();
  const auth = useAuth();

  // User menu items
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Profile',
      onClick: () => console.log('Profile clicked'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Settings',
      onClick: () => console.log('Settings clicked'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
      onClick: () => auth.logoutUser(),
      danger: true,
    },
  ];

  // Notification menu items
  const notificationItems = [
    {
      key: '1',
      label: (
        <div style={{ padding: '8px 0' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
            New fixture sync completed
          </div>
          <div style={{ fontSize: '12px', color: themeStyles.getTextColor('secondary') }}>
            2 minutes ago
          </div>
        </div>
      ),
    },
    {
      key: '2',
      label: (
        <div style={{ padding: '8px 0' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
            User John Doe registered
          </div>
          <div style={{ fontSize: '12px', color: themeStyles.getTextColor('secondary') }}>
            5 minutes ago
          </div>
        </div>
      ),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'view-all',
      label: (
        <div style={{ textAlign: 'center', padding: '8px 0' }}>
          <Button type="link" size="small">
            View All Notifications
          </Button>
        </div>
      ),
    },
  ];

  const headerStyle: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    height: '64px',
    padding: '0 24px',
    backgroundColor: themeStyles.getBackgroundColor('container'),
    borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    ...style,
  };

  return (
    <Header className={className} style={headerStyle}>
      {/* Left section */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
        {/* Sidebar toggle */}
        {showSidebarToggle && (
          <Button
            type="text"
            icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={onSidebarToggle}
            style={{
              fontSize: '16px',
              width: '40px',
              height: '40px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          />
        )}

        {/* Logo and title */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <div
            style={{
              width: '32px',
              height: '32px',
              backgroundColor: themeStyles.getColor('primary'),
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold',
              fontSize: '16px',
            }}
          >
            ⚽
          </div>
          {(!isMobile || sidebarCollapsed) && (
            <div>
              <Text
                style={{
                  fontSize: '18px',
                  fontWeight: 'bold',
                  color: themeStyles.getTextColor('primary'),
                }}
              >
                APISportsGame
              </Text>
              <div
                style={{
                  fontSize: '12px',
                  color: themeStyles.getTextColor('secondary'),
                  lineHeight: 1,
                }}
              >
                CMS Dashboard
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Right section */}
      <Space size="middle">
        {/* Theme toggle */}
        <Tooltip title={`Switch to ${isDark ? 'light' : 'dark'} mode`}>
          <Button
            type="text"
            icon={isDark ? <SunOutlined /> : <MoonOutlined />}
            onClick={toggleTheme}
            style={{
              fontSize: '16px',
              width: '40px',
              height: '40px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          />
        </Tooltip>

        {/* Language selector */}
        <Tooltip title="Language">
          <Button
            type="text"
            icon={<GlobalOutlined />}
            style={{
              fontSize: '16px',
              width: '40px',
              height: '40px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          />
        </Tooltip>

        {/* Notifications */}
        <Dropdown
          menu={{ items: notificationItems }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Badge count={2} size="small">
            <Button
              type="text"
              icon={<BellOutlined />}
              style={{
                fontSize: '16px',
                width: '40px',
                height: '40px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            />
          </Badge>
        </Dropdown>

        {/* User menu */}
        <Dropdown
          menu={{ items: userMenuItems }}
          trigger={['click']}
          placement="bottomRight"
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              cursor: 'pointer',
              padding: '4px 8px',
              borderRadius: '6px',
              transition: 'background-color 0.2s ease',
            }}
          >
            <Avatar
              size="small"
              icon={<UserOutlined />}
              style={{
                backgroundColor: themeStyles.getColor('primary'),
              }}
            />
            {!isMobile && (
              <div>
                <div
                  style={{
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: themeStyles.getTextColor('primary'),
                    lineHeight: 1.2,
                  }}
                >
                  {auth.user?.username || 'Admin'}
                </div>
                <div
                  style={{
                    fontSize: '12px',
                    color: themeStyles.getTextColor('secondary'),
                    lineHeight: 1,
                  }}
                >
                  {auth.user?.role || 'Administrator'}
                </div>
              </div>
            )}
          </div>
        </Dropdown>
      </Space>
    </Header>
  );
}

/**
 * Header breadcrumb component
 */
export interface HeaderBreadcrumbProps {
  items: Array<{
    title: string;
    href?: string;
  }>;
  className?: string;
  style?: React.CSSProperties;
}

export function HeaderBreadcrumb({ items, className, style }: HeaderBreadcrumbProps) {
  const themeStyles = useThemeStyles();

  return (
    <div
      className={className}
      style={{
        padding: '8px 24px',
        backgroundColor: themeStyles.getBackgroundColor('elevated'),
        borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,
        ...style,
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        {items.map((item, index) => (
          <React.Fragment key={index}>
            {index > 0 && (
              <span style={{ color: themeStyles.getTextColor('tertiary') }}>
                /
              </span>
            )}
            {item.href ? (
              <a
                href={item.href}
                style={{
                  color: themeStyles.getColor('primary'),
                  textDecoration: 'none',
                  fontSize: '14px',
                }}
              >
                {item.title}
              </a>
            ) : (
              <span
                style={{
                  color: themeStyles.getTextColor('primary'),
                  fontSize: '14px',
                  fontWeight: index === items.length - 1 ? 'bold' : 'normal',
                }}
              >
                {item.title}
              </span>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}
