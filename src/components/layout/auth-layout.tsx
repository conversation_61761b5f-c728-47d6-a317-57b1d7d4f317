/**
 * Authentication Layout Component
 * Specialized layout for authentication pages
 */

'use client';

import React from 'react';
import { Layout, Card, Typography, Space, Divider } from 'antd';
import { useThemeStyles } from '@/theme';
import { SimpleFooter } from './app-footer';

const { Content } = Layout;
const { Title, Text, Link } = Typography;

/**
 * Auth layout props
 */
export interface AuthLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  showFooter?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * Authentication Layout component
 */
export function AuthLayout({
  children,
  title = 'APISportsGame CMS',
  subtitle = 'System Administration Portal',
  showFooter = true,
  className,
  style,
}: AuthLayoutProps) {
  const themeStyles = useThemeStyles();

  const layoutStyle: React.CSSProperties = {
    minHeight: '100vh',
    backgroundColor: themeStyles.getBackgroundColor('layout'),
    display: 'flex',
    flexDirection: 'column',
    ...style,
  };

  const contentStyle: React.CSSProperties = {
    flex: 1,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '24px',
    backgroundImage: `linear-gradient(135deg, ${themeStyles.getColor('primary')}10 0%, ${themeStyles.getColor('primary')}05 100%)`,
  };

  return (
    <Layout className={className} style={layoutStyle}>
      <Content style={contentStyle}>
        <div
          style={{
            width: '100%',
            maxWidth: '400px',
            margin: '0 auto',
          }}
        >
          {/* Header Section */}
          <div
            style={{
              textAlign: 'center',
              marginBottom: '32px',
            }}
          >
            {/* Logo */}
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                marginBottom: '16px',
              }}
            >
              <div
                style={{
                  width: '64px',
                  height: '64px',
                  backgroundColor: themeStyles.getColor('primary'),
                  borderRadius: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '32px',
                  fontWeight: 'bold',
                  boxShadow: `0 8px 24px ${themeStyles.getColor('primary')}30`,
                }}
              >
                ⚽
              </div>
            </div>

            {/* Title */}
            <Title
              level={2}
              style={{
                color: themeStyles.getTextColor('primary'),
                marginBottom: '8px',
                fontSize: '28px',
                fontWeight: 'bold',
              }}
            >
              {title}
            </Title>

            {/* Subtitle */}
            <Text
              style={{
                color: themeStyles.getTextColor('secondary'),
                fontSize: '16px',
              }}
            >
              {subtitle}
            </Text>
          </div>

          {/* Auth Card */}
          <Card
            style={{
              backgroundColor: themeStyles.getBackgroundColor('container'),
              border: `1px solid ${themeStyles.getBorderColor('primary')}`,
              borderRadius: '12px',
              boxShadow: `0 8px 32px ${themeStyles.getColor('primary')}08`,
            }}
            bodyStyle={{
              padding: '32px',
            }}
          >
            {children}
          </Card>

          {/* Additional Links */}
          <div
            style={{
              textAlign: 'center',
              marginTop: '24px',
            }}
          >
            <Space split={<Divider type="vertical" />}>
              <Link
                href="/help"
                style={{
                  color: themeStyles.getTextColor('secondary'),
                  fontSize: '14px',
                }}
              >
                Help & Support
              </Link>
              <Link
                href="/privacy"
                style={{
                  color: themeStyles.getTextColor('secondary'),
                  fontSize: '14px',
                }}
              >
                Privacy Policy
              </Link>
              <Link
                href="/terms"
                style={{
                  color: themeStyles.getTextColor('secondary'),
                  fontSize: '14px',
                }}
              >
                Terms of Service
              </Link>
            </Space>
          </div>
        </div>
      </Content>

      {/* Footer */}
      {showFooter && (
        <SimpleFooter
          style={{
            backgroundColor: themeStyles.getBackgroundColor('container'),
            borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,
          }}
        />
      )}
    </Layout>
  );
}

/**
 * Auth card wrapper for consistent styling
 */
export interface AuthCardProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  className?: string;
  style?: React.CSSProperties;
}

export function AuthCard({
  children,
  title,
  description,
  className,
  style,
}: AuthCardProps) {
  const themeStyles = useThemeStyles();

  return (
    <div className={className} style={style}>
      {(title || description) && (
        <div style={{ marginBottom: '24px', textAlign: 'center' }}>
          {title && (
            <Title
              level={3}
              style={{
                color: themeStyles.getTextColor('primary'),
                marginBottom: description ? '8px' : '0',
                fontSize: '24px',
                fontWeight: 'bold',
              }}
            >
              {title}
            </Title>
          )}
          {description && (
            <Text
              style={{
                color: themeStyles.getTextColor('secondary'),
                fontSize: '14px',
                lineHeight: 1.5,
              }}
            >
              {description}
            </Text>
          )}
        </div>
      )}
      {children}
    </div>
  );
}

/**
 * Auth form wrapper with consistent spacing
 */
export interface AuthFormProps {
  children: React.ReactNode;
  onSubmit?: (e: React.FormEvent) => void;
  className?: string;
  style?: React.CSSProperties;
}

export function AuthForm({
  children,
  onSubmit,
  className,
  style,
}: AuthFormProps) {
  return (
    <form
      className={className}
      style={{
        width: '100%',
        ...style,
      }}
      onSubmit={onSubmit}
    >
      <Space
        direction="vertical"
        size="large"
        style={{ width: '100%' }}
      >
        {children}
      </Space>
    </form>
  );
}

/**
 * Auth divider with text
 */
export interface AuthDividerProps {
  text?: string;
  className?: string;
  style?: React.CSSProperties;
}

export function AuthDivider({
  text = 'OR',
  className,
  style,
}: AuthDividerProps) {
  const themeStyles = useThemeStyles();

  return (
    <div
      className={className}
      style={{
        position: 'relative',
        textAlign: 'center',
        margin: '24px 0',
        ...style,
      }}
    >
      <Divider
        style={{
          borderColor: themeStyles.getBorderColor('primary'),
        }}
      />
      <span
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          backgroundColor: themeStyles.getBackgroundColor('container'),
          padding: '0 16px',
          color: themeStyles.getTextColor('tertiary'),
          fontSize: '12px',
          fontWeight: 'bold',
        }}
      >
        {text}
      </span>
    </div>
  );
}
