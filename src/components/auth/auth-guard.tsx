/**
 * Auth Guard Component
 * Protects routes and handles authentication redirects
 */

'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Spin, Alert, Button } from 'antd';
import { LoadingOutlined, LockOutlined } from '@ant-design/icons';
import { useAuth } from '@/hooks/api';
import { useThemeStyles } from '@/theme';

/**
 * Auth guard props
 */
export interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requiredRoles?: string[];
  fallback?: React.ReactNode;
  redirectTo?: string;
}

/**
 * Auth Guard component
 */
export function AuthGuard({
  children,
  requireAuth = true,
  requiredRoles = [],
  fallback,
  redirectTo,
}: AuthGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const auth = useAuth();
  const themeStyles = useThemeStyles();
  
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    // Skip auth check in development mode if authentication is disabled
    if (process.env.NODE_ENV === 'development' && !requireAuth) {
      setIsChecking(false);
      return;
    }

    // Check authentication status
    const checkAuth = async () => {
      try {
        // Wait for auth state to be determined
        if (auth.isLoading) {
          return;
        }

        setIsChecking(false);

        // If authentication is required but user is not authenticated
        if (requireAuth && !auth.isAuthenticated) {
          const loginUrl = `/login?redirect=${encodeURIComponent(pathname)}`;
          router.push(redirectTo || loginUrl);
          return;
        }

        // If user is authenticated but doesn't have required roles
        if (auth.isAuthenticated && requiredRoles.length > 0) {
          const userRole = auth.user?.role;
          if (!userRole || !requiredRoles.includes(userRole)) {
            router.push('/unauthorized');
            return;
          }
        }
      } catch (error) {
        console.error('Auth guard error:', error);
        setIsChecking(false);
      }
    };

    checkAuth();
  }, [auth.isAuthenticated, auth.isLoading, auth.user, requireAuth, requiredRoles, router, pathname, redirectTo]);

  // Show loading state while checking authentication
  if (isChecking || auth.isLoading) {
    return fallback || <AuthLoadingFallback />;
  }

  // In development mode, allow access without authentication
  if (process.env.NODE_ENV === 'development' && !requireAuth) {
    return <>{children}</>;
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !auth.isAuthenticated) {
    return <AuthRequiredFallback />;
  }

  // If user is authenticated but doesn't have required roles
  if (auth.isAuthenticated && requiredRoles.length > 0) {
    const userRole = auth.user?.role;
    if (!userRole || !requiredRoles.includes(userRole)) {
      return <InsufficientPermissionsFallback requiredRoles={requiredRoles} userRole={userRole} />;
    }
  }

  // All checks passed, render children
  return <>{children}</>;
}

/**
 * Loading fallback component
 */
function AuthLoadingFallback() {
  const themeStyles = useThemeStyles();

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        backgroundColor: themeStyles.getBackgroundColor('layout'),
        gap: '16px',
      }}
    >
      <Spin
        indicator={
          <LoadingOutlined
            style={{
              fontSize: '48px',
              color: themeStyles.getColor('primary'),
            }}
          />
        }
      />
      <div
        style={{
          color: themeStyles.getTextColor('secondary'),
          fontSize: '16px',
        }}
      >
        Checking authentication...
      </div>
    </div>
  );
}

/**
 * Authentication required fallback component
 */
function AuthRequiredFallback() {
  const router = useRouter();
  const pathname = usePathname();
  const themeStyles = useThemeStyles();

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        backgroundColor: themeStyles.getBackgroundColor('layout'),
        padding: '24px',
      }}
    >
      <div
        style={{
          maxWidth: '400px',
          textAlign: 'center',
        }}
      >
        <LockOutlined
          style={{
            fontSize: '64px',
            color: themeStyles.getColor('warning'),
            marginBottom: '24px',
          }}
        />
        
        <Alert
          message="Authentication Required"
          description="You need to sign in to access this page. Please log in with your administrator credentials."
          type="warning"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <Button
          type="primary"
          size="large"
          onClick={() => {
            const loginUrl = `/login?redirect=${encodeURIComponent(pathname)}`;
            router.push(loginUrl);
          }}
          style={{
            borderRadius: '8px',
            height: '48px',
            fontSize: '16px',
            fontWeight: 'bold',
          }}
        >
          Go to Login
        </Button>
      </div>
    </div>
  );
}

/**
 * Insufficient permissions fallback component
 */
interface InsufficientPermissionsFallbackProps {
  requiredRoles: string[];
  userRole?: string;
}

function InsufficientPermissionsFallback({
  requiredRoles,
  userRole,
}: InsufficientPermissionsFallbackProps) {
  const router = useRouter();
  const themeStyles = useThemeStyles();

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        backgroundColor: themeStyles.getBackgroundColor('layout'),
        padding: '24px',
      }}
    >
      <div
        style={{
          maxWidth: '400px',
          textAlign: 'center',
        }}
      >
        <LockOutlined
          style={{
            fontSize: '64px',
            color: themeStyles.getColor('error'),
            marginBottom: '24px',
          }}
        />
        
        <Alert
          message="Insufficient Permissions"
          description={
            <div>
              <p>You don't have permission to access this page.</p>
              <p><strong>Required roles:</strong> {requiredRoles.join(', ')}</p>
              {userRole && <p><strong>Your role:</strong> {userRole}</p>}
            </div>
          }
          type="error"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <Button
          type="primary"
          size="large"
          onClick={() => router.push('/')}
          style={{
            borderRadius: '8px',
            height: '48px',
            fontSize: '16px',
            fontWeight: 'bold',
          }}
        >
          Go to Dashboard
        </Button>
      </div>
    </div>
  );
}

/**
 * HOC to wrap components with auth guard
 */
export function withAuthGuard<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<AuthGuardProps, 'children'> = {}
) {
  const WrappedComponent = (props: P) => {
    return (
      <AuthGuard {...options}>
        <Component {...props} />
      </AuthGuard>
    );
  };

  WrappedComponent.displayName = `withAuthGuard(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Hook to check if user has required permissions
 */
export function usePermissions(requiredRoles: string[] = []) {
  const auth = useAuth();

  const hasPermission = () => {
    if (!auth.isAuthenticated || !auth.user) {
      return false;
    }

    if (requiredRoles.length === 0) {
      return true;
    }

    const userRole = auth.user.role;
    return userRole && requiredRoles.includes(userRole);
  };

  return {
    hasPermission: hasPermission(),
    userRole: auth.user?.role,
    isAuthenticated: auth.isAuthenticated,
  };
}
