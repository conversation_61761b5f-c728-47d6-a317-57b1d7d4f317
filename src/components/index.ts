/**
 * Component Library Index
 * Central export for all reusable components
 */

// Common UI Components
export * from './ui';

// Layout Components
export * from './layout';

// Form Components
export * from './forms';

// Data Display Components
export * from './data-display';

// Feedback Components
export * from './feedback';

// Authentication components
export * from './auth';

// Future component categories:
// export * from './navigation';
// export * from './charts';
// export * from './media';

/**
 * Component library metadata
 */
export const COMPONENTS_VERSION = '1.0.0';
export const COMPONENTS_NAME = 'APISportsGame Component Library';

/**
 * Setup function for component library
 */
export function setupComponents() {
  console.log(`${COMPONENTS_NAME} v${COMPONENTS_VERSION} initialized`);
}
