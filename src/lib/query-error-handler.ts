/**
 * Query Error Handler
 * Centralized error handling for TanStack Query
 */

import { QueryClient } from '@tanstack/react-query';

/**
 * API Error interface
 */
export interface ApiError {
  status: number;
  statusText: string;
  message: string;
  details?: any;
  timestamp: string;
}

/**
 * Error types for different scenarios
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  VALIDATION = 'VALIDATION',
  SERVER = 'SERVER',
  UNKNOWN = 'UNKNOWN',
}

/**
 * Determine error type based on status code
 */
export function getErrorType(status: number): ErrorType {
  if (status === 401) return ErrorType.AUTHENTICATION;
  if (status === 403) return ErrorType.AUTHORIZATION;
  if (status >= 400 && status < 500) return ErrorType.VALIDATION;
  if (status >= 500) return ErrorType.SERVER;
  if (status === 0) return ErrorType.NETWORK;
  return ErrorType.UNKNOWN;
}

/**
 * Create standardized API error
 */
export function createApiError(
  status: number,
  statusText: string,
  message: string,
  details?: any
): ApiError {
  return {
    status,
    statusText,
    message,
    details,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Parse error response from API
 */
export async function parseErrorResponse(response: Response): Promise<ApiError> {
  let message = response.statusText || 'An error occurred';
  let details = null;

  try {
    const errorData = await response.json();
    message = errorData.message || errorData.error || message;
    details = errorData.details || errorData;
  } catch {
    // If response is not JSON, use status text
  }

  return createApiError(response.status, response.statusText, message, details);
}

/**
 * Global error handler for queries
 */
export function createGlobalErrorHandler() {
  return (error: unknown) => {
    console.error('[Query Error]', error);

    // Handle different types of errors
    if (error instanceof Error) {
      // Network errors, parsing errors, etc.
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });
    }

    // Handle API errors
    if (isApiError(error)) {
      handleApiError(error);
    }
  };
}

/**
 * Check if error is an API error
 */
export function isApiError(error: unknown): error is ApiError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'status' in error &&
    'message' in error
  );
}

/**
 * Handle specific API error types
 */
export function handleApiError(error: ApiError) {
  const errorType = getErrorType(error.status);

  switch (errorType) {
    case ErrorType.AUTHENTICATION:
      handleAuthenticationError(error);
      break;
    case ErrorType.AUTHORIZATION:
      handleAuthorizationError(error);
      break;
    case ErrorType.VALIDATION:
      handleValidationError(error);
      break;
    case ErrorType.SERVER:
      handleServerError(error);
      break;
    case ErrorType.NETWORK:
      handleNetworkError(error);
      break;
    default:
      handleUnknownError(error);
  }
}

/**
 * Handle authentication errors (401)
 */
function handleAuthenticationError(error: ApiError) {
  console.warn('[Auth Error]', error.message);
  
  // In development mode, authentication is disabled
  if (process.env.NODE_ENV === 'development') {
    console.log('[Dev Mode] Authentication error ignored');
    return;
  }
  
  // In production, redirect to login or refresh token
  // This will be implemented when auth system is ready
}

/**
 * Handle authorization errors (403)
 */
function handleAuthorizationError(error: ApiError) {
  console.warn('[Authorization Error]', error.message);
  
  // Show user-friendly message about insufficient permissions
  // This will be integrated with notification system
}

/**
 * Handle validation errors (400-499)
 */
function handleValidationError(error: ApiError) {
  console.warn('[Validation Error]', error.message);
  
  // These are usually handled by individual components
  // Global handler just logs for debugging
}

/**
 * Handle server errors (500+)
 */
function handleServerError(error: ApiError) {
  console.error('[Server Error]', error.message);
  
  // Show generic error message to user
  // Log detailed error for debugging
}

/**
 * Handle network errors
 */
function handleNetworkError(error: ApiError) {
  console.error('[Network Error]', error.message);
  
  // Show network connectivity message
  // Suggest retry or check connection
}

/**
 * Handle unknown errors
 */
function handleUnknownError(error: ApiError) {
  console.error('[Unknown Error]', error);
  
  // Show generic error message
  // Log for investigation
}

/**
 * Error boundary for query errors
 */
export function setupQueryErrorHandling(queryClient: QueryClient) {
  // Set up global error handler
  queryClient.setDefaultOptions({
    queries: {
      ...queryClient.getDefaultOptions().queries,
      throwOnError: false, // Handle errors gracefully
    },
    mutations: {
      ...queryClient.getDefaultOptions().mutations,
      throwOnError: false, // Handle errors gracefully
    },
  });

  // Set up global error handler
  queryClient.setMutationDefaults(['mutation'], {
    onError: createGlobalErrorHandler(),
  });
}

/**
 * Utility functions for error handling
 */
export const errorUtils = {
  /**
   * Check if error should trigger retry
   */
  shouldRetry: (error: unknown): boolean => {
    if (isApiError(error)) {
      const errorType = getErrorType(error.status);
      // Don't retry client errors (4xx)
      return errorType !== ErrorType.VALIDATION && 
             errorType !== ErrorType.AUTHENTICATION && 
             errorType !== ErrorType.AUTHORIZATION;
    }
    return true; // Retry network and unknown errors
  },

  /**
   * Get user-friendly error message
   */
  getUserMessage: (error: unknown): string => {
    if (isApiError(error)) {
      const errorType = getErrorType(error.status);
      
      switch (errorType) {
        case ErrorType.AUTHENTICATION:
          return 'Please log in to continue';
        case ErrorType.AUTHORIZATION:
          return 'You do not have permission to perform this action';
        case ErrorType.VALIDATION:
          return error.message || 'Please check your input and try again';
        case ErrorType.SERVER:
          return 'Server error occurred. Please try again later';
        case ErrorType.NETWORK:
          return 'Network error. Please check your connection';
        default:
          return 'An unexpected error occurred';
      }
    }
    
    return 'An unexpected error occurred';
  },

  /**
   * Check if error is retryable
   */
  isRetryable: (error: unknown): boolean => {
    if (isApiError(error)) {
      return error.status >= 500 || error.status === 0;
    }
    return true;
  },
} as const;
