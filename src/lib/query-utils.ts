/**
 * Query Utilities and Helpers
 * Common utilities for working with TanStack Query
 */

import { QueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { QUERY_CONFIG } from './query-client';
import { ApiError, isApiError, errorUtils } from './query-error-handler';

/**
 * Base API response interface
 */
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
  timestamp: string;
}

/**
 * Paginated response interface
 */
export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * Query options builder for common patterns
 */
export const queryOptionsBuilder = {
  /**
   * Build options for real-time data (short cache)
   */
  realTime: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({
    staleTime: QUERY_CONFIG.STALE_TIME.SHORT,
    gcTime: QUERY_CONFIG.STALE_TIME.MEDIUM,
    refetchInterval: QUERY_CONFIG.REFETCH_INTERVAL.FAST,
    ...options,
  }),

  /**
   * Build options for static data (long cache)
   */
  static: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({
    staleTime: QUERY_CONFIG.STALE_TIME.VERY_LONG,
    gcTime: QUERY_CONFIG.STALE_TIME.VERY_LONG,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    ...options,
  }),

  /**
   * Build options for user-specific data
   */
  userSpecific: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({
    staleTime: QUERY_CONFIG.STALE_TIME.MEDIUM,
    gcTime: QUERY_CONFIG.STALE_TIME.LONG,
    refetchOnWindowFocus: true,
    ...options,
  }),

  /**
   * Build options for background sync data
   */
  backgroundSync: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({
    staleTime: QUERY_CONFIG.STALE_TIME.LONG,
    gcTime: QUERY_CONFIG.STALE_TIME.VERY_LONG,
    refetchInterval: QUERY_CONFIG.REFETCH_INTERVAL.SLOW,
    refetchIntervalInBackground: true,
    ...options,
  }),
};

/**
 * Mutation options builder for common patterns
 */
export const mutationOptionsBuilder = {
  /**
   * Build options for optimistic updates
   */
  optimistic: <T, V>(options?: Partial<UseMutationOptions<T, ApiError, V>>): Partial<UseMutationOptions<T, ApiError, V>> => ({
    retry: QUERY_CONFIG.RETRY.ONCE,
    ...options,
  }),

  /**
   * Build options for critical operations
   */
  critical: <T, V>(options?: Partial<UseMutationOptions<T, ApiError, V>>): Partial<UseMutationOptions<T, ApiError, V>> => ({
    retry: QUERY_CONFIG.RETRY.DEFAULT,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options,
  }),

  /**
   * Build options for background operations
   */
  background: <T, V>(options?: Partial<UseMutationOptions<T, ApiError, V>>): Partial<UseMutationOptions<T, ApiError, V>> => ({
    retry: QUERY_CONFIG.RETRY.TWICE,
    ...options,
  }),
};

/**
 * Cache management utilities
 */
export const cacheUtils = {
  /**
   * Invalidate queries by pattern
   */
  invalidateByPattern: async (queryClient: QueryClient, pattern: string[]) => {
    await queryClient.invalidateQueries({ queryKey: pattern });
  },

  /**
   * Remove queries by pattern
   */
  removeByPattern: (queryClient: QueryClient, pattern: string[]) => {
    queryClient.removeQueries({ queryKey: pattern });
  },

  /**
   * Update query data
   */
  updateQueryData: <T>(
    queryClient: QueryClient,
    queryKey: string[],
    updater: (oldData: T | undefined) => T
  ) => {
    queryClient.setQueryData(queryKey, updater);
  },

  /**
   * Optimistically update list data
   */
  optimisticListUpdate: <T extends { id: string | number }>(
    queryClient: QueryClient,
    queryKey: string[],
    item: T,
    operation: 'add' | 'update' | 'remove'
  ) => {
    queryClient.setQueryData<T[]>(queryKey, (oldData) => {
      if (!oldData) return operation === 'add' ? [item] : [];

      switch (operation) {
        case 'add':
          return [...oldData, item];
        case 'update':
          return oldData.map((existing) =>
            existing.id === item.id ? { ...existing, ...item } : existing
          );
        case 'remove':
          return oldData.filter((existing) => existing.id !== item.id);
        default:
          return oldData;
      }
    });
  },

  /**
   * Optimistically update paginated data
   */
  optimisticPaginatedUpdate: <T extends { id: string | number }>(
    queryClient: QueryClient,
    queryKey: string[],
    item: T,
    operation: 'add' | 'update' | 'remove'
  ) => {
    queryClient.setQueryData<PaginatedResponse<T>>(queryKey, (oldData) => {
      if (!oldData) return oldData;

      const updatedData = cacheUtils.optimisticListUpdate(
        queryClient,
        ['temp'],
        item,
        operation
      );

      return {
        ...oldData,
        data: updatedData || oldData.data,
      };
    });
  },
};

/**
 * Query state utilities
 */
export const queryStateUtils = {
  /**
   * Check if any queries are loading
   */
  isAnyLoading: (queryClient: QueryClient, queryKeys: string[][]): boolean => {
    return queryKeys.some((key) => {
      const query = queryClient.getQueryState(key);
      return query?.fetchStatus === 'fetching';
    });
  },

  /**
   * Check if any queries have errors
   */
  hasAnyErrors: (queryClient: QueryClient, queryKeys: string[][]): boolean => {
    return queryKeys.some((key) => {
      const query = queryClient.getQueryState(key);
      return query?.status === 'error';
    });
  },

  /**
   * Get all errors from queries
   */
  getAllErrors: (queryClient: QueryClient, queryKeys: string[][]): ApiError[] => {
    return queryKeys
      .map((key) => {
        const query = queryClient.getQueryState(key);
        return query?.error;
      })
      .filter((error): error is ApiError => isApiError(error));
  },

  /**
   * Check if data is stale
   */
  isStale: (queryClient: QueryClient, queryKey: string[]): boolean => {
    const query = queryClient.getQueryState(queryKey);
    return query ? query.isStale : true;
  },
};

/**
 * Development utilities
 */
export const devUtils = {
  /**
   * Log query cache state
   */
  logCacheState: (queryClient: QueryClient) => {
    if (process.env.NODE_ENV === 'development') {
      const cache = queryClient.getQueryCache();
      console.log('[Query Cache]', {
        queries: cache.getAll().length,
        state: cache.getAll().map((query) => ({
          key: query.queryKey,
          status: query.state.status,
          dataUpdatedAt: query.state.dataUpdatedAt,
          error: query.state.error,
        })),
      });
    }
  },

  /**
   * Clear all cache (development only)
   */
  clearAllCache: (queryClient: QueryClient) => {
    if (process.env.NODE_ENV === 'development') {
      queryClient.clear();
      console.log('[Dev] Query cache cleared');
    }
  },

  /**
   * Force refetch all queries (development only)
   */
  refetchAll: async (queryClient: QueryClient) => {
    if (process.env.NODE_ENV === 'development') {
      await queryClient.refetchQueries();
      console.log('[Dev] All queries refetched');
    }
  },
};

/**
 * Error handling utilities
 */
export const queryErrorUtils = {
  /**
   * Handle query error with user feedback
   */
  handleQueryError: (error: unknown, context?: string) => {
    const message = errorUtils.getUserMessage(error);
    console.error(`[Query Error${context ? ` - ${context}` : ''}]`, error);
    
    // This will be integrated with notification system
    // For now, just log the user-friendly message
    console.log('[User Message]', message);
    
    return message;
  },

  /**
   * Handle mutation error with user feedback
   */
  handleMutationError: (error: unknown, context?: string) => {
    const message = errorUtils.getUserMessage(error);
    console.error(`[Mutation Error${context ? ` - ${context}` : ''}]`, error);
    
    // This will be integrated with notification system
    // For now, just log the user-friendly message
    console.log('[User Message]', message);
    
    return message;
  },
};

/**
 * Type guards for API responses
 */
export const typeGuards = {
  /**
   * Check if response is a valid API response
   */
  isApiResponse: <T>(data: unknown): data is ApiResponse<T> => {
    return (
      typeof data === 'object' &&
      data !== null &&
      'data' in data &&
      'success' in data &&
      'timestamp' in data
    );
  },

  /**
   * Check if response is a paginated response
   */
  isPaginatedResponse: <T>(data: unknown): data is PaginatedResponse<T> => {
    return (
      typeGuards.isApiResponse(data) &&
      'pagination' in data &&
      typeof (data as any).pagination === 'object'
    );
  },
};
