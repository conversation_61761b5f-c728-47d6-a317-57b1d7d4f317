/**
 * Query DevTools Configuration
 * Development tools for TanStack Query debugging
 */

'use client';

import React from 'react';

/**
 * Lazy-loaded React Query DevTools
 * Only loads in development mode
 */
const ReactQueryDevtools = React.lazy(() =>
  import('@tanstack/react-query-devtools').then((module) => ({
    default: module.ReactQueryDevtools,
  }))
);

/**
 * DevTools component with error boundary
 */
export function QueryDevTools() {
  // Only render in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <React.Suspense fallback={null}>
      <ReactQueryDevtools
        initialIsOpen={false}
        position="bottom-right"
        buttonPosition="bottom-right"
        panelProps={{
          style: {
            zIndex: 99999,
          },
        }}
      />
    </React.Suspense>
  );
}

/**
 * Query DevTools with error boundary
 */
export function QueryDevToolsWithErrorBoundary() {
  return (
    <QueryDevToolsErrorBoundary>
      <QueryDevTools />
    </QueryDevToolsErrorBoundary>
  );
}

/**
 * Error boundary for DevTools
 */
class QueryDevToolsErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('[QueryDevTools Error]', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return null; // Silently fail in production
    }

    return this.props.children;
  }
}

/**
 * Development utilities for query debugging
 */
export const queryDevUtils = {
  /**
   * Log query information
   */
  logQuery: (queryKey: unknown[], data: unknown, status: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.group(`[Query] ${queryKey.join(' → ')}`);
      console.log('Status:', status);
      console.log('Data:', data);
      console.log('Key:', queryKey);
      console.groupEnd();
    }
  },

  /**
   * Log mutation information
   */
  logMutation: (mutationKey: unknown[], variables: unknown, status: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.group(`[Mutation] ${mutationKey?.join(' → ') || 'Unknown'}`);
      console.log('Status:', status);
      console.log('Variables:', variables);
      console.log('Key:', mutationKey);
      console.groupEnd();
    }
  },

  /**
   * Log cache operations
   */
  logCacheOperation: (operation: string, queryKey: unknown[], data?: unknown) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Cache ${operation}]`, {
        key: queryKey,
        data: data ? '✓' : '✗',
      });
    }
  },

  /**
   * Performance monitoring
   */
  measureQueryTime: <T>(
    queryKey: unknown[],
    queryFn: () => Promise<T>
  ): Promise<T> => {
    if (process.env.NODE_ENV !== 'development') {
      return queryFn();
    }

    const startTime = performance.now();
    const label = `Query: ${queryKey.join(' → ')}`;

    console.time(label);

    return queryFn()
      .then((result) => {
        const endTime = performance.now();
        console.timeEnd(label);
        console.log(`[Query Performance] ${label}: ${(endTime - startTime).toFixed(2)}ms`);
        return result;
      })
      .catch((error) => {
        const endTime = performance.now();
        console.timeEnd(label);
        console.log(`[Query Performance] ${label}: ${(endTime - startTime).toFixed(2)}ms (ERROR)`);
        throw error;
      });
  },
};

/**
 * Query debugging hooks for development
 */
export const useQueryDebug = () => {
  if (process.env.NODE_ENV !== 'development') {
    return {
      logQuery: () => {},
      logMutation: () => {},
      logCacheOperation: () => {},
    };
  }

  return queryDevUtils;
};

/**
 * Development-only query inspector
 */
export function QueryInspector({ 
  queryKey, 
  data, 
  status, 
  error 
}: {
  queryKey: unknown[];
  data: unknown;
  status: string;
  error: unknown;
}) {
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      top: 10,
      right: 10,
      background: 'rgba(0, 0, 0, 0.8)',
      color: 'white',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      fontFamily: 'monospace',
      zIndex: 10000,
      maxWidth: '300px',
      maxHeight: '200px',
      overflow: 'auto',
    }}>
      <div><strong>Query Key:</strong> {JSON.stringify(queryKey)}</div>
      <div><strong>Status:</strong> {status}</div>
      {error && <div><strong>Error:</strong> {String(error)}</div>}
      <details>
        <summary>Data</summary>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </details>
    </div>
  );
}

/**
 * Development query stats component
 */
export function QueryStats() {
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  // This would integrate with QueryClient to show stats
  // For now, just a placeholder
  return (
    <div style={{
      position: 'fixed',
      bottom: 10,
      left: 10,
      background: 'rgba(0, 0, 0, 0.8)',
      color: 'white',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      fontFamily: 'monospace',
      zIndex: 10000,
    }}>
      <div>Query Stats (Dev Mode)</div>
      <div>Active Queries: -</div>
      <div>Cache Size: -</div>
    </div>
  );
}
