/**
 * Manual Test Runner for API Configuration and Utils
 * Run this to validate all functionality
 */

import { manualTests } from './__tests__/api-config.test';
import { manualApiUtilsTests } from './__tests__/api-utils.test';
import { 
  requiresAuthentication, 
  buildApiUrl, 
  getDefaultHeaders, 
  getAuthHeaders,
  API_CONFIG,
  API_ENDPOINTS 
} from './api-config';

// Test Results Interface
interface TestResult {
  name: string;
  passed: boolean;
  error?: string;
  details?: any;
}

class TestRunner {
  private results: TestResult[] = [];

  private addResult(name: string, passed: boolean, error?: string, details?: any) {
    this.results.push({ name, passed, error, details });
  }

  private assert(condition: boolean, message: string) {
    if (!condition) {
      throw new Error(message);
    }
  }

  // Test API Configuration
  testApiConfig() {
    console.log('\n🔧 Testing API Configuration...');
    
    try {
      // Test base configuration
      this.assert(API_CONFIG.BASE_URL === 'http://localhost:3000', 'Base URL should be localhost:3000');
      this.assert(API_CONFIG.TIMEOUT === 10000, 'Timeout should be 10000ms');
      this.addResult('API Base Configuration', true);
      
      // Test endpoint definitions
      this.assert(API_ENDPOINTS.FOOTBALL.FIXTURES === '/football/fixtures', 'Fixtures endpoint should be correct');
      this.assert(API_ENDPOINTS.SYSTEM_AUTH.LOGIN === '/system-auth/login', 'Login endpoint should be correct');
      this.addResult('API Endpoint Definitions', true);
      
    } catch (error) {
      this.addResult('API Configuration', false, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  // Test Authentication Logic
  testAuthenticationLogic() {
    console.log('\n🔐 Testing Authentication Logic...');
    
    try {
      // Test public endpoints
      this.assert(!requiresAuthentication('/football/fixtures'), 'Fixtures should not require auth');
      this.assert(!requiresAuthentication('/system-auth/login'), 'Login should not require auth');
      this.assert(!requiresAuthentication('/system-auth/refresh'), 'Refresh should not require auth');
      
      // Test protected endpoints
      this.assert(requiresAuthentication('/system-auth/profile'), 'Profile should require auth');
      this.assert(requiresAuthentication('/football/leagues'), 'Leagues should require auth');
      this.assert(requiresAuthentication('/broadcast-links'), 'Broadcast links should require auth');
      
      // Test unknown endpoints (should default to protected)
      this.assert(requiresAuthentication('/unknown/endpoint'), 'Unknown endpoints should require auth');
      
      this.addResult('Authentication Logic', true);
      
    } catch (error) {
      this.addResult('Authentication Logic', false, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  // Test URL Building
  testUrlBuilding() {
    console.log('\n🔗 Testing URL Building...');
    
    try {
      // Test with leading slash
      this.assert(
        buildApiUrl('/test') === 'http://localhost:3000/test',
        'URL with leading slash should be correct'
      );
      
      // Test without leading slash
      this.assert(
        buildApiUrl('test') === 'http://localhost:3000/test',
        'URL without leading slash should be correct'
      );
      
      // Test complex paths
      this.assert(
        buildApiUrl('/football/fixtures?page=1') === 'http://localhost:3000/football/fixtures?page=1',
        'URL with query params should be correct'
      );
      
      this.addResult('URL Building', true);
      
    } catch (error) {
      this.addResult('URL Building', false, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  // Test Header Generation
  testHeaderGeneration() {
    console.log('\n📋 Testing Header Generation...');
    
    try {
      // Test default headers
      const defaultHeaders = getDefaultHeaders();
      this.assert(defaultHeaders['Content-Type'] === 'application/json', 'Content-Type should be application/json');
      this.assert(defaultHeaders['Accept'] === 'application/json', 'Accept should be application/json');
      
      // Test auth headers without token
      const emptyAuthHeaders = getAuthHeaders();
      this.assert(Object.keys(emptyAuthHeaders).length === 0, 'Auth headers without token should be empty');
      
      // Test auth headers with token
      const authHeaders = getAuthHeaders('test-token');
      this.assert(authHeaders['Authorization'] === 'Bearer test-token', 'Auth header should be correct');
      
      this.addResult('Header Generation', true);
      
    } catch (error) {
      this.addResult('Header Generation', false, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  // Test Edge Cases
  testEdgeCases() {
    console.log('\n⚠️ Testing Edge Cases...');
    
    try {
      // Test empty strings
      this.assert(buildApiUrl('') === 'http://localhost:3000/', 'Empty endpoint should work');
      
      // Test special characters
      this.assert(
        buildApiUrl('/test/path with spaces') === 'http://localhost:3000/test/path with spaces',
        'Special characters should be preserved'
      );
      
      // Test very long endpoints
      const longEndpoint = '/very/long/endpoint/with/many/segments/that/goes/on/and/on';
      this.assert(
        buildApiUrl(longEndpoint).includes(longEndpoint),
        'Long endpoints should work'
      );
      
      // Test auth headers with empty token
      const emptyTokenHeaders = getAuthHeaders('');
      this.assert(Object.keys(emptyTokenHeaders).length === 0, 'Empty token should return empty headers');
      
      this.addResult('Edge Cases', true);
      
    } catch (error) {
      this.addResult('Edge Cases', false, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  // Test Endpoint Coverage
  testEndpointCoverage() {
    console.log('\n📊 Testing Endpoint Coverage...');
    
    try {
      // Test all defined endpoints
      const allEndpoints = [
        ...Object.values(API_ENDPOINTS.SYSTEM_AUTH),
        ...Object.values(API_ENDPOINTS.FOOTBALL),
        ...Object.values(API_ENDPOINTS.BROADCAST_LINKS),
      ];
      
      let publicCount = 0;
      let protectedCount = 0;
      
      allEndpoints.forEach(endpoint => {
        if (requiresAuthentication(endpoint)) {
          protectedCount++;
        } else {
          publicCount++;
        }
      });
      
      console.log(`   📈 Total endpoints: ${allEndpoints.length}`);
      console.log(`   🔓 Public endpoints: ${publicCount}`);
      console.log(`   🔒 Protected endpoints: ${protectedCount}`);
      
      this.assert(allEndpoints.length > 0, 'Should have defined endpoints');
      this.assert(publicCount > 0, 'Should have some public endpoints');
      this.assert(protectedCount > 0, 'Should have some protected endpoints');
      
      this.addResult('Endpoint Coverage', true, undefined, {
        total: allEndpoints.length,
        public: publicCount,
        protected: protectedCount
      });
      
    } catch (error) {
      this.addResult('Endpoint Coverage', false, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  // Run all tests
  runAllTests() {
    console.log('🚀 Starting API Configuration Tests...');
    console.log('=====================================');
    
    this.testApiConfig();
    this.testAuthenticationLogic();
    this.testUrlBuilding();
    this.testHeaderGeneration();
    this.testEdgeCases();
    this.testEndpointCoverage();
    
    this.printResults();
  }

  // Print test results
  printResults() {
    console.log('\n📊 Test Results Summary');
    console.log('========================');
    
    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => !r.passed).length;
    
    this.results.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} ${result.name}`);
      
      if (!result.passed && result.error) {
        console.log(`   Error: ${result.error}`);
      }
      
      if (result.details) {
        console.log(`   Details:`, result.details);
      }
    });
    
    console.log('\n📈 Summary:');
    console.log(`   ✅ Passed: ${passed}`);
    console.log(`   ❌ Failed: ${failed}`);
    console.log(`   📊 Total: ${this.results.length}`);
    console.log(`   🎯 Success Rate: ${((passed / this.results.length) * 100).toFixed(1)}%`);
    
    if (failed === 0) {
      console.log('\n🎉 All tests passed! Module 2.1.1 is working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Please review the errors above.');
    }
  }
}

// Export test runner for use
export const runTests = () => {
  const runner = new TestRunner();
  runner.runAllTests();
};

// Run manual tests from imported modules
export const runManualTests = () => {
  console.log('\n🔧 Running Manual Tests...');
  console.log('===========================');
  
  manualTests.testEndpointClassification();
  manualTests.testUrlBuilding();
  manualTests.testHeaders();
  
  console.log('\n🛠️ Running API Utils Tests...');
  manualApiUtilsTests.testTokenExtraction();
  manualApiUtilsTests.testMethodValidation();
  manualApiUtilsTests.testResponseCreation();
};

// Auto-run if this file is executed directly
if (require.main === module) {
  runTests();
  runManualTests();
}
