/**
 * Query Library Index
 * Central export for all query-related functionality
 */

// Core query client
export * from '../query-client';

// Error handling
export * from '../query-error-handler';

// Utilities and helpers
export * from '../query-utils';

// TypeScript types
export * from '../query-types';

// Development tools
export * from '../query-devtools';

// Provider components
export * from '../query-provider';

// Re-export TanStack Query essentials
export {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery,
  QueryClient,
  QueryClientProvider,
} from '@tanstack/react-query';

/**
 * Query library version and metadata
 */
export const QUERY_LIB_VERSION = '1.0.0';
export const QUERY_LIB_NAME = 'APISportsGame Query Library';

/**
 * Quick setup function for new components
 */
export function setupQueryLibrary() {
  console.log(`${QUERY_LIB_NAME} v${QUERY_LIB_VERSION} initialized`);
}
