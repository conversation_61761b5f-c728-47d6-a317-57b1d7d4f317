/**
 * TanStack Query Client Configuration
 * Centralized configuration for React Query client
 */

import { QueryClient, DefaultOptions } from '@tanstack/react-query';

/**
 * Default query options for the application
 */
const defaultQueryOptions: DefaultOptions = {
  queries: {
    // Stale time - how long data is considered fresh (5 minutes)
    staleTime: 5 * 60 * 1000,
    
    // Cache time - how long data stays in cache when unused (10 minutes)
    gcTime: 10 * 60 * 1000,
    
    // Retry configuration
    retry: (failureCount, error: any) => {
      // Don't retry on 4xx errors (client errors)
      if (error?.status >= 400 && error?.status < 500) {
        return false;
      }
      // Retry up to 3 times for other errors
      return failureCount < 3;
    },
    
    // Retry delay with exponential backoff
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    
    // Refetch on window focus (disabled for CMS to avoid unnecessary requests)
    refetchOnWindowFocus: false,
    
    // Refetch on reconnect
    refetchOnReconnect: true,
    
    // Refetch on mount if data is stale
    refetchOnMount: true,
  },
  mutations: {
    // Retry mutations once on failure
    retry: 1,
    
    // Retry delay for mutations
    retryDelay: 1000,
  },
};

/**
 * Development-specific query options
 */
const developmentQueryOptions: DefaultOptions = {
  queries: {
    ...defaultQueryOptions.queries,
    // Shorter stale time in development for faster feedback
    staleTime: 1 * 60 * 1000, // 1 minute
    // Shorter cache time in development
    gcTime: 2 * 60 * 1000, // 2 minutes
    // Enable refetch on window focus in development
    refetchOnWindowFocus: true,
  },
  mutations: {
    ...defaultQueryOptions.mutations,
  },
};

/**
 * Production-specific query options
 */
const productionQueryOptions: DefaultOptions = {
  queries: {
    ...defaultQueryOptions.queries,
    // Longer stale time in production for better performance
    staleTime: 10 * 60 * 1000, // 10 minutes
    // Longer cache time in production
    gcTime: 30 * 60 * 1000, // 30 minutes
  },
  mutations: {
    ...defaultQueryOptions.mutations,
  },
};

/**
 * Get query options based on environment
 */
function getQueryOptions(): DefaultOptions {
  const isDevelopment = process.env.NODE_ENV === 'development';
  return isDevelopment ? developmentQueryOptions : productionQueryOptions;
}

/**
 * Create and configure the QueryClient instance
 */
export function createQueryClient(): QueryClient {
  return new QueryClient({
    defaultOptions: getQueryOptions(),
    logger: {
      log: (message) => {
        if (process.env.NODE_ENV === 'development') {
          console.log(`[QueryClient] ${message}`);
        }
      },
      warn: (message) => {
        console.warn(`[QueryClient] ${message}`);
      },
      error: (message) => {
        console.error(`[QueryClient] ${message}`);
      },
    },
  });
}

/**
 * Singleton QueryClient instance
 */
let queryClient: QueryClient | undefined = undefined;

/**
 * Get the singleton QueryClient instance
 */
export function getQueryClient(): QueryClient {
  if (typeof window === 'undefined') {
    // Server-side: always create a new client
    return createQueryClient();
  }
  
  // Client-side: create client once and reuse
  if (!queryClient) {
    queryClient = createQueryClient();
  }
  
  return queryClient;
}

/**
 * Query client configuration constants
 */
export const QUERY_CONFIG = {
  // Cache times
  STALE_TIME: {
    SHORT: 1 * 60 * 1000,      // 1 minute
    MEDIUM: 5 * 60 * 1000,     // 5 minutes
    LONG: 10 * 60 * 1000,      // 10 minutes
    VERY_LONG: 30 * 60 * 1000, // 30 minutes
  },
  
  // Retry configuration
  RETRY: {
    NONE: 0,
    ONCE: 1,
    TWICE: 2,
    DEFAULT: 3,
  },
  
  // Refetch intervals
  REFETCH_INTERVAL: {
    FAST: 30 * 1000,      // 30 seconds
    MEDIUM: 60 * 1000,    // 1 minute
    SLOW: 5 * 60 * 1000,  // 5 minutes
  },
} as const;

/**
 * Query key factories for consistent key generation
 */
export const queryKeys = {
  // System authentication
  auth: {
    all: ['auth'] as const,
    profile: () => [...queryKeys.auth.all, 'profile'] as const,
    users: () => [...queryKeys.auth.all, 'users'] as const,
    user: (id: string) => [...queryKeys.auth.users(), id] as const,
  },
  
  // Football data
  football: {
    all: ['football'] as const,
    leagues: () => [...queryKeys.football.all, 'leagues'] as const,
    league: (id: string) => [...queryKeys.football.leagues(), id] as const,
    teams: () => [...queryKeys.football.all, 'teams'] as const,
    team: (id: string) => [...queryKeys.football.teams(), id] as const,
    fixtures: () => [...queryKeys.football.all, 'fixtures'] as const,
    fixture: (id: string) => [...queryKeys.football.fixtures(), id] as const,
    sync: () => [...queryKeys.football.all, 'sync'] as const,
    syncStatus: () => [...queryKeys.football.sync(), 'status'] as const,
  },
  
  // Broadcast links
  broadcast: {
    all: ['broadcast'] as const,
    links: () => [...queryKeys.broadcast.all, 'links'] as const,
    link: (id: string) => [...queryKeys.broadcast.links(), id] as const,
    fixture: (fixtureId: string) => [...queryKeys.broadcast.all, 'fixture', fixtureId] as const,
  },
  
  // Health checks
  health: {
    all: ['health'] as const,
    api: () => [...queryKeys.health.all, 'api'] as const,
  },
} as const;

/**
 * Utility functions for query management
 */
export const queryUtils = {
  /**
   * Invalidate all queries for a specific domain
   */
  invalidateAuth: (client: QueryClient) => {
    return client.invalidateQueries({ queryKey: queryKeys.auth.all });
  },
  
  invalidateFootball: (client: QueryClient) => {
    return client.invalidateQueries({ queryKey: queryKeys.football.all });
  },
  
  invalidateBroadcast: (client: QueryClient) => {
    return client.invalidateQueries({ queryKey: queryKeys.broadcast.all });
  },
  
  /**
   * Clear all cached data
   */
  clearAll: (client: QueryClient) => {
    return client.clear();
  },
  
  /**
   * Remove specific queries from cache
   */
  removeQueries: (client: QueryClient, queryKey: readonly unknown[]) => {
    return client.removeQueries({ queryKey });
  },
  
  /**
   * Prefetch data
   */
  prefetchAuth: (client: QueryClient) => {
    // Prefetch user profile if authenticated
    // Implementation will be added when auth hooks are ready
  },
} as const;
