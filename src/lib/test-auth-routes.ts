/**
 * Manual Test Runner for Authentication Routes
 * Tests all system-auth proxy routes with real HTTP requests
 */

const BASE_URL = 'http://localhost:4000';

interface TestResult {
  name: string;
  url: string;
  method: string;
  expectedStatus: number;
  actualStatus: number;
  responseTime: number;
  success: boolean;
  error?: string;
}

/**
 * Execute a test case
 */
async function executeTest(
  name: string,
  url: string,
  method: string,
  expectedStatus: number,
  headers?: Record<string, string>,
  body?: any
): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
      body: body ? JSON.stringify(body) : undefined,
    });
    
    const responseTime = Date.now() - startTime;
    const success = response.status === expectedStatus;
    
    return {
      name,
      url,
      method,
      expectedStatus,
      actualStatus: response.status,
      responseTime,
      success,
    };
  } catch (error) {
    return {
      name,
      url,
      method,
      expectedStatus,
      actualStatus: 0,
      responseTime: Date.now() - startTime,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Run all authentication route tests
 */
export async function testAuthRoutes(): Promise<TestResult[]> {
  const results: TestResult[] = [];
  
  console.log('🧪 Testing Authentication Routes...\n');
  
  // Test 1: Login - POST (should work but return 400/401 due to no backend)
  results.push(await executeTest(
    'Login POST',
    `${BASE_URL}/api/system-auth/login`,
    'POST',
    400, // Expected 400 because backend is not available
    {},
    { email: '<EMAIL>', password: 'password123' }
  ));
  
  // Test 2: Login - GET (should return 405 Method Not Allowed)
  results.push(await executeTest(
    'Login GET (should fail)',
    `${BASE_URL}/api/system-auth/login`,
    'GET',
    405
  ));
  
  // Test 3: Profile - GET without auth (should return 401)
  results.push(await executeTest(
    'Profile GET (no auth)',
    `${BASE_URL}/api/system-auth/profile`,
    'GET',
    401
  ));
  
  // Test 4: Profile - GET with auth (should return 401 due to no backend)
  results.push(await executeTest(
    'Profile GET (with auth)',
    `${BASE_URL}/api/system-auth/profile`,
    'GET',
    401, // Expected 401 because backend is not available
    { Authorization: 'Bearer test-token' }
  ));
  
  // Test 5: Profile - PUT with auth (should return 401 due to no backend)
  results.push(await executeTest(
    'Profile PUT (with auth)',
    `${BASE_URL}/api/system-auth/profile`,
    'PUT',
    401,
    { Authorization: 'Bearer test-token' },
    { name: 'Updated Name' }
  ));
  
  // Test 6: Profile - DELETE (should return 405 Method Not Allowed)
  results.push(await executeTest(
    'Profile DELETE (should fail)',
    `${BASE_URL}/api/system-auth/profile`,
    'DELETE',
    405
  ));
  
  // Test 7: Logout - POST with auth (should return 401 due to no backend)
  results.push(await executeTest(
    'Logout POST (with auth)',
    `${BASE_URL}/api/system-auth/logout`,
    'POST',
    401,
    { Authorization: 'Bearer test-token' }
  ));
  
  // Test 8: Logout - GET (should return 405 Method Not Allowed)
  results.push(await executeTest(
    'Logout GET (should fail)',
    `${BASE_URL}/api/system-auth/logout`,
    'GET',
    405
  ));
  
  // Test 9: Create User - POST with auth (should return 401 due to no backend)
  results.push(await executeTest(
    'Create User POST (with auth)',
    `${BASE_URL}/api/system-auth/create-user`,
    'POST',
    401,
    { Authorization: 'Bearer test-token' },
    {
      email: '<EMAIL>',
      password: 'password123',
      name: 'New User',
      role: 'Editor'
    }
  ));
  
  // Test 10: Create User - GET (should return 405 Method Not Allowed)
  results.push(await executeTest(
    'Create User GET (should fail)',
    `${BASE_URL}/api/system-auth/create-user`,
    'GET',
    405
  ));
  
  // Test 11: Logout All - POST with auth (should return 401 due to no backend)
  results.push(await executeTest(
    'Logout All POST (with auth)',
    `${BASE_URL}/api/system-auth/logout-all`,
    'POST',
    401,
    { Authorization: 'Bearer test-token' }
  ));
  
  return results;
}

/**
 * Print test results
 */
export function printTestResults(results: TestResult[]): void {
  console.log('\n📊 Test Results Summary:\n');
  
  let passed = 0;
  let failed = 0;
  
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    const statusText = result.success ? 'PASS' : 'FAIL';
    
    console.log(`${index + 1}. ${status} ${result.name}`);
    console.log(`   ${result.method} ${result.url}`);
    console.log(`   Expected: ${result.expectedStatus}, Got: ${result.actualStatus}`);
    console.log(`   Time: ${result.responseTime}ms`);
    
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
    
    console.log(`   Status: ${statusText}\n`);
    
    if (result.success) {
      passed++;
    } else {
      failed++;
    }
  });
  
  console.log(`📈 Summary: ${passed} passed, ${failed} failed out of ${results.length} tests`);
  console.log(`✅ Success Rate: ${((passed / results.length) * 100).toFixed(1)}%`);
}

/**
 * Main test function
 */
export async function runAuthRouteTests(): Promise<void> {
  try {
    const results = await testAuthRoutes();
    printTestResults(results);
  } catch (error) {
    console.error('❌ Test execution failed:', error);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAuthRouteTests();
}
