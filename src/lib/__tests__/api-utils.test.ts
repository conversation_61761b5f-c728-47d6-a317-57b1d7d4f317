/**
 * Test cases for API Utilities
 * Tests proxy utility functions
 */

import { NextRequest } from 'next/server';
import {
  extractAuthToken,
  createErrorResponse,
  createSuccessResponse,
  validateMethod,
  parseRequestBody,
} from '../api-utils';

// Mock NextRequest for testing
const createMockRequest = (options: {
  method?: string;
  headers?: Record<string, string>;
  body?: any;
  url?: string;
}) => {
  const headers = new Headers(options.headers || {});
  
  return {
    method: options.method || 'GET',
    headers,
    url: options.url || 'http://localhost:4000/api/test',
    json: async () => options.body || {},
    formData: async () => {
      const formData = new FormData();
      if (options.body && typeof options.body === 'object') {
        Object.entries(options.body).forEach(([key, value]) => {
          formData.append(key, value as string);
        });
      }
      return formData;
    },
  } as unknown as NextRequest;
};

describe('API Utils Tests', () => {
  describe('extractAuthToken', () => {
    test('should extract token from valid Authorization header', () => {
      const request = createMockRequest({
        headers: { 'authorization': 'Bearer test-token-123' }
      });
      
      expect(extractAuthToken(request)).toBe('test-token-123');
    });

    test('should return null for missing Authorization header', () => {
      const request = createMockRequest({});
      expect(extractAuthToken(request)).toBeNull();
    });

    test('should return null for invalid Authorization header format', () => {
      const request = createMockRequest({
        headers: { 'authorization': 'Invalid format' }
      });
      expect(extractAuthToken(request)).toBeNull();
    });

    test('should return null for empty Bearer token', () => {
      const request = createMockRequest({
        headers: { 'authorization': 'Bearer ' }
      });
      expect(extractAuthToken(request)).toBe('');
    });

    test('should handle different token formats', () => {
      const testCases = [
        { header: 'Bearer simple-token', expected: 'simple-token' },
        { header: 'Bearer jwt.token.here', expected: 'jwt.token.here' },
        { header: 'Bearer token-with-dashes', expected: 'token-with-dashes' },
      ];

      testCases.forEach(({ header, expected }) => {
        const request = createMockRequest({
          headers: { 'authorization': header }
        });
        expect(extractAuthToken(request)).toBe(expected);
      });
    });
  });

  describe('createErrorResponse', () => {
    test('should create error response with default values', () => {
      const response = createErrorResponse('Test error');
      
      expect(response.status).toBe(500);
      // Note: We can't easily test the JSON content in this environment
      // but we can verify the response is created
      expect(response).toBeDefined();
    });

    test('should create error response with custom status', () => {
      const response = createErrorResponse('Not found', 404, 'Not Found');
      expect(response.status).toBe(404);
    });

    test('should handle different error types', () => {
      const testCases = [
        { message: 'Validation error', status: 400, error: 'Bad Request' },
        { message: 'Unauthorized', status: 401, error: 'Unauthorized' },
        { message: 'Forbidden', status: 403, error: 'Forbidden' },
        { message: 'Not found', status: 404, error: 'Not Found' },
        { message: 'Server error', status: 500, error: 'Internal Server Error' },
      ];

      testCases.forEach(({ message, status, error }) => {
        const response = createErrorResponse(message, status, error);
        expect(response.status).toBe(status);
      });
    });
  });

  describe('createSuccessResponse', () => {
    test('should create success response with data', () => {
      const testData = { id: 1, name: 'Test' };
      const response = createSuccessResponse(testData);
      
      expect(response.status).toBe(200);
      expect(response).toBeDefined();
    });

    test('should create success response with custom status', () => {
      const response = createSuccessResponse({ created: true }, 201, 'Created');
      expect(response.status).toBe(201);
    });

    test('should handle different data types', () => {
      const testCases = [
        { data: 'string data', status: 200 },
        { data: 123, status: 200 },
        { data: { object: 'data' }, status: 200 },
        { data: [1, 2, 3], status: 200 },
        { data: null, status: 204 },
      ];

      testCases.forEach(({ data, status }) => {
        const response = createSuccessResponse(data, status);
        expect(response.status).toBe(status);
      });
    });
  });

  describe('validateMethod', () => {
    test('should return true for allowed methods', () => {
      const request = createMockRequest({ method: 'GET' });
      expect(validateMethod(request, ['GET', 'POST'])).toBe(true);
    });

    test('should return false for disallowed methods', () => {
      const request = createMockRequest({ method: 'DELETE' });
      expect(validateMethod(request, ['GET', 'POST'])).toBe(false);
    });

    test('should handle case sensitivity', () => {
      const request = createMockRequest({ method: 'get' });
      expect(validateMethod(request, ['GET', 'POST'])).toBe(false);
    });

    test('should work with different method combinations', () => {
      const testCases = [
        { method: 'GET', allowed: ['GET'], expected: true },
        { method: 'POST', allowed: ['GET', 'POST'], expected: true },
        { method: 'PUT', allowed: ['GET', 'POST'], expected: false },
        { method: 'PATCH', allowed: ['GET', 'POST', 'PUT', 'PATCH'], expected: true },
        { method: 'DELETE', allowed: ['GET'], expected: false },
      ];

      testCases.forEach(({ method, allowed, expected }) => {
        const request = createMockRequest({ method });
        expect(validateMethod(request, allowed)).toBe(expected);
      });
    });
  });

  describe('parseRequestBody', () => {
    test('should parse JSON body', async () => {
      const testBody = { name: 'test', value: 123 };
      const request = createMockRequest({
        headers: { 'content-type': 'application/json' },
        body: testBody
      });

      const result = await parseRequestBody(request);
      expect(result).toEqual(testBody);
    });

    test('should parse form data', async () => {
      const testBody = { name: 'test', value: '123' };
      const request = createMockRequest({
        headers: { 'content-type': 'application/x-www-form-urlencoded' },
        body: testBody
      });

      const result = await parseRequestBody(request);
      expect(result).toEqual(testBody);
    });

    test('should return null for unsupported content type', async () => {
      const request = createMockRequest({
        headers: { 'content-type': 'text/plain' }
      });

      const result = await parseRequestBody(request);
      expect(result).toBeNull();
    });

    test('should return null for missing content type', async () => {
      const request = createMockRequest({});
      const result = await parseRequestBody(request);
      expect(result).toBeNull();
    });
  });
});

// Manual test functions for development
export const manualApiUtilsTests = {
  testTokenExtraction: () => {
    console.log('=== Token Extraction Tests ===');
    
    const testHeaders = [
      { 'authorization': 'Bearer valid-token' },
      { 'authorization': 'Invalid format' },
      {},
      { 'authorization': 'Bearer ' },
    ];
    
    testHeaders.forEach((headers, index) => {
      const request = createMockRequest({ headers });
      const token = extractAuthToken(request);
      console.log(`Test ${index + 1}: ${JSON.stringify(headers)} -> ${token}`);
    });
  },
  
  testMethodValidation: () => {
    console.log('=== Method Validation Tests ===');
    
    const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
    const allowedMethods = ['GET', 'POST'];
    
    methods.forEach(method => {
      const request = createMockRequest({ method });
      const isValid = validateMethod(request, allowedMethods);
      console.log(`${method}: ${isValid ? 'ALLOWED' : 'DENIED'}`);
    });
  },
  
  testResponseCreation: () => {
    console.log('=== Response Creation Tests ===');
    
    const errorResponse = createErrorResponse('Test error', 400, 'Bad Request');
    console.log('Error response status:', errorResponse.status);
    
    const successResponse = createSuccessResponse({ test: 'data' }, 200, 'Success');
    console.log('Success response status:', successResponse.status);
  },
};
