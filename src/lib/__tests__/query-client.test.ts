/**
 * Query Client Tests
 * Tests for TanStack Query client configuration
 */

import { QueryClient } from '@tanstack/react-query';
import { 
  createQueryClient, 
  getQueryClient, 
  QUERY_CONFIG, 
  queryKeys,
  queryUtils 
} from '../query-client';

describe('Query Client Configuration', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = createQueryClient();
  });

  afterEach(() => {
    queryClient.clear();
  });

  describe('createQueryClient', () => {
    it('should create a QueryClient instance', () => {
      expect(queryClient).toBeInstanceOf(QueryClient);
    });

    it('should have correct default options', () => {
      const defaultOptions = queryClient.getDefaultOptions();
      
      expect(defaultOptions.queries?.retry).toBeDefined();
      expect(defaultOptions.queries?.refetchOnWindowFocus).toBe(false);
      expect(defaultOptions.queries?.refetchOnReconnect).toBe(true);
    });

    it('should have different options for development and production', () => {
      const originalEnv = process.env.NODE_ENV;
      
      // Test development
      process.env.NODE_ENV = 'development';
      const devClient = createQueryClient();
      const devOptions = devClient.getDefaultOptions();
      
      // Test production
      process.env.NODE_ENV = 'production';
      const prodClient = createQueryClient();
      const prodOptions = prodClient.getDefaultOptions();
      
      expect(devOptions.queries?.staleTime).not.toBe(prodOptions.queries?.staleTime);
      
      // Restore environment
      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('getQueryClient', () => {
    it('should return singleton instance on client side', () => {
      // Mock window object to simulate client side
      const originalWindow = global.window;
      (global as any).window = {};
      
      const client1 = getQueryClient();
      const client2 = getQueryClient();
      
      expect(client1).toBe(client2);
      
      // Restore window
      global.window = originalWindow;
    });

    it('should create new instance on server side', () => {
      // Ensure we're in server environment
      const originalWindow = global.window;
      delete (global as any).window;
      
      const client1 = getQueryClient();
      const client2 = getQueryClient();
      
      expect(client1).not.toBe(client2);
      
      // Restore window
      global.window = originalWindow;
    });
  });

  describe('QUERY_CONFIG', () => {
    it('should have correct stale time constants', () => {
      expect(QUERY_CONFIG.STALE_TIME.SHORT).toBe(1 * 60 * 1000);
      expect(QUERY_CONFIG.STALE_TIME.MEDIUM).toBe(5 * 60 * 1000);
      expect(QUERY_CONFIG.STALE_TIME.LONG).toBe(10 * 60 * 1000);
      expect(QUERY_CONFIG.STALE_TIME.VERY_LONG).toBe(30 * 60 * 1000);
    });

    it('should have correct retry constants', () => {
      expect(QUERY_CONFIG.RETRY.NONE).toBe(0);
      expect(QUERY_CONFIG.RETRY.ONCE).toBe(1);
      expect(QUERY_CONFIG.RETRY.TWICE).toBe(2);
      expect(QUERY_CONFIG.RETRY.DEFAULT).toBe(3);
    });

    it('should have correct refetch interval constants', () => {
      expect(QUERY_CONFIG.REFETCH_INTERVAL.FAST).toBe(30 * 1000);
      expect(QUERY_CONFIG.REFETCH_INTERVAL.MEDIUM).toBe(60 * 1000);
      expect(QUERY_CONFIG.REFETCH_INTERVAL.SLOW).toBe(5 * 60 * 1000);
    });
  });

  describe('queryKeys', () => {
    it('should generate correct auth query keys', () => {
      expect(queryKeys.auth.all).toEqual(['auth']);
      expect(queryKeys.auth.profile()).toEqual(['auth', 'profile']);
      expect(queryKeys.auth.users()).toEqual(['auth', 'users']);
      expect(queryKeys.auth.user('123')).toEqual(['auth', 'users', '123']);
    });

    it('should generate correct football query keys', () => {
      expect(queryKeys.football.all).toEqual(['football']);
      expect(queryKeys.football.leagues()).toEqual(['football', 'leagues']);
      expect(queryKeys.football.league('123')).toEqual(['football', 'leagues', '123']);
      expect(queryKeys.football.teams()).toEqual(['football', 'teams']);
      expect(queryKeys.football.fixtures()).toEqual(['football', 'fixtures']);
    });

    it('should generate correct broadcast query keys', () => {
      expect(queryKeys.broadcast.all).toEqual(['broadcast']);
      expect(queryKeys.broadcast.links()).toEqual(['broadcast', 'links']);
      expect(queryKeys.broadcast.fixture('123')).toEqual(['broadcast', 'fixture', '123']);
    });

    it('should generate correct health query keys', () => {
      expect(queryKeys.health.all).toEqual(['health']);
      expect(queryKeys.health.api()).toEqual(['health', 'api']);
    });
  });

  describe('queryUtils', () => {
    it('should invalidate auth queries', async () => {
      const invalidateSpy = jest.spyOn(queryClient, 'invalidateQueries');
      
      await queryUtils.invalidateAuth(queryClient);
      
      expect(invalidateSpy).toHaveBeenCalledWith({ queryKey: queryKeys.auth.all });
    });

    it('should invalidate football queries', async () => {
      const invalidateSpy = jest.spyOn(queryClient, 'invalidateQueries');
      
      await queryUtils.invalidateFootball(queryClient);
      
      expect(invalidateSpy).toHaveBeenCalledWith({ queryKey: queryKeys.football.all });
    });

    it('should invalidate broadcast queries', async () => {
      const invalidateSpy = jest.spyOn(queryClient, 'invalidateQueries');
      
      await queryUtils.invalidateBroadcast(queryClient);
      
      expect(invalidateSpy).toHaveBeenCalledWith({ queryKey: queryKeys.broadcast.all });
    });

    it('should clear all queries', () => {
      const clearSpy = jest.spyOn(queryClient, 'clear');
      
      queryUtils.clearAll(queryClient);
      
      expect(clearSpy).toHaveBeenCalled();
    });

    it('should remove specific queries', () => {
      const removeSpy = jest.spyOn(queryClient, 'removeQueries');
      const testKey = ['test', 'key'];
      
      queryUtils.removeQueries(queryClient, testKey);
      
      expect(removeSpy).toHaveBeenCalledWith({ queryKey: testKey });
    });
  });

  describe('retry logic', () => {
    it('should not retry on 4xx errors', () => {
      const defaultOptions = queryClient.getDefaultOptions();
      const retryFn = defaultOptions.queries?.retry as Function;
      
      // Mock 4xx error
      const clientError = { status: 400 };
      expect(retryFn(1, clientError)).toBe(false);
      
      const authError = { status: 401 };
      expect(retryFn(1, authError)).toBe(false);
      
      const forbiddenError = { status: 403 };
      expect(retryFn(1, forbiddenError)).toBe(false);
    });

    it('should retry on 5xx errors up to 3 times', () => {
      const defaultOptions = queryClient.getDefaultOptions();
      const retryFn = defaultOptions.queries?.retry as Function;
      
      const serverError = { status: 500 };
      expect(retryFn(0, serverError)).toBe(true);
      expect(retryFn(1, serverError)).toBe(true);
      expect(retryFn(2, serverError)).toBe(true);
      expect(retryFn(3, serverError)).toBe(false);
    });

    it('should have exponential backoff for retry delay', () => {
      const defaultOptions = queryClient.getDefaultOptions();
      const retryDelayFn = defaultOptions.queries?.retryDelay as Function;
      
      expect(retryDelayFn(0)).toBe(1000);
      expect(retryDelayFn(1)).toBe(2000);
      expect(retryDelayFn(2)).toBe(4000);
      expect(retryDelayFn(10)).toBe(30000); // Max delay
    });
  });
});
