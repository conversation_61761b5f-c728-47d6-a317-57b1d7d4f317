/**
 * Test cases for API Configuration
 * Tests all utility functions and configurations
 */

import {
  API_CONFIG,
  API_ENDPOINTS,
  requiresAuthentication,
  buildApiUrl,
  getDefaultHeaders,
  getAuthHeaders,
  PUBLIC_ENDPOINTS,
  PROTECTED_ENDPOINTS,
} from '../api-config';

describe('API Configuration Tests', () => {
  describe('API_CONFIG', () => {
    test('should have correct base configuration', () => {
      expect(API_CONFIG.BASE_URL).toBe('http://localhost:3000');
      expect(API_CONFIG.TIMEOUT).toBe(10000);
      expect(API_CONFIG.RETRY_ATTEMPTS).toBe(3);
      expect(API_CONFIG.RETRY_DELAY).toBe(1000);
    });
  });

  describe('API_ENDPOINTS', () => {
    test('should have all system auth endpoints', () => {
      expect(API_ENDPOINTS.SYSTEM_AUTH.LOGIN).toBe('/system-auth/login');
      expect(API_ENDPOINTS.SYSTEM_AUTH.PROFILE).toBe('/system-auth/profile');
      expect(API_ENDPOINTS.SYSTEM_AUTH.LOGOUT).toBe('/system-auth/logout');
      expect(API_ENDPOINTS.SYSTEM_AUTH.CREATE_USER).toBe('/system-auth/create-user');
    });

    test('should have all football endpoints', () => {
      expect(API_ENDPOINTS.FOOTBALL.LEAGUES).toBe('/football/leagues');
      expect(API_ENDPOINTS.FOOTBALL.TEAMS).toBe('/football/teams');
      expect(API_ENDPOINTS.FOOTBALL.FIXTURES).toBe('/football/fixtures');
      expect(API_ENDPOINTS.FOOTBALL.FIXTURES_SYNC).toBe('/football/fixtures/sync');
    });

    test('should have broadcast links endpoints', () => {
      expect(API_ENDPOINTS.BROADCAST_LINKS.BASE).toBe('/broadcast-links');
      expect(API_ENDPOINTS.BROADCAST_LINKS.BY_FIXTURE).toBe('/broadcast-links/fixture');
    });
  });

  describe('requiresAuthentication', () => {
    test('should return false for public endpoints', () => {
      expect(requiresAuthentication('/system-auth/login')).toBe(false);
      expect(requiresAuthentication('/system-auth/refresh')).toBe(false);
      expect(requiresAuthentication('/football/fixtures')).toBe(false);
    });

    test('should return true for protected endpoints', () => {
      expect(requiresAuthentication('/system-auth/profile')).toBe(true);
      expect(requiresAuthentication('/system-auth/create-user')).toBe(true);
      expect(requiresAuthentication('/football/leagues')).toBe(true);
      expect(requiresAuthentication('/football/teams')).toBe(true);
      expect(requiresAuthentication('/broadcast-links')).toBe(true);
    });

    test('should return true for unknown endpoints (security first)', () => {
      expect(requiresAuthentication('/unknown/endpoint')).toBe(true);
      expect(requiresAuthentication('/random/path')).toBe(true);
    });

    test('should handle endpoint variations correctly', () => {
      // Test with query parameters
      expect(requiresAuthentication('/football/fixtures?page=1')).toBe(false);
      expect(requiresAuthentication('/football/leagues?country=US')).toBe(true);
      
      // Test with sub-paths
      expect(requiresAuthentication('/football/fixtures/123')).toBe(false);
      expect(requiresAuthentication('/broadcast-links/fixture/123')).toBe(true);
    });
  });

  describe('buildApiUrl', () => {
    test('should build correct URLs with leading slash', () => {
      expect(buildApiUrl('/test/endpoint')).toBe('http://localhost:3000/test/endpoint');
      expect(buildApiUrl('/system-auth/login')).toBe('http://localhost:3000/system-auth/login');
    });

    test('should build correct URLs without leading slash', () => {
      expect(buildApiUrl('test/endpoint')).toBe('http://localhost:3000/test/endpoint');
      expect(buildApiUrl('football/fixtures')).toBe('http://localhost:3000/football/fixtures');
    });

    test('should handle base URL with trailing slash', () => {
      const originalBaseUrl = API_CONFIG.BASE_URL;
      // Mock base URL with trailing slash
      (API_CONFIG as any).BASE_URL = 'http://localhost:3000/';
      
      expect(buildApiUrl('/test')).toBe('http://localhost:3000/test');
      expect(buildApiUrl('test')).toBe('http://localhost:3000/test');
      
      // Restore original
      (API_CONFIG as any).BASE_URL = originalBaseUrl;
    });
  });

  describe('getDefaultHeaders', () => {
    test('should return correct default headers', () => {
      const headers = getDefaultHeaders();
      expect(headers['Content-Type']).toBe('application/json');
      expect(headers['Accept']).toBe('application/json');
    });

    test('should return new object each time', () => {
      const headers1 = getDefaultHeaders();
      const headers2 = getDefaultHeaders();
      expect(headers1).not.toBe(headers2);
      expect(headers1).toEqual(headers2);
    });
  });

  describe('getAuthHeaders', () => {
    test('should return empty object when no token provided', () => {
      expect(getAuthHeaders()).toEqual({});
      expect(getAuthHeaders(undefined)).toEqual({});
      expect(getAuthHeaders('')).toEqual({});
    });

    test('should return correct auth header with token', () => {
      const token = 'test-token-123';
      const headers = getAuthHeaders(token);
      expect(headers['Authorization']).toBe('Bearer test-token-123');
    });

    test('should handle different token formats', () => {
      expect(getAuthHeaders('simple-token')).toEqual({
        'Authorization': 'Bearer simple-token'
      });
      
      expect(getAuthHeaders('jwt.token.here')).toEqual({
        'Authorization': 'Bearer jwt.token.here'
      });
    });
  });

  describe('Endpoint Arrays', () => {
    test('PUBLIC_ENDPOINTS should contain expected endpoints', () => {
      expect(PUBLIC_ENDPOINTS).toContain('/system-auth/login');
      expect(PUBLIC_ENDPOINTS).toContain('/system-auth/refresh');
      expect(PUBLIC_ENDPOINTS).toContain('/football/fixtures');
    });

    test('PROTECTED_ENDPOINTS should contain expected endpoints', () => {
      expect(PROTECTED_ENDPOINTS).toContain('/system-auth/profile');
      expect(PROTECTED_ENDPOINTS).toContain('/system-auth/create-user');
      expect(PROTECTED_ENDPOINTS).toContain('/football/leagues');
      expect(PROTECTED_ENDPOINTS).toContain('/football/teams');
      expect(PROTECTED_ENDPOINTS).toContain('/broadcast-links');
    });

    test('should not have overlapping endpoints', () => {
      const publicSet = new Set(PUBLIC_ENDPOINTS);
      const protectedSet = new Set(PROTECTED_ENDPOINTS);
      
      PUBLIC_ENDPOINTS.forEach(endpoint => {
        expect(protectedSet.has(endpoint)).toBe(false);
      });
      
      PROTECTED_ENDPOINTS.forEach(endpoint => {
        expect(publicSet.has(endpoint)).toBe(false);
      });
    });
  });
});

// Manual test functions for development
export const manualTests = {
  testEndpointClassification: () => {
    console.log('=== Endpoint Classification Tests ===');
    
    const testEndpoints = [
      '/system-auth/login',
      '/system-auth/profile',
      '/football/fixtures',
      '/football/leagues',
      '/broadcast-links',
      '/unknown/endpoint',
    ];
    
    testEndpoints.forEach(endpoint => {
      const requiresAuth = requiresAuthentication(endpoint);
      console.log(`${endpoint}: ${requiresAuth ? 'PROTECTED' : 'PUBLIC'}`);
    });
  },
  
  testUrlBuilding: () => {
    console.log('=== URL Building Tests ===');
    
    const testPaths = [
      '/test',
      'test',
      '/football/fixtures',
      'system-auth/login',
    ];
    
    testPaths.forEach(path => {
      const url = buildApiUrl(path);
      console.log(`${path} -> ${url}`);
    });
  },
  
  testHeaders: () => {
    console.log('=== Headers Tests ===');
    
    console.log('Default headers:', getDefaultHeaders());
    console.log('Auth headers (no token):', getAuthHeaders());
    console.log('Auth headers (with token):', getAuthHeaders('test-token'));
  },
};
