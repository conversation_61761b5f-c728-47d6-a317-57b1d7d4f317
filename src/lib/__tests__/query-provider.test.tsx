/**
 * Query Provider Tests
 * Tests for Query Provider functionality
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { QueryProvider, QueryProviderWithErrorBoundary, QueryProviderUtils } from '../query-provider';

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Test component that uses query
function TestQueryComponent() {
  const queryClient = useQueryClient();
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['test'],
    queryFn: async () => {
      return { message: 'Hello from query!' };
    },
  });

  return (
    <div>
      <div data-testid="query-client-available">
        {queryClient ? 'true' : 'false'}
      </div>
      <div data-testid="query-loading">
        {isLoading ? 'true' : 'false'}
      </div>
      <div data-testid="query-error">
        {error ? String(error) : 'none'}
      </div>
      <div data-testid="query-data">
        {data ? JSON.stringify(data) : 'none'}
      </div>
    </div>
  );
}

// Test component that throws error
function ErrorComponent() {
  throw new Error('Test error');
}

describe('QueryProvider', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should provide QueryClient to child components', async () => {
    render(
      <QueryProvider>
        <TestQueryComponent />
      </QueryProvider>
    );

    // Check if QueryClient is available
    expect(screen.getByTestId('query-client-available')).toHaveTextContent('true');
    
    // Wait for query to complete
    await waitFor(() => {
      expect(screen.getByTestId('query-loading')).toHaveTextContent('false');
    });

    // Check query result
    expect(screen.getByTestId('query-error')).toHaveTextContent('none');
    expect(screen.getByTestId('query-data')).toHaveTextContent('{"message":"Hello from query!"}');
  });

  it('should handle query errors gracefully', async () => {
    function ErrorQueryComponent() {
      const { data, isLoading, error } = useQuery({
        queryKey: ['error-test'],
        queryFn: async () => {
          throw new Error('Query error');
        },
        retry: false,
      });

      return (
        <div>
          <div data-testid="error-query-loading">{isLoading ? 'true' : 'false'}</div>
          <div data-testid="error-query-error">{error ? error.message : 'none'}</div>
          <div data-testid="error-query-data">{data ? JSON.stringify(data) : 'none'}</div>
        </div>
      );
    }

    render(
      <QueryProvider>
        <ErrorQueryComponent />
      </QueryProvider>
    );

    // Wait for query to complete with error
    await waitFor(() => {
      expect(screen.getByTestId('error-query-loading')).toHaveTextContent('false');
    });

    // Check error handling
    expect(screen.getByTestId('error-query-error')).toHaveTextContent('Query error');
    expect(screen.getByTestId('error-query-data')).toHaveTextContent('none');
  });

  it('should include DevTools in development', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    render(
      <QueryProvider>
        <div>Test content</div>
      </QueryProvider>
    );

    // DevTools should be rendered (though not visible by default)
    // This is hard to test directly, but we can verify no errors occur
    expect(screen.getByText('Test content')).toBeInTheDocument();

    process.env.NODE_ENV = originalEnv;
  });
});

describe('QueryProviderWithErrorBoundary', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Suppress console.error for error boundary tests
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should render children when no error occurs', () => {
    render(
      <QueryProviderWithErrorBoundary>
        <div>No error content</div>
      </QueryProviderWithErrorBoundary>
    );

    expect(screen.getByText('No error content')).toBeInTheDocument();
  });

  it('should catch and display error fallback when error occurs', () => {
    render(
      <QueryProviderWithErrorBoundary>
        <ErrorComponent />
      </QueryProviderWithErrorBoundary>
    );

    expect(screen.getByText('Query Provider Error')).toBeInTheDocument();
    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  it('should show error details in development mode', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    render(
      <QueryProviderWithErrorBoundary>
        <ErrorComponent />
      </QueryProviderWithErrorBoundary>
    );

    expect(screen.getByText('Error Details (Development)')).toBeInTheDocument();

    process.env.NODE_ENV = originalEnv;
  });
});

describe('QueryProviderUtils', () => {
  it('should check if QueryClient is available', () => {
    // Mock successful QueryClient access
    const result = QueryProviderUtils.isQueryClientAvailable();
    expect(typeof result).toBe('boolean');
  });

  it('should get current QueryClient instance', () => {
    const client = QueryProviderUtils.getCurrentQueryClient();
    // Should return QueryClient instance or null
    expect(client === null || typeof client === 'object').toBe(true);
  });

  it('should reset QueryClient in development mode', () => {
    const originalEnv = process.env.NODE_ENV;
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    
    process.env.NODE_ENV = 'development';
    
    QueryProviderUtils.resetQueryClient();
    
    // Should not throw error
    expect(consoleSpy).toHaveBeenCalledWith('[Dev] QueryClient reset');
    
    process.env.NODE_ENV = originalEnv;
    consoleSpy.mockRestore();
  });

  it('should not reset QueryClient in production mode', () => {
    const originalEnv = process.env.NODE_ENV;
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    
    process.env.NODE_ENV = 'production';
    
    QueryProviderUtils.resetQueryClient();
    
    // Should not log in production
    expect(consoleSpy).not.toHaveBeenCalledWith('[Dev] QueryClient reset');
    
    process.env.NODE_ENV = originalEnv;
    consoleSpy.mockRestore();
  });
});
