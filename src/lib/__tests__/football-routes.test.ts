/**
 * Test cases for Football Routes
 * Tests all football proxy routes functionality
 */

import { NextRequest } from 'next/server';
import { GET as leaguesGET, POST as leaguesPOST, PATCH as leaguesPATCH } from '@/app/api/football/leagues/route';
import { GET as teamsGET } from '@/app/api/football/teams/route';
import { GET as fixturesGET, POST as fixturesPOST, PATCH as fixturesPATCH } from '@/app/api/football/fixtures/route';
import { GET as syncGET, POST as syncPOST } from '@/app/api/football/fixtures/sync/route';
import { GET as syncStatusGET } from '@/app/api/football/fixtures/sync/status/route';
import { POST as syncDailyPOST } from '@/app/api/football/fixtures/sync/daily/route';

// Mock the API utilities
jest.mock('@/lib/api-utils', () => ({
  handleProxyRequest: jest.fn(),
}));

import { handleProxyRequest } from '@/lib/api-utils';
const mockHandleProxyRequest = handleProxyRequest as jest.MockedFunction<typeof handleProxyRequest>;

describe('Football Routes Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockHandleProxyRequest.mockResolvedValue(
      new Response(JSON.stringify({ success: true }), { status: 200 })
    );
  });

  describe('Leagues Route', () => {
    test('should handle GET request to leagues', async () => {
      const request = new NextRequest('http://localhost:4000/api/football/leagues', {
        method: 'GET',
      });

      await leaguesGET(request);

      expect(mockHandleProxyRequest).toHaveBeenCalledWith(
        request,
        '/football/leagues',
        ['GET']
      );
    });

    test('should handle POST request to leagues', async () => {
      const request = new NextRequest('http://localhost:4000/api/football/leagues', {
        method: 'POST',
        body: JSON.stringify({ name: 'New League', country: 'Test Country' }),
      });

      await leaguesPOST(request);

      expect(mockHandleProxyRequest).toHaveBeenCalledWith(
        request,
        '/football/leagues',
        ['POST']
      );
    });

    test('should handle PATCH request to leagues', async () => {
      const request = new NextRequest('http://localhost:4000/api/football/leagues', {
        method: 'PATCH',
        body: JSON.stringify({ id: 'league-id', name: 'Updated League' }),
      });

      await leaguesPATCH(request);

      expect(mockHandleProxyRequest).toHaveBeenCalledWith(
        request,
        '/football/leagues',
        ['PATCH']
      );
    });
  });

  describe('Teams Route', () => {
    test('should handle GET request to teams', async () => {
      const request = new NextRequest('http://localhost:4000/api/football/teams', {
        method: 'GET',
      });

      await teamsGET(request);

      expect(mockHandleProxyRequest).toHaveBeenCalledWith(
        request,
        '/football/teams',
        ['GET']
      );
    });
  });

  describe('Fixtures Route', () => {
    test('should handle GET request to fixtures', async () => {
      const request = new NextRequest('http://localhost:4000/api/football/fixtures', {
        method: 'GET',
      });

      await fixturesGET(request);

      expect(mockHandleProxyRequest).toHaveBeenCalledWith(
        request,
        '/football/fixtures',
        ['GET']
      );
    });

    test('should handle POST request to fixtures', async () => {
      const request = new NextRequest('http://localhost:4000/api/football/fixtures', {
        method: 'POST',
        body: JSON.stringify({
          externalId: 'ext-id',
          homeTeamId: 'team1',
          awayTeamId: 'team2'
        }),
      });

      await fixturesPOST(request);

      expect(mockHandleProxyRequest).toHaveBeenCalledWith(
        request,
        '/football/fixtures',
        ['POST']
      );
    });

    test('should handle PATCH request to fixtures', async () => {
      const request = new NextRequest('http://localhost:4000/api/football/fixtures', {
        method: 'PATCH',
        body: JSON.stringify({
          externalId: 'ext-id',
          status: 'finished'
        }),
      });

      await fixturesPATCH(request);

      expect(mockHandleProxyRequest).toHaveBeenCalledWith(
        request,
        '/football/fixtures',
        ['PATCH']
      );
    });
  });

  describe('Sync Routes', () => {
    test('should handle GET request to sync', async () => {
      const request = new NextRequest('http://localhost:4000/api/football/fixtures/sync', {
        method: 'GET',
      });

      await syncGET(request);

      expect(mockHandleProxyRequest).toHaveBeenCalledWith(
        request,
        '/football/fixtures/sync',
        ['GET']
      );
    });

    test('should handle POST request to sync', async () => {
      const request = new NextRequest('http://localhost:4000/api/football/fixtures/sync', {
        method: 'POST',
        body: JSON.stringify({ leagues: ['league1'], force: false }),
      });

      await syncPOST(request);

      expect(mockHandleProxyRequest).toHaveBeenCalledWith(
        request,
        '/football/fixtures/sync',
        ['POST']
      );
    });

    test('should handle GET request to sync status', async () => {
      const request = new NextRequest('http://localhost:4000/api/football/fixtures/sync/status', {
        method: 'GET',
      });

      await syncStatusGET(request);

      expect(mockHandleProxyRequest).toHaveBeenCalledWith(
        request,
        '/football/fixtures/sync/status',
        ['GET']
      );
    });

    test('should handle POST request to daily sync', async () => {
      const request = new NextRequest('http://localhost:4000/api/football/fixtures/sync/daily', {
        method: 'POST',
        body: JSON.stringify({ date: '2024-05-24' }),
      });

      await syncDailyPOST(request);

      expect(mockHandleProxyRequest).toHaveBeenCalledWith(
        request,
        '/football/fixtures/sync/daily',
        ['POST']
      );
    });
  });
});
