/**
 * Test cases for Authentication Routes
 * Tests all system-auth proxy routes functionality
 */

import { NextRequest } from 'next/server';
import { POST as loginPOST } from '@/app/api/system-auth/login/route';
import { GET as profileGET, PUT as profilePUT } from '@/app/api/system-auth/profile/route';
import { POST as logoutPOST } from '@/app/api/system-auth/logout/route';
import { POST as createUserPOST } from '@/app/api/system-auth/create-user/route';
import { POST as logoutAllPOST } from '@/app/api/system-auth/logout-all/route';

// Mock the API utilities
jest.mock('@/lib/api-utils', () => ({
  handleProxyRequest: jest.fn(),
}));

import { handleProxyRequest } from '@/lib/api-utils';
const mockHandleProxyRequest = handleProxyRequest as jest.MockedFunction<typeof handleProxyRequest>;

describe('Authentication Routes Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockHandleProxyRequest.mockResolvedValue(
      new Response(JSON.stringify({ success: true }), { status: 200 })
    );
  });

  describe('Login Route', () => {
    test('should handle POST request to login', async () => {
      const request = new NextRequest('http://localhost:4000/api/system-auth/login', {
        method: 'POST',
        body: JSON.stringify({ email: '<EMAIL>', password: 'password' }),
      });

      await loginPOST(request);

      expect(mockHandleProxyRequest).toHaveBeenCalledWith(
        request,
        '/system-auth/login',
        ['POST']
      );
    });
  });

  describe('Profile Route', () => {
    test('should handle GET request to profile', async () => {
      const request = new NextRequest('http://localhost:4000/api/system-auth/profile', {
        method: 'GET',
      });

      await profileGET(request);

      expect(mockHandleProxyRequest).toHaveBeenCalledWith(
        request,
        '/system-auth/profile',
        ['GET']
      );
    });

    test('should handle PUT request to profile', async () => {
      const request = new NextRequest('http://localhost:4000/api/system-auth/profile', {
        method: 'PUT',
        body: JSON.stringify({ name: 'Updated Name' }),
      });

      await profilePUT(request);

      expect(mockHandleProxyRequest).toHaveBeenCalledWith(
        request,
        '/system-auth/profile',
        ['PUT']
      );
    });
  });

  describe('Logout Route', () => {
    test('should handle POST request to logout', async () => {
      const request = new NextRequest('http://localhost:4000/api/system-auth/logout', {
        method: 'POST',
      });

      await logoutPOST(request);

      expect(mockHandleProxyRequest).toHaveBeenCalledWith(
        request,
        '/system-auth/logout',
        ['POST']
      );
    });
  });

  describe('Create User Route', () => {
    test('should handle POST request to create user', async () => {
      const request = new NextRequest('http://localhost:4000/api/system-auth/create-user', {
        method: 'POST',
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123',
          name: 'New User',
          role: 'Editor'
        }),
      });

      await createUserPOST(request);

      expect(mockHandleProxyRequest).toHaveBeenCalledWith(
        request,
        '/system-auth/create-user',
        ['POST']
      );
    });
  });

  describe('Logout All Route', () => {
    test('should handle POST request to logout all', async () => {
      const request = new NextRequest('http://localhost:4000/api/system-auth/logout-all', {
        method: 'POST',
      });

      await logoutAllPOST(request);

      expect(mockHandleProxyRequest).toHaveBeenCalledWith(
        request,
        '/system-auth/logout-all',
        ['POST']
      );
    });
  });
});
