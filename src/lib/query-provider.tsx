/**
 * Query Provider Component
 * Wrapper for QueryClientProvider with Next.js integration
 */

'use client';

import React, { ReactNode } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { getQueryClient, setupQueryErrorHandling } from './query-client';
import { QueryDevToolsWithErrorBoundary } from './query-devtools';

/**
 * Query provider props
 */
interface QueryProviderProps {
  children: ReactNode;
}

/**
 * Query provider component
 * Provides QueryClient to the application with proper SSR handling
 */
export function QueryProvider({ children }: QueryProviderProps) {
  // Create query client instance (singleton on client, new on server)
  const [queryClient] = React.useState(() => {
    const client = getQueryClient();

    // Setup error handling
    setupQueryErrorHandling(client);

    return client;
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === 'development' && (
        <QueryDevToolsWithErrorBoundary />
      )}
    </QueryClientProvider>
  );
}

/**
 * Query provider with error boundary
 */
export function QueryProviderWithErrorBoundary({ children }: QueryProviderProps) {
  return (
    <QueryProviderErrorBoundary>
      <QueryProvider>
        {children}
      </QueryProvider>
    </QueryProviderErrorBoundary>
  );
}

/**
 * Error boundary for Query Provider
 */
class QueryProviderErrorBoundary extends React.Component<
  { children: ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): { hasError: boolean; error: Error } {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('[QueryProvider Error]', error, errorInfo);

    // Log to external service in production
    if (process.env.NODE_ENV === 'production') {
      // TODO: Integrate with error reporting service
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <QueryProviderErrorFallback
          error={this.state.error}
          onRetry={() => this.setState({ hasError: false, error: undefined })}
        />
      );
    }

    return this.props.children;
  }
}

/**
 * Error fallback component for Query Provider
 */
function QueryProviderErrorFallback({
  error,
  onRetry
}: {
  error?: Error;
  onRetry: () => void;
}) {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      padding: '20px',
      textAlign: 'center',
      fontFamily: 'system-ui, sans-serif',
    }}>
      <h1 style={{ color: '#dc2626', marginBottom: '16px' }}>
        Query Provider Error
      </h1>
      <p style={{ color: '#6b7280', marginBottom: '24px', maxWidth: '500px' }}>
        An error occurred while initializing the query system. This might be due to a
        network issue or a configuration problem.
      </p>
      {error && process.env.NODE_ENV === 'development' && (
        <details style={{
          marginBottom: '24px',
          padding: '16px',
          backgroundColor: '#f3f4f6',
          borderRadius: '8px',
          textAlign: 'left',
          maxWidth: '600px',
          width: '100%',
        }}>
          <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
            Error Details (Development)
          </summary>
          <pre style={{
            marginTop: '12px',
            fontSize: '12px',
            overflow: 'auto',
            whiteSpace: 'pre-wrap',
          }}>
            {error.message}
            {error.stack && `\n\n${error.stack}`}
          </pre>
        </details>
      )}
      <button
        onClick={onRetry}
        style={{
          padding: '12px 24px',
          backgroundColor: '#3b82f6',
          color: 'white',
          border: 'none',
          borderRadius: '6px',
          cursor: 'pointer',
          fontSize: '16px',
        }}
      >
        Retry
      </button>
    </div>
  );
}

/**
 * HOC to wrap components with Query Provider
 */
export function withQueryProvider<P extends object>(
  Component: React.ComponentType<P>
) {
  const WrappedComponent = (props: P) => (
    <QueryProvider>
      <Component {...props} />
    </QueryProvider>
  );

  WrappedComponent.displayName = `withQueryProvider(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

/**
 * Query provider utilities
 */
export const QueryProviderUtils = {
  /**
   * Check if QueryClient is available
   */
  isQueryClientAvailable: (): boolean => {
    try {
      getQueryClient();
      return true;
    } catch {
      return false;
    }
  },

  /**
   * Get current QueryClient instance
   */
  getCurrentQueryClient: (): QueryClient | null => {
    try {
      return getQueryClient();
    } catch {
      return null;
    }
  },

  /**
   * Reset QueryClient (development only)
   */
  resetQueryClient: (): void => {
    if (process.env.NODE_ENV === 'development') {
      const client = QueryProviderUtils.getCurrentQueryClient();
      if (client) {
        client.clear();
        console.log('[Dev] QueryClient reset');
      }
    }
  },
} as const;
