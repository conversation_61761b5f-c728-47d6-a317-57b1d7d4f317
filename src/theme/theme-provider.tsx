/**
 * Theme Provider
 * Ant Design theme provider with global state integration
 */

'use client';

import React, { ReactNode, useEffect } from 'react';
import { ConfigProvider, App as AntApp } from 'antd';
import { useAppProvider } from '@/stores';
import { themeConfigs, defaultTheme, type ThemeMode } from './config';
import { applyThemeToDocument } from './utils';

/**
 * Theme provider props
 */
interface ThemeProviderProps {
  children: ReactNode;
}

/**
 * Theme provider component
 * Wraps children with Ant Design ConfigProvider and applies theme
 */
export function ThemeProvider({ children }: ThemeProviderProps) {
  const app = useAppProvider();
  // Extract theme mode from theme config object
  const currentTheme = (app.theme?.mode as ThemeMode) || defaultTheme;
  const themeConfig = themeConfigs[currentTheme];

  // Apply theme to document when theme changes
  useEffect(() => {
    applyThemeToDocument(currentTheme);
  }, [currentTheme]);

  return (
    <ConfigProvider
      theme={themeConfig}
      componentSize="middle"
      direction="ltr"
    >
      <AntApp>
        <ThemeInitializer theme={currentTheme}>
          {children}
        </ThemeInitializer>
      </AntApp>
    </ConfigProvider>
  );
}

/**
 * Theme initializer component
 * Handles theme initialization and CSS variables
 */
function ThemeInitializer({
  children,
  theme
}: {
  children: ReactNode;
  theme: ThemeMode;
}) {
  useEffect(() => {
    // Set theme attribute on document
    document.documentElement.setAttribute('data-theme', theme);

    // Apply CSS variables for the current theme
    const root = document.documentElement;
    const themeConfig = themeConfigs[theme];

    if (themeConfig.token) {
      // Apply color variables
      if (themeConfig.token.colorPrimary) {
        root.style.setProperty('--ant-color-primary', themeConfig.token.colorPrimary);
      }
      if (themeConfig.token.colorSuccess) {
        root.style.setProperty('--ant-color-success', themeConfig.token.colorSuccess);
      }
      if (themeConfig.token.colorWarning) {
        root.style.setProperty('--ant-color-warning', themeConfig.token.colorWarning);
      }
      if (themeConfig.token.colorError) {
        root.style.setProperty('--ant-color-error', themeConfig.token.colorError);
      }

      // Apply background variables
      if (themeConfig.token.colorBgContainer) {
        root.style.setProperty('--ant-color-bg-container', themeConfig.token.colorBgContainer);
      }
      if (themeConfig.token.colorBgLayout) {
        root.style.setProperty('--ant-color-bg-layout', themeConfig.token.colorBgLayout);
      }

      // Apply text variables
      if (themeConfig.token.colorText) {
        root.style.setProperty('--ant-color-text', themeConfig.token.colorText);
      }
      if (themeConfig.token.colorTextSecondary) {
        root.style.setProperty('--ant-color-text-secondary', themeConfig.token.colorTextSecondary);
      }

      // Apply border variables
      if (themeConfig.token.colorBorder) {
        root.style.setProperty('--ant-color-border', themeConfig.token.colorBorder);
      }

      // Apply border radius variables
      if (themeConfig.token.borderRadius) {
        root.style.setProperty('--ant-border-radius', `${themeConfig.token.borderRadius}px`);
      }
      if (themeConfig.token.borderRadiusLG) {
        root.style.setProperty('--ant-border-radius-lg', `${themeConfig.token.borderRadiusLG}px`);
      }
    }

    console.log(`🎨 Theme applied: ${theme}`);
  }, [theme]);

  return <>{children}</>;
}

/**
 * Theme provider error boundary
 */
export class ThemeProviderErrorBoundary extends React.Component<
  { children: ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): { hasError: boolean; error: Error } {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('[ThemeProvider Error]', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          padding: '20px',
          textAlign: 'center',
          fontFamily: 'system-ui, sans-serif',
        }}>
          <h1 style={{ color: '#dc2626', marginBottom: '16px' }}>
            Theme Provider Error
          </h1>
          <p style={{ color: '#6b7280', marginBottom: '24px' }}>
            An error occurred while loading the theme system.
          </p>
          {this.state.error && process.env.NODE_ENV === 'development' && (
            <details style={{
              marginBottom: '24px',
              padding: '16px',
              backgroundColor: '#f3f4f6',
              borderRadius: '8px',
              textAlign: 'left',
              maxWidth: '600px',
              width: '100%',
            }}>
              <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
                Error Details (Development)
              </summary>
              <pre style={{
                marginTop: '12px',
                fontSize: '12px',
                overflow: 'auto',
                whiteSpace: 'pre-wrap',
              }}>
                {this.state.error.message}
                {this.state.error.stack && `\n\n${this.state.error.stack}`}
              </pre>
            </details>
          )}
          <button
            onClick={() => window.location.reload()}
            style={{
              padding: '12px 24px',
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '16px',
            }}
          >
            Reload Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Theme provider with error boundary
 */
export function ThemeProviderWithErrorBoundary({ children }: ThemeProviderProps) {
  return (
    <ThemeProviderErrorBoundary>
      <ThemeProvider>
        {children}
      </ThemeProvider>
    </ThemeProviderErrorBoundary>
  );
}

/**
 * HOC to wrap components with theme provider
 */
export function withThemeProvider<P extends object>(
  Component: React.ComponentType<P>
) {
  const WrappedComponent = (props: P) => (
    <ThemeProvider>
      <Component {...props} />
    </ThemeProvider>
  );

  WrappedComponent.displayName = `withThemeProvider(${Component.displayName || Component.name})`;

  return WrappedComponent;
}
