/**
 * Theme Hooks
 * Custom hooks for theme management
 */

'use client';

import { useEffect, useState, useCallback } from 'react';
import { useAppProvider } from '@/stores';
import {
  themeUtils,
  type ThemeMode,
  getThemeColors,
  createThemeMediaQueryListener
} from './utils';

/**
 * Hook to use theme with global state integration
 */
export function useTheme() {
  const app = useAppProvider();
  const currentTheme = (app.theme?.mode as ThemeMode) || 'light';

  const setTheme = useCallback((theme: ThemeMode) => {
    app.setTheme({ mode: theme });
    themeUtils.store(theme);
    themeUtils.apply(theme);
  }, [app]);

  const toggleTheme = useCallback(() => {
    const newTheme = themeUtils.toggle(currentTheme);
    setTheme(newTheme);
  }, [currentTheme, setTheme]);

  const resetToSystem = useCallback(() => {
    const systemTheme = themeUtils.getSystem();
    setTheme(systemTheme);
  }, [setTheme]);

  return {
    // Current theme state
    theme: currentTheme,
    isDark: themeUtils.isDark(currentTheme),
    isLight: themeUtils.isLight(currentTheme),

    // Theme colors
    colors: getThemeColors(currentTheme),

    // Theme actions
    setTheme,
    toggleTheme,
    resetToSystem,

    // Theme utilities
    utils: themeUtils,
  };
}

/**
 * Hook to listen to system theme changes
 */
export function useSystemTheme() {
  const [systemTheme, setSystemTheme] = useState<ThemeMode>(() => {
    if (typeof window === 'undefined') return 'light';
    return themeUtils.getSystem();
  });

  useEffect(() => {
    const cleanup = createThemeMediaQueryListener((theme) => {
      setSystemTheme(theme);
    });

    return cleanup || undefined;
  }, []);

  return {
    systemTheme,
    isSystemDark: themeUtils.isDark(systemTheme),
    isSystemLight: themeUtils.isLight(systemTheme),
  };
}

/**
 * Hook to sync theme with system preferences
 */
export function useThemeSync(autoSync: boolean = false) {
  const { theme, setTheme } = useTheme();
  const { systemTheme } = useSystemTheme();

  useEffect(() => {
    if (autoSync && theme !== systemTheme) {
      setTheme(systemTheme);
    }
  }, [autoSync, theme, systemTheme, setTheme]);

  const syncWithSystem = useCallback(() => {
    setTheme(systemTheme);
  }, [systemTheme, setTheme]);

  return {
    theme,
    systemTheme,
    isInSync: theme === systemTheme,
    syncWithSystem,
  };
}

/**
 * Hook to get theme-aware styles
 */
export function useThemeStyles() {
  const { theme, colors } = useTheme();

  const getStyle = useCallback((
    lightStyle: React.CSSProperties,
    darkStyle: React.CSSProperties
  ): React.CSSProperties => {
    return themeUtils.isDark(theme) ? darkStyle : lightStyle;
  }, [theme]);

  const getColor = useCallback((
    colorKey: keyof typeof colors
  ) => {
    return colors[colorKey];
  }, [colors]);

  const getBackgroundColor = useCallback((
    backgroundKey: keyof typeof colors.background
  ) => {
    return colors.background[backgroundKey];
  }, [colors]);

  const getTextColor = useCallback((
    textKey: keyof typeof colors.text
  ) => {
    return colors.text[textKey];
  }, [colors]);

  const getBorderColor = useCallback((
    borderKey: keyof typeof colors.border
  ) => {
    return colors.border[borderKey];
  }, [colors]);

  return {
    theme,
    colors,
    getStyle,
    getColor,
    getBackgroundColor,
    getTextColor,
    getBorderColor,

    // Common styles
    containerStyle: {
      backgroundColor: colors.background.container,
      color: colors.text.primary,
    },

    cardStyle: {
      backgroundColor: colors.background.elevated,
      color: colors.text.primary,
      border: `1px solid ${colors.border.primary}`,
    },

    headerStyle: {
      backgroundColor: colors.background.container,
      color: colors.text.primary,
      borderBottom: `1px solid ${colors.border.primary}`,
    },
  };
}

/**
 * Hook to manage theme persistence
 */
export function useThemePersistence() {
  const { theme, setTheme } = useTheme();

  const loadStoredTheme = useCallback(() => {
    const stored = themeUtils.getStored();
    if (stored && stored !== theme) {
      setTheme(stored);
    }
  }, [theme, setTheme]);

  const clearStoredTheme = useCallback(() => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('apisportsgame_theme');
    }
  }, []);

  const resetToDefault = useCallback(() => {
    clearStoredTheme();
    setTheme('light');
  }, [clearStoredTheme, setTheme]);

  return {
    theme,
    loadStoredTheme,
    clearStoredTheme,
    resetToDefault,
    hasStoredTheme: themeUtils.getStored() !== null,
  };
}

/**
 * Hook for theme debugging (development only)
 */
export function useThemeDebug() {
  const { theme, colors } = useTheme();
  const { systemTheme } = useSystemTheme();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const debugInfo = {
    currentTheme: theme,
    systemTheme,
    storedTheme: themeUtils.getStored(),
    effectiveTheme: themeUtils.getEffective(),
    colors,
    cssVariables: themeUtils.generateCSSVariables(theme),
  };

  const logThemeInfo = useCallback(() => {
    console.group('🎨 Theme Debug Info');
    console.log('Current Theme:', debugInfo.currentTheme);
    console.log('System Theme:', debugInfo.systemTheme);
    console.log('Stored Theme:', debugInfo.storedTheme);
    console.log('Effective Theme:', debugInfo.effectiveTheme);
    console.log('Colors:', debugInfo.colors);
    console.log('CSS Variables:', debugInfo.cssVariables);
    console.groupEnd();
  }, [debugInfo]);

  return {
    debugInfo,
    logThemeInfo,
  };
}

/**
 * Hook to preload theme assets
 */
export function useThemePreload() {
  const [isPreloaded, setIsPreloaded] = useState(false);

  useEffect(() => {
    // Preload theme-related assets
    const preloadAssets = async () => {
      try {
        // Apply initial theme
        const effectiveTheme = themeUtils.getEffective();
        themeUtils.apply(effectiveTheme);
        themeUtils.applyCSSVariables(effectiveTheme);

        setIsPreloaded(true);
        console.log('🎨 Theme assets preloaded');
      } catch (error) {
        console.error('Failed to preload theme assets:', error);
        setIsPreloaded(true); // Set to true anyway to prevent blocking
      }
    };

    preloadAssets();
  }, []);

  return {
    isPreloaded,
  };
}
