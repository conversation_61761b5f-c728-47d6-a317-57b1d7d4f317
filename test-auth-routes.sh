#!/bin/bash

# Authentication Routes Test Script
# Tests all system-auth proxy routes

BASE_URL="http://localhost:4000"
echo "🧪 Testing Authentication Routes..."
echo "Base URL: $BASE_URL"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counter
TOTAL_TESTS=0
PASSED_TESTS=0

# Function to run a test
run_test() {
    local test_name="$1"
    local method="$2"
    local endpoint="$3"
    local expected_status="$4"
    local headers="$5"
    local body="$6"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -n "Test $TOTAL_TESTS: $test_name... "
    
    # Build curl command
    local curl_cmd="curl -s -w '%{http_code}' -o /dev/null"
    
    if [ ! -z "$headers" ]; then
        curl_cmd="$curl_cmd $headers"
    fi
    
    if [ ! -z "$body" ]; then
        curl_cmd="$curl_cmd -d '$body'"
    fi
    
    curl_cmd="$curl_cmd -X $method $BASE_URL$endpoint"
    
    # Execute test
    local actual_status=$(eval $curl_cmd)
    
    # Check result
    if [ "$actual_status" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} (Status: $actual_status)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ FAIL${NC} (Expected: $expected_status, Got: $actual_status)"
    fi
}

echo "Starting tests..."
echo ""

# Test 1: Login POST (should return 400 - no backend)
run_test "Login POST" "POST" "/api/system-auth/login" "400" "-H 'Content-Type: application/json'" '{"email":"<EMAIL>","password":"password123"}'

# Test 2: Login GET (should return 405 - method not allowed)
run_test "Login GET (should fail)" "GET" "/api/system-auth/login" "405"

# Test 3: Profile GET without auth (should return 401)
run_test "Profile GET (no auth)" "GET" "/api/system-auth/profile" "401"

# Test 4: Profile GET with auth (should return 401 - no backend)
run_test "Profile GET (with auth)" "GET" "/api/system-auth/profile" "401" "-H 'Authorization: Bearer test-token'"

# Test 5: Profile PUT with auth (should return 401 - no backend)
run_test "Profile PUT (with auth)" "PUT" "/api/system-auth/profile" "401" "-H 'Authorization: Bearer test-token' -H 'Content-Type: application/json'" '{"name":"Updated Name"}'

# Test 6: Profile DELETE (should return 405 - method not allowed)
run_test "Profile DELETE (should fail)" "DELETE" "/api/system-auth/profile" "405"

# Test 7: Logout POST with auth (should return 401 - no backend)
run_test "Logout POST (with auth)" "POST" "/api/system-auth/logout" "401" "-H 'Authorization: Bearer test-token'"

# Test 8: Logout GET (should return 405 - method not allowed)
run_test "Logout GET (should fail)" "GET" "/api/system-auth/logout" "405"

# Test 9: Create User POST with auth (should return 401 - no backend)
run_test "Create User POST (with auth)" "POST" "/api/system-auth/create-user" "401" "-H 'Authorization: Bearer test-token' -H 'Content-Type: application/json'" '{"email":"<EMAIL>","password":"password123","name":"New User","role":"Editor"}'

# Test 10: Create User GET (should return 405 - method not allowed)
run_test "Create User GET (should fail)" "GET" "/api/system-auth/create-user" "405"

# Test 11: Logout All POST with auth (should return 401 - no backend)
run_test "Logout All POST (with auth)" "POST" "/api/system-auth/logout-all" "401" "-H 'Authorization: Bearer test-token'"

echo ""
echo "📊 Test Results Summary:"
echo "Total Tests: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $((TOTAL_TESTS - PASSED_TESTS))"
echo "Success Rate: $(echo "scale=1; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc)%"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    echo -e "${GREEN}🎉 All tests passed!${NC}"
else
    echo -e "${YELLOW}⚠️  Some tests failed. Check the results above.${NC}"
fi
