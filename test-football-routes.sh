#!/bin/bash

# Football Routes Test Script
# Tests all football proxy routes

BASE_URL="http://localhost:4000"
echo "🏈 Testing Football Routes..."
echo "Base URL: $BASE_URL"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counter
TOTAL_TESTS=0
PASSED_TESTS=0

# Function to run a test
run_test() {
    local test_name="$1"
    local method="$2"
    local endpoint="$3"
    local expected_status="$4"
    local headers="$5"
    local body="$6"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -n "Test $TOTAL_TESTS: $test_name... "
    
    # Build curl command
    local curl_cmd="curl -s -w '%{http_code}' -o /dev/null"
    
    if [ ! -z "$headers" ]; then
        curl_cmd="$curl_cmd $headers"
    fi
    
    if [ ! -z "$body" ]; then
        curl_cmd="$curl_cmd -d '$body'"
    fi
    
    curl_cmd="$curl_cmd -X $method $BASE_URL$endpoint"
    
    # Execute test
    local actual_status=$(eval $curl_cmd)
    
    # Check result
    if [ "$actual_status" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} (Status: $actual_status)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ FAIL${NC} (Expected: $expected_status, Got: $actual_status)"
    fi
}

echo "Starting football routes tests..."
echo ""

# Test 1: Fixtures GET (public endpoint - should work)
run_test "Fixtures GET (public)" "GET" "/api/football/fixtures?limit=3" "200"

# Test 2: Fixtures POST (protected - should return 401)
run_test "Fixtures POST (no auth)" "POST" "/api/football/fixtures" "401" "-H 'Content-Type: application/json'" '{"externalId":"test","homeTeamId":"1","awayTeamId":"2"}'

# Test 3: Fixtures PATCH (protected - should return 401)
run_test "Fixtures PATCH (no auth)" "PATCH" "/api/football/fixtures" "401" "-H 'Content-Type: application/json'" '{"externalId":"test","status":"finished"}'

# Test 4: Fixtures DELETE (method not allowed - should return 405)
run_test "Fixtures DELETE (not allowed)" "DELETE" "/api/football/fixtures" "405"

# Test 5: Leagues GET (protected - should return 401)
run_test "Leagues GET (no auth)" "GET" "/api/football/leagues" "401"

# Test 6: Leagues POST (protected - should return 401)
run_test "Leagues POST (no auth)" "POST" "/api/football/leagues" "401" "-H 'Content-Type: application/json'" '{"name":"Test League","country":"Test"}'

# Test 7: Leagues PATCH (protected - should return 401)
run_test "Leagues PATCH (no auth)" "PATCH" "/api/football/leagues" "401" "-H 'Content-Type: application/json'" '{"id":"1","name":"Updated"}'

# Test 8: Leagues DELETE (method not allowed - should return 405)
run_test "Leagues DELETE (not allowed)" "DELETE" "/api/football/leagues" "405"

# Test 9: Teams GET (protected - should return 401)
run_test "Teams GET (no auth)" "GET" "/api/football/teams" "401"

# Test 10: Teams POST (method not allowed - should return 405)
run_test "Teams POST (not allowed)" "POST" "/api/football/teams" "405"

# Test 11: Sync GET (protected - should return 401)
run_test "Sync GET (no auth)" "GET" "/api/football/fixtures/sync" "401"

# Test 12: Sync POST (protected - should return 401)
run_test "Sync POST (no auth)" "POST" "/api/football/fixtures/sync" "401" "-H 'Content-Type: application/json'" '{"leagues":["1"]}'

# Test 13: Sync Status GET (protected - should return 401)
run_test "Sync Status GET (no auth)" "GET" "/api/football/fixtures/sync/status" "401"

# Test 14: Sync Daily POST (protected - should return 401)
run_test "Sync Daily POST (no auth)" "POST" "/api/football/fixtures/sync/daily" "401" "-H 'Content-Type: application/json'" '{"date":"2024-05-24"}'

# Test 15: Test with auth token (should still return 401 due to no backend)
run_test "Leagues GET (with token)" "GET" "/api/football/leagues" "401" "-H 'Authorization: Bearer test-token'"

echo ""
echo "📊 Test Results Summary:"
echo "Total Tests: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $((TOTAL_TESTS - PASSED_TESTS))"
echo "Success Rate: $(echo "scale=1; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc)%"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    echo -e "${GREEN}🎉 All tests passed!${NC}"
else
    echo -e "${YELLOW}⚠️  Some tests failed. Check the results above.${NC}"
fi
