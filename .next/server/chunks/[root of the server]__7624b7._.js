module.exports = {

"[externals]/next/dist/compiled/next-server/app-route.runtime.dev.js [external] (next/dist/compiled/next-server/app-route.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/compiled/next-server/app-route.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page.runtime.dev.js [external] (next/dist/compiled/next-server/app-page.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/compiled/next-server/app-page.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/api-config.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * API Configuration for APISportsGame CMS
 * Handles proxy configuration and API utilities
 */ // API Base Configuration
__turbopack_esm__({
    "API_CONFIG": (()=>API_CONFIG),
    "API_ENDPOINTS": (()=>API_ENDPOINTS),
    "AUTH_CONFIG": (()=>AUTH_CONFIG),
    "HTTP_METHODS": (()=>HTTP_METHODS),
    "PROTECTED_ENDPOINTS": (()=>PROTECTED_ENDPOINTS),
    "PUBLIC_ENDPOINTS": (()=>PUBLIC_ENDPOINTS),
    "buildApiUrl": (()=>buildApiUrl),
    "getAuthHeaders": (()=>getAuthHeaders),
    "getDefaultHeaders": (()=>getDefaultHeaders),
    "requiresAuthentication": (()=>requiresAuthentication)
});
const API_CONFIG = {
    BASE_URL: ("TURBOPACK compile-time value", "http://localhost:3000") || 'http://localhost:3000',
    TIMEOUT: 10000,
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000
};
const API_ENDPOINTS = {
    // System Authentication (requires auth)
    SYSTEM_AUTH: {
        LOGIN: '/system-auth/login',
        PROFILE: '/system-auth/profile',
        LOGOUT: '/system-auth/logout',
        LOGOUT_ALL: '/system-auth/logout-all',
        CREATE_USER: '/system-auth/create-user',
        CHANGE_PASSWORD: '/system-auth/change-password',
        REFRESH: '/system-auth/refresh'
    },
    // Football Data (some endpoints no auth required)
    FOOTBALL: {
        LEAGUES: '/football/leagues',
        TEAMS: '/football/teams',
        FIXTURES: '/football/fixtures',
        FIXTURES_SYNC: '/football/fixtures/sync',
        FIXTURES_SYNC_STATUS: '/football/fixtures/sync/status',
        FIXTURES_SYNC_DAILY: '/football/fixtures/sync/daily'
    },
    // Broadcast Links (requires auth)
    BROADCAST_LINKS: {
        BASE: '/broadcast-links',
        BY_FIXTURE: '/broadcast-links/fixture'
    }
};
const HTTP_METHODS = {
    GET: 'GET',
    POST: 'POST',
    PUT: 'PUT',
    PATCH: 'PATCH',
    DELETE: 'DELETE'
};
const AUTH_CONFIG = {
    TOKEN_HEADER: 'Authorization',
    TOKEN_PREFIX: 'Bearer',
    REFRESH_TOKEN_HEADER: 'X-Refresh-Token'
};
const PROTECTED_ENDPOINTS = [
    '/system-auth/profile',
    '/system-auth/logout',
    '/system-auth/logout-all',
    '/system-auth/create-user',
    '/system-auth/change-password',
    '/football/leagues',
    '/football/teams',
    '/football/fixtures/sync',
    '/broadcast-links'
];
const PUBLIC_ENDPOINTS = [
    '/system-auth/login',
    '/system-auth/refresh',
    '/football/fixtures'
];
function requiresAuthentication(endpoint) {
    // Check if it's explicitly in public endpoints
    if (PUBLIC_ENDPOINTS.some((publicEndpoint)=>endpoint.startsWith(publicEndpoint))) {
        return false;
    }
    // Check if it's in protected endpoints
    if (PROTECTED_ENDPOINTS.some((protectedEndpoint)=>endpoint.startsWith(protectedEndpoint))) {
        return true;
    }
    // Default to requiring authentication for unknown endpoints (security first)
    return true;
}
function buildApiUrl(endpoint) {
    const baseUrl = API_CONFIG.BASE_URL.replace(/\/$/, '');
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return `${baseUrl}${cleanEndpoint}`;
}
function getDefaultHeaders() {
    return {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    };
}
function getAuthHeaders(token) {
    if (!token) return {};
    return {
        [AUTH_CONFIG.TOKEN_HEADER]: `${AUTH_CONFIG.TOKEN_PREFIX} ${token}`
    };
}
}}),
"[project]/src/lib/api-utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * API Utility Functions for Proxy Operations
 */ __turbopack_esm__({
    "buildProxyConfig": (()=>buildProxyConfig),
    "createErrorResponse": (()=>createErrorResponse),
    "createSuccessResponse": (()=>createSuccessResponse),
    "executeProxyRequest": (()=>executeProxyRequest),
    "extractAuthToken": (()=>extractAuthToken),
    "handleProxyRequest": (()=>handleProxyRequest),
    "handleProxyResponse": (()=>handleProxyResponse),
    "logProxyRequest": (()=>logProxyRequest),
    "parseRequestBody": (()=>parseRequestBody),
    "validateMethod": (()=>validateMethod)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/api-config.ts [app-route] (ecmascript)");
;
;
function extractAuthToken(request) {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return null;
    }
    return authHeader.substring(7); // Remove 'Bearer ' prefix
}
function createErrorResponse(message, statusCode = 500, error) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        message,
        statusCode,
        error: error || 'Internal Server Error'
    }, {
        status: statusCode
    });
}
function createSuccessResponse(data, statusCode = 200, message) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        success: true,
        data,
        message,
        statusCode
    }, {
        status: statusCode
    });
}
function validateMethod(request, allowedMethods) {
    return allowedMethods.includes(request.method);
}
async function parseRequestBody(request) {
    try {
        const contentType = request.headers.get('content-type');
        if (contentType?.includes('application/json')) {
            return await request.json();
        }
        if (contentType?.includes('application/x-www-form-urlencoded')) {
            const formData = await request.formData();
            const body = {};
            formData.forEach((value, key)=>{
                body[key] = value;
            });
            return body;
        }
        return null;
    } catch (error) {
        console.error('Error parsing request body:', error);
        return null;
    }
}
async function buildProxyConfig(request, targetEndpoint) {
    const body = await parseRequestBody(request);
    const token = extractAuthToken(request);
    const requiresAuth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["requiresAuthentication"])(targetEndpoint);
    // Build headers
    const headers = {
        ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDefaultHeaders"])(),
        ...requiresAuth && token ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAuthHeaders"])(token) : {}
    };
    // Copy relevant headers from original request
    const relevantHeaders = [
        'user-agent',
        'accept-language',
        'x-forwarded-for'
    ];
    relevantHeaders.forEach((headerName)=>{
        const headerValue = request.headers.get(headerName);
        if (headerValue) {
            headers[headerName] = headerValue;
        }
    });
    return {
        method: request.method,
        url: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["buildApiUrl"])(targetEndpoint),
        headers,
        body: body ? JSON.stringify(body) : undefined,
        requiresAuth
    };
}
async function executeProxyRequest(config) {
    const { method, url, headers, body } = config;
    try {
        const response = await fetch(url, {
            method,
            headers,
            body,
            signal: AbortSignal.timeout(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_CONFIG"].TIMEOUT)
        });
        return response;
    } catch (error) {
        console.error('Proxy request failed:', error);
        throw new Error(`Proxy request failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function handleProxyResponse(response) {
    try {
        const contentType = response.headers.get('content-type');
        // Handle JSON responses
        if (contentType?.includes('application/json')) {
            const data = await response.json();
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(data, {
                status: response.status,
                statusText: response.statusText
            });
        }
        // Handle text responses
        if (contentType?.includes('text/')) {
            const text = await response.text();
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](text, {
                status: response.status,
                statusText: response.statusText,
                headers: {
                    'content-type': contentType
                }
            });
        }
        // Handle binary responses
        const buffer = await response.arrayBuffer();
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](buffer, {
            status: response.status,
            statusText: response.statusText,
            headers: {
                'content-type': contentType || 'application/octet-stream'
            }
        });
    } catch (error) {
        console.error('Error handling proxy response:', error);
        return createErrorResponse('Failed to process response from backend API', 500);
    }
}
async function handleProxyRequest(request, targetEndpoint, allowedMethods = [
    'GET',
    'POST',
    'PUT',
    'PATCH',
    'DELETE'
]) {
    try {
        // Validate HTTP method
        if (!validateMethod(request, allowedMethods)) {
            return createErrorResponse(`Method ${request.method} not allowed`, 405, 'Method Not Allowed');
        }
        // Check authentication if required
        const requiresAuth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["requiresAuthentication"])(targetEndpoint);
        if (requiresAuth) {
            const token = extractAuthToken(request);
            if (!token) {
                return createErrorResponse('Authentication required', 401, 'Unauthorized');
            }
        }
        // Build proxy configuration
        const proxyConfig = await buildProxyConfig(request, targetEndpoint);
        // Execute proxy request
        const response = await executeProxyRequest(proxyConfig);
        // Handle and return response
        return await handleProxyResponse(response);
    } catch (error) {
        console.error('Proxy handler error:', error);
        return createErrorResponse(error instanceof Error ? error.message : 'Internal server error', 500);
    }
}
function logProxyRequest(request, targetEndpoint, config) {
    if ("TURBOPACK compile-time truthy", 1) {
        console.log(`[PROXY] ${request.method} ${request.url} -> ${config.url}`);
        console.log(`[PROXY] Auth required: ${config.requiresAuth}`);
        console.log(`[PROXY] Headers:`, Object.keys(config.headers || {}));
    }
}
}}),
"[project]/src/app/api/football/leagues/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Football Leagues Route
 * Handles football leagues management operations
 */ __turbopack_esm__({
    "GET": (()=>GET),
    "PATCH": (()=>PATCH),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/api-utils.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/api-config.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleProxyRequest"])(request, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].FOOTBALL.LEAGUES, [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTTP_METHODS"].GET
    ]);
}
async function POST(request) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleProxyRequest"])(request, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].FOOTBALL.LEAGUES, [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTTP_METHODS"].POST
    ]);
}
async function PATCH(request) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleProxyRequest"])(request, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].FOOTBALL.LEAGUES, [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HTTP_METHODS"].PATCH
    ]);
}
}}),
"[project]/ (server-utils)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__7624b7._.js.map