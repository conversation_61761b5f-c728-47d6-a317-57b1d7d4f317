{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/ui/button.tsx"], "sourcesContent": ["/**\n * Enhanced Button Component\n * Extended Ant Design Button with theme integration and additional variants\n */\n\n'use client';\n\nimport React from 'react';\nimport { Button as AntButton, ButtonProps as AntButtonProps } from 'antd';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Extended button props\n */\nexport interface ButtonProps extends AntButtonProps {\n  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost';\n  fullWidth?: boolean;\n  compact?: boolean;\n}\n\n/**\n * Enhanced Button component with theme integration\n */\nexport function Button({ \n  variant = 'primary',\n  fullWidth = false,\n  compact = false,\n  className,\n  style,\n  children,\n  ...props \n}: ButtonProps) {\n  const themeStyles = useThemeStyles();\n\n  // Get variant-specific styles\n  const getVariantStyle = () => {\n    const baseStyle = {\n      transition: 'all 0.2s ease',\n      ...(fullWidth && { width: '100%' }),\n      ...(compact && { \n        height: '32px', \n        padding: '0 12px',\n        fontSize: '12px'\n      }),\n    };\n\n    switch (variant) {\n      case 'secondary':\n        return {\n          ...baseStyle,\n          backgroundColor: 'transparent',\n          borderColor: themeStyles.getBorderColor('primary'),\n          color: themeStyles.getTextColor('primary'),\n        };\n      case 'success':\n        return {\n          ...baseStyle,\n          backgroundColor: themeStyles.getColor('success'),\n          borderColor: themeStyles.getColor('success'),\n          color: 'white',\n        };\n      case 'warning':\n        return {\n          ...baseStyle,\n          backgroundColor: themeStyles.getColor('warning'),\n          borderColor: themeStyles.getColor('warning'),\n          color: 'white',\n        };\n      case 'error':\n        return {\n          ...baseStyle,\n          backgroundColor: themeStyles.getColor('error'),\n          borderColor: themeStyles.getColor('error'),\n          color: 'white',\n        };\n      case 'ghost':\n        return {\n          ...baseStyle,\n          backgroundColor: 'transparent',\n          borderColor: 'transparent',\n          color: themeStyles.getTextColor('secondary'),\n        };\n      default:\n        return baseStyle;\n    }\n  };\n\n  // Map variant to Ant Design type\n  const getAntType = (): AntButtonProps['type'] => {\n    switch (variant) {\n      case 'primary':\n        return 'primary';\n      case 'secondary':\n        return 'default';\n      case 'ghost':\n        return 'text';\n      default:\n        return 'primary';\n    }\n  };\n\n  return (\n    <AntButton\n      type={getAntType()}\n      className={className}\n      style={{\n        ...getVariantStyle(),\n        ...style,\n      }}\n      {...props}\n    >\n      {children}\n    </AntButton>\n  );\n}\n\n/**\n * Button group component\n */\nexport interface ButtonGroupProps {\n  children: React.ReactNode;\n  direction?: 'horizontal' | 'vertical';\n  spacing?: number;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function ButtonGroup({ \n  children, \n  direction = 'horizontal',\n  spacing = 8,\n  className,\n  style \n}: ButtonGroupProps) {\n  const groupStyle: React.CSSProperties = {\n    display: 'flex',\n    flexDirection: direction === 'vertical' ? 'column' : 'row',\n    gap: `${spacing}px`,\n    ...style,\n  };\n\n  return (\n    <div className={className} style={groupStyle}>\n      {children}\n    </div>\n  );\n}\n\n/**\n * Icon button component\n */\nexport interface IconButtonProps extends Omit<ButtonProps, 'children'> {\n  icon: React.ReactNode;\n  tooltip?: string;\n  size?: 'small' | 'medium' | 'large';\n}\n\nexport function IconButton({ \n  icon, \n  tooltip, \n  size = 'medium',\n  ...props \n}: IconButtonProps) {\n  const sizeMap = {\n    small: { width: '32px', height: '32px', padding: '0' },\n    medium: { width: '40px', height: '40px', padding: '0' },\n    large: { width: '48px', height: '48px', padding: '0' },\n  };\n\n  const buttonElement = (\n    <Button\n      style={{\n        ...sizeMap[size],\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n      }}\n      {...props}\n    >\n      {icon}\n    </Button>\n  );\n\n  if (tooltip) {\n    const { Tooltip } = require('antd');\n    return <Tooltip title={tooltip}>{buttonElement}</Tooltip>;\n  }\n\n  return buttonElement;\n}\n\n/**\n * Loading button component\n */\nexport interface LoadingButtonProps extends ButtonProps {\n  isLoading?: boolean;\n  loadingText?: string;\n}\n\nexport function LoadingButton({ \n  isLoading = false,\n  loadingText = 'Loading...',\n  children,\n  disabled,\n  ...props \n}: LoadingButtonProps) {\n  return (\n    <Button\n      loading={isLoading}\n      disabled={disabled || isLoading}\n      {...props}\n    >\n      {isLoading ? loadingText : children}\n    </Button>\n  );\n}\n\n/**\n * Confirm button component\n */\nexport interface ConfirmButtonProps extends ButtonProps {\n  onConfirm: () => void;\n  confirmTitle?: string;\n  confirmDescription?: string;\n  okText?: string;\n  cancelText?: string;\n}\n\nexport function ConfirmButton({\n  onConfirm,\n  confirmTitle = 'Are you sure?',\n  confirmDescription = 'This action cannot be undone.',\n  okText = 'Yes',\n  cancelText = 'No',\n  children,\n  ...props\n}: ConfirmButtonProps) {\n  const { Popconfirm } = require('antd');\n\n  return (\n    <Popconfirm\n      title={confirmTitle}\n      description={confirmDescription}\n      onConfirm={onConfirm}\n      okText={okText}\n      cancelText={cancelText}\n    >\n      <Button {...props}>\n        {children}\n      </Button>\n    </Popconfirm>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAMD;AAAA;AADA;AAHA;;;;AAkBO,SAAS,OAAO,EACrB,UAAU,SAAS,EACnB,YAAY,KAAK,EACjB,UAAU,KAAK,EACf,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OACS;IACZ,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,8BAA8B;IAC9B,MAAM,kBAAkB;QACtB,MAAM,YAAY;YAChB,YAAY;YACZ,GAAI,aAAa;gBAAE,OAAO;YAAO,CAAC;YAClC,GAAI,WAAW;gBACb,QAAQ;gBACR,SAAS;gBACT,UAAU;YACZ,CAAC;QACH;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,iBAAiB;oBACjB,aAAa,YAAY,cAAc,CAAC;oBACxC,OAAO,YAAY,YAAY,CAAC;gBAClC;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,iBAAiB,YAAY,QAAQ,CAAC;oBACtC,aAAa,YAAY,QAAQ,CAAC;oBAClC,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,iBAAiB,YAAY,QAAQ,CAAC;oBACtC,aAAa,YAAY,QAAQ,CAAC;oBAClC,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,iBAAiB,YAAY,QAAQ,CAAC;oBACtC,aAAa,YAAY,QAAQ,CAAC;oBAClC,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,iBAAiB;oBACjB,aAAa;oBACb,OAAO,YAAY,YAAY,CAAC;gBAClC;YACF;gBACE,OAAO;QACX;IACF;IAEA,iCAAiC;IACjC,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,kMAAA,CAAA,SAAS;QACR,MAAM;QACN,WAAW;QACX,OAAO;YACL,GAAG,iBAAiB;YACpB,GAAG,KAAK;QACV;QACC,GAAG,KAAK;kBAER;;;;;;AAGP;AAaO,SAAS,YAAY,EAC1B,QAAQ,EACR,YAAY,YAAY,EACxB,UAAU,CAAC,EACX,SAAS,EACT,KAAK,EACY;IACjB,MAAM,aAAkC;QACtC,SAAS;QACT,eAAe,cAAc,aAAa,WAAW;QACrD,KAAK,GAAG,QAAQ,EAAE,CAAC;QACnB,GAAG,KAAK;IACV;IAEA,qBACE,8OAAC;QAAI,WAAW;QAAW,OAAO;kBAC/B;;;;;;AAGP;AAWO,SAAS,WAAW,EACzB,IAAI,EACJ,OAAO,EACP,OAAO,QAAQ,EACf,GAAG,OACa;IAChB,MAAM,UAAU;QACd,OAAO;YAAE,OAAO;YAAQ,QAAQ;YAAQ,SAAS;QAAI;QACrD,QAAQ;YAAE,OAAO;YAAQ,QAAQ;YAAQ,SAAS;QAAI;QACtD,OAAO;YAAE,OAAO;YAAQ,QAAQ;YAAQ,SAAS;QAAI;IACvD;IAEA,MAAM,8BACJ,8OAAC;QACC,OAAO;YACL,GAAG,OAAO,CAAC,KAAK;YAChB,SAAS;YACT,YAAY;YACZ,gBAAgB;QAClB;QACC,GAAG,KAAK;kBAER;;;;;;IAIL,IAAI,SAAS;QACX,MAAM,EAAE,OAAO,EAAE;QACjB,qBAAO,8OAAC;YAAQ,OAAO;sBAAU;;;;;;IACnC;IAEA,OAAO;AACT;AAUO,SAAS,cAAc,EAC5B,YAAY,KAAK,EACjB,cAAc,YAAY,EAC1B,QAAQ,EACR,QAAQ,EACR,GAAG,OACgB;IACnB,qBACE,8OAAC;QACC,SAAS;QACT,UAAU,YAAY;QACrB,GAAG,KAAK;kBAER,YAAY,cAAc;;;;;;AAGjC;AAaO,SAAS,cAAc,EAC5B,SAAS,EACT,eAAe,eAAe,EAC9B,qBAAqB,+BAA+B,EACpD,SAAS,KAAK,EACd,aAAa,IAAI,EACjB,QAAQ,EACR,GAAG,OACgB;IACnB,MAAM,EAAE,UAAU,EAAE;IAEpB,qBACE,8OAAC;QACC,OAAO;QACP,aAAa;QACb,WAAW;QACX,QAAQ;QACR,YAAY;kBAEZ,cAAA,8OAAC;YAAQ,GAAG,KAAK;sBACd;;;;;;;;;;;AAIT"}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/ui/input.tsx"], "sourcesContent": ["/**\n * Enhanced Input Components\n * Extended Ant Design Input with theme integration and additional features\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { \n  Input as AntInput, \n  InputProps as AntInputProps,\n  Select as AntSelect,\n  SelectProps as AntSelectProps,\n  DatePicker as AntDatePicker,\n  DatePickerProps as AntDatePickerProps\n} from 'antd';\nimport { SearchOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\n\nconst { TextArea, Password } = AntInput;\nconst { Option } = AntSelect;\n\n/**\n * Enhanced input props\n */\nexport interface InputProps extends AntInputProps {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  fullWidth?: boolean;\n  compact?: boolean;\n}\n\n/**\n * Enhanced Input component\n */\nexport function Input({ \n  label,\n  error,\n  helperText,\n  fullWidth = false,\n  compact = false,\n  className,\n  style,\n  ...props \n}: InputProps) {\n  const themeStyles = useThemeStyles();\n\n  const inputStyle: React.CSSProperties = {\n    ...(fullWidth && { width: '100%' }),\n    ...(compact && { height: '32px' }),\n    ...style,\n  };\n\n  const containerStyle: React.CSSProperties = {\n    width: fullWidth ? '100%' : 'auto',\n  };\n\n  return (\n    <div style={containerStyle}>\n      {label && (\n        <label style={{ \n          display: 'block', \n          marginBottom: '4px',\n          color: themeStyles.getTextColor('primary'),\n          fontSize: '14px',\n          fontWeight: 500,\n        }}>\n          {label}\n        </label>\n      )}\n      <AntInput\n        className={className}\n        style={inputStyle}\n        status={error ? 'error' : undefined}\n        {...props}\n      />\n      {(error || helperText) && (\n        <div style={{ \n          marginTop: '4px',\n          fontSize: '12px',\n          color: error ? themeStyles.getColor('error') : themeStyles.getTextColor('secondary'),\n        }}>\n          {error || helperText}\n        </div>\n      )}\n    </div>\n  );\n}\n\n/**\n * Search input component\n */\nexport interface SearchInputProps extends Omit<InputProps, 'prefix'> {\n  onSearch?: (value: string) => void;\n  searchButton?: boolean;\n}\n\nexport function SearchInput({ \n  onSearch,\n  searchButton = false,\n  ...props \n}: SearchInputProps) {\n  if (searchButton) {\n    return (\n      <Input\n        {...props}\n        suffix={\n          <SearchOutlined \n            style={{ cursor: 'pointer' }}\n            onClick={() => onSearch?.(props.value as string || '')}\n          />\n        }\n      />\n    );\n  }\n\n  return (\n    <AntInput.Search\n      onSearch={onSearch}\n      {...props}\n    />\n  );\n}\n\n/**\n * Password input component\n */\nexport interface PasswordInputProps extends InputProps {\n  showToggle?: boolean;\n  strength?: boolean;\n}\n\nexport function PasswordInput({ \n  showToggle = true,\n  strength = false,\n  ...props \n}: PasswordInputProps) {\n  const [visible, setVisible] = useState(false);\n\n  const getPasswordStrength = (password: string): { level: number; text: string; color: string } => {\n    if (!password) return { level: 0, text: '', color: '' };\n    \n    let score = 0;\n    if (password.length >= 8) score++;\n    if (/[a-z]/.test(password)) score++;\n    if (/[A-Z]/.test(password)) score++;\n    if (/[0-9]/.test(password)) score++;\n    if (/[^A-Za-z0-9]/.test(password)) score++;\n\n    const levels = [\n      { level: 0, text: '', color: '' },\n      { level: 1, text: 'Very Weak', color: '#ff4d4f' },\n      { level: 2, text: 'Weak', color: '#ff7a45' },\n      { level: 3, text: 'Fair', color: '#ffa940' },\n      { level: 4, text: 'Good', color: '#52c41a' },\n      { level: 5, text: 'Strong', color: '#389e0d' },\n    ];\n\n    return levels[score];\n  };\n\n  const passwordStrength = strength ? getPasswordStrength(props.value as string || '') : null;\n\n  return (\n    <div style={{ width: props.fullWidth ? '100%' : 'auto' }}>\n      {showToggle ? (\n        <Input\n          type={visible ? 'text' : 'password'}\n          suffix={\n            visible ? (\n              <EyeInvisibleOutlined onClick={() => setVisible(false)} />\n            ) : (\n              <EyeOutlined onClick={() => setVisible(true)} />\n            )\n          }\n          {...props}\n        />\n      ) : (\n        <Password {...props} />\n      )}\n      {strength && passwordStrength && passwordStrength.level > 0 && (\n        <div style={{ marginTop: '4px' }}>\n          <div style={{ \n            height: '4px', \n            backgroundColor: '#f0f0f0', \n            borderRadius: '2px',\n            overflow: 'hidden',\n          }}>\n            <div style={{\n              height: '100%',\n              width: `${(passwordStrength.level / 5) * 100}%`,\n              backgroundColor: passwordStrength.color,\n              transition: 'all 0.3s ease',\n            }} />\n          </div>\n          <div style={{ \n            marginTop: '2px',\n            fontSize: '12px',\n            color: passwordStrength.color,\n          }}>\n            {passwordStrength.text}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\n/**\n * Textarea component\n */\nexport interface TextareaProps extends InputProps {\n  rows?: number;\n  autoSize?: boolean | { minRows?: number; maxRows?: number };\n  showCount?: boolean;\n  maxLength?: number;\n}\n\nexport function Textarea({ \n  rows = 4,\n  autoSize = false,\n  showCount = false,\n  maxLength,\n  ...props \n}: TextareaProps) {\n  return (\n    <Input\n      as={TextArea}\n      rows={rows}\n      autoSize={autoSize}\n      showCount={showCount}\n      maxLength={maxLength}\n      {...props}\n    />\n  );\n}\n\n/**\n * Select component\n */\nexport interface SelectProps extends AntSelectProps {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  fullWidth?: boolean;\n  compact?: boolean;\n  options?: Array<{ label: string; value: any; disabled?: boolean }>;\n}\n\nexport function Select({ \n  label,\n  error,\n  helperText,\n  fullWidth = false,\n  compact = false,\n  options = [],\n  children,\n  style,\n  ...props \n}: SelectProps) {\n  const themeStyles = useThemeStyles();\n\n  const selectStyle: React.CSSProperties = {\n    ...(fullWidth && { width: '100%' }),\n    ...(compact && { height: '32px' }),\n    ...style,\n  };\n\n  return (\n    <div style={{ width: fullWidth ? '100%' : 'auto' }}>\n      {label && (\n        <label style={{ \n          display: 'block', \n          marginBottom: '4px',\n          color: themeStyles.getTextColor('primary'),\n          fontSize: '14px',\n          fontWeight: 500,\n        }}>\n          {label}\n        </label>\n      )}\n      <AntSelect\n        style={selectStyle}\n        status={error ? 'error' : undefined}\n        {...props}\n      >\n        {options.length > 0 \n          ? options.map(option => (\n              <Option \n                key={option.value} \n                value={option.value}\n                disabled={option.disabled}\n              >\n                {option.label}\n              </Option>\n            ))\n          : children\n        }\n      </AntSelect>\n      {(error || helperText) && (\n        <div style={{ \n          marginTop: '4px',\n          fontSize: '12px',\n          color: error ? themeStyles.getColor('error') : themeStyles.getTextColor('secondary'),\n        }}>\n          {error || helperText}\n        </div>\n      )}\n    </div>\n  );\n}\n\n/**\n * Date picker component\n */\nexport interface DatePickerProps extends AntDatePickerProps {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  fullWidth?: boolean;\n  compact?: boolean;\n}\n\nexport function DatePicker({ \n  label,\n  error,\n  helperText,\n  fullWidth = false,\n  compact = false,\n  style,\n  ...props \n}: DatePickerProps) {\n  const themeStyles = useThemeStyles();\n\n  const pickerStyle: React.CSSProperties = {\n    ...(fullWidth && { width: '100%' }),\n    ...(compact && { height: '32px' }),\n    ...style,\n  };\n\n  return (\n    <div style={{ width: fullWidth ? '100%' : 'auto' }}>\n      {label && (\n        <label style={{ \n          display: 'block', \n          marginBottom: '4px',\n          color: themeStyles.getTextColor('primary'),\n          fontSize: '14px',\n          fontWeight: 500,\n        }}>\n          {label}\n        </label>\n      )}\n      <AntDatePicker\n        style={pickerStyle}\n        status={error ? 'error' : undefined}\n        {...props}\n      />\n      {(error || helperText) && (\n        <div style={{ \n          marginTop: '4px',\n          fontSize: '12px',\n          color: error ? themeStyles.getColor('error') : themeStyles.getTextColor('secondary'),\n        }}>\n          {error || helperText}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAID;AAUA;AATA;AAAA;AASA;AADA;AAAA;AAAA;AARA;AAHA;;;;;;AAcA,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,gLAAA,CAAA,QAAQ;AACvC,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAS;AAgBrB,SAAS,MAAM,EACpB,KAAK,EACL,KAAK,EACL,UAAU,EACV,YAAY,KAAK,EACjB,UAAU,KAAK,EACf,SAAS,EACT,KAAK,EACL,GAAG,OACQ;IACX,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,aAAkC;QACtC,GAAI,aAAa;YAAE,OAAO;QAAO,CAAC;QAClC,GAAI,WAAW;YAAE,QAAQ;QAAO,CAAC;QACjC,GAAG,KAAK;IACV;IAEA,MAAM,iBAAsC;QAC1C,OAAO,YAAY,SAAS;IAC9B;IAEA,qBACE,8OAAC;QAAI,OAAO;;YACT,uBACC,8OAAC;gBAAM,OAAO;oBACZ,SAAS;oBACT,cAAc;oBACd,OAAO,YAAY,YAAY,CAAC;oBAChC,UAAU;oBACV,YAAY;gBACd;0BACG;;;;;;0BAGL,8OAAC,gLAAA,CAAA,QAAQ;gBACP,WAAW;gBACX,OAAO;gBACP,QAAQ,QAAQ,UAAU;gBACzB,GAAG,KAAK;;;;;;YAEV,CAAC,SAAS,UAAU,mBACnB,8OAAC;gBAAI,OAAO;oBACV,WAAW;oBACX,UAAU;oBACV,OAAO,QAAQ,YAAY,QAAQ,CAAC,WAAW,YAAY,YAAY,CAAC;gBAC1E;0BACG,SAAS;;;;;;;;;;;;AAKpB;AAUO,SAAS,YAAY,EAC1B,QAAQ,EACR,eAAe,KAAK,EACpB,GAAG,OACc;IACjB,IAAI,cAAc;QAChB,qBACE,8OAAC;YACE,GAAG,KAAK;YACT,sBACE,8OAAC,sNAAA,CAAA,iBAAc;gBACb,OAAO;oBAAE,QAAQ;gBAAU;gBAC3B,SAAS,IAAM,WAAW,MAAM,KAAK,IAAc;;;;;;;;;;;IAK7D;IAEA,qBACE,8OAAC,gLAAA,CAAA,QAAQ,CAAC,MAAM;QACd,UAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAUO,SAAS,cAAc,EAC5B,aAAa,IAAI,EACjB,WAAW,KAAK,EAChB,GAAG,OACgB;IACnB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,UAAU,OAAO;YAAE,OAAO;YAAG,MAAM;YAAI,OAAO;QAAG;QAEtD,IAAI,QAAQ;QACZ,IAAI,SAAS,MAAM,IAAI,GAAG;QAC1B,IAAI,QAAQ,IAAI,CAAC,WAAW;QAC5B,IAAI,QAAQ,IAAI,CAAC,WAAW;QAC5B,IAAI,QAAQ,IAAI,CAAC,WAAW;QAC5B,IAAI,eAAe,IAAI,CAAC,WAAW;QAEnC,MAAM,SAAS;YACb;gBAAE,OAAO;gBAAG,MAAM;gBAAI,OAAO;YAAG;YAChC;gBAAE,OAAO;gBAAG,MAAM;gBAAa,OAAO;YAAU;YAChD;gBAAE,OAAO;gBAAG,MAAM;gBAAQ,OAAO;YAAU;YAC3C;gBAAE,OAAO;gBAAG,MAAM;gBAAQ,OAAO;YAAU;YAC3C;gBAAE,OAAO;gBAAG,MAAM;gBAAQ,OAAO;YAAU;YAC3C;gBAAE,OAAO;gBAAG,MAAM;gBAAU,OAAO;YAAU;SAC9C;QAED,OAAO,MAAM,CAAC,MAAM;IACtB;IAEA,MAAM,mBAAmB,WAAW,oBAAoB,MAAM,KAAK,IAAc,MAAM;IAEvF,qBACE,8OAAC;QAAI,OAAO;YAAE,OAAO,MAAM,SAAS,GAAG,SAAS;QAAO;;YACpD,2BACC,8OAAC;gBACC,MAAM,UAAU,SAAS;gBACzB,QACE,wBACE,8OAAC,kOAAA,CAAA,uBAAoB;oBAAC,SAAS,IAAM,WAAW;;;;;2CAEhD,8OAAC,gNAAA,CAAA,cAAW;oBAAC,SAAS,IAAM,WAAW;;;;;;gBAG1C,GAAG,KAAK;;;;;qCAGX,8OAAC;gBAAU,GAAG,KAAK;;;;;;YAEpB,YAAY,oBAAoB,iBAAiB,KAAK,GAAG,mBACxD,8OAAC;gBAAI,OAAO;oBAAE,WAAW;gBAAM;;kCAC7B,8OAAC;wBAAI,OAAO;4BACV,QAAQ;4BACR,iBAAiB;4BACjB,cAAc;4BACd,UAAU;wBACZ;kCACE,cAAA,8OAAC;4BAAI,OAAO;gCACV,QAAQ;gCACR,OAAO,GAAG,AAAC,iBAAiB,KAAK,GAAG,IAAK,IAAI,CAAC,CAAC;gCAC/C,iBAAiB,iBAAiB,KAAK;gCACvC,YAAY;4BACd;;;;;;;;;;;kCAEF,8OAAC;wBAAI,OAAO;4BACV,WAAW;4BACX,UAAU;4BACV,OAAO,iBAAiB,KAAK;wBAC/B;kCACG,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;AAMlC;AAYO,SAAS,SAAS,EACvB,OAAO,CAAC,EACR,WAAW,KAAK,EAChB,YAAY,KAAK,EACjB,SAAS,EACT,GAAG,OACW;IACd,qBACE,8OAAC;QACC,IAAI;QACJ,MAAM;QACN,UAAU;QACV,WAAW;QACX,WAAW;QACV,GAAG,KAAK;;;;;;AAGf;AAcO,SAAS,OAAO,EACrB,KAAK,EACL,KAAK,EACL,UAAU,EACV,YAAY,KAAK,EACjB,UAAU,KAAK,EACf,UAAU,EAAE,EACZ,QAAQ,EACR,KAAK,EACL,GAAG,OACS;IACZ,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,cAAmC;QACvC,GAAI,aAAa;YAAE,OAAO;QAAO,CAAC;QAClC,GAAI,WAAW;YAAE,QAAQ;QAAO,CAAC;QACjC,GAAG,KAAK;IACV;IAEA,qBACE,8OAAC;QAAI,OAAO;YAAE,OAAO,YAAY,SAAS;QAAO;;YAC9C,uBACC,8OAAC;gBAAM,OAAO;oBACZ,SAAS;oBACT,cAAc;oBACd,OAAO,YAAY,YAAY,CAAC;oBAChC,UAAU;oBACV,YAAY;gBACd;0BACG;;;;;;0BAGL,8OAAC,kLAAA,CAAA,SAAS;gBACR,OAAO;gBACP,QAAQ,QAAQ,UAAU;gBACzB,GAAG,KAAK;0BAER,QAAQ,MAAM,GAAG,IACd,QAAQ,GAAG,CAAC,CAAA,uBACV,8OAAC;wBAEC,OAAO,OAAO,KAAK;wBACnB,UAAU,OAAO,QAAQ;kCAExB,OAAO,KAAK;uBAJR,OAAO,KAAK;;;;gCAOrB;;;;;;YAGL,CAAC,SAAS,UAAU,mBACnB,8OAAC;gBAAI,OAAO;oBACV,WAAW;oBACX,UAAU;oBACV,OAAO,QAAQ,YAAY,QAAQ,CAAC,WAAW,YAAY,YAAY,CAAC;gBAC1E;0BACG,SAAS;;;;;;;;;;;;AAKpB;AAaO,SAAS,WAAW,EACzB,KAAK,EACL,KAAK,EACL,UAAU,EACV,YAAY,KAAK,EACjB,UAAU,KAAK,EACf,KAAK,EACL,GAAG,OACa;IAChB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,cAAmC;QACvC,GAAI,aAAa;YAAE,OAAO;QAAO,CAAC;QAClC,GAAI,WAAW;YAAE,QAAQ;QAAO,CAAC;QACjC,GAAG,KAAK;IACV;IAEA,qBACE,8OAAC;QAAI,OAAO;YAAE,OAAO,YAAY,SAAS;QAAO;;YAC9C,uBACC,8OAAC;gBAAM,OAAO;oBACZ,SAAS;oBACT,cAAc;oBACd,OAAO,YAAY,YAAY,CAAC;oBAChC,UAAU;oBACV,YAAY;gBACd;0BACG;;;;;;0BAGL,8OAAC,8LAAA,CAAA,aAAa;gBACZ,OAAO;gBACP,QAAQ,QAAQ,UAAU;gBACzB,GAAG,KAAK;;;;;;YAEV,CAAC,SAAS,UAAU,mBACnB,8OAAC;gBAAI,OAAO;oBACV,WAAW;oBACX,UAAU;oBACV,OAAO,QAAQ,YAAY,QAAQ,CAAC,WAAW,YAAY,YAAY,CAAC;gBAC1E;0BACG,SAAS;;;;;;;;;;;;AAKpB"}}, {"offset": {"line": 602, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/ui/card.tsx"], "sourcesContent": ["/**\n * Enhanced Card Component\n * Extended Ant Design Card with theme integration and additional variants\n */\n\n'use client';\n\nimport React from 'react';\nimport { Card as AntCard, CardProps as AntCardProps, Skeleton, Empty } from 'antd';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Enhanced card props\n */\nexport interface CardProps extends AntCardProps {\n  variant?: 'default' | 'outlined' | 'elevated' | 'flat';\n  padding?: 'none' | 'small' | 'medium' | 'large';\n  loading?: boolean;\n  empty?: boolean;\n  emptyText?: string;\n  emptyDescription?: string;\n  hover?: boolean;\n  clickable?: boolean;\n  onClick?: () => void;\n}\n\n/**\n * Enhanced Card component\n */\nexport function Card({ \n  variant = 'default',\n  padding = 'medium',\n  loading = false,\n  empty = false,\n  emptyText = 'No data',\n  emptyDescription,\n  hover = false,\n  clickable = false,\n  onClick,\n  className,\n  style,\n  children,\n  ...props \n}: CardProps) {\n  const themeStyles = useThemeStyles();\n\n  // Get variant-specific styles\n  const getVariantStyle = (): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      transition: 'all 0.2s ease',\n      ...(clickable && { cursor: 'pointer' }),\n      ...(hover && {\n        ':hover': {\n          transform: 'translateY(-2px)',\n          boxShadow: themeStyles.colors.background.elevated,\n        }\n      }),\n    };\n\n    switch (variant) {\n      case 'outlined':\n        return {\n          ...baseStyle,\n          border: `1px solid ${themeStyles.getBorderColor('primary')}`,\n          boxShadow: 'none',\n        };\n      case 'elevated':\n        return {\n          ...baseStyle,\n          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n          border: 'none',\n        };\n      case 'flat':\n        return {\n          ...baseStyle,\n          boxShadow: 'none',\n          border: 'none',\n          backgroundColor: 'transparent',\n        };\n      default:\n        return baseStyle;\n    }\n  };\n\n  // Get padding styles\n  const getPaddingStyle = (): React.CSSProperties => {\n    const paddingMap = {\n      none: { padding: 0 },\n      small: { padding: '12px' },\n      medium: { padding: '16px' },\n      large: { padding: '24px' },\n    };\n    return paddingMap[padding];\n  };\n\n  const cardStyle: React.CSSProperties = {\n    ...getVariantStyle(),\n    ...style,\n  };\n\n  const bodyStyle: React.CSSProperties = {\n    ...getPaddingStyle(),\n    ...props.bodyStyle,\n  };\n\n  // Handle loading state\n  if (loading) {\n    return (\n      <AntCard\n        className={className}\n        style={cardStyle}\n        bodyStyle={bodyStyle}\n        {...props}\n      >\n        <Skeleton active />\n      </AntCard>\n    );\n  }\n\n  // Handle empty state\n  if (empty) {\n    return (\n      <AntCard\n        className={className}\n        style={cardStyle}\n        bodyStyle={bodyStyle}\n        {...props}\n      >\n        <Empty \n          description={emptyText}\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n        >\n          {emptyDescription && (\n            <div style={{ \n              marginTop: '8px',\n              color: themeStyles.getTextColor('secondary'),\n              fontSize: '12px',\n            }}>\n              {emptyDescription}\n            </div>\n          )}\n        </Empty>\n      </AntCard>\n    );\n  }\n\n  return (\n    <AntCard\n      className={className}\n      style={cardStyle}\n      bodyStyle={bodyStyle}\n      onClick={clickable ? onClick : undefined}\n      {...props}\n    >\n      {children}\n    </AntCard>\n  );\n}\n\n/**\n * Stat card component for displaying statistics\n */\nexport interface StatCardProps {\n  title: string;\n  value: string | number;\n  subtitle?: string;\n  icon?: React.ReactNode;\n  trend?: {\n    value: number;\n    isPositive: boolean;\n  };\n  loading?: boolean;\n  onClick?: () => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function StatCard({\n  title,\n  value,\n  subtitle,\n  icon,\n  trend,\n  loading = false,\n  onClick,\n  className,\n  style,\n}: StatCardProps) {\n  const themeStyles = useThemeStyles();\n\n  if (loading) {\n    return (\n      <Card className={className} style={style} loading />\n    );\n  }\n\n  return (\n    <Card \n      className={className} \n      style={style}\n      clickable={!!onClick}\n      onClick={onClick}\n      hover={!!onClick}\n    >\n      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <div style={{ flex: 1 }}>\n          <div style={{ \n            fontSize: '14px',\n            color: themeStyles.getTextColor('secondary'),\n            marginBottom: '4px',\n          }}>\n            {title}\n          </div>\n          <div style={{ \n            fontSize: '24px',\n            fontWeight: 'bold',\n            color: themeStyles.getTextColor('primary'),\n            marginBottom: subtitle || trend ? '4px' : 0,\n          }}>\n            {value}\n          </div>\n          {subtitle && (\n            <div style={{ \n              fontSize: '12px',\n              color: themeStyles.getTextColor('tertiary'),\n            }}>\n              {subtitle}\n            </div>\n          )}\n          {trend && (\n            <div style={{ \n              fontSize: '12px',\n              color: trend.isPositive ? themeStyles.getColor('success') : themeStyles.getColor('error'),\n              marginTop: '4px',\n            }}>\n              {trend.isPositive ? '↗' : '↘'} {Math.abs(trend.value)}%\n            </div>\n          )}\n        </div>\n        {icon && (\n          <div style={{ \n            fontSize: '32px',\n            color: themeStyles.getColor('primary'),\n            opacity: 0.7,\n          }}>\n            {icon}\n          </div>\n        )}\n      </div>\n    </Card>\n  );\n}\n\n/**\n * Info card component for displaying information with actions\n */\nexport interface InfoCardProps {\n  title: string;\n  description?: string;\n  icon?: React.ReactNode;\n  actions?: React.ReactNode[];\n  extra?: React.ReactNode;\n  children?: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function InfoCard({\n  title,\n  description,\n  icon,\n  actions,\n  extra,\n  children,\n  className,\n  style,\n}: InfoCardProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <Card \n      className={className} \n      style={style}\n      title={\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          {icon && (\n            <div style={{ \n              fontSize: '20px',\n              color: themeStyles.getColor('primary'),\n            }}>\n              {icon}\n            </div>\n          )}\n          <div>\n            <div style={{ \n              fontSize: '16px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n            }}>\n              {title}\n            </div>\n            {description && (\n              <div style={{ \n                fontSize: '12px',\n                color: themeStyles.getTextColor('secondary'),\n                marginTop: '2px',\n              }}>\n                {description}\n              </div>\n            )}\n          </div>\n        </div>\n      }\n      extra={extra}\n      actions={actions}\n    >\n      {children}\n    </Card>\n  );\n}\n\n/**\n * Grid card component for displaying items in a grid\n */\nexport interface GridCardProps {\n  title?: string;\n  items: Array<{\n    id: string | number;\n    title: string;\n    description?: string;\n    icon?: React.ReactNode;\n    onClick?: () => void;\n  }>;\n  columns?: number;\n  loading?: boolean;\n  empty?: boolean;\n  emptyText?: string;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function GridCard({\n  title,\n  items,\n  columns = 3,\n  loading = false,\n  empty = false,\n  emptyText = 'No items',\n  className,\n  style,\n}: GridCardProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <Card \n      title={title}\n      className={className} \n      style={style}\n      loading={loading}\n      empty={empty || items.length === 0}\n      emptyText={emptyText}\n    >\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: `repeat(${columns}, 1fr)`,\n        gap: '16px',\n      }}>\n        {items.map((item) => (\n          <div\n            key={item.id}\n            style={{\n              padding: '12px',\n              border: `1px solid ${themeStyles.getBorderColor('primary')}`,\n              borderRadius: '6px',\n              cursor: item.onClick ? 'pointer' : 'default',\n              transition: 'all 0.2s ease',\n              ':hover': item.onClick ? {\n                borderColor: themeStyles.getColor('primary'),\n                backgroundColor: themeStyles.getBackgroundColor('elevated'),\n              } : {},\n            }}\n            onClick={item.onClick}\n          >\n            {item.icon && (\n              <div style={{ \n                fontSize: '24px',\n                color: themeStyles.getColor('primary'),\n                marginBottom: '8px',\n              }}>\n                {item.icon}\n              </div>\n            )}\n            <div style={{ \n              fontSize: '14px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n              marginBottom: '4px',\n            }}>\n              {item.title}\n            </div>\n            {item.description && (\n              <div style={{ \n                fontSize: '12px',\n                color: themeStyles.getTextColor('secondary'),\n              }}>\n                {item.description}\n              </div>\n            )}\n          </div>\n        ))}\n      </div>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAMD;AAAA;AADA;AAAA;AAAA;AAHA;;;;AAwBO,SAAS,KAAK,EACnB,UAAU,SAAS,EACnB,UAAU,QAAQ,EAClB,UAAU,KAAK,EACf,QAAQ,KAAK,EACb,YAAY,SAAS,EACrB,gBAAgB,EAChB,QAAQ,KAAK,EACb,YAAY,KAAK,EACjB,OAAO,EACP,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OACO;IACV,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,8BAA8B;IAC9B,MAAM,kBAAkB;QACtB,MAAM,YAAiC;YACrC,YAAY;YACZ,GAAI,aAAa;gBAAE,QAAQ;YAAU,CAAC;YACtC,GAAI,SAAS;gBACX,UAAU;oBACR,WAAW;oBACX,WAAW,YAAY,MAAM,CAAC,UAAU,CAAC,QAAQ;gBACnD;YACF,CAAC;QACH;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,QAAQ,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;oBAC5D,WAAW;gBACb;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,WAAW;oBACX,QAAQ;gBACV;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,WAAW;oBACX,QAAQ;oBACR,iBAAiB;gBACnB;YACF;gBACE,OAAO;QACX;IACF;IAEA,qBAAqB;IACrB,MAAM,kBAAkB;QACtB,MAAM,aAAa;YACjB,MAAM;gBAAE,SAAS;YAAE;YACnB,OAAO;gBAAE,SAAS;YAAO;YACzB,QAAQ;gBAAE,SAAS;YAAO;YAC1B,OAAO;gBAAE,SAAS;YAAO;QAC3B;QACA,OAAO,UAAU,CAAC,QAAQ;IAC5B;IAEA,MAAM,YAAiC;QACrC,GAAG,iBAAiB;QACpB,GAAG,KAAK;IACV;IAEA,MAAM,YAAiC;QACrC,GAAG,iBAAiB;QACpB,GAAG,MAAM,SAAS;IACpB;IAEA,uBAAuB;IACvB,IAAI,SAAS;QACX,qBACE,8OAAC,8KAAA,CAAA,OAAO;YACN,WAAW;YACX,OAAO;YACP,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC,sLAAA,CAAA,WAAQ;gBAAC,MAAM;;;;;;;;;;;IAGtB;IAEA,qBAAqB;IACrB,IAAI,OAAO;QACT,qBACE,8OAAC,8KAAA,CAAA,OAAO;YACN,WAAW;YACX,OAAO;YACP,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC,gLAAA,CAAA,QAAK;gBACJ,aAAa;gBACb,OAAO,gLAAA,CAAA,QAAK,CAAC,sBAAsB;0BAElC,kCACC,8OAAC;oBAAI,OAAO;wBACV,WAAW;wBACX,OAAO,YAAY,YAAY,CAAC;wBAChC,UAAU;oBACZ;8BACG;;;;;;;;;;;;;;;;IAMb;IAEA,qBACE,8OAAC,8KAAA,CAAA,OAAO;QACN,WAAW;QACX,OAAO;QACP,WAAW;QACX,SAAS,YAAY,UAAU;QAC9B,GAAG,KAAK;kBAER;;;;;;AAGP;AAoBO,SAAS,SAAS,EACvB,KAAK,EACL,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,UAAU,KAAK,EACf,OAAO,EACP,SAAS,EACT,KAAK,EACS;IACd,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,IAAI,SAAS;QACX,qBACE,8OAAC;YAAK,WAAW;YAAW,OAAO;YAAO,OAAO;;;;;;IAErD;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;QACP,WAAW,CAAC,CAAC;QACb,SAAS;QACT,OAAO,CAAC,CAAC;kBAET,cAAA,8OAAC;YAAI,OAAO;gBAAE,SAAS;gBAAQ,YAAY;gBAAU,gBAAgB;YAAgB;;8BACnF,8OAAC;oBAAI,OAAO;wBAAE,MAAM;oBAAE;;sCACpB,8OAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;gCAChC,cAAc;4BAChB;sCACG;;;;;;sCAEH,8OAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,YAAY;gCACZ,OAAO,YAAY,YAAY,CAAC;gCAChC,cAAc,YAAY,QAAQ,QAAQ;4BAC5C;sCACG;;;;;;wBAEF,0BACC,8OAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;4BAClC;sCACG;;;;;;wBAGJ,uBACC,8OAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,MAAM,UAAU,GAAG,YAAY,QAAQ,CAAC,aAAa,YAAY,QAAQ,CAAC;gCACjF,WAAW;4BACb;;gCACG,MAAM,UAAU,GAAG,MAAM;gCAAI;gCAAE,KAAK,GAAG,CAAC,MAAM,KAAK;gCAAE;;;;;;;;;;;;;gBAI3D,sBACC,8OAAC;oBAAI,OAAO;wBACV,UAAU;wBACV,OAAO,YAAY,QAAQ,CAAC;wBAC5B,SAAS;oBACX;8BACG;;;;;;;;;;;;;;;;;AAMb;AAgBO,SAAS,SAAS,EACvB,KAAK,EACL,WAAW,EACX,IAAI,EACJ,OAAO,EACP,KAAK,EACL,QAAQ,EACR,SAAS,EACT,KAAK,EACS;IACd,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;QACP,qBACE,8OAAC;YAAI,OAAO;gBAAE,SAAS;gBAAQ,YAAY;gBAAU,KAAK;YAAO;;gBAC9D,sBACC,8OAAC;oBAAI,OAAO;wBACV,UAAU;wBACV,OAAO,YAAY,QAAQ,CAAC;oBAC9B;8BACG;;;;;;8BAGL,8OAAC;;sCACC,8OAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,YAAY;gCACZ,OAAO,YAAY,YAAY,CAAC;4BAClC;sCACG;;;;;;wBAEF,6BACC,8OAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;gCAChC,WAAW;4BACb;sCACG;;;;;;;;;;;;;;;;;;QAMX,OAAO;QACP,SAAS;kBAER;;;;;;AAGP;AAsBO,SAAS,SAAS,EACvB,KAAK,EACL,KAAK,EACL,UAAU,CAAC,EACX,UAAU,KAAK,EACf,QAAQ,KAAK,EACb,YAAY,UAAU,EACtB,SAAS,EACT,KAAK,EACS;IACd,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,8OAAC;QACC,OAAO;QACP,WAAW;QACX,OAAO;QACP,SAAS;QACT,OAAO,SAAS,MAAM,MAAM,KAAK;QACjC,WAAW;kBAEX,cAAA,8OAAC;YAAI,OAAO;gBACV,SAAS;gBACT,qBAAqB,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC;gBAC9C,KAAK;YACP;sBACG,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;oBAEC,OAAO;wBACL,SAAS;wBACT,QAAQ,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;wBAC5D,cAAc;wBACd,QAAQ,KAAK,OAAO,GAAG,YAAY;wBACnC,YAAY;wBACZ,UAAU,KAAK,OAAO,GAAG;4BACvB,aAAa,YAAY,QAAQ,CAAC;4BAClC,iBAAiB,YAAY,kBAAkB,CAAC;wBAClD,IAAI,CAAC;oBACP;oBACA,SAAS,KAAK,OAAO;;wBAEpB,KAAK,IAAI,kBACR,8OAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,YAAY,QAAQ,CAAC;gCAC5B,cAAc;4BAChB;sCACG,KAAK,IAAI;;;;;;sCAGd,8OAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,YAAY;gCACZ,OAAO,YAAY,YAAY,CAAC;gCAChC,cAAc;4BAChB;sCACG,KAAK,KAAK;;;;;;wBAEZ,KAAK,WAAW,kBACf,8OAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;4BAClC;sCACG,KAAK,WAAW;;;;;;;mBApChB,KAAK,EAAE;;;;;;;;;;;;;;;AA4CxB"}}, {"offset": {"line": 1025, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1031, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/ui/index.ts"], "sourcesContent": ["/**\n * UI Components Index\n * Export all common UI components\n */\n\n// Button components\nexport * from './button';\n\n// Input components\nexport * from './input';\n\n// Card components\nexport * from './card';\n\n// Re-export commonly used Ant Design components for convenience\nexport {\n  Typography,\n  Space,\n  Divider,\n  Tag,\n  Badge,\n  Avatar,\n  Tooltip,\n  Popover,\n  Dropdown,\n  Menu,\n  Breadcrumb,\n  Steps,\n  Progress,\n  Spin,\n  Alert,\n  Message,\n  Notification,\n  Modal,\n  Drawer,\n  Popconfirm,\n} from 'antd';\n\n/**\n * UI components metadata\n */\nexport const UI_COMPONENTS_VERSION = '1.0.0';\nexport const UI_COMPONENTS_NAME = 'APISportsGame UI Components';\n\n/**\n * Setup function for UI components\n */\nexport function setupUIComponents() {\n  console.log(`${UI_COMPONENTS_NAME} v${UI_COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,oBAAoB;;;;;;;;;;AAoCb,MAAM,wBAAwB;AAC9B,MAAM,qBAAqB;AAK3B,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,mBAAmB,EAAE,EAAE,sBAAsB,YAAY,CAAC;AAC3E"}}, {"offset": {"line": 1049, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1066, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/query-error-handler.ts"], "sourcesContent": ["/**\n * Query Error Handler\n * Centralized error handling for TanStack Query\n */\n\nimport { QueryClient } from '@tanstack/react-query';\n\n/**\n * API Error interface\n */\nexport interface ApiError {\n  status: number;\n  statusText: string;\n  message: string;\n  details?: any;\n  timestamp: string;\n}\n\n/**\n * Error types for different scenarios\n */\nexport enum ErrorType {\n  NETWORK = 'NETWORK',\n  AUTHENTICATION = 'AUTHENTICATION',\n  AUTHORIZATION = 'AUTHORIZATION',\n  VALIDATION = 'VALIDATION',\n  SERVER = 'SERVER',\n  UNKNOWN = 'UNKNOWN',\n}\n\n/**\n * Determine error type based on status code\n */\nexport function getErrorType(status: number): ErrorType {\n  if (status === 401) return ErrorType.AUTHENTICATION;\n  if (status === 403) return ErrorType.AUTHORIZATION;\n  if (status >= 400 && status < 500) return ErrorType.VALIDATION;\n  if (status >= 500) return ErrorType.SERVER;\n  if (status === 0) return ErrorType.NETWORK;\n  return ErrorType.UNKNOWN;\n}\n\n/**\n * Create standardized API error\n */\nexport function createApiError(\n  status: number,\n  statusText: string,\n  message: string,\n  details?: any\n): ApiError {\n  return {\n    status,\n    statusText,\n    message,\n    details,\n    timestamp: new Date().toISOString(),\n  };\n}\n\n/**\n * Parse error response from API\n */\nexport async function parseErrorResponse(response: Response): Promise<ApiError> {\n  let message = response.statusText || 'An error occurred';\n  let details = null;\n\n  try {\n    const errorData = await response.json();\n    message = errorData.message || errorData.error || message;\n    details = errorData.details || errorData;\n  } catch {\n    // If response is not JSON, use status text\n  }\n\n  return createApiError(response.status, response.statusText, message, details);\n}\n\n/**\n * Global error handler for queries\n */\nexport function createGlobalErrorHandler() {\n  return (error: unknown) => {\n    console.error('[Query Error]', error);\n\n    // Handle different types of errors\n    if (error instanceof Error) {\n      // Network errors, parsing errors, etc.\n      console.error('Error details:', {\n        name: error.name,\n        message: error.message,\n        stack: error.stack,\n      });\n    }\n\n    // Handle API errors\n    if (isApiError(error)) {\n      handleApiError(error);\n    }\n  };\n}\n\n/**\n * Check if error is an API error\n */\nexport function isApiError(error: unknown): error is ApiError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    'status' in error &&\n    'message' in error\n  );\n}\n\n/**\n * Handle specific API error types\n */\nexport function handleApiError(error: ApiError) {\n  const errorType = getErrorType(error.status);\n\n  switch (errorType) {\n    case ErrorType.AUTHENTICATION:\n      handleAuthenticationError(error);\n      break;\n    case ErrorType.AUTHORIZATION:\n      handleAuthorizationError(error);\n      break;\n    case ErrorType.VALIDATION:\n      handleValidationError(error);\n      break;\n    case ErrorType.SERVER:\n      handleServerError(error);\n      break;\n    case ErrorType.NETWORK:\n      handleNetworkError(error);\n      break;\n    default:\n      handleUnknownError(error);\n  }\n}\n\n/**\n * Handle authentication errors (401)\n */\nfunction handleAuthenticationError(error: ApiError) {\n  console.warn('[Auth Error]', error.message);\n  \n  // In development mode, authentication is disabled\n  if (process.env.NODE_ENV === 'development') {\n    console.log('[Dev Mode] Authentication error ignored');\n    return;\n  }\n  \n  // In production, redirect to login or refresh token\n  // This will be implemented when auth system is ready\n}\n\n/**\n * Handle authorization errors (403)\n */\nfunction handleAuthorizationError(error: ApiError) {\n  console.warn('[Authorization Error]', error.message);\n  \n  // Show user-friendly message about insufficient permissions\n  // This will be integrated with notification system\n}\n\n/**\n * Handle validation errors (400-499)\n */\nfunction handleValidationError(error: ApiError) {\n  console.warn('[Validation Error]', error.message);\n  \n  // These are usually handled by individual components\n  // Global handler just logs for debugging\n}\n\n/**\n * Handle server errors (500+)\n */\nfunction handleServerError(error: ApiError) {\n  console.error('[Server Error]', error.message);\n  \n  // Show generic error message to user\n  // Log detailed error for debugging\n}\n\n/**\n * Handle network errors\n */\nfunction handleNetworkError(error: ApiError) {\n  console.error('[Network Error]', error.message);\n  \n  // Show network connectivity message\n  // Suggest retry or check connection\n}\n\n/**\n * Handle unknown errors\n */\nfunction handleUnknownError(error: ApiError) {\n  console.error('[Unknown Error]', error);\n  \n  // Show generic error message\n  // Log for investigation\n}\n\n/**\n * Error boundary for query errors\n */\nexport function setupQueryErrorHandling(queryClient: QueryClient) {\n  // Set up global error handler\n  queryClient.setDefaultOptions({\n    queries: {\n      ...queryClient.getDefaultOptions().queries,\n      throwOnError: false, // Handle errors gracefully\n    },\n    mutations: {\n      ...queryClient.getDefaultOptions().mutations,\n      throwOnError: false, // Handle errors gracefully\n    },\n  });\n\n  // Set up global error handler\n  queryClient.setMutationDefaults(['mutation'], {\n    onError: createGlobalErrorHandler(),\n  });\n}\n\n/**\n * Utility functions for error handling\n */\nexport const errorUtils = {\n  /**\n   * Check if error should trigger retry\n   */\n  shouldRetry: (error: unknown): boolean => {\n    if (isApiError(error)) {\n      const errorType = getErrorType(error.status);\n      // Don't retry client errors (4xx)\n      return errorType !== ErrorType.VALIDATION && \n             errorType !== ErrorType.AUTHENTICATION && \n             errorType !== ErrorType.AUTHORIZATION;\n    }\n    return true; // Retry network and unknown errors\n  },\n\n  /**\n   * Get user-friendly error message\n   */\n  getUserMessage: (error: unknown): string => {\n    if (isApiError(error)) {\n      const errorType = getErrorType(error.status);\n      \n      switch (errorType) {\n        case ErrorType.AUTHENTICATION:\n          return 'Please log in to continue';\n        case ErrorType.AUTHORIZATION:\n          return 'You do not have permission to perform this action';\n        case ErrorType.VALIDATION:\n          return error.message || 'Please check your input and try again';\n        case ErrorType.SERVER:\n          return 'Server error occurred. Please try again later';\n        case ErrorType.NETWORK:\n          return 'Network error. Please check your connection';\n        default:\n          return 'An unexpected error occurred';\n      }\n    }\n    \n    return 'An unexpected error occurred';\n  },\n\n  /**\n   * Check if error is retryable\n   */\n  isRetryable: (error: unknown): boolean => {\n    if (isApiError(error)) {\n      return error.status >= 500 || error.status === 0;\n    }\n    return true;\n  },\n} as const;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;AAkBM,IAAA,AAAK,mCAAA;;;;;;;WAAA;;AAYL,SAAS,aAAa,MAAc;IACzC,IAAI,WAAW,KAAK;IACpB,IAAI,WAAW,KAAK;IACpB,IAAI,UAAU,OAAO,SAAS,KAAK;IACnC,IAAI,UAAU,KAAK;IACnB,IAAI,WAAW,GAAG;IAClB;AACF;AAKO,SAAS,eACd,MAAc,EACd,UAAkB,EAClB,OAAe,EACf,OAAa;IAEb,OAAO;QACL;QACA;QACA;QACA;QACA,WAAW,IAAI,OAAO,WAAW;IACnC;AACF;AAKO,eAAe,mBAAmB,QAAkB;IACzD,IAAI,UAAU,SAAS,UAAU,IAAI;IACrC,IAAI,UAAU;IAEd,IAAI;QACF,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,UAAU,UAAU,OAAO,IAAI,UAAU,KAAK,IAAI;QAClD,UAAU,UAAU,OAAO,IAAI;IACjC,EAAE,OAAM;IACN,2CAA2C;IAC7C;IAEA,OAAO,eAAe,SAAS,MAAM,EAAE,SAAS,UAAU,EAAE,SAAS;AACvE;AAKO,SAAS;IACd,OAAO,CAAC;QACN,QAAQ,KAAK,CAAC,iBAAiB;QAE/B,mCAAmC;QACnC,IAAI,iBAAiB,OAAO;YAC1B,uCAAuC;YACvC,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,MAAM,MAAM,IAAI;gBAChB,SAAS,MAAM,OAAO;gBACtB,OAAO,MAAM,KAAK;YACpB;QACF;QAEA,oBAAoB;QACpB,IAAI,WAAW,QAAQ;YACrB,eAAe;QACjB;IACF;AACF;AAKO,SAAS,WAAW,KAAc;IACvC,OACE,OAAO,UAAU,YACjB,UAAU,QACV,YAAY,SACZ,aAAa;AAEjB;AAKO,SAAS,eAAe,KAAe;IAC5C,MAAM,YAAY,aAAa,MAAM,MAAM;IAE3C,OAAQ;QACN;YACE,0BAA0B;YAC1B;QACF;YACE,yBAAyB;YACzB;QACF;YACE,sBAAsB;YACtB;QACF;YACE,kBAAkB;YAClB;QACF;YACE,mBAAmB;YACnB;QACF;YACE,mBAAmB;IACvB;AACF;AAEA;;CAEC,GACD,SAAS,0BAA0B,KAAe;IAChD,QAAQ,IAAI,CAAC,gBAAgB,MAAM,OAAO;IAE1C,kDAAkD;IAClD,wCAA4C;QAC1C,QAAQ,GAAG,CAAC;QACZ;IACF;AAEA,oDAAoD;AACpD,qDAAqD;AACvD;AAEA;;CAEC,GACD,SAAS,yBAAyB,KAAe;IAC/C,QAAQ,IAAI,CAAC,yBAAyB,MAAM,OAAO;AAEnD,4DAA4D;AAC5D,mDAAmD;AACrD;AAEA;;CAEC,GACD,SAAS,sBAAsB,KAAe;IAC5C,QAAQ,IAAI,CAAC,sBAAsB,MAAM,OAAO;AAEhD,qDAAqD;AACrD,yCAAyC;AAC3C;AAEA;;CAEC,GACD,SAAS,kBAAkB,KAAe;IACxC,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;AAE7C,qCAAqC;AACrC,mCAAmC;AACrC;AAEA;;CAEC,GACD,SAAS,mBAAmB,KAAe;IACzC,QAAQ,KAAK,CAAC,mBAAmB,MAAM,OAAO;AAE9C,oCAAoC;AACpC,oCAAoC;AACtC;AAEA;;CAEC,GACD,SAAS,mBAAmB,KAAe;IACzC,QAAQ,KAAK,CAAC,mBAAmB;AAEjC,6BAA6B;AAC7B,wBAAwB;AAC1B;AAKO,SAAS,wBAAwB,WAAwB;IAC9D,8BAA8B;IAC9B,YAAY,iBAAiB,CAAC;QAC5B,SAAS;YACP,GAAG,YAAY,iBAAiB,GAAG,OAAO;YAC1C,cAAc;QAChB;QACA,WAAW;YACT,GAAG,YAAY,iBAAiB,GAAG,SAAS;YAC5C,cAAc;QAChB;IACF;IAEA,8BAA8B;IAC9B,YAAY,mBAAmB,CAAC;QAAC;KAAW,EAAE;QAC5C,SAAS;IACX;AACF;AAKO,MAAM,aAAa;IACxB;;GAEC,GACD,aAAa,CAAC;QACZ,IAAI,WAAW,QAAQ;YACrB,MAAM,YAAY,aAAa,MAAM,MAAM;YAC3C,kCAAkC;YAClC,OAAO,8BACA,kCACA;QACT;QACA,OAAO,MAAM,mCAAmC;IAClD;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,IAAI,WAAW,QAAQ;YACrB,MAAM,YAAY,aAAa,MAAM,MAAM;YAE3C,OAAQ;gBACN;oBACE,OAAO;gBACT;oBACE,OAAO;gBACT;oBACE,OAAO,MAAM,OAAO,IAAI;gBAC1B;oBACE,OAAO;gBACT;oBACE,OAAO;gBACT;oBACE,OAAO;YACX;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,aAAa,CAAC;QACZ,IAAI,WAAW,QAAQ;YACrB,OAAO,MAAM,MAAM,IAAI,OAAO,MAAM,MAAM,KAAK;QACjD;QACA,OAAO;IACT;AACF"}}, {"offset": {"line": 1269, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1275, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/query-utils.ts"], "sourcesContent": ["/**\n * Query Utilities and Helpers\n * Common utilities for working with TanStack Query\n */\n\nimport { QueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';\nimport { QUERY_CONFIG } from './query-client';\nimport { ApiError, isApiError, errorUtils } from './query-error-handler';\n\n/**\n * Base API response interface\n */\nexport interface ApiResponse<T = any> {\n  data: T;\n  message?: string;\n  success: boolean;\n  timestamp: string;\n}\n\n/**\n * Paginated response interface\n */\nexport interface PaginatedResponse<T = any> extends ApiResponse<T[]> {\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n}\n\n/**\n * Query options builder for common patterns\n */\nexport const queryOptionsBuilder = {\n  /**\n   * Build options for real-time data (short cache)\n   */\n  realTime: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({\n    staleTime: QUERY_CONFIG.STALE_TIME.SHORT,\n    gcTime: QUERY_CONFIG.STALE_TIME.MEDIUM,\n    refetchInterval: QUERY_CONFIG.REFETCH_INTERVAL.FAST,\n    ...options,\n  }),\n\n  /**\n   * Build options for static data (long cache)\n   */\n  static: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({\n    staleTime: QUERY_CONFIG.STALE_TIME.VERY_LONG,\n    gcTime: QUERY_CONFIG.STALE_TIME.VERY_LONG,\n    refetchOnWindowFocus: false,\n    refetchOnReconnect: false,\n    ...options,\n  }),\n\n  /**\n   * Build options for user-specific data\n   */\n  userSpecific: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({\n    staleTime: QUERY_CONFIG.STALE_TIME.MEDIUM,\n    gcTime: QUERY_CONFIG.STALE_TIME.LONG,\n    refetchOnWindowFocus: true,\n    ...options,\n  }),\n\n  /**\n   * Build options for background sync data\n   */\n  backgroundSync: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({\n    staleTime: QUERY_CONFIG.STALE_TIME.LONG,\n    gcTime: QUERY_CONFIG.STALE_TIME.VERY_LONG,\n    refetchInterval: QUERY_CONFIG.REFETCH_INTERVAL.SLOW,\n    refetchIntervalInBackground: true,\n    ...options,\n  }),\n};\n\n/**\n * Mutation options builder for common patterns\n */\nexport const mutationOptionsBuilder = {\n  /**\n   * Build options for optimistic updates\n   */\n  optimistic: <T, V>(options?: Partial<UseMutationOptions<T, ApiError, V>>): Partial<UseMutationOptions<T, ApiError, V>> => ({\n    retry: QUERY_CONFIG.RETRY.ONCE,\n    ...options,\n  }),\n\n  /**\n   * Build options for critical operations\n   */\n  critical: <T, V>(options?: Partial<UseMutationOptions<T, ApiError, V>>): Partial<UseMutationOptions<T, ApiError, V>> => ({\n    retry: QUERY_CONFIG.RETRY.DEFAULT,\n    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),\n    ...options,\n  }),\n\n  /**\n   * Build options for background operations\n   */\n  background: <T, V>(options?: Partial<UseMutationOptions<T, ApiError, V>>): Partial<UseMutationOptions<T, ApiError, V>> => ({\n    retry: QUERY_CONFIG.RETRY.TWICE,\n    ...options,\n  }),\n};\n\n/**\n * Cache management utilities\n */\nexport const cacheUtils = {\n  /**\n   * Invalidate queries by pattern\n   */\n  invalidateByPattern: async (queryClient: QueryClient, pattern: string[]) => {\n    await queryClient.invalidateQueries({ queryKey: pattern });\n  },\n\n  /**\n   * Remove queries by pattern\n   */\n  removeByPattern: (queryClient: QueryClient, pattern: string[]) => {\n    queryClient.removeQueries({ queryKey: pattern });\n  },\n\n  /**\n   * Update query data\n   */\n  updateQueryData: <T>(\n    queryClient: QueryClient,\n    queryKey: string[],\n    updater: (oldData: T | undefined) => T\n  ) => {\n    queryClient.setQueryData(queryKey, updater);\n  },\n\n  /**\n   * Optimistically update list data\n   */\n  optimisticListUpdate: <T extends { id: string | number }>(\n    queryClient: QueryClient,\n    queryKey: string[],\n    item: T,\n    operation: 'add' | 'update' | 'remove'\n  ) => {\n    queryClient.setQueryData<T[]>(queryKey, (oldData) => {\n      if (!oldData) return operation === 'add' ? [item] : [];\n\n      switch (operation) {\n        case 'add':\n          return [...oldData, item];\n        case 'update':\n          return oldData.map((existing) =>\n            existing.id === item.id ? { ...existing, ...item } : existing\n          );\n        case 'remove':\n          return oldData.filter((existing) => existing.id !== item.id);\n        default:\n          return oldData;\n      }\n    });\n  },\n\n  /**\n   * Optimistically update paginated data\n   */\n  optimisticPaginatedUpdate: <T extends { id: string | number }>(\n    queryClient: QueryClient,\n    queryKey: string[],\n    item: T,\n    operation: 'add' | 'update' | 'remove'\n  ) => {\n    queryClient.setQueryData<PaginatedResponse<T>>(queryKey, (oldData) => {\n      if (!oldData) return oldData;\n\n      const updatedData = cacheUtils.optimisticListUpdate(\n        queryClient,\n        ['temp'],\n        item,\n        operation\n      );\n\n      return {\n        ...oldData,\n        data: updatedData || oldData.data,\n      };\n    });\n  },\n};\n\n/**\n * Query state utilities\n */\nexport const queryStateUtils = {\n  /**\n   * Check if any queries are loading\n   */\n  isAnyLoading: (queryClient: QueryClient, queryKeys: string[][]): boolean => {\n    return queryKeys.some((key) => {\n      const query = queryClient.getQueryState(key);\n      return query?.fetchStatus === 'fetching';\n    });\n  },\n\n  /**\n   * Check if any queries have errors\n   */\n  hasAnyErrors: (queryClient: QueryClient, queryKeys: string[][]): boolean => {\n    return queryKeys.some((key) => {\n      const query = queryClient.getQueryState(key);\n      return query?.status === 'error';\n    });\n  },\n\n  /**\n   * Get all errors from queries\n   */\n  getAllErrors: (queryClient: QueryClient, queryKeys: string[][]): ApiError[] => {\n    return queryKeys\n      .map((key) => {\n        const query = queryClient.getQueryState(key);\n        return query?.error;\n      })\n      .filter((error): error is ApiError => isApiError(error));\n  },\n\n  /**\n   * Check if data is stale\n   */\n  isStale: (queryClient: QueryClient, queryKey: string[]): boolean => {\n    const query = queryClient.getQueryState(queryKey);\n    return query ? query.isStale : true;\n  },\n};\n\n/**\n * Development utilities\n */\nexport const devUtils = {\n  /**\n   * Log query cache state\n   */\n  logCacheState: (queryClient: QueryClient) => {\n    if (process.env.NODE_ENV === 'development') {\n      const cache = queryClient.getQueryCache();\n      console.log('[Query Cache]', {\n        queries: cache.getAll().length,\n        state: cache.getAll().map((query) => ({\n          key: query.queryKey,\n          status: query.state.status,\n          dataUpdatedAt: query.state.dataUpdatedAt,\n          error: query.state.error,\n        })),\n      });\n    }\n  },\n\n  /**\n   * Clear all cache (development only)\n   */\n  clearAllCache: (queryClient: QueryClient) => {\n    if (process.env.NODE_ENV === 'development') {\n      queryClient.clear();\n      console.log('[Dev] Query cache cleared');\n    }\n  },\n\n  /**\n   * Force refetch all queries (development only)\n   */\n  refetchAll: async (queryClient: QueryClient) => {\n    if (process.env.NODE_ENV === 'development') {\n      await queryClient.refetchQueries();\n      console.log('[Dev] All queries refetched');\n    }\n  },\n};\n\n/**\n * Error handling utilities\n */\nexport const queryErrorUtils = {\n  /**\n   * Handle query error with user feedback\n   */\n  handleQueryError: (error: unknown, context?: string) => {\n    const message = errorUtils.getUserMessage(error);\n    console.error(`[Query Error${context ? ` - ${context}` : ''}]`, error);\n    \n    // This will be integrated with notification system\n    // For now, just log the user-friendly message\n    console.log('[User Message]', message);\n    \n    return message;\n  },\n\n  /**\n   * Handle mutation error with user feedback\n   */\n  handleMutationError: (error: unknown, context?: string) => {\n    const message = errorUtils.getUserMessage(error);\n    console.error(`[Mutation Error${context ? ` - ${context}` : ''}]`, error);\n    \n    // This will be integrated with notification system\n    // For now, just log the user-friendly message\n    console.log('[User Message]', message);\n    \n    return message;\n  },\n};\n\n/**\n * Type guards for API responses\n */\nexport const typeGuards = {\n  /**\n   * Check if response is a valid API response\n   */\n  isApiResponse: <T>(data: unknown): data is ApiResponse<T> => {\n    return (\n      typeof data === 'object' &&\n      data !== null &&\n      'data' in data &&\n      'success' in data &&\n      'timestamp' in data\n    );\n  },\n\n  /**\n   * Check if response is a paginated response\n   */\n  isPaginatedResponse: <T>(data: unknown): data is PaginatedResponse<T> => {\n    return (\n      typeGuards.isApiResponse(data) &&\n      'pagination' in data &&\n      typeof (data as any).pagination === 'object'\n    );\n  },\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAGD;AACA;;;AA6BO,MAAM,sBAAsB;IACjC;;GAEC,GACD,UAAU,CAAI,UAAuE,CAAC;YACpF,WAAW,6HAAA,CAAA,eAAY,CAAC,UAAU,CAAC,KAAK;YACxC,QAAQ,6HAAA,CAAA,eAAY,CAAC,UAAU,CAAC,MAAM;YACtC,iBAAiB,6HAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC,IAAI;YACnD,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,QAAQ,CAAI,UAAuE,CAAC;YAClF,WAAW,6HAAA,CAAA,eAAY,CAAC,UAAU,CAAC,SAAS;YAC5C,QAAQ,6HAAA,CAAA,eAAY,CAAC,UAAU,CAAC,SAAS;YACzC,sBAAsB;YACtB,oBAAoB;YACpB,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,cAAc,CAAI,UAAuE,CAAC;YACxF,WAAW,6HAAA,CAAA,eAAY,CAAC,UAAU,CAAC,MAAM;YACzC,QAAQ,6HAAA,CAAA,eAAY,CAAC,UAAU,CAAC,IAAI;YACpC,sBAAsB;YACtB,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,gBAAgB,CAAI,UAAuE,CAAC;YAC1F,WAAW,6HAAA,CAAA,eAAY,CAAC,UAAU,CAAC,IAAI;YACvC,QAAQ,6HAAA,CAAA,eAAY,CAAC,UAAU,CAAC,SAAS;YACzC,iBAAiB,6HAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC,IAAI;YACnD,6BAA6B;YAC7B,GAAG,OAAO;QACZ,CAAC;AACH;AAKO,MAAM,yBAAyB;IACpC;;GAEC,GACD,YAAY,CAAO,UAAuG,CAAC;YACzH,OAAO,6HAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI;YAC9B,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,UAAU,CAAO,UAAuG,CAAC;YACvH,OAAO,6HAAA,CAAA,eAAY,CAAC,KAAK,CAAC,OAAO;YACjC,YAAY,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;YACjE,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,YAAY,CAAO,UAAuG,CAAC;YACzH,OAAO,6HAAA,CAAA,eAAY,CAAC,KAAK,CAAC,KAAK;YAC/B,GAAG,OAAO;QACZ,CAAC;AACH;AAKO,MAAM,aAAa;IACxB;;GAEC,GACD,qBAAqB,OAAO,aAA0B;QACpD,MAAM,YAAY,iBAAiB,CAAC;YAAE,UAAU;QAAQ;IAC1D;IAEA;;GAEC,GACD,iBAAiB,CAAC,aAA0B;QAC1C,YAAY,aAAa,CAAC;YAAE,UAAU;QAAQ;IAChD;IAEA;;GAEC,GACD,iBAAiB,CACf,aACA,UACA;QAEA,YAAY,YAAY,CAAC,UAAU;IACrC;IAEA;;GAEC,GACD,sBAAsB,CACpB,aACA,UACA,MACA;QAEA,YAAY,YAAY,CAAM,UAAU,CAAC;YACvC,IAAI,CAAC,SAAS,OAAO,cAAc,QAAQ;gBAAC;aAAK,GAAG,EAAE;YAEtD,OAAQ;gBACN,KAAK;oBACH,OAAO;2BAAI;wBAAS;qBAAK;gBAC3B,KAAK;oBACH,OAAO,QAAQ,GAAG,CAAC,CAAC,WAClB,SAAS,EAAE,KAAK,KAAK,EAAE,GAAG;4BAAE,GAAG,QAAQ;4BAAE,GAAG,IAAI;wBAAC,IAAI;gBAEzD,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAC,WAAa,SAAS,EAAE,KAAK,KAAK,EAAE;gBAC7D;oBACE,OAAO;YACX;QACF;IACF;IAEA;;GAEC,GACD,2BAA2B,CACzB,aACA,UACA,MACA;QAEA,YAAY,YAAY,CAAuB,UAAU,CAAC;YACxD,IAAI,CAAC,SAAS,OAAO;YAErB,MAAM,cAAc,WAAW,oBAAoB,CACjD,aACA;gBAAC;aAAO,EACR,MACA;YAGF,OAAO;gBACL,GAAG,OAAO;gBACV,MAAM,eAAe,QAAQ,IAAI;YACnC;QACF;IACF;AACF;AAKO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,cAAc,CAAC,aAA0B;QACvC,OAAO,UAAU,IAAI,CAAC,CAAC;YACrB,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,OAAO,gBAAgB;QAChC;IACF;IAEA;;GAEC,GACD,cAAc,CAAC,aAA0B;QACvC,OAAO,UAAU,IAAI,CAAC,CAAC;YACrB,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,OAAO,WAAW;QAC3B;IACF;IAEA;;GAEC,GACD,cAAc,CAAC,aAA0B;QACvC,OAAO,UACJ,GAAG,CAAC,CAAC;YACJ,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,OAAO;QAChB,GACC,MAAM,CAAC,CAAC,QAA6B,CAAA,GAAA,uIAAA,CAAA,aAAU,AAAD,EAAE;IACrD;IAEA;;GAEC,GACD,SAAS,CAAC,aAA0B;QAClC,MAAM,QAAQ,YAAY,aAAa,CAAC;QACxC,OAAO,QAAQ,MAAM,OAAO,GAAG;IACjC;AACF;AAKO,MAAM,WAAW;IACtB;;GAEC,GACD,eAAe,CAAC;QACd,wCAA4C;YAC1C,MAAM,QAAQ,YAAY,aAAa;YACvC,QAAQ,GAAG,CAAC,iBAAiB;gBAC3B,SAAS,MAAM,MAAM,GAAG,MAAM;gBAC9B,OAAO,MAAM,MAAM,GAAG,GAAG,CAAC,CAAC,QAAU,CAAC;wBACpC,KAAK,MAAM,QAAQ;wBACnB,QAAQ,MAAM,KAAK,CAAC,MAAM;wBAC1B,eAAe,MAAM,KAAK,CAAC,aAAa;wBACxC,OAAO,MAAM,KAAK,CAAC,KAAK;oBAC1B,CAAC;YACH;QACF;IACF;IAEA;;GAEC,GACD,eAAe,CAAC;QACd,wCAA4C;YAC1C,YAAY,KAAK;YACjB,QAAQ,GAAG,CAAC;QACd;IACF;IAEA;;GAEC,GACD,YAAY,OAAO;QACjB,wCAA4C;YAC1C,MAAM,YAAY,cAAc;YAChC,QAAQ,GAAG,CAAC;QACd;IACF;AACF;AAKO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,kBAAkB,CAAC,OAAgB;QACjC,MAAM,UAAU,uIAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QAC1C,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,GAAG,EAAE,SAAS,GAAG,GAAG,CAAC,CAAC,EAAE;QAEhE,mDAAmD;QACnD,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,kBAAkB;QAE9B,OAAO;IACT;IAEA;;GAEC,GACD,qBAAqB,CAAC,OAAgB;QACpC,MAAM,UAAU,uIAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QAC1C,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,UAAU,CAAC,GAAG,EAAE,SAAS,GAAG,GAAG,CAAC,CAAC,EAAE;QAEnE,mDAAmD;QACnD,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,kBAAkB;QAE9B,OAAO;IACT;AACF;AAKO,MAAM,aAAa;IACxB;;GAEC,GACD,eAAe,CAAI;QACjB,OACE,OAAO,SAAS,YAChB,SAAS,QACT,UAAU,QACV,aAAa,QACb,eAAe;IAEnB;IAEA;;GAEC,GACD,qBAAqB,CAAI;QACvB,OACE,WAAW,aAAa,CAAC,SACzB,gBAAgB,QAChB,OAAO,AAAC,KAAa,UAAU,KAAK;IAExC;AACF"}}, {"offset": {"line": 1508, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1514, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/base-hooks.ts"], "sourcesContent": ["/**\n * Base API Hooks\n * Foundation hooks for API operations with TanStack Query\n */\n\n'use client';\n\nimport { \n  useQuery, \n  useMutation, \n  useQueryClient,\n  UseQueryOptions,\n  UseMutationOptions \n} from '@tanstack/react-query';\nimport { \n  queryOptionsBuilder, \n  mutationOptionsBuilder,\n  ApiResponse,\n  PaginatedResponse,\n  BaseQueryOptions,\n  BaseMutationOptions\n} from '@/lib/query-utils';\nimport { ApiError, isApiError, errorUtils } from '@/lib/query-error-handler';\n\n/**\n * Base query hook with error handling and type safety\n */\nexport function useBaseQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<TData>,\n  options?: BaseQueryOptions<TData>\n) {\n  return useQuery({\n    queryKey,\n    queryFn,\n    ...options,\n    onError: (error: unknown) => {\n      console.error(`[Query Error] ${queryKey.join(' → ')}:`, error);\n      if (options?.onError) {\n        options.onError(error as ApiError);\n      }\n    },\n  });\n}\n\n/**\n * Base mutation hook with error handling and type safety\n */\nexport function useBaseMutation<TData, TVariables>(\n  mutationFn: (variables: TVariables) => Promise<TData>,\n  options?: BaseMutationOptions<TData, TVariables>\n) {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn,\n    ...options,\n    onError: (error: unknown, variables: TVariables, context: unknown) => {\n      console.error('[Mutation Error]:', error);\n      if (options?.onError) {\n        options.onError(error as ApiError, variables, context);\n      }\n    },\n    onSuccess: (data: TData, variables: TVariables, context: unknown) => {\n      if (options?.onSuccess) {\n        options.onSuccess(data, variables, context);\n      }\n    },\n  });\n}\n\n/**\n * Paginated query hook for list endpoints\n */\nexport function usePaginatedQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<PaginatedResponse<TData>>,\n  options?: BaseQueryOptions<PaginatedResponse<TData>>\n) {\n  return useBaseQuery(queryKey, queryFn, {\n    ...queryOptionsBuilder.userSpecific(),\n    ...options,\n  });\n}\n\n/**\n * Real-time query hook for frequently updated data\n */\nexport function useRealTimeQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<TData>,\n  options?: BaseQueryOptions<TData>\n) {\n  return useBaseQuery(queryKey, queryFn, {\n    ...queryOptionsBuilder.realTime(),\n    ...options,\n  });\n}\n\n/**\n * Static query hook for rarely changing data\n */\nexport function useStaticQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<TData>,\n  options?: BaseQueryOptions<TData>\n) {\n  return useBaseQuery(queryKey, queryFn, {\n    ...queryOptionsBuilder.static(),\n    ...options,\n  });\n}\n\n/**\n * Background sync query hook for data that updates in background\n */\nexport function useBackgroundSyncQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<TData>,\n  options?: BaseQueryOptions<TData>\n) {\n  return useBaseQuery(queryKey, queryFn, {\n    ...queryOptionsBuilder.backgroundSync(),\n    ...options,\n  });\n}\n\n/**\n * Optimistic mutation hook for immediate UI updates\n */\nexport function useOptimisticMutation<TData, TVariables>(\n  mutationFn: (variables: TVariables) => Promise<TData>,\n  options?: BaseMutationOptions<TData, TVariables>\n) {\n  return useBaseMutation(mutationFn, {\n    ...mutationOptionsBuilder.optimistic(),\n    ...options,\n  });\n}\n\n/**\n * Critical mutation hook for important operations with retries\n */\nexport function useCriticalMutation<TData, TVariables>(\n  mutationFn: (variables: TVariables) => Promise<TData>,\n  options?: BaseMutationOptions<TData, TVariables>\n) {\n  return useBaseMutation(mutationFn, {\n    ...mutationOptionsBuilder.critical(),\n    ...options,\n  });\n}\n\n/**\n * Background mutation hook for non-critical operations\n */\nexport function useBackgroundMutation<TData, TVariables>(\n  mutationFn: (variables: TVariables) => Promise<TData>,\n  options?: BaseMutationOptions<TData, TVariables>\n) {\n  return useBaseMutation(mutationFn, {\n    ...mutationOptionsBuilder.background(),\n    ...options,\n  });\n}\n\n/**\n * Hook utilities for common operations\n */\nexport const useApiHookUtils = () => {\n  const queryClient = useQueryClient();\n\n  return {\n    /**\n     * Invalidate queries by pattern\n     */\n    invalidateQueries: (queryKey: readonly unknown[]) => {\n      return queryClient.invalidateQueries({ queryKey });\n    },\n\n    /**\n     * Remove queries from cache\n     */\n    removeQueries: (queryKey: readonly unknown[]) => {\n      return queryClient.removeQueries({ queryKey });\n    },\n\n    /**\n     * Update query data optimistically\n     */\n    updateQueryData: <T>(queryKey: readonly unknown[], updater: (oldData: T | undefined) => T) => {\n      queryClient.setQueryData(queryKey, updater);\n    },\n\n    /**\n     * Get cached query data\n     */\n    getQueryData: <T>(queryKey: readonly unknown[]): T | undefined => {\n      return queryClient.getQueryData(queryKey);\n    },\n\n    /**\n     * Prefetch query data\n     */\n    prefetchQuery: <T>(queryKey: readonly unknown[], queryFn: () => Promise<T>) => {\n      return queryClient.prefetchQuery({ queryKey, queryFn });\n    },\n\n    /**\n     * Check if query is loading\n     */\n    isQueryLoading: (queryKey: readonly unknown[]): boolean => {\n      const query = queryClient.getQueryState(queryKey);\n      return query?.fetchStatus === 'fetching';\n    },\n\n    /**\n     * Get query error\n     */\n    getQueryError: (queryKey: readonly unknown[]): ApiError | null => {\n      const query = queryClient.getQueryState(queryKey);\n      return isApiError(query?.error) ? query.error : null;\n    },\n\n    /**\n     * Handle API error with user feedback\n     */\n    handleApiError: (error: unknown, context?: string): string => {\n      return errorUtils.getUserMessage(error);\n    },\n  };\n};\n\n/**\n * Hook for API status monitoring\n */\nexport const useApiStatus = () => {\n  const queryClient = useQueryClient();\n\n  return {\n    /**\n     * Get overall API status\n     */\n    getApiStatus: () => {\n      const queries = queryClient.getQueryCache().getAll();\n      const totalQueries = queries.length;\n      const loadingQueries = queries.filter(q => q.state.fetchStatus === 'fetching').length;\n      const errorQueries = queries.filter(q => q.state.status === 'error').length;\n      const successQueries = queries.filter(q => q.state.status === 'success').length;\n\n      return {\n        total: totalQueries,\n        loading: loadingQueries,\n        error: errorQueries,\n        success: successQueries,\n        isLoading: loadingQueries > 0,\n        hasErrors: errorQueries > 0,\n        healthScore: totalQueries > 0 ? (successQueries / totalQueries) * 100 : 100,\n      };\n    },\n\n    /**\n     * Get queries by status\n     */\n    getQueriesByStatus: (status: 'loading' | 'error' | 'success' | 'idle') => {\n      const queries = queryClient.getQueryCache().getAll();\n      \n      switch (status) {\n        case 'loading':\n          return queries.filter(q => q.state.fetchStatus === 'fetching');\n        case 'error':\n          return queries.filter(q => q.state.status === 'error');\n        case 'success':\n          return queries.filter(q => q.state.status === 'success');\n        case 'idle':\n          return queries.filter(q => q.state.fetchStatus === 'idle');\n        default:\n          return [];\n      }\n    },\n\n    /**\n     * Clear all errors\n     */\n    clearAllErrors: () => {\n      const errorQueries = queryClient.getQueryCache().getAll()\n        .filter(q => q.state.status === 'error');\n      \n      errorQueries.forEach(query => {\n        queryClient.resetQueries({ queryKey: query.queryKey });\n      });\n    },\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAWD;AAQA;AAfA;AAAA;AAAA;AAFA;;;;AAsBO,SAAS,aACd,QAA4B,EAC5B,OAA6B,EAC7B,OAAiC;IAEjC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd;QACA;QACA,GAAG,OAAO;QACV,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE;YACxD,IAAI,SAAS,SAAS;gBACpB,QAAQ,OAAO,CAAC;YAClB;QACF;IACF;AACF;AAKO,SAAS,gBACd,UAAqD,EACrD,OAAgD;IAEhD,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB;QACA,GAAG,OAAO;QACV,SAAS,CAAC,OAAgB,WAAuB;YAC/C,QAAQ,KAAK,CAAC,qBAAqB;YACnC,IAAI,SAAS,SAAS;gBACpB,QAAQ,OAAO,CAAC,OAAmB,WAAW;YAChD;QACF;QACA,WAAW,CAAC,MAAa,WAAuB;YAC9C,IAAI,SAAS,WAAW;gBACtB,QAAQ,SAAS,CAAC,MAAM,WAAW;YACrC;QACF;IACF;AACF;AAKO,SAAS,kBACd,QAA4B,EAC5B,OAAgD,EAChD,OAAoD;IAEpD,OAAO,aAAa,UAAU,SAAS;QACrC,GAAG,4HAAA,CAAA,sBAAmB,CAAC,YAAY,EAAE;QACrC,GAAG,OAAO;IACZ;AACF;AAKO,SAAS,iBACd,QAA4B,EAC5B,OAA6B,EAC7B,OAAiC;IAEjC,OAAO,aAAa,UAAU,SAAS;QACrC,GAAG,4HAAA,CAAA,sBAAmB,CAAC,QAAQ,EAAE;QACjC,GAAG,OAAO;IACZ;AACF;AAKO,SAAS,eACd,QAA4B,EAC5B,OAA6B,EAC7B,OAAiC;IAEjC,OAAO,aAAa,UAAU,SAAS;QACrC,GAAG,4HAAA,CAAA,sBAAmB,CAAC,MAAM,EAAE;QAC/B,GAAG,OAAO;IACZ;AACF;AAKO,SAAS,uBACd,QAA4B,EAC5B,OAA6B,EAC7B,OAAiC;IAEjC,OAAO,aAAa,UAAU,SAAS;QACrC,GAAG,4HAAA,CAAA,sBAAmB,CAAC,cAAc,EAAE;QACvC,GAAG,OAAO;IACZ;AACF;AAKO,SAAS,sBACd,UAAqD,EACrD,OAAgD;IAEhD,OAAO,gBAAgB,YAAY;QACjC,GAAG,4HAAA,CAAA,yBAAsB,CAAC,UAAU,EAAE;QACtC,GAAG,OAAO;IACZ;AACF;AAKO,SAAS,oBACd,UAAqD,EACrD,OAAgD;IAEhD,OAAO,gBAAgB,YAAY;QACjC,GAAG,4HAAA,CAAA,yBAAsB,CAAC,QAAQ,EAAE;QACpC,GAAG,OAAO;IACZ;AACF;AAKO,SAAS,sBACd,UAAqD,EACrD,OAAgD;IAEhD,OAAO,gBAAgB,YAAY;QACjC,GAAG,4HAAA,CAAA,yBAAsB,CAAC,UAAU,EAAE;QACtC,GAAG,OAAO;IACZ;AACF;AAKO,MAAM,kBAAkB;IAC7B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO;QACL;;KAEC,GACD,mBAAmB,CAAC;YAClB,OAAO,YAAY,iBAAiB,CAAC;gBAAE;YAAS;QAClD;QAEA;;KAEC,GACD,eAAe,CAAC;YACd,OAAO,YAAY,aAAa,CAAC;gBAAE;YAAS;QAC9C;QAEA;;KAEC,GACD,iBAAiB,CAAI,UAA8B;YACjD,YAAY,YAAY,CAAC,UAAU;QACrC;QAEA;;KAEC,GACD,cAAc,CAAI;YAChB,OAAO,YAAY,YAAY,CAAC;QAClC;QAEA;;KAEC,GACD,eAAe,CAAI,UAA8B;YAC/C,OAAO,YAAY,aAAa,CAAC;gBAAE;gBAAU;YAAQ;QACvD;QAEA;;KAEC,GACD,gBAAgB,CAAC;YACf,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,OAAO,gBAAgB;QAChC;QAEA;;KAEC,GACD,eAAe,CAAC;YACd,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,CAAA,GAAA,uIAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS,MAAM,KAAK,GAAG;QAClD;QAEA;;KAEC,GACD,gBAAgB,CAAC,OAAgB;YAC/B,OAAO,uIAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QACnC;IACF;AACF;AAKO,MAAM,eAAe;IAC1B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO;QACL;;KAEC,GACD,cAAc;YACZ,MAAM,UAAU,YAAY,aAAa,GAAG,MAAM;YAClD,MAAM,eAAe,QAAQ,MAAM;YACnC,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,WAAW,KAAK,YAAY,MAAM;YACrF,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK,SAAS,MAAM;YAC3E,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK,WAAW,MAAM;YAE/E,OAAO;gBACL,OAAO;gBACP,SAAS;gBACT,OAAO;gBACP,SAAS;gBACT,WAAW,iBAAiB;gBAC5B,WAAW,eAAe;gBAC1B,aAAa,eAAe,IAAI,AAAC,iBAAiB,eAAgB,MAAM;YAC1E;QACF;QAEA;;KAEC,GACD,oBAAoB,CAAC;YACnB,MAAM,UAAU,YAAY,aAAa,GAAG,MAAM;YAElD,OAAQ;gBACN,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,WAAW,KAAK;gBACrD,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK;gBAChD,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK;gBAChD,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,WAAW,KAAK;gBACrD;oBACE,OAAO,EAAE;YACb;QACF;QAEA;;KAEC,GACD,gBAAgB;YACd,MAAM,eAAe,YAAY,aAAa,GAAG,MAAM,GACpD,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK;YAElC,aAAa,OAAO,CAAC,CAAA;gBACnB,YAAY,YAAY,CAAC;oBAAE,UAAU,MAAM,QAAQ;gBAAC;YACtD;QACF;IACF;AACF"}}, {"offset": {"line": 1716, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1722, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/auth-hooks.ts"], "sourcesContent": ["/**\n * Authentication API Hooks\n * Hooks for system authentication operations\n */\n\n'use client';\n\nimport { queryKeys } from '@/lib/query-client';\nimport { AuthQueries } from '@/lib/query-types';\nimport { useBaseQuery, useBaseMutation, useApiHookUtils } from './base-hooks';\n\n/**\n * Hook for user login\n */\nexport function useLogin() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<AuthQueries.LoginResponse, AuthQueries.LoginRequest>(\n    async (credentials) => {\n      const response = await fetch('/api/system-auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(credentials),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Login failed: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: (data) => {\n        // Invalidate auth queries on successful login\n        invalidateQueries(queryKeys.auth.all);\n        console.log('✅ Login successful:', data.user.username);\n      },\n      onError: (error) => {\n        console.error('❌ Login failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for user logout\n */\nexport function useLogout() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string }, void>(\n    async () => {\n      const response = await fetch('/api/system-auth/logout', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Logout failed: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Clear all auth-related queries on logout\n        invalidateQueries(queryKeys.auth.all);\n        console.log('✅ Logout successful');\n      },\n      onError: (error) => {\n        console.error('❌ Logout failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for logout from all devices\n */\nexport function useLogoutAll() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string }, void>(\n    async () => {\n      const response = await fetch('/api/system-auth/logout-all', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Logout all failed: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Clear all auth-related queries on logout all\n        invalidateQueries(queryKeys.auth.all);\n        console.log('✅ Logout all successful');\n      },\n      onError: (error) => {\n        console.error('❌ Logout all failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for getting user profile\n */\nexport function useProfile() {\n  return useBaseQuery(\n    queryKeys.auth.profile(),\n    async (): Promise<AuthQueries.ProfileResponse> => {\n      const response = await fetch('/api/system-auth/profile');\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch profile: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: false, // Disable by default, enable when user is authenticated\n      staleTime: 5 * 60 * 1000, // 5 minutes\n      retry: (failureCount, error: any) => {\n        // Don't retry on 401 (unauthorized)\n        if (error?.status === 401) return false;\n        return failureCount < 2;\n      },\n    }\n  );\n}\n\n/**\n * Hook for updating user profile\n */\nexport function useUpdateProfile() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<AuthQueries.ProfileResponse, AuthQueries.UpdateProfileRequest>(\n    async (data) => {\n      const response = await fetch('/api/system-auth/profile', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to update profile: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Invalidate profile query to refetch updated data\n        invalidateQueries(queryKeys.auth.profile());\n        console.log('✅ Profile updated successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Profile update failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for changing password\n */\nexport function useChangePassword() {\n  return useBaseMutation<{ message: string }, AuthQueries.ChangePasswordRequest>(\n    async (data) => {\n      const response = await fetch('/api/system-auth/change-password', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to change password: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        console.log('✅ Password changed successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Password change failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for creating new system user (Admin only)\n */\nexport function useCreateUser() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<AuthQueries.ProfileResponse, AuthQueries.CreateUserRequest>(\n    async (data) => {\n      const response = await fetch('/api/system-auth/users', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to create user: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Invalidate users list to show new user\n        invalidateQueries(queryKeys.auth.users());\n        console.log('✅ User created successfully');\n      },\n      onError: (error) => {\n        console.error('❌ User creation failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for getting system users list (Admin only)\n */\nexport function useSystemUsers() {\n  return useBaseQuery(\n    queryKeys.auth.users(),\n    async (): Promise<AuthQueries.ProfileResponse[]> => {\n      const response = await fetch('/api/system-auth/users');\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch users: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: false, // Enable only for Admin users\n      staleTime: 2 * 60 * 1000, // 2 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting specific system user (Admin only)\n */\nexport function useSystemUser(userId: string) {\n  return useBaseQuery(\n    queryKeys.auth.user(userId),\n    async (): Promise<AuthQueries.ProfileResponse> => {\n      const response = await fetch(`/api/system-auth/users/${userId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch user: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!userId, // Only fetch if userId is provided\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    }\n  );\n}\n\n/**\n * Composite hook for authentication state and actions\n */\nexport function useAuth() {\n  const login = useLogin();\n  const logout = useLogout();\n  const logoutAll = useLogoutAll();\n  const profile = useProfile();\n  const updateProfile = useUpdateProfile();\n  const changePassword = useChangePassword();\n  const createUser = useCreateUser();\n\n  return {\n    // Queries\n    profile,\n    \n    // Mutations\n    login,\n    logout,\n    logoutAll,\n    updateProfile,\n    changePassword,\n    createUser,\n    \n    // Computed state\n    isAuthenticated: !!profile.data,\n    user: profile.data,\n    isLoading: profile.isLoading || login.isPending || logout.isPending,\n    error: profile.error || login.error || logout.error,\n    \n    // Actions\n    loginUser: login.mutate,\n    logoutUser: logout.mutate,\n    logoutAllDevices: logoutAll.mutate,\n    updateUserProfile: updateProfile.mutate,\n    changeUserPassword: changePassword.mutate,\n    createNewUser: createUser.mutate,\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;AAID;AAEA;AAJA;;;AASO,SAAS;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EACnB,OAAO;QACL,MAAM,WAAW,MAAM,MAAM,0BAA0B;YACrD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,UAAU,EAAE;QACxD;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,WAAW,CAAC;YACV,8CAA8C;YAC9C,kBAAkB,6HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,GAAG;YACpC,QAAQ,GAAG,CAAC,uBAAuB,KAAK,IAAI,CAAC,QAAQ;QACvD;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;AAEJ;AAKO,SAAS;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EACnB;QACE,MAAM,WAAW,MAAM,MAAM,2BAA2B;YACtD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,SAAS,UAAU,EAAE;QACzD;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,WAAW;YACT,2CAA2C;YAC3C,kBAAkB,6HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,GAAG;YACpC,QAAQ,GAAG,CAAC;QACd;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,oBAAoB;QACpC;IACF;AAEJ;AAKO,SAAS;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EACnB;QACE,MAAM,WAAW,MAAM,MAAM,+BAA+B;YAC1D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,SAAS,UAAU,EAAE;QAC7D;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,WAAW;YACT,+CAA+C;YAC/C,kBAAkB,6HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,GAAG;YACpC,QAAQ,GAAG,CAAC;QACd;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;AAEJ;AAKO,SAAS;IACd,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB,6HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,OAAO,IACtB;QACE,MAAM,WAAW,MAAM,MAAM;QAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,SAAS;QACT,WAAW,IAAI,KAAK;QACpB,OAAO,CAAC,cAAc;YACpB,oCAAoC;YACpC,IAAI,OAAO,WAAW,KAAK,OAAO;YAClC,OAAO,eAAe;QACxB;IACF;AAEJ;AAKO,SAAS;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EACnB,OAAO;QACL,MAAM,WAAW,MAAM,MAAM,4BAA4B;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;QACpE;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,WAAW;YACT,mDAAmD;YACnD,kBAAkB,6HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,OAAO;YACxC,QAAQ,GAAG,CAAC;QACd;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;AAEJ;AAKO,SAAS;IACd,OAAO,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EACnB,OAAO;QACL,MAAM,WAAW,MAAM,MAAM,oCAAoC;YAC/D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,SAAS,UAAU,EAAE;QACrE;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,WAAW;YACT,QAAQ,GAAG,CAAC;QACd;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;AAEJ;AAKO,SAAS;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EACnB,OAAO;QACL,MAAM,WAAW,MAAM,MAAM,0BAA0B;YACrD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,WAAW;YACT,yCAAyC;YACzC,kBAAkB,6HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,KAAK;YACtC,QAAQ,GAAG,CAAC;QACd;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;AAEJ;AAKO,SAAS;IACd,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB,6HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,KAAK,IACpB;QACE,MAAM,WAAW,MAAM,MAAM;QAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,SAAS;QACT,WAAW,IAAI,KAAK;IACtB;AAEJ;AAKO,SAAS,cAAc,MAAc;IAC1C,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB,6HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SACpB;QACE,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,QAAQ;QAE/D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;QAChE;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;AAKO,SAAS;IACd,MAAM,QAAQ;IACd,MAAM,SAAS;IACf,MAAM,YAAY;IAClB,MAAM,UAAU;IAChB,MAAM,gBAAgB;IACtB,MAAM,iBAAiB;IACvB,MAAM,aAAa;IAEnB,OAAO;QACL,UAAU;QACV;QAEA,YAAY;QACZ;QACA;QACA;QACA;QACA;QACA;QAEA,iBAAiB;QACjB,iBAAiB,CAAC,CAAC,QAAQ,IAAI;QAC/B,MAAM,QAAQ,IAAI;QAClB,WAAW,QAAQ,SAAS,IAAI,MAAM,SAAS,IAAI,OAAO,SAAS;QACnE,OAAO,QAAQ,KAAK,IAAI,MAAM,KAAK,IAAI,OAAO,KAAK;QAEnD,UAAU;QACV,WAAW,MAAM,MAAM;QACvB,YAAY,OAAO,MAAM;QACzB,kBAAkB,UAAU,MAAM;QAClC,mBAAmB,cAAc,MAAM;QACvC,oBAAoB,eAAe,MAAM;QACzC,eAAe,WAAW,MAAM;IAClC;AACF"}}, {"offset": {"line": 1960, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1966, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/football-hooks.ts"], "sourcesContent": ["/**\n * Football Data API Hooks\n * Hooks for football leagues, teams, and fixtures operations\n */\n\n'use client';\n\nimport { queryKeys } from '@/lib/query-client';\nimport { FootballQueries } from '@/lib/query-types';\nimport { PaginatedResponse } from '@/lib/query-utils';\nimport { \n  useBaseQuery, \n  usePaginatedQuery, \n  useBackgroundSyncQuery,\n  useBaseMutation,\n  useApiHookUtils \n} from './base-hooks';\n\n/**\n * Hook for getting football leagues\n */\nexport function useLeagues(params?: FootballQueries.LeagueQueryParams) {\n  const queryParams = new URLSearchParams();\n  if (params?.page) queryParams.set('page', params.page.toString());\n  if (params?.limit) queryParams.set('limit', params.limit.toString());\n  if (params?.country) queryParams.set('country', params.country);\n  if (params?.isActive !== undefined) queryParams.set('isActive', params.isActive.toString());\n  if (params?.query) queryParams.set('query', params.query);\n\n  return usePaginatedQuery(\n    [...queryKeys.football.leagues(), params],\n    async (): Promise<PaginatedResponse<FootballQueries.League>> => {\n      const response = await fetch(`/api/football/leagues?${queryParams.toString()}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch leagues: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 10 * 60 * 1000, // 10 minutes - leagues don't change often\n    }\n  );\n}\n\n/**\n * Hook for getting specific league\n */\nexport function useLeague(leagueId: string) {\n  return useBaseQuery(\n    queryKeys.football.league(leagueId),\n    async (): Promise<FootballQueries.League> => {\n      const response = await fetch(`/api/football/leagues/${leagueId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch league: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!leagueId,\n      staleTime: 10 * 60 * 1000, // 10 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting football teams\n */\nexport function useTeams(params?: FootballQueries.TeamQueryParams) {\n  const queryParams = new URLSearchParams();\n  if (params?.page) queryParams.set('page', params.page.toString());\n  if (params?.limit) queryParams.set('limit', params.limit.toString());\n  if (params?.leagueId) queryParams.set('leagueId', params.leagueId);\n  if (params?.country) queryParams.set('country', params.country);\n  if (params?.query) queryParams.set('query', params.query);\n\n  return usePaginatedQuery(\n    [...queryKeys.football.teams(), params],\n    async (): Promise<PaginatedResponse<FootballQueries.Team>> => {\n      const response = await fetch(`/api/football/teams?${queryParams.toString()}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch teams: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting specific team\n */\nexport function useTeam(teamId: string) {\n  return useBaseQuery(\n    queryKeys.football.team(teamId),\n    async (): Promise<FootballQueries.Team> => {\n      const response = await fetch(`/api/football/teams/${teamId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch team: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!teamId,\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting football fixtures\n */\nexport function useFixtures(params?: FootballQueries.FixtureQueryParams) {\n  const queryParams = new URLSearchParams();\n  if (params?.page) queryParams.set('page', params.page.toString());\n  if (params?.limit) queryParams.set('limit', params.limit.toString());\n  if (params?.leagueId) queryParams.set('leagueId', params.leagueId);\n  if (params?.teamId) queryParams.set('teamId', params.teamId);\n  if (params?.status) queryParams.set('status', params.status);\n  if (params?.dateFrom) queryParams.set('dateFrom', params.dateFrom);\n  if (params?.dateTo) queryParams.set('dateTo', params.dateTo);\n  if (params?.query) queryParams.set('query', params.query);\n\n  return usePaginatedQuery(\n    [...queryKeys.football.fixtures(), params],\n    async (): Promise<PaginatedResponse<FootballQueries.Fixture>> => {\n      const response = await fetch(`/api/football/fixtures?${queryParams.toString()}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch fixtures: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 1 * 60 * 1000, // 1 minute - fixtures change frequently\n    }\n  );\n}\n\n/**\n * Hook for getting specific fixture\n */\nexport function useFixture(fixtureId: string) {\n  return useBaseQuery(\n    queryKeys.football.fixture(fixtureId),\n    async (): Promise<FootballQueries.Fixture> => {\n      const response = await fetch(`/api/football/fixtures/${fixtureId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch fixture: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!fixtureId,\n      staleTime: 30 * 1000, // 30 seconds - live fixtures need frequent updates\n    }\n  );\n}\n\n/**\n * Hook for getting sync status\n */\nexport function useSyncStatus() {\n  return useBackgroundSyncQuery(\n    queryKeys.football.syncStatus(),\n    async (): Promise<FootballQueries.SyncStatus> => {\n      const response = await fetch('/api/football/fixtures/sync/status');\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch sync status: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 30 * 1000, // 30 seconds\n      refetchInterval: 60 * 1000, // Refetch every minute\n    }\n  );\n}\n\n/**\n * Hook for triggering fixtures sync\n */\nexport function useSyncFixtures() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string; syncId: string }, void>(\n    async () => {\n      const response = await fetch('/api/football/fixtures/sync', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to start sync: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Invalidate sync status and fixtures to show updated data\n        invalidateQueries(queryKeys.football.syncStatus());\n        invalidateQueries(queryKeys.football.fixtures());\n        console.log('✅ Fixtures sync started');\n      },\n      onError: (error) => {\n        console.error('❌ Fixtures sync failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for triggering daily sync\n */\nexport function useDailySync() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string; syncId: string }, void>(\n    async () => {\n      const response = await fetch('/api/football/fixtures/sync/daily', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to start daily sync: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Invalidate sync status and fixtures\n        invalidateQueries(queryKeys.football.syncStatus());\n        invalidateQueries(queryKeys.football.fixtures());\n        console.log('✅ Daily sync started');\n      },\n      onError: (error) => {\n        console.error('❌ Daily sync failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Composite hook for football data operations\n */\nexport function useFootball() {\n  const syncFixtures = useSyncFixtures();\n  const dailySync = useDailySync();\n  const syncStatus = useSyncStatus();\n\n  return {\n    // Sync operations\n    syncFixtures,\n    dailySync,\n    syncStatus,\n    \n    // Sync actions\n    startSync: syncFixtures.mutate,\n    startDailySync: dailySync.mutate,\n    \n    // Sync state\n    isSyncing: syncFixtures.isPending || dailySync.isPending,\n    syncError: syncFixtures.error || dailySync.error,\n    lastSyncStatus: syncStatus.data,\n  };\n}\n\n/**\n * Hook for live fixtures (real-time updates)\n */\nexport function useLiveFixtures() {\n  return useFixtures({\n    status: 'live',\n    limit: 50,\n  });\n}\n\n/**\n * Hook for today's fixtures\n */\nexport function useTodayFixtures() {\n  const today = new Date().toISOString().split('T')[0];\n  \n  return useFixtures({\n    dateFrom: today,\n    dateTo: today,\n    limit: 100,\n  });\n}\n\n/**\n * Hook for upcoming fixtures\n */\nexport function useUpcomingFixtures(days: number = 7) {\n  const today = new Date();\n  const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);\n  \n  return useFixtures({\n    dateFrom: today.toISOString().split('T')[0],\n    dateTo: futureDate.toISOString().split('T')[0],\n    status: 'scheduled',\n    limit: 100,\n  });\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;AAID;AAGA;AALA;;;AAgBO,SAAS,WAAW,MAA0C;IACnE,MAAM,cAAc,IAAI;IACxB,IAAI,QAAQ,MAAM,YAAY,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,QAAQ,SAAS,YAAY,GAAG,CAAC,WAAW,OAAO,OAAO;IAC9D,IAAI,QAAQ,aAAa,WAAW,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ,CAAC,QAAQ;IACxF,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK;IAExD,OAAO,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EACrB;WAAI,6HAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO;QAAI;KAAO,EACzC;QACE,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,YAAY,QAAQ,IAAI;QAE9E,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,WAAW,KAAK,KAAK;IACvB;AAEJ;AAKO,SAAS,UAAU,QAAgB;IACxC,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB,6HAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,WAC1B;QACE,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,UAAU;QAEhE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;QAClE;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,KAAK,KAAK;IACvB;AAEJ;AAKO,SAAS,SAAS,MAAwC;IAC/D,MAAM,cAAc,IAAI;IACxB,IAAI,QAAQ,MAAM,YAAY,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,QAAQ,UAAU,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ;IACjE,IAAI,QAAQ,SAAS,YAAY,GAAG,CAAC,WAAW,OAAO,OAAO;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK;IAExD,OAAO,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EACrB;WAAI,6HAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,KAAK;QAAI;KAAO,EACvC;QACE,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,YAAY,QAAQ,IAAI;QAE5E,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,WAAW,IAAI,KAAK;IACtB;AAEJ;AAKO,SAAS,QAAQ,MAAc;IACpC,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB,6HAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,SACxB;QACE,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,QAAQ;QAE5D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;QAChE;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;AAKO,SAAS,YAAY,MAA2C;IACrE,MAAM,cAAc,IAAI;IACxB,IAAI,QAAQ,MAAM,YAAY,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,QAAQ,UAAU,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ;IACjE,IAAI,QAAQ,QAAQ,YAAY,GAAG,CAAC,UAAU,OAAO,MAAM;IAC3D,IAAI,QAAQ,QAAQ,YAAY,GAAG,CAAC,UAAU,OAAO,MAAM;IAC3D,IAAI,QAAQ,UAAU,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ;IACjE,IAAI,QAAQ,QAAQ,YAAY,GAAG,CAAC,UAAU,OAAO,MAAM;IAC3D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK;IAExD,OAAO,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EACrB;WAAI,6HAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,QAAQ;QAAI;KAAO,EAC1C;QACE,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,YAAY,QAAQ,IAAI;QAE/E,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;QACpE;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,WAAW,IAAI,KAAK;IACtB;AAEJ;AAKO,SAAS,WAAW,SAAiB;IAC1C,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB,6HAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,YAC3B;QACE,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,WAAW;QAElE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,KAAK;IAClB;AAEJ;AAKO,SAAS;IACd,OAAO,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD,EAC1B,6HAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,UAAU,IAC7B;QACE,MAAM,WAAW,MAAM,MAAM;QAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,SAAS,UAAU,EAAE;QACvE;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,WAAW,KAAK;QAChB,iBAAiB,KAAK;IACxB;AAEJ;AAKO,SAAS;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EACnB;QACE,MAAM,WAAW,MAAM,MAAM,+BAA+B;YAC1D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;QAChE;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,WAAW;YACT,2DAA2D;YAC3D,kBAAkB,6HAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,UAAU;YAC/C,kBAAkB,6HAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,QAAQ;YAC7C,QAAQ,GAAG,CAAC;QACd;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;AAEJ;AAKO,SAAS;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EACnB;QACE,MAAM,WAAW,MAAM,MAAM,qCAAqC;YAChE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,SAAS,UAAU,EAAE;QACtE;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,WAAW;YACT,sCAAsC;YACtC,kBAAkB,6HAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,UAAU;YAC/C,kBAAkB,6HAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,QAAQ;YAC7C,QAAQ,GAAG,CAAC;QACd;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;AAEJ;AAKO,SAAS;IACd,MAAM,eAAe;IACrB,MAAM,YAAY;IAClB,MAAM,aAAa;IAEnB,OAAO;QACL,kBAAkB;QAClB;QACA;QACA;QAEA,eAAe;QACf,WAAW,aAAa,MAAM;QAC9B,gBAAgB,UAAU,MAAM;QAEhC,aAAa;QACb,WAAW,aAAa,SAAS,IAAI,UAAU,SAAS;QACxD,WAAW,aAAa,KAAK,IAAI,UAAU,KAAK;QAChD,gBAAgB,WAAW,IAAI;IACjC;AACF;AAKO,SAAS;IACd,OAAO,YAAY;QACjB,QAAQ;QACR,OAAO;IACT;AACF;AAKO,SAAS;IACd,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAEpD,OAAO,YAAY;QACjB,UAAU;QACV,QAAQ;QACR,OAAO;IACT;AACF;AAKO,SAAS,oBAAoB,OAAe,CAAC;IAClD,MAAM,QAAQ,IAAI;IAClB,MAAM,aAAa,IAAI,KAAK,MAAM,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK;IAEpE,OAAO,YAAY;QACjB,UAAU,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC3C,QAAQ,WAAW,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC9C,QAAQ;QACR,OAAO;IACT;AACF"}}, {"offset": {"line": 2192, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2198, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/types/user.ts"], "sourcesContent": ["/**\n * User Types & Interfaces\n * SystemUser management types for APISportsGame CMS\n */\n\n/**\n * System user roles\n */\nexport type SystemUserRole = 'admin' | 'editor' | 'moderator';\n\n/**\n * User status\n */\nexport type UserStatus = 'active' | 'inactive' | 'suspended';\n\n/**\n * System user interface\n */\nexport interface SystemUser {\n  id: string;\n  username: string;\n  email: string;\n  firstName?: string;\n  lastName?: string;\n  role: SystemUserRole;\n  status: UserStatus;\n  lastLogin?: string;\n  createdAt: string;\n  updatedAt: string;\n  createdBy?: string;\n  avatar?: string;\n  permissions?: string[];\n}\n\n/**\n * Create user request\n */\nexport interface CreateUserRequest {\n  username: string;\n  email: string;\n  password: string;\n  firstName?: string;\n  lastName?: string;\n  role: SystemUserRole;\n  status?: UserStatus;\n}\n\n/**\n * Update user request\n */\nexport interface UpdateUserRequest {\n  email?: string;\n  firstName?: string;\n  lastName?: string;\n  role?: SystemUserRole;\n  status?: UserStatus;\n  avatar?: string;\n}\n\n/**\n * Change password request\n */\nexport interface ChangePasswordRequest {\n  currentPassword: string;\n  newPassword: string;\n  confirmPassword: string;\n}\n\n/**\n * User list query parameters\n */\nexport interface UserListParams {\n  page?: number;\n  limit?: number;\n  search?: string;\n  role?: SystemUserRole;\n  status?: UserStatus;\n  sortBy?: 'username' | 'email' | 'role' | 'status' | 'createdAt' | 'lastLogin';\n  sortOrder?: 'asc' | 'desc';\n}\n\n/**\n * User list response\n */\nexport interface UserListResponse {\n  users: SystemUser[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\n/**\n * User statistics\n */\nexport interface UserStatistics {\n  total: number;\n  active: number;\n  inactive: number;\n  suspended: number;\n  byRole: {\n    admin: number;\n    editor: number;\n    moderator: number;\n  };\n  recentLogins: number;\n  newThisMonth: number;\n}\n\n/**\n * User activity log\n */\nexport interface UserActivity {\n  id: string;\n  userId: string;\n  action: string;\n  description: string;\n  ipAddress?: string;\n  userAgent?: string;\n  createdAt: string;\n}\n\n/**\n * User session\n */\nexport interface UserSession {\n  id: string;\n  userId: string;\n  deviceInfo: string;\n  ipAddress: string;\n  lastActivity: string;\n  createdAt: string;\n  isActive: boolean;\n}\n\n/**\n * Role permissions mapping\n */\nexport const ROLE_PERMISSIONS: Record<SystemUserRole, string[]> = {\n  admin: [\n    'users.create',\n    'users.read',\n    'users.update',\n    'users.delete',\n    'users.manage_roles',\n    'football.create',\n    'football.read',\n    'football.update',\n    'football.delete',\n    'football.sync',\n    'broadcast.create',\n    'broadcast.read',\n    'broadcast.update',\n    'broadcast.delete',\n    'system.settings',\n    'system.logs',\n    'system.health',\n  ],\n  editor: [\n    'users.read',\n    'football.create',\n    'football.read',\n    'football.update',\n    'football.sync',\n    'broadcast.create',\n    'broadcast.read',\n    'broadcast.update',\n    'broadcast.delete',\n  ],\n  moderator: [\n    'users.read',\n    'football.read',\n    'broadcast.read',\n    'broadcast.update',\n  ],\n};\n\n/**\n * Role display names\n */\nexport const ROLE_LABELS: Record<SystemUserRole, string> = {\n  admin: 'Administrator',\n  editor: 'Editor',\n  moderator: 'Moderator',\n};\n\n/**\n * Status display names\n */\nexport const STATUS_LABELS: Record<UserStatus, string> = {\n  active: 'Active',\n  inactive: 'Inactive',\n  suspended: 'Suspended',\n};\n\n/**\n * Role colors for UI\n */\nexport const ROLE_COLORS: Record<SystemUserRole, string> = {\n  admin: '#ff4d4f',\n  editor: '#1890ff',\n  moderator: '#52c41a',\n};\n\n/**\n * Status colors for UI\n */\nexport const STATUS_COLORS: Record<UserStatus, string> = {\n  active: '#52c41a',\n  inactive: '#d9d9d9',\n  suspended: '#ff4d4f',\n};\n\n/**\n * User form validation rules\n */\nexport const USER_VALIDATION = {\n  username: {\n    min: 3,\n    max: 50,\n    pattern: /^[a-zA-Z0-9_-]+$/,\n    message: 'Username must be 3-50 characters and contain only letters, numbers, hyphens, and underscores',\n  },\n  email: {\n    pattern: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n    message: 'Please enter a valid email address',\n  },\n  password: {\n    min: 8,\n    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/,\n    message: 'Password must be at least 8 characters with uppercase, lowercase, number, and special character',\n  },\n  firstName: {\n    max: 50,\n    message: 'First name must not exceed 50 characters',\n  },\n  lastName: {\n    max: 50,\n    message: 'Last name must not exceed 50 characters',\n  },\n};\n\n/**\n * Default user list params\n */\nexport const DEFAULT_USER_PARAMS: UserListParams = {\n  page: 1,\n  limit: 20,\n  sortBy: 'createdAt',\n  sortOrder: 'desc',\n};\n\n/**\n * User helper functions\n */\nexport const userHelpers = {\n  /**\n   * Get user full name\n   */\n  getFullName: (user: SystemUser): string => {\n    if (user.firstName && user.lastName) {\n      return `${user.firstName} ${user.lastName}`;\n    }\n    if (user.firstName) {\n      return user.firstName;\n    }\n    if (user.lastName) {\n      return user.lastName;\n    }\n    return user.username;\n  },\n\n  /**\n   * Get user display name\n   */\n  getDisplayName: (user: SystemUser): string => {\n    const fullName = userHelpers.getFullName(user);\n    return fullName !== user.username ? `${fullName} (${user.username})` : user.username;\n  },\n\n  /**\n   * Check if user has permission\n   */\n  hasPermission: (user: SystemUser, permission: string): boolean => {\n    const rolePermissions = ROLE_PERMISSIONS[user.role] || [];\n    return rolePermissions.includes(permission);\n  },\n\n  /**\n   * Check if user is active\n   */\n  isActive: (user: SystemUser): boolean => {\n    return user.status === 'active';\n  },\n\n  /**\n   * Get user avatar URL or initials\n   */\n  getAvatarDisplay: (user: SystemUser): { type: 'url' | 'initials'; value: string } => {\n    if (user.avatar) {\n      return { type: 'url', value: user.avatar };\n    }\n    \n    const fullName = userHelpers.getFullName(user);\n    const initials = fullName\n      .split(' ')\n      .map(name => name.charAt(0).toUpperCase())\n      .slice(0, 2)\n      .join('');\n    \n    return { type: 'initials', value: initials || user.username.charAt(0).toUpperCase() };\n  },\n\n  /**\n   * Format last login time\n   */\n  formatLastLogin: (lastLogin?: string): string => {\n    if (!lastLogin) return 'Never';\n    \n    const date = new Date(lastLogin);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return 'Yesterday';\n    if (diffDays < 7) return `${diffDays} days ago`;\n    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;\n    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;\n    \n    return `${Math.floor(diffDays / 365)} years ago`;\n  },\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;CAEC;;;;;;;;;;AAmIM,MAAM,mBAAqD;IAChE,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,QAAQ;QACN;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW;QACT;QACA;QACA;QACA;KACD;AACH;AAKO,MAAM,cAA8C;IACzD,OAAO;IACP,QAAQ;IACR,WAAW;AACb;AAKO,MAAM,gBAA4C;IACvD,QAAQ;IACR,UAAU;IACV,WAAW;AACb;AAKO,MAAM,cAA8C;IACzD,OAAO;IACP,QAAQ;IACR,WAAW;AACb;AAKO,MAAM,gBAA4C;IACvD,QAAQ;IACR,UAAU;IACV,WAAW;AACb;AAKO,MAAM,kBAAkB;IAC7B,UAAU;QACR,KAAK;QACL,KAAK;QACL,SAAS;QACT,SAAS;IACX;IACA,OAAO;QACL,SAAS;QACT,SAAS;IACX;IACA,UAAU;QACR,KAAK;QACL,SAAS;QACT,SAAS;IACX;IACA,WAAW;QACT,KAAK;QACL,SAAS;IACX;IACA,UAAU;QACR,KAAK;QACL,SAAS;IACX;AACF;AAKO,MAAM,sBAAsC;IACjD,MAAM;IACN,OAAO;IACP,QAAQ;IACR,WAAW;AACb;AAKO,MAAM,cAAc;IACzB;;GAEC,GACD,aAAa,CAAC;QACZ,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,EAAE;YACnC,OAAO,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;QAC7C;QACA,IAAI,KAAK,SAAS,EAAE;YAClB,OAAO,KAAK,SAAS;QACvB;QACA,IAAI,KAAK,QAAQ,EAAE;YACjB,OAAO,KAAK,QAAQ;QACtB;QACA,OAAO,KAAK,QAAQ;IACtB;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,MAAM,WAAW,YAAY,WAAW,CAAC;QACzC,OAAO,aAAa,KAAK,QAAQ,GAAG,GAAG,SAAS,EAAE,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,QAAQ;IACtF;IAEA;;GAEC,GACD,eAAe,CAAC,MAAkB;QAChC,MAAM,kBAAkB,gBAAgB,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE;QACzD,OAAO,gBAAgB,QAAQ,CAAC;IAClC;IAEA;;GAEC,GACD,UAAU,CAAC;QACT,OAAO,KAAK,MAAM,KAAK;IACzB;IAEA;;GAEC,GACD,kBAAkB,CAAC;QACjB,IAAI,KAAK,MAAM,EAAE;YACf,OAAO;gBAAE,MAAM;gBAAO,OAAO,KAAK,MAAM;YAAC;QAC3C;QAEA,MAAM,WAAW,YAAY,WAAW,CAAC;QACzC,MAAM,WAAW,SACd,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,KAAK,CAAC,GAAG,GACT,IAAI,CAAC;QAER,OAAO;YAAE,MAAM;YAAY,OAAO,YAAY,KAAK,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;QAAG;IACtF;IAEA;;GAEC,GACD,iBAAiB,CAAC;QAChB,IAAI,CAAC,WAAW,OAAO;QAEvB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;QAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;QAEzD,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,WAAW,GAAG,OAAO,GAAG,SAAS,SAAS,CAAC;QAC/C,IAAI,WAAW,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,GAAG,UAAU,CAAC;QACjE,IAAI,WAAW,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,IAAI,WAAW,CAAC;QAEpE,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,KAAK,UAAU,CAAC;IAClD;AACF"}}, {"offset": {"line": 2366, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2372, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/users.ts"], "sourcesContent": ["/**\n * User API Hooks\n * TanStack Query hooks for SystemUser management\n */\n\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { message } from 'antd';\nimport {\n  SystemUser,\n  CreateUserRequest,\n  UpdateUserRequest,\n  ChangePasswordRequest,\n  UserListParams,\n  UserListResponse,\n  UserStatistics,\n  UserActivity,\n  UserSession,\n  DEFAULT_USER_PARAMS,\n} from '@/types/user';\n\n/**\n * API endpoints\n */\nconst API_ENDPOINTS = {\n  users: '/api/system-auth/users',\n  userById: (id: string) => `/api/system-auth/users/${id}`,\n  userStats: '/api/system-auth/users/statistics',\n  userActivity: (id: string) => `/api/system-auth/users/${id}/activity`,\n  userSessions: (id: string) => `/api/system-auth/users/${id}/sessions`,\n  changePassword: (id: string) => `/api/system-auth/users/${id}/change-password`,\n  resetPassword: (id: string) => `/api/system-auth/users/${id}/reset-password`,\n};\n\n/**\n * Query keys\n */\nexport const userQueryKeys = {\n  all: ['users'] as const,\n  lists: () => [...userQueryKeys.all, 'list'] as const,\n  list: (params: UserListParams) => [...userQueryKeys.lists(), params] as const,\n  details: () => [...userQueryKeys.all, 'detail'] as const,\n  detail: (id: string) => [...userQueryKeys.details(), id] as const,\n  statistics: () => [...userQueryKeys.all, 'statistics'] as const,\n  activity: (id: string) => [...userQueryKeys.all, 'activity', id] as const,\n  sessions: (id: string) => [...userQueryKeys.all, 'sessions', id] as const,\n};\n\n/**\n * Mock data for development\n */\nconst mockUsers: SystemUser[] = [\n  {\n    id: '1',\n    username: 'admin',\n    email: '<EMAIL>',\n    firstName: 'System',\n    lastName: 'Administrator',\n    role: 'admin',\n    status: 'active',\n    lastLogin: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago\n    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(), // 30 days ago\n    updatedAt: new Date().toISOString(),\n    createdBy: 'system',\n  },\n  {\n    id: '2',\n    username: 'editor1',\n    email: '<EMAIL>',\n    firstName: 'John',\n    lastName: 'Editor',\n    role: 'editor',\n    status: 'active',\n    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago\n    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15).toISOString(), // 15 days ago\n    updatedAt: new Date().toISOString(),\n    createdBy: '1',\n  },\n  {\n    id: '3',\n    username: 'moderator1',\n    email: '<EMAIL>',\n    firstName: 'Jane',\n    lastName: 'Moderator',\n    role: 'moderator',\n    status: 'active',\n    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago\n    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(), // 7 days ago\n    updatedAt: new Date().toISOString(),\n    createdBy: '1',\n  },\n  {\n    id: '4',\n    username: 'inactive_user',\n    email: '<EMAIL>',\n    firstName: 'Inactive',\n    lastName: 'User',\n    role: 'editor',\n    status: 'inactive',\n    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 60).toISOString(), // 60 days ago\n    updatedAt: new Date().toISOString(),\n    createdBy: '1',\n  },\n];\n\n/**\n * Mock API functions\n */\nconst mockAPI = {\n  getUsers: async (params: UserListParams): Promise<UserListResponse> => {\n    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay\n    \n    let filteredUsers = [...mockUsers];\n    \n    // Apply filters\n    if (params.search) {\n      const search = params.search.toLowerCase();\n      filteredUsers = filteredUsers.filter(user =>\n        user.username.toLowerCase().includes(search) ||\n        user.email.toLowerCase().includes(search) ||\n        user.firstName?.toLowerCase().includes(search) ||\n        user.lastName?.toLowerCase().includes(search)\n      );\n    }\n    \n    if (params.role) {\n      filteredUsers = filteredUsers.filter(user => user.role === params.role);\n    }\n    \n    if (params.status) {\n      filteredUsers = filteredUsers.filter(user => user.status === params.status);\n    }\n    \n    // Apply sorting\n    if (params.sortBy) {\n      filteredUsers.sort((a, b) => {\n        const aValue = a[params.sortBy!] || '';\n        const bValue = b[params.sortBy!] || '';\n        const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n        return params.sortOrder === 'desc' ? -comparison : comparison;\n      });\n    }\n    \n    // Apply pagination\n    const page = params.page || 1;\n    const limit = params.limit || 20;\n    const startIndex = (page - 1) * limit;\n    const endIndex = startIndex + limit;\n    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);\n    \n    return {\n      users: paginatedUsers,\n      total: filteredUsers.length,\n      page,\n      limit,\n      totalPages: Math.ceil(filteredUsers.length / limit),\n    };\n  },\n\n  getUser: async (id: string): Promise<SystemUser> => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    const user = mockUsers.find(u => u.id === id);\n    if (!user) throw new Error('User not found');\n    return user;\n  },\n\n  createUser: async (data: CreateUserRequest): Promise<SystemUser> => {\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    const newUser: SystemUser = {\n      id: String(mockUsers.length + 1),\n      ...data,\n      status: data.status || 'active',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      createdBy: '1', // Current user\n    };\n    mockUsers.push(newUser);\n    return newUser;\n  },\n\n  updateUser: async (id: string, data: UpdateUserRequest): Promise<SystemUser> => {\n    await new Promise(resolve => setTimeout(resolve, 800));\n    const userIndex = mockUsers.findIndex(u => u.id === id);\n    if (userIndex === -1) throw new Error('User not found');\n    \n    mockUsers[userIndex] = {\n      ...mockUsers[userIndex],\n      ...data,\n      updatedAt: new Date().toISOString(),\n    };\n    \n    return mockUsers[userIndex];\n  },\n\n  deleteUser: async (id: string): Promise<void> => {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const userIndex = mockUsers.findIndex(u => u.id === id);\n    if (userIndex === -1) throw new Error('User not found');\n    mockUsers.splice(userIndex, 1);\n  },\n\n  getStatistics: async (): Promise<UserStatistics> => {\n    await new Promise(resolve => setTimeout(resolve, 400));\n    \n    const total = mockUsers.length;\n    const active = mockUsers.filter(u => u.status === 'active').length;\n    const inactive = mockUsers.filter(u => u.status === 'inactive').length;\n    const suspended = mockUsers.filter(u => u.status === 'suspended').length;\n    \n    const byRole = {\n      admin: mockUsers.filter(u => u.role === 'admin').length,\n      editor: mockUsers.filter(u => u.role === 'editor').length,\n      moderator: mockUsers.filter(u => u.role === 'moderator').length,\n    };\n    \n    const recentLogins = mockUsers.filter(u => {\n      if (!u.lastLogin) return false;\n      const lastLogin = new Date(u.lastLogin);\n      const dayAgo = new Date(Date.now() - 1000 * 60 * 60 * 24);\n      return lastLogin > dayAgo;\n    }).length;\n    \n    const monthAgo = new Date(Date.now() - 1000 * 60 * 60 * 24 * 30);\n    const newThisMonth = mockUsers.filter(u => {\n      const created = new Date(u.createdAt);\n      return created > monthAgo;\n    }).length;\n    \n    return {\n      total,\n      active,\n      inactive,\n      suspended,\n      byRole,\n      recentLogins,\n      newThisMonth,\n    };\n  },\n};\n\n/**\n * Get users list\n */\nexport function useUsers(params: UserListParams = DEFAULT_USER_PARAMS) {\n  return useQuery({\n    queryKey: userQueryKeys.list(params),\n    queryFn: () => mockAPI.getUsers(params),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Get user by ID\n */\nexport function useUser(id: string) {\n  return useQuery({\n    queryKey: userQueryKeys.detail(id),\n    queryFn: () => mockAPI.getUser(id),\n    enabled: !!id,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Get user statistics\n */\nexport function useUserStatistics() {\n  return useQuery({\n    queryKey: userQueryKeys.statistics(),\n    queryFn: () => mockAPI.getStatistics(),\n    staleTime: 2 * 60 * 1000, // 2 minutes\n  });\n}\n\n/**\n * Create user mutation\n */\nexport function useCreateUser() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data: CreateUserRequest) => mockAPI.createUser(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.statistics() });\n      message.success('User created successfully');\n    },\n    onError: (error: Error) => {\n      message.error(`Failed to create user: ${error.message}`);\n    },\n  });\n}\n\n/**\n * Update user mutation\n */\nexport function useUpdateUser() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ id, data }: { id: string; data: UpdateUserRequest }) => \n      mockAPI.updateUser(id, data),\n    onSuccess: (updatedUser) => {\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.detail(updatedUser.id) });\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.statistics() });\n      message.success('User updated successfully');\n    },\n    onError: (error: Error) => {\n      message.error(`Failed to update user: ${error.message}`);\n    },\n  });\n}\n\n/**\n * Delete user mutation\n */\nexport function useDeleteUser() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (id: string) => mockAPI.deleteUser(id),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.statistics() });\n      message.success('User deleted successfully');\n    },\n    onError: (error: Error) => {\n      message.error(`Failed to delete user: ${error.message}`);\n    },\n  });\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAID;AAFA;AAAA;AAAA;AACA;;;;AAcA;;CAEC,GACD,MAAM,gBAAgB;IACpB,OAAO;IACP,UAAU,CAAC,KAAe,CAAC,uBAAuB,EAAE,IAAI;IACxD,WAAW;IACX,cAAc,CAAC,KAAe,CAAC,uBAAuB,EAAE,GAAG,SAAS,CAAC;IACrE,cAAc,CAAC,KAAe,CAAC,uBAAuB,EAAE,GAAG,SAAS,CAAC;IACrE,gBAAgB,CAAC,KAAe,CAAC,uBAAuB,EAAE,GAAG,gBAAgB,CAAC;IAC9E,eAAe,CAAC,KAAe,CAAC,uBAAuB,EAAE,GAAG,eAAe,CAAC;AAC9E;AAKO,MAAM,gBAAgB;IAC3B,KAAK;QAAC;KAAQ;IACd,OAAO,IAAM;eAAI,cAAc,GAAG;YAAE;SAAO;IAC3C,MAAM,CAAC,SAA2B;eAAI,cAAc,KAAK;YAAI;SAAO;IACpE,SAAS,IAAM;eAAI,cAAc,GAAG;YAAE;SAAS;IAC/C,QAAQ,CAAC,KAAe;eAAI,cAAc,OAAO;YAAI;SAAG;IACxD,YAAY,IAAM;eAAI,cAAc,GAAG;YAAE;SAAa;IACtD,UAAU,CAAC,KAAe;eAAI,cAAc,GAAG;YAAE;YAAY;SAAG;IAChE,UAAU,CAAC,KAAe;eAAI,cAAc,GAAG;YAAE;YAAY;SAAG;AAClE;AAEA;;CAEC,GACD,MAAM,YAA0B;IAC9B;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,IAAI,WAAW;QAC5D,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,KAAK,IAAI,WAAW;QACtE,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,GAAG,WAAW;QAChE,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,KAAK,IAAI,WAAW;QACtE,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,IAAI,WAAW;QACjE,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,KAAK,GAAG,WAAW;QACrE,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,KAAK,IAAI,WAAW;QACtE,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;CACD;AAED;;CAEC,GACD,MAAM,UAAU;IACd,UAAU,OAAO;QACf,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,OAAO,yBAAyB;QAEjF,IAAI,gBAAgB;eAAI;SAAU;QAElC,gBAAgB;QAChB,IAAI,OAAO,MAAM,EAAE;YACjB,MAAM,SAAS,OAAO,MAAM,CAAC,WAAW;YACxC,gBAAgB,cAAc,MAAM,CAAC,CAAA,OACnC,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WACrC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAClC,KAAK,SAAS,EAAE,cAAc,SAAS,WACvC,KAAK,QAAQ,EAAE,cAAc,SAAS;QAE1C;QAEA,IAAI,OAAO,IAAI,EAAE;YACf,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,OAAO,IAAI;QACxE;QAEA,IAAI,OAAO,MAAM,EAAE;YACjB,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,OAAO,MAAM;QAC5E;QAEA,gBAAgB;QAChB,IAAI,OAAO,MAAM,EAAE;YACjB,cAAc,IAAI,CAAC,CAAC,GAAG;gBACrB,MAAM,SAAS,CAAC,CAAC,OAAO,MAAM,CAAE,IAAI;gBACpC,MAAM,SAAS,CAAC,CAAC,OAAO,MAAM,CAAE,IAAI;gBACpC,MAAM,aAAa,SAAS,SAAS,CAAC,IAAI,SAAS,SAAS,IAAI;gBAChE,OAAO,OAAO,SAAS,KAAK,SAAS,CAAC,aAAa;YACrD;QACF;QAEA,mBAAmB;QACnB,MAAM,OAAO,OAAO,IAAI,IAAI;QAC5B,MAAM,QAAQ,OAAO,KAAK,IAAI;QAC9B,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;QAChC,MAAM,WAAW,aAAa;QAC9B,MAAM,iBAAiB,cAAc,KAAK,CAAC,YAAY;QAEvD,OAAO;YACL,OAAO;YACP,OAAO,cAAc,MAAM;YAC3B;YACA;YACA,YAAY,KAAK,IAAI,CAAC,cAAc,MAAM,GAAG;QAC/C;IACF;IAEA,SAAS,OAAO;QACd,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAC3B,OAAO;IACT;IAEA,YAAY,OAAO;QACjB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,MAAM,UAAsB;YAC1B,IAAI,OAAO,UAAU,MAAM,GAAG;YAC9B,GAAG,IAAI;YACP,QAAQ,KAAK,MAAM,IAAI;YACvB,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW;QACb;QACA,UAAU,IAAI,CAAC;QACf,OAAO;IACT;IAEA,YAAY,OAAO,IAAY;QAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACpD,IAAI,cAAc,CAAC,GAAG,MAAM,IAAI,MAAM;QAEtC,SAAS,CAAC,UAAU,GAAG;YACrB,GAAG,SAAS,CAAC,UAAU;YACvB,GAAG,IAAI;YACP,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,OAAO,SAAS,CAAC,UAAU;IAC7B;IAEA,YAAY,OAAO;QACjB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACpD,IAAI,cAAc,CAAC,GAAG,MAAM,IAAI,MAAM;QACtC,UAAU,MAAM,CAAC,WAAW;IAC9B;IAEA,eAAe;QACb,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,MAAM,QAAQ,UAAU,MAAM;QAC9B,MAAM,SAAS,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;QAClE,MAAM,WAAW,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;QACtE,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QAExE,MAAM,SAAS;YACb,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;YACvD,QAAQ,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;YACzD,WAAW,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,MAAM;QACjE;QAEA,MAAM,eAAe,UAAU,MAAM,CAAC,CAAA;YACpC,IAAI,CAAC,EAAE,SAAS,EAAE,OAAO;YACzB,MAAM,YAAY,IAAI,KAAK,EAAE,SAAS;YACtC,MAAM,SAAS,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK;YACtD,OAAO,YAAY;QACrB,GAAG,MAAM;QAET,MAAM,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,KAAK;QAC7D,MAAM,eAAe,UAAU,MAAM,CAAC,CAAA;YACpC,MAAM,UAAU,IAAI,KAAK,EAAE,SAAS;YACpC,OAAO,UAAU;QACnB,GAAG,MAAM;QAET,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;AACF;AAKO,SAAS,SAAS,SAAyB,oHAAA,CAAA,sBAAmB;IACnE,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,cAAc,IAAI,CAAC;QAC7B,SAAS,IAAM,QAAQ,QAAQ,CAAC;QAChC,WAAW,IAAI,KAAK;IACtB;AACF;AAKO,SAAS,QAAQ,EAAU;IAChC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,cAAc,MAAM,CAAC;QAC/B,SAAS,IAAM,QAAQ,OAAO,CAAC;QAC/B,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AACF;AAKO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,cAAc,UAAU;QAClC,SAAS,IAAM,QAAQ,aAAa;QACpC,WAAW,IAAI,KAAK;IACtB;AACF;AAKO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OAA4B,QAAQ,UAAU,CAAC;QAC5D,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU,cAAc,KAAK;YAAG;YAChE,YAAY,iBAAiB,CAAC;gBAAE,UAAU,cAAc,UAAU;YAAG;YACrE,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB;QACA,SAAS,CAAC;YACR,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;QACzD;IACF;AACF;AAKO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,EAAE,EAAE,EAAE,IAAI,EAA2C,GAChE,QAAQ,UAAU,CAAC,IAAI;QACzB,WAAW,CAAC;YACV,YAAY,iBAAiB,CAAC;gBAAE,UAAU,cAAc,KAAK;YAAG;YAChE,YAAY,iBAAiB,CAAC;gBAAE,UAAU,cAAc,MAAM,CAAC,YAAY,EAAE;YAAE;YAC/E,YAAY,iBAAiB,CAAC;gBAAE,UAAU,cAAc,UAAU;YAAG;YACrE,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB;QACA,SAAS,CAAC;YACR,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;QACzD;IACF;AACF;AAKO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,KAAe,QAAQ,UAAU,CAAC;QAC/C,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU,cAAc,KAAK;YAAG;YAChE,YAAY,iBAAiB,CAAC;gBAAE,UAAU,cAAc,UAAU;YAAG;YACrE,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB;QACA,SAAS,CAAC;YACR,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;QACzD;IACF;AACF"}}, {"offset": {"line": 2683, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2689, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/broadcast-hooks.ts"], "sourcesContent": ["/**\n * Broadcast Links API Hooks\n * Hooks for broadcast links management operations\n */\n\n'use client';\n\nimport { queryKeys } from '@/lib/query-client';\nimport { BroadcastQueries } from '@/lib/query-types';\nimport { PaginatedResponse } from '@/lib/query-utils';\nimport { \n  useBaseQuery, \n  usePaginatedQuery,\n  useBaseMutation,\n  useOptimisticMutation,\n  useApiHookUtils \n} from './base-hooks';\n\n/**\n * Hook for getting broadcast links\n */\nexport function useBroadcastLinks(params?: BroadcastQueries.BroadcastLinkQueryParams) {\n  const queryParams = new URLSearchParams();\n  if (params?.page) queryParams.set('page', params.page.toString());\n  if (params?.limit) queryParams.set('limit', params.limit.toString());\n  if (params?.fixtureId) queryParams.set('fixtureId', params.fixtureId);\n  if (params?.quality) queryParams.set('quality', params.quality);\n  if (params?.language) queryParams.set('language', params.language);\n  if (params?.isActive !== undefined) queryParams.set('isActive', params.isActive.toString());\n  if (params?.query) queryParams.set('query', params.query);\n\n  return usePaginatedQuery(\n    [...queryKeys.broadcast.links(), params],\n    async (): Promise<PaginatedResponse<BroadcastQueries.BroadcastLink>> => {\n      const response = await fetch(`/api/broadcast-links?${queryParams.toString()}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch broadcast links: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 2 * 60 * 1000, // 2 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting specific broadcast link\n */\nexport function useBroadcastLink(linkId: string) {\n  return useBaseQuery(\n    queryKeys.broadcast.link(linkId),\n    async (): Promise<BroadcastQueries.BroadcastLink> => {\n      const response = await fetch(`/api/broadcast-links/${linkId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch broadcast link: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!linkId,\n      staleTime: 2 * 60 * 1000, // 2 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting broadcast links for a specific fixture\n */\nexport function useFixtureBroadcastLinks(fixtureId: string) {\n  return useBaseQuery(\n    queryKeys.broadcast.fixture(fixtureId),\n    async (): Promise<BroadcastQueries.BroadcastLink[]> => {\n      const response = await fetch(`/api/broadcast-links/fixture/${fixtureId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch fixture broadcast links: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!fixtureId,\n      staleTime: 1 * 60 * 1000, // 1 minute - links for live fixtures change frequently\n    }\n  );\n}\n\n/**\n * Hook for creating broadcast link\n */\nexport function useCreateBroadcastLink() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useOptimisticMutation<BroadcastQueries.BroadcastLink, BroadcastQueries.CreateBroadcastLinkRequest>(\n    async (data) => {\n      const response = await fetch('/api/broadcast-links', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to create broadcast link: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: (data) => {\n        // Invalidate broadcast links queries\n        invalidateQueries(queryKeys.broadcast.links());\n        invalidateQueries(queryKeys.broadcast.fixture(data.fixtureId));\n        console.log('✅ Broadcast link created successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Failed to create broadcast link:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for updating broadcast link\n */\nexport function useUpdateBroadcastLink() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useOptimisticMutation<BroadcastQueries.BroadcastLink, { id: string; data: BroadcastQueries.UpdateBroadcastLinkRequest }>(\n    async ({ id, data }) => {\n      const response = await fetch(`/api/broadcast-links/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to update broadcast link: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: (data) => {\n        // Invalidate specific link and related queries\n        invalidateQueries(queryKeys.broadcast.link(data.id));\n        invalidateQueries(queryKeys.broadcast.links());\n        invalidateQueries(queryKeys.broadcast.fixture(data.fixtureId));\n        console.log('✅ Broadcast link updated successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Failed to update broadcast link:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for deleting broadcast link\n */\nexport function useDeleteBroadcastLink() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string }, string>(\n    async (linkId) => {\n      const response = await fetch(`/api/broadcast-links/${linkId}`, {\n        method: 'DELETE',\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to delete broadcast link: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: (_, linkId) => {\n        // Invalidate broadcast links queries\n        invalidateQueries(queryKeys.broadcast.links());\n        invalidateQueries(queryKeys.broadcast.link(linkId));\n        console.log('✅ Broadcast link deleted successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Failed to delete broadcast link:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for toggling broadcast link active status\n */\nexport function useToggleBroadcastLinkStatus() {\n  const { invalidateQueries, updateQueryData } = useApiHookUtils();\n\n  return useOptimisticMutation<BroadcastQueries.BroadcastLink, { id: string; isActive: boolean }>(\n    async ({ id, isActive }) => {\n      const response = await fetch(`/api/broadcast-links/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ isActive }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to toggle broadcast link status: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onMutate: async ({ id, isActive }) => {\n        // Optimistically update the link status\n        const linkQueryKey = queryKeys.broadcast.link(id);\n        const previousLink = updateQueryData<BroadcastQueries.BroadcastLink>(\n          linkQueryKey,\n          (old) => old ? { ...old, isActive } : old\n        );\n\n        return { previousLink, linkQueryKey };\n      },\n      onError: (error, variables, context) => {\n        // Revert optimistic update on error\n        if (context?.previousLink && context?.linkQueryKey) {\n          updateQueryData(context.linkQueryKey, () => context.previousLink);\n        }\n        console.error('❌ Failed to toggle broadcast link status:', error);\n      },\n      onSuccess: (data) => {\n        // Invalidate related queries\n        invalidateQueries(queryKeys.broadcast.links());\n        invalidateQueries(queryKeys.broadcast.fixture(data.fixtureId));\n        console.log('✅ Broadcast link status toggled successfully');\n      },\n    }\n  );\n}\n\n/**\n * Composite hook for broadcast links operations\n */\nexport function useBroadcastLinksManager() {\n  const createLink = useCreateBroadcastLink();\n  const updateLink = useUpdateBroadcastLink();\n  const deleteLink = useDeleteBroadcastLink();\n  const toggleStatus = useToggleBroadcastLinkStatus();\n\n  return {\n    // Mutations\n    createLink,\n    updateLink,\n    deleteLink,\n    toggleStatus,\n    \n    // Actions\n    createBroadcastLink: createLink.mutate,\n    updateBroadcastLink: updateLink.mutate,\n    deleteBroadcastLink: deleteLink.mutate,\n    toggleLinkStatus: toggleStatus.mutate,\n    \n    // State\n    isCreating: createLink.isPending,\n    isUpdating: updateLink.isPending,\n    isDeleting: deleteLink.isPending,\n    isToggling: toggleStatus.isPending,\n    isLoading: createLink.isPending || updateLink.isPending || deleteLink.isPending || toggleStatus.isPending,\n    \n    // Errors\n    createError: createLink.error,\n    updateError: updateLink.error,\n    deleteError: deleteLink.error,\n    toggleError: toggleStatus.error,\n  };\n}\n\n/**\n * Hook for broadcast links by quality\n */\nexport function useBroadcastLinksByQuality(quality: 'HD' | 'SD' | 'Mobile') {\n  return useBroadcastLinks({ quality, isActive: true });\n}\n\n/**\n * Hook for broadcast links by language\n */\nexport function useBroadcastLinksByLanguage(language: string) {\n  return useBroadcastLinks({ language, isActive: true });\n}\n\n/**\n * Hook for active broadcast links\n */\nexport function useActiveBroadcastLinks() {\n  return useBroadcastLinks({ isActive: true });\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAID;AAGA;AALA;;;AAgBO,SAAS,kBAAkB,MAAkD;IAClF,MAAM,cAAc,IAAI;IACxB,IAAI,QAAQ,MAAM,YAAY,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,QAAQ,WAAW,YAAY,GAAG,CAAC,aAAa,OAAO,SAAS;IACpE,IAAI,QAAQ,SAAS,YAAY,GAAG,CAAC,WAAW,OAAO,OAAO;IAC9D,IAAI,QAAQ,UAAU,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ;IACjE,IAAI,QAAQ,aAAa,WAAW,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ,CAAC,QAAQ;IACxF,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK;IAExD,OAAO,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EACrB;WAAI,6HAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;QAAI;KAAO,EACxC;QACE,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,YAAY,QAAQ,IAAI;QAE7E,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,WAAW,IAAI,KAAK;IACtB;AAEJ;AAKO,SAAS,iBAAiB,MAAc;IAC7C,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB,6HAAA,CAAA,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC,SACzB;QACE,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,QAAQ;QAE7D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,SAAS,UAAU,EAAE;QAC1E;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;AAKO,SAAS,yBAAyB,SAAiB;IACxD,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB,6HAAA,CAAA,YAAS,CAAC,SAAS,CAAC,OAAO,CAAC,YAC5B;QACE,MAAM,WAAW,MAAM,MAAM,CAAC,6BAA6B,EAAE,WAAW;QAExE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,yCAAyC,EAAE,SAAS,UAAU,EAAE;QACnF;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;AAKO,SAAS;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,oIAAA,CAAA,wBAAqB,AAAD,EACzB,OAAO;QACL,MAAM,WAAW,MAAM,MAAM,wBAAwB;YACnD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,WAAW,CAAC;YACV,qCAAqC;YACrC,kBAAkB,6HAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;YAC3C,kBAAkB,6HAAA,CAAA,YAAS,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,SAAS;YAC5D,QAAQ,GAAG,CAAC;QACd;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF;AAEJ;AAKO,SAAS;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,oIAAA,CAAA,wBAAqB,AAAD,EACzB,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE;QACjB,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,IAAI,EAAE;YACzD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,WAAW,CAAC;YACV,+CAA+C;YAC/C,kBAAkB,6HAAA,CAAA,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE;YAClD,kBAAkB,6HAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;YAC3C,kBAAkB,6HAAA,CAAA,YAAS,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,SAAS;YAC5D,QAAQ,GAAG,CAAC;QACd;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF;AAEJ;AAKO,SAAS;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EACnB,OAAO;QACL,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,QAAQ,EAAE;YAC7D,QAAQ;QACV;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,WAAW,CAAC,GAAG;YACb,qCAAqC;YACrC,kBAAkB,6HAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;YAC3C,kBAAkB,6HAAA,CAAA,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC;YAC3C,QAAQ,GAAG,CAAC;QACd;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF;AAEJ;AAKO,SAAS;IACd,MAAM,EAAE,iBAAiB,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD;IAE7D,OAAO,CAAA,GAAA,oIAAA,CAAA,wBAAqB,AAAD,EACzB,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE;QACrB,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,IAAI,EAAE;YACzD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAS;QAClC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,wCAAwC,EAAE,SAAS,UAAU,EAAE;QAClF;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,UAAU,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE;YAC/B,wCAAwC;YACxC,MAAM,eAAe,6HAAA,CAAA,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC;YAC9C,MAAM,eAAe,gBACnB,cACA,CAAC,MAAQ,MAAM;oBAAE,GAAG,GAAG;oBAAE;gBAAS,IAAI;YAGxC,OAAO;gBAAE;gBAAc;YAAa;QACtC;QACA,SAAS,CAAC,OAAO,WAAW;YAC1B,oCAAoC;YACpC,IAAI,SAAS,gBAAgB,SAAS,cAAc;gBAClD,gBAAgB,QAAQ,YAAY,EAAE,IAAM,QAAQ,YAAY;YAClE;YACA,QAAQ,KAAK,CAAC,6CAA6C;QAC7D;QACA,WAAW,CAAC;YACV,6BAA6B;YAC7B,kBAAkB,6HAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;YAC3C,kBAAkB,6HAAA,CAAA,YAAS,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,SAAS;YAC5D,QAAQ,GAAG,CAAC;QACd;IACF;AAEJ;AAKO,SAAS;IACd,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,eAAe;IAErB,OAAO;QACL,YAAY;QACZ;QACA;QACA;QACA;QAEA,UAAU;QACV,qBAAqB,WAAW,MAAM;QACtC,qBAAqB,WAAW,MAAM;QACtC,qBAAqB,WAAW,MAAM;QACtC,kBAAkB,aAAa,MAAM;QAErC,QAAQ;QACR,YAAY,WAAW,SAAS;QAChC,YAAY,WAAW,SAAS;QAChC,YAAY,WAAW,SAAS;QAChC,YAAY,aAAa,SAAS;QAClC,WAAW,WAAW,SAAS,IAAI,WAAW,SAAS,IAAI,WAAW,SAAS,IAAI,aAAa,SAAS;QAEzG,SAAS;QACT,aAAa,WAAW,KAAK;QAC7B,aAAa,WAAW,KAAK;QAC7B,aAAa,WAAW,KAAK;QAC7B,aAAa,aAAa,KAAK;IACjC;AACF;AAKO,SAAS,2BAA2B,OAA+B;IACxE,OAAO,kBAAkB;QAAE;QAAS,UAAU;IAAK;AACrD;AAKO,SAAS,4BAA4B,QAAgB;IAC1D,OAAO,kBAAkB;QAAE;QAAU,UAAU;IAAK;AACtD;AAKO,SAAS;IACd,OAAO,kBAAkB;QAAE,UAAU;IAAK;AAC5C"}}, {"offset": {"line": 2921, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2927, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/health-hooks.ts"], "sourcesContent": ["/**\n * Health Check API Hooks\n * Hooks for API health monitoring operations\n */\n\n'use client';\n\nimport { queryKeys } from '@/lib/query-client';\nimport { HealthQueries } from '@/lib/query-types';\nimport { useBackgroundSyncQuery, useBaseQuery } from './base-hooks';\n\n/**\n * Hook for API health check\n */\nexport function useApiHealth() {\n  return useBackgroundSyncQuery(\n    queryKeys.health.api(),\n    async (): Promise<HealthQueries.HealthResponse> => {\n      const response = await fetch('/api/health');\n\n      if (!response.ok) {\n        throw new Error(`Health check failed: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 30 * 1000, // 30 seconds\n      refetchInterval: 60 * 1000, // Refetch every minute\n      retry: 3,\n      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),\n    }\n  );\n}\n\n/**\n * Hook for database health check\n */\nexport function useDatabaseHealth() {\n  return useBaseQuery(\n    [...queryKeys.health.all, 'database'],\n    async (): Promise<{ status: 'up' | 'down'; responseTime: number }> => {\n      const startTime = performance.now();\n      const response = await fetch('/api/health/database');\n      const endTime = performance.now();\n\n      if (!response.ok) {\n        throw new Error(`Database health check failed: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      return {\n        ...data,\n        responseTime: endTime - startTime,\n      };\n    },\n    {\n      staleTime: 30 * 1000, // 30 seconds\n      retry: 2,\n    }\n  );\n}\n\n/**\n * Hook for external API health check\n */\nexport function useExternalApiHealth() {\n  return useBaseQuery(\n    [...queryKeys.health.all, 'external-api'],\n    async (): Promise<{ status: 'up' | 'down'; responseTime: number }> => {\n      const startTime = performance.now();\n      const response = await fetch('/api/health/external-api');\n      const endTime = performance.now();\n\n      if (!response.ok) {\n        throw new Error(`External API health check failed: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      return {\n        ...data,\n        responseTime: endTime - startTime,\n      };\n    },\n    {\n      staleTime: 60 * 1000, // 1 minute\n      retry: 2,\n    }\n  );\n}\n\n/**\n * Hook for comprehensive system health\n */\nexport function useSystemHealth() {\n  const apiHealth = useApiHealth();\n  const dbHealth = useDatabaseHealth();\n  const externalApiHealth = useExternalApiHealth();\n\n  const isLoading = apiHealth.isLoading || dbHealth.isLoading || externalApiHealth.isLoading;\n  const hasErrors = apiHealth.isError || dbHealth.isError || externalApiHealth.isError;\n\n  // Calculate overall health status\n  const getOverallStatus = (): 'healthy' | 'degraded' | 'unhealthy' => {\n    if (hasErrors) return 'unhealthy';\n    \n    const apiStatus = apiHealth.data?.status;\n    const dbStatus = dbHealth.data?.status;\n    const externalStatus = externalApiHealth.data?.status;\n\n    if (apiStatus === 'healthy' && dbStatus === 'up' && externalStatus === 'up') {\n      return 'healthy';\n    }\n    \n    if (apiStatus === 'unhealthy' || dbStatus === 'down') {\n      return 'unhealthy';\n    }\n    \n    return 'degraded';\n  };\n\n  // Calculate average response time\n  const getAverageResponseTime = (): number => {\n    const times = [\n      dbHealth.data?.responseTime,\n      externalApiHealth.data?.responseTime,\n    ].filter((time): time is number => typeof time === 'number');\n\n    if (times.length === 0) return 0;\n    return times.reduce((sum, time) => sum + time, 0) / times.length;\n  };\n\n  return {\n    // Individual health checks\n    api: apiHealth,\n    database: dbHealth,\n    externalApi: externalApiHealth,\n    \n    // Overall status\n    isLoading,\n    hasErrors,\n    overallStatus: getOverallStatus(),\n    averageResponseTime: getAverageResponseTime(),\n    \n    // Health data\n    healthData: {\n      api: apiHealth.data,\n      database: dbHealth.data,\n      externalApi: externalApiHealth.data,\n    },\n    \n    // Error information\n    errors: {\n      api: apiHealth.error,\n      database: dbHealth.error,\n      externalApi: externalApiHealth.error,\n    },\n    \n    // Refetch functions\n    refetchAll: () => {\n      apiHealth.refetch();\n      dbHealth.refetch();\n      externalApiHealth.refetch();\n    },\n  };\n}\n\n/**\n * Hook for monitoring API performance\n */\nexport function useApiPerformance() {\n  const systemHealth = useSystemHealth();\n\n  const getPerformanceMetrics = () => {\n    const { healthData } = systemHealth;\n    \n    return {\n      uptime: healthData.api?.uptime || 0,\n      responseTime: systemHealth.averageResponseTime,\n      status: systemHealth.overallStatus,\n      services: {\n        database: healthData.database?.status || 'unknown',\n        externalApi: healthData.externalApi?.status || 'unknown',\n      },\n      lastCheck: new Date().toISOString(),\n    };\n  };\n\n  return {\n    ...systemHealth,\n    performanceMetrics: getPerformanceMetrics(),\n    \n    // Performance indicators\n    isPerformanceGood: systemHealth.averageResponseTime < 1000, // Less than 1 second\n    isPerformanceFair: systemHealth.averageResponseTime < 3000, // Less than 3 seconds\n    isPerformancePoor: systemHealth.averageResponseTime >= 3000, // 3+ seconds\n  };\n}\n\n/**\n * Hook for health monitoring dashboard\n */\nexport function useHealthDashboard() {\n  const performance = useApiPerformance();\n  \n  const getDashboardData = () => {\n    const { healthData, overallStatus, averageResponseTime } = performance;\n    \n    return {\n      status: overallStatus,\n      uptime: healthData.api?.uptime || 0,\n      version: healthData.api?.version || 'unknown',\n      responseTime: averageResponseTime,\n      services: [\n        {\n          name: 'Database',\n          status: healthData.database?.status || 'unknown',\n          responseTime: healthData.database?.responseTime || 0,\n        },\n        {\n          name: 'External API',\n          status: healthData.externalApi?.status || 'unknown',\n          responseTime: healthData.externalApi?.responseTime || 0,\n        },\n      ],\n      lastUpdated: new Date().toISOString(),\n    };\n  };\n\n  return {\n    ...performance,\n    dashboardData: getDashboardData(),\n    \n    // Dashboard actions\n    refreshDashboard: performance.refetchAll,\n    \n    // Status indicators\n    statusColor: {\n      healthy: '#10b981', // green\n      degraded: '#f59e0b', // yellow\n      unhealthy: '#ef4444', // red\n    }[performance.overallStatus],\n    \n    statusIcon: {\n      healthy: '✅',\n      degraded: '⚠️',\n      unhealthy: '❌',\n    }[performance.overallStatus],\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAID;AAEA;AAJA;;;AASO,SAAS;IACd,OAAO,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD,EAC1B,6HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,GAAG,IACpB;QACE,MAAM,WAAW,MAAM,MAAM;QAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,SAAS,UAAU,EAAE;QAC/D;QAEA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,WAAW,KAAK;QAChB,iBAAiB,KAAK;QACtB,OAAO;QACP,YAAY,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;IACnE;AAEJ;AAKO,SAAS;IACd,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,6HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,GAAG;QAAE;KAAW,EACrC;QACE,MAAM,YAAY,YAAY,GAAG;QACjC,MAAM,WAAW,MAAM,MAAM;QAC7B,MAAM,UAAU,YAAY,GAAG;QAE/B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,SAAS,UAAU,EAAE;QACxE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;YACL,GAAG,IAAI;YACP,cAAc,UAAU;QAC1B;IACF,GACA;QACE,WAAW,KAAK;QAChB,OAAO;IACT;AAEJ;AAKO,SAAS;IACd,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,6HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,GAAG;QAAE;KAAe,EACzC;QACE,MAAM,YAAY,YAAY,GAAG;QACjC,MAAM,WAAW,MAAM,MAAM;QAC7B,MAAM,UAAU,YAAY,GAAG;QAE/B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,SAAS,UAAU,EAAE;QAC5E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;YACL,GAAG,IAAI;YACP,cAAc,UAAU;QAC1B;IACF,GACA;QACE,WAAW,KAAK;QAChB,OAAO;IACT;AAEJ;AAKO,SAAS;IACd,MAAM,YAAY;IAClB,MAAM,WAAW;IACjB,MAAM,oBAAoB;IAE1B,MAAM,YAAY,UAAU,SAAS,IAAI,SAAS,SAAS,IAAI,kBAAkB,SAAS;IAC1F,MAAM,YAAY,UAAU,OAAO,IAAI,SAAS,OAAO,IAAI,kBAAkB,OAAO;IAEpF,kCAAkC;IAClC,MAAM,mBAAmB;QACvB,IAAI,WAAW,OAAO;QAEtB,MAAM,YAAY,UAAU,IAAI,EAAE;QAClC,MAAM,WAAW,SAAS,IAAI,EAAE;QAChC,MAAM,iBAAiB,kBAAkB,IAAI,EAAE;QAE/C,IAAI,cAAc,aAAa,aAAa,QAAQ,mBAAmB,MAAM;YAC3E,OAAO;QACT;QAEA,IAAI,cAAc,eAAe,aAAa,QAAQ;YACpD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,kCAAkC;IAClC,MAAM,yBAAyB;QAC7B,MAAM,QAAQ;YACZ,SAAS,IAAI,EAAE;YACf,kBAAkB,IAAI,EAAE;SACzB,CAAC,MAAM,CAAC,CAAC,OAAyB,OAAO,SAAS;QAEnD,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;QAC/B,OAAO,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,MAAM,KAAK,MAAM,MAAM;IAClE;IAEA,OAAO;QACL,2BAA2B;QAC3B,KAAK;QACL,UAAU;QACV,aAAa;QAEb,iBAAiB;QACjB;QACA;QACA,eAAe;QACf,qBAAqB;QAErB,cAAc;QACd,YAAY;YACV,KAAK,UAAU,IAAI;YACnB,UAAU,SAAS,IAAI;YACvB,aAAa,kBAAkB,IAAI;QACrC;QAEA,oBAAoB;QACpB,QAAQ;YACN,KAAK,UAAU,KAAK;YACpB,UAAU,SAAS,KAAK;YACxB,aAAa,kBAAkB,KAAK;QACtC;QAEA,oBAAoB;QACpB,YAAY;YACV,UAAU,OAAO;YACjB,SAAS,OAAO;YAChB,kBAAkB,OAAO;QAC3B;IACF;AACF;AAKO,SAAS;IACd,MAAM,eAAe;IAErB,MAAM,wBAAwB;QAC5B,MAAM,EAAE,UAAU,EAAE,GAAG;QAEvB,OAAO;YACL,QAAQ,WAAW,GAAG,EAAE,UAAU;YAClC,cAAc,aAAa,mBAAmB;YAC9C,QAAQ,aAAa,aAAa;YAClC,UAAU;gBACR,UAAU,WAAW,QAAQ,EAAE,UAAU;gBACzC,aAAa,WAAW,WAAW,EAAE,UAAU;YACjD;YACA,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;IAEA,OAAO;QACL,GAAG,YAAY;QACf,oBAAoB;QAEpB,yBAAyB;QACzB,mBAAmB,aAAa,mBAAmB,GAAG;QACtD,mBAAmB,aAAa,mBAAmB,GAAG;QACtD,mBAAmB,aAAa,mBAAmB,IAAI;IACzD;AACF;AAKO,SAAS;IACd,MAAM,eAAc;IAEpB,MAAM,mBAAmB;QACvB,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,mBAAmB,EAAE,GAAG;QAE3D,OAAO;YACL,QAAQ;YACR,QAAQ,WAAW,GAAG,EAAE,UAAU;YAClC,SAAS,WAAW,GAAG,EAAE,WAAW;YACpC,cAAc;YACd,UAAU;gBACR;oBACE,MAAM;oBACN,QAAQ,WAAW,QAAQ,EAAE,UAAU;oBACvC,cAAc,WAAW,QAAQ,EAAE,gBAAgB;gBACrD;gBACA;oBACE,MAAM;oBACN,QAAQ,WAAW,WAAW,EAAE,UAAU;oBAC1C,cAAc,WAAW,WAAW,EAAE,gBAAgB;gBACxD;aACD;YACD,aAAa,IAAI,OAAO,WAAW;QACrC;IACF;IAEA,OAAO;QACL,GAAG,YAAW;QACd,eAAe;QAEf,oBAAoB;QACpB,kBAAkB,aAAY,UAAU;QAExC,oBAAoB;QACpB,aAAa,CAAA;YACX,SAAS;YACT,UAAU;YACV,WAAW;QACb,CAAA,CAAC,CAAC,aAAY,aAAa,CAAC;QAE5B,YAAY,CAAA;YACV,SAAS;YACT,UAAU;YACV,WAAW;QACb,CAAA,CAAC,CAAC,aAAY,aAAa,CAAC;IAC9B;AACF"}}, {"offset": {"line": 3124, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3130, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/index.ts"], "sourcesContent": ["/**\n * API Hooks Index\n * Central export for all API hooks\n */\n\n// Base hooks and utilities\nexport * from './base-hooks';\n\n// Domain-specific hooks\nexport * from './auth-hooks';\nexport * from './football-hooks';\nexport * from './users';\nexport * from './broadcast-hooks';\nexport * from './health-hooks';\n\n// Re-export TanStack Query hooks for convenience\nexport {\n  useQuery,\n  useMutation,\n  useQueryClient,\n  useInfiniteQuery,\n} from '@tanstack/react-query';\n\n/**\n * API hooks library metadata\n */\nexport const API_HOOKS_VERSION = '1.0.0';\nexport const API_HOOKS_NAME = 'APISportsGame API Hooks';\n\n/**\n * Quick setup function for API hooks\n */\nexport function setupApiHooks() {\n  console.log(`${API_HOOKS_NAME} v${API_HOOKS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,2BAA2B;;;;;;;;;;;;;AAqBpB,MAAM,oBAAoB;AAC1B,MAAM,iBAAiB;AAKvB,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,eAAe,EAAE,EAAE,kBAAkB,YAAY,CAAC;AACnE"}}, {"offset": {"line": 3151, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3171, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-header.tsx"], "sourcesContent": ["/**\n * App Header Component\n * Main header for the APISportsGame CMS\n */\n\n'use client';\n\nimport React from 'react';\nimport { Layout, Space, Button, Dropdown, Avatar, Badge, Tooltip, Typography } from 'antd';\nimport {\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  BellOutlined,\n  UserOutlined,\n  SettingOutlined,\n  LogoutOutlined,\n  SunOutlined,\n  MoonOutlined,\n  GlobalOutlined,\n} from '@ant-design/icons';\nimport { useTheme, useThemeStyles } from '@/theme';\nimport { useAuth } from '@/hooks/api';\n\nconst { Header } = Layout;\nconst { Text } = Typography;\n\n/**\n * App header props\n */\nexport interface AppHeaderProps {\n  sidebarCollapsed: boolean;\n  onSidebarToggle: () => void;\n  isMobile: boolean;\n  showSidebarToggle?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * App Header component\n */\nexport function AppHeader({\n  sidebarCollapsed,\n  onSidebarToggle,\n  isMobile,\n  showSidebarToggle = true,\n  className,\n  style,\n}: AppHeaderProps) {\n  const { theme, toggleTheme, isDark } = useTheme();\n  const themeStyles = useThemeStyles();\n  const auth = useAuth();\n\n  // User menu items\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: 'Profile',\n      onClick: () => console.log('Profile clicked'),\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: 'Settings',\n      onClick: () => console.log('Settings clicked'),\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: 'Logout',\n      onClick: () => auth.logoutUser(),\n      danger: true,\n    },\n  ];\n\n  // Notification menu items\n  const notificationItems = [\n    {\n      key: '1',\n      label: (\n        <div style={{ padding: '8px 0' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>\n            New fixture sync completed\n          </div>\n          <div style={{ fontSize: '12px', color: themeStyles.getTextColor('secondary') }}>\n            2 minutes ago\n          </div>\n        </div>\n      ),\n    },\n    {\n      key: '2',\n      label: (\n        <div style={{ padding: '8px 0' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>\n            User John Doe registered\n          </div>\n          <div style={{ fontSize: '12px', color: themeStyles.getTextColor('secondary') }}>\n            5 minutes ago\n          </div>\n        </div>\n      ),\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'view-all',\n      label: (\n        <div style={{ textAlign: 'center', padding: '8px 0' }}>\n          <Button type=\"link\" size=\"small\">\n            View All Notifications\n          </Button>\n        </div>\n      ),\n    },\n  ];\n\n  const headerStyle: React.CSSProperties = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    zIndex: 1000,\n    height: '64px',\n    padding: '0 24px',\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    ...style,\n  };\n\n  return (\n    <Header className={className} style={headerStyle}>\n      {/* Left section */}\n      <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>\n        {/* Sidebar toggle */}\n        {showSidebarToggle && (\n          <Button\n            type=\"text\"\n            icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n            onClick={onSidebarToggle}\n            style={{\n              fontSize: '16px',\n              width: '40px',\n              height: '40px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n            }}\n          />\n        )}\n\n        {/* Logo and title */}\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <div\n            style={{\n              width: '32px',\n              height: '32px',\n              backgroundColor: themeStyles.getColor('primary'),\n              borderRadius: '6px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontWeight: 'bold',\n              fontSize: '16px',\n            }}\n          >\n            ⚽\n          </div>\n          {(!isMobile || sidebarCollapsed) && (\n            <div>\n              <Text\n                style={{\n                  fontSize: '18px',\n                  fontWeight: 'bold',\n                  color: themeStyles.getTextColor('primary'),\n                }}\n              >\n                APISportsGame\n              </Text>\n              <div\n                style={{\n                  fontSize: '12px',\n                  color: themeStyles.getTextColor('secondary'),\n                  lineHeight: 1,\n                }}\n              >\n                CMS Dashboard\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Right section */}\n      <Space size=\"middle\">\n        {/* Theme toggle */}\n        <Tooltip title={`Switch to ${isDark ? 'light' : 'dark'} mode`}>\n          <Button\n            type=\"text\"\n            icon={isDark ? <SunOutlined /> : <MoonOutlined />}\n            onClick={toggleTheme}\n            style={{\n              fontSize: '16px',\n              width: '40px',\n              height: '40px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n            }}\n          />\n        </Tooltip>\n\n        {/* Language selector */}\n        <Tooltip title=\"Language\">\n          <Button\n            type=\"text\"\n            icon={<GlobalOutlined />}\n            style={{\n              fontSize: '16px',\n              width: '40px',\n              height: '40px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n            }}\n          />\n        </Tooltip>\n\n        {/* Notifications */}\n        <Dropdown\n          menu={{ items: notificationItems }}\n          trigger={['click']}\n          placement=\"bottomRight\"\n        >\n          <Badge count={2} size=\"small\">\n            <Button\n              type=\"text\"\n              icon={<BellOutlined />}\n              style={{\n                fontSize: '16px',\n                width: '40px',\n                height: '40px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n              }}\n            />\n          </Badge>\n        </Dropdown>\n\n        {/* User menu */}\n        <Dropdown\n          menu={{ items: userMenuItems }}\n          trigger={['click']}\n          placement=\"bottomRight\"\n        >\n          <div\n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px',\n              cursor: 'pointer',\n              padding: '4px 8px',\n              borderRadius: '6px',\n              transition: 'background-color 0.2s ease',\n            }}\n          >\n            <Avatar\n              size=\"small\"\n              icon={<UserOutlined />}\n              style={{\n                backgroundColor: themeStyles.getColor('primary'),\n              }}\n            />\n            {!isMobile && (\n              <div>\n                <div\n                  style={{\n                    fontSize: '14px',\n                    fontWeight: 'bold',\n                    color: themeStyles.getTextColor('primary'),\n                    lineHeight: 1.2,\n                  }}\n                >\n                  {auth.user?.username || 'Admin'}\n                </div>\n                <div\n                  style={{\n                    fontSize: '12px',\n                    color: themeStyles.getTextColor('secondary'),\n                    lineHeight: 1,\n                  }}\n                >\n                  {auth.user?.role || 'Administrator'}\n                </div>\n              </div>\n            )}\n          </div>\n        </Dropdown>\n      </Space>\n    </Header>\n  );\n}\n\n/**\n * Header breadcrumb component\n */\nexport interface HeaderBreadcrumbProps {\n  items: Array<{\n    title: string;\n    href?: string;\n  }>;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function HeaderBreadcrumb({ items, className, style }: HeaderBreadcrumbProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div\n      className={className}\n      style={{\n        padding: '8px 24px',\n        backgroundColor: themeStyles.getBackgroundColor('elevated'),\n        borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        ...style,\n      }}\n    >\n      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n        {items.map((item, index) => (\n          <React.Fragment key={index}>\n            {index > 0 && (\n              <span style={{ color: themeStyles.getTextColor('tertiary') }}>\n                /\n              </span>\n            )}\n            {item.href ? (\n              <a\n                href={item.href}\n                style={{\n                  color: themeStyles.getColor('primary'),\n                  textDecoration: 'none',\n                  fontSize: '14px',\n                }}\n              >\n                {item.title}\n              </a>\n            ) : (\n              <span\n                style={{\n                  color: themeStyles.getTextColor('primary'),\n                  fontSize: '14px',\n                  fontWeight: index === items.length - 1 ? 'bold' : 'normal',\n                }}\n              >\n                {item.title}\n              </span>\n            )}\n          </React.Fragment>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAID;AAaA;AACA;AAbA;AAAA;AAYA;AACA;AAZA;AAAA;AAAA;AADA;AACA;AAAA;AADA;AAAA;AACA;AAAA;AAAA;AADA;AAAA;AACA;AADA;AAHA;;;;;;;AAkBA,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAiBpB,SAAS,UAAU,EACxB,gBAAgB,EAChB,eAAe,EACf,QAAQ,EACR,oBAAoB,IAAI,EACxB,SAAS,EACT,KAAK,EACU;IACf,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;IAC9C,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,OAAO,CAAA,GAAA,oIAAA,CAAA,UAAO,AAAD;IAEnB,kBAAkB;IAClB,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,SAAS,IAAM,KAAK,UAAU;YAC9B,QAAQ;QACV;KACD;IAED,0BAA0B;IAC1B,MAAM,oBAAoB;QACxB;YACE,KAAK;YACL,qBACE,8OAAC;gBAAI,OAAO;oBAAE,SAAS;gBAAQ;;kCAC7B,8OAAC;wBAAI,OAAO;4BAAE,YAAY;4BAAQ,cAAc;wBAAM;kCAAG;;;;;;kCAGzD,8OAAC;wBAAI,OAAO;4BAAE,UAAU;4BAAQ,OAAO,YAAY,YAAY,CAAC;wBAAa;kCAAG;;;;;;;;;;;;QAKtF;QACA;YACE,KAAK;YACL,qBACE,8OAAC;gBAAI,OAAO;oBAAE,SAAS;gBAAQ;;kCAC7B,8OAAC;wBAAI,OAAO;4BAAE,YAAY;4BAAQ,cAAc;wBAAM;kCAAG;;;;;;kCAGzD,8OAAC;wBAAI,OAAO;4BAAE,UAAU;4BAAQ,OAAO,YAAY,YAAY,CAAC;wBAAa;kCAAG;;;;;;;;;;;;QAKtF;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,qBACE,8OAAC;gBAAI,OAAO;oBAAE,WAAW;oBAAU,SAAS;gBAAQ;0BAClD,cAAA,8OAAC,kMAAA,CAAA,SAAM;oBAAC,MAAK;oBAAO,MAAK;8BAAQ;;;;;;;;;;;QAKvC;KACD;IAED,MAAM,cAAmC;QACvC,UAAU;QACV,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;QAClE,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,GAAG,KAAK;IACV;IAEA,qBACE,8OAAC;QAAO,WAAW;QAAW,OAAO;;0BAEnC,8OAAC;gBAAI,OAAO;oBAAE,SAAS;oBAAQ,YAAY;oBAAU,KAAK;gBAAO;;oBAE9D,mCACC,8OAAC,kMAAA,CAAA,SAAM;wBACL,MAAK;wBACL,MAAM,iCAAmB,8OAAC,8NAAA,CAAA,qBAAkB;;;;mDAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wBACnE,SAAS;wBACT,OAAO;4BACL,UAAU;4BACV,OAAO;4BACP,QAAQ;4BACR,SAAS;4BACT,YAAY;4BACZ,gBAAgB;wBAClB;;;;;;kCAKJ,8OAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,YAAY;4BAAU,KAAK;wBAAO;;0CAC/D,8OAAC;gCACC,OAAO;oCACL,OAAO;oCACP,QAAQ;oCACR,iBAAiB,YAAY,QAAQ,CAAC;oCACtC,cAAc;oCACd,SAAS;oCACT,YAAY;oCACZ,gBAAgB;oCAChB,OAAO;oCACP,YAAY;oCACZ,UAAU;gCACZ;0CACD;;;;;;4BAGA,CAAC,CAAC,YAAY,gBAAgB,mBAC7B,8OAAC;;kDACC,8OAAC;wCACC,OAAO;4CACL,UAAU;4CACV,YAAY;4CACZ,OAAO,YAAY,YAAY,CAAC;wCAClC;kDACD;;;;;;kDAGD,8OAAC;wCACC,OAAO;4CACL,UAAU;4CACV,OAAO,YAAY,YAAY,CAAC;4CAChC,YAAY;wCACd;kDACD;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC,gMAAA,CAAA,QAAK;gBAAC,MAAK;;kCAEV,8OAAC,oLAAA,CAAA,UAAO;wBAAC,OAAO,CAAC,UAAU,EAAE,SAAS,UAAU,OAAO,KAAK,CAAC;kCAC3D,cAAA,8OAAC,kMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,MAAM,uBAAS,8OAAC,gNAAA,CAAA,cAAW;;;;uDAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;4BAC9C,SAAS;4BACT,OAAO;gCACL,UAAU;gCACV,OAAO;gCACP,QAAQ;gCACR,SAAS;gCACT,YAAY;gCACZ,gBAAgB;4BAClB;;;;;;;;;;;kCAKJ,8OAAC,oLAAA,CAAA,UAAO;wBAAC,OAAM;kCACb,cAAA,8OAAC,kMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;4BACrB,OAAO;gCACL,UAAU;gCACV,OAAO;gCACP,QAAQ;gCACR,SAAS;gCACT,YAAY;gCACZ,gBAAgB;4BAClB;;;;;;;;;;;kCAKJ,8OAAC,sLAAA,CAAA,WAAQ;wBACP,MAAM;4BAAE,OAAO;wBAAkB;wBACjC,SAAS;4BAAC;yBAAQ;wBAClB,WAAU;kCAEV,cAAA,8OAAC,gLAAA,CAAA,QAAK;4BAAC,OAAO;4BAAG,MAAK;sCACpB,cAAA,8OAAC,kMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gCACnB,OAAO;oCACL,UAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,SAAS;oCACT,YAAY;oCACZ,gBAAgB;gCAClB;;;;;;;;;;;;;;;;kCAMN,8OAAC,sLAAA,CAAA,WAAQ;wBACP,MAAM;4BAAE,OAAO;wBAAc;wBAC7B,SAAS;4BAAC;yBAAQ;wBAClB,WAAU;kCAEV,cAAA,8OAAC;4BACC,OAAO;gCACL,SAAS;gCACT,YAAY;gCACZ,KAAK;gCACL,QAAQ;gCACR,SAAS;gCACT,cAAc;gCACd,YAAY;4BACd;;8CAEA,8OAAC,kLAAA,CAAA,SAAM;oCACL,MAAK;oCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oCACnB,OAAO;wCACL,iBAAiB,YAAY,QAAQ,CAAC;oCACxC;;;;;;gCAED,CAAC,0BACA,8OAAC;;sDACC,8OAAC;4CACC,OAAO;gDACL,UAAU;gDACV,YAAY;gDACZ,OAAO,YAAY,YAAY,CAAC;gDAChC,YAAY;4CACd;sDAEC,KAAK,IAAI,EAAE,YAAY;;;;;;sDAE1B,8OAAC;4CACC,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;gDAChC,YAAY;4CACd;sDAEC,KAAK,IAAI,EAAE,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStC;AAcO,SAAS,iBAAiB,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAyB;IACjF,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAClE,GAAG,KAAK;QACV;kBAEA,cAAA,8OAAC;YAAI,OAAO;gBAAE,SAAS;gBAAQ,YAAY;gBAAU,KAAK;YAAM;sBAC7D,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;wBACZ,QAAQ,mBACP,8OAAC;4BAAK,OAAO;gCAAE,OAAO,YAAY,YAAY,CAAC;4BAAY;sCAAG;;;;;;wBAI/D,KAAK,IAAI,iBACR,8OAAC;4BACC,MAAM,KAAK,IAAI;4BACf,OAAO;gCACL,OAAO,YAAY,QAAQ,CAAC;gCAC5B,gBAAgB;gCAChB,UAAU;4BACZ;sCAEC,KAAK,KAAK;;;;;iDAGb,8OAAC;4BACC,OAAO;gCACL,OAAO,YAAY,YAAY,CAAC;gCAChC,UAAU;gCACV,YAAY,UAAU,MAAM,MAAM,GAAG,IAAI,SAAS;4BACpD;sCAEC,KAAK,KAAK;;;;;;;mBAzBI;;;;;;;;;;;;;;;AAiC/B"}}, {"offset": {"line": 3735, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3765, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-sidebar.tsx"], "sourcesContent": ["/**\n * App Sidebar Component\n * Navigation sidebar for the APISportsGame CMS\n */\n\n'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Layout, Menu, Divider } from 'antd';\nimport {\n  DashboardOutlined,\n  UserOutlined,\n  TeamOutlined,\n  TrophyOutlined,\n  CalendarOutlined,\n  LinkOutlined,\n  SettingOutlined,\n  BarChartOutlined,\n  FileTextOutlined,\n  DatabaseOutlined,\n  ApiOutlined,\n  HeartOutlined,\n} from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\nimport { useRouter, usePathname } from 'next/navigation';\n\nconst { Sider } = Layout;\n\n/**\n * App sidebar props\n */\nexport interface AppSidebarProps {\n  collapsed: boolean;\n  isMobile: boolean;\n  onCollapse: (collapsed: boolean) => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Menu item interface\n */\ninterface MenuItem {\n  key: string;\n  icon: React.ReactNode;\n  label: string;\n  path?: string;\n  children?: MenuItem[];\n  disabled?: boolean;\n}\n\n/**\n * App Sidebar component\n */\nexport function AppSidebar({\n  collapsed,\n  isMobile,\n  onCollapse,\n  className,\n  style,\n}: AppSidebarProps) {\n  const themeStyles = useThemeStyles();\n  const router = useRouter();\n  const pathname = usePathname();\n  \n  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);\n  const [openKeys, setOpenKeys] = useState<string[]>([]);\n\n  // Menu items configuration\n  const menuItems: MenuItem[] = [\n    {\n      key: 'dashboard',\n      icon: <DashboardOutlined />,\n      label: 'Dashboard',\n      path: '/',\n    },\n    {\n      key: 'divider-1',\n      icon: null,\n      label: '',\n    },\n    {\n      key: 'user-management',\n      icon: <UserOutlined />,\n      label: 'User System',\n      children: [\n        {\n          key: 'system-users',\n          icon: <TeamOutlined />,\n          label: 'System Users',\n          path: '/users/system',\n        },\n        {\n          key: 'user-roles',\n          icon: <SettingOutlined />,\n          label: 'Roles & Permissions',\n          path: '/users/roles',\n        },\n      ],\n    },\n    {\n      key: 'football-management',\n      icon: <TrophyOutlined />,\n      label: 'Football Data',\n      children: [\n        {\n          key: 'leagues',\n          icon: <TrophyOutlined />,\n          label: 'Leagues',\n          path: '/football/leagues',\n        },\n        {\n          key: 'teams',\n          icon: <TeamOutlined />,\n          label: 'Teams',\n          path: '/football/teams',\n        },\n        {\n          key: 'fixtures',\n          icon: <CalendarOutlined />,\n          label: 'Fixtures',\n          path: '/football/fixtures',\n        },\n        {\n          key: 'sync-status',\n          icon: <DatabaseOutlined />,\n          label: 'Sync Status',\n          path: '/football/sync',\n        },\n      ],\n    },\n    {\n      key: 'broadcast-management',\n      icon: <LinkOutlined />,\n      label: 'Broadcast Links',\n      children: [\n        {\n          key: 'broadcast-links',\n          icon: <LinkOutlined />,\n          label: 'Manage Links',\n          path: '/broadcast/links',\n        },\n        {\n          key: 'broadcast-quality',\n          icon: <BarChartOutlined />,\n          label: 'Quality Control',\n          path: '/broadcast/quality',\n        },\n      ],\n    },\n    {\n      key: 'divider-2',\n      icon: null,\n      label: '',\n    },\n    {\n      key: 'system',\n      icon: <SettingOutlined />,\n      label: 'System',\n      children: [\n        {\n          key: 'api-health',\n          icon: <HeartOutlined />,\n          label: 'API Health',\n          path: '/system/health',\n        },\n        {\n          key: 'api-docs',\n          icon: <ApiOutlined />,\n          label: 'API Documentation',\n          path: '/system/api-docs',\n        },\n        {\n          key: 'logs',\n          icon: <FileTextOutlined />,\n          label: 'System Logs',\n          path: '/system/logs',\n        },\n        {\n          key: 'settings',\n          icon: <SettingOutlined />,\n          label: 'Settings',\n          path: '/system/settings',\n        },\n      ],\n    },\n    {\n      key: 'divider-3',\n      icon: null,\n      label: '',\n    },\n    {\n      key: 'demos',\n      icon: <BarChartOutlined />,\n      label: 'Demos',\n      children: [\n        {\n          key: 'components-demo',\n          icon: <BarChartOutlined />,\n          label: 'Components',\n          path: '/components-demo',\n        },\n        {\n          key: 'theme-demo',\n          icon: <BarChartOutlined />,\n          label: 'Theme System',\n          path: '/theme-demo',\n        },\n        {\n          key: 'api-hooks-demo',\n          icon: <ApiOutlined />,\n          label: 'API Hooks',\n          path: '/api-hooks-demo',\n        },\n      ],\n    },\n  ];\n\n  // Update selected keys based on current pathname\n  useEffect(() => {\n    const findSelectedKey = (items: MenuItem[], path: string): string | null => {\n      for (const item of items) {\n        if (item.path === path) {\n          return item.key;\n        }\n        if (item.children) {\n          const childKey = findSelectedKey(item.children, path);\n          if (childKey) {\n            return childKey;\n          }\n        }\n      }\n      return null;\n    };\n\n    const selectedKey = findSelectedKey(menuItems, pathname);\n    if (selectedKey) {\n      setSelectedKeys([selectedKey]);\n      \n      // Auto-expand parent menu\n      const findParentKey = (items: MenuItem[], targetKey: string): string | null => {\n        for (const item of items) {\n          if (item.children) {\n            const hasChild = item.children.some(child => child.key === targetKey);\n            if (hasChild) {\n              return item.key;\n            }\n          }\n        }\n        return null;\n      };\n\n      const parentKey = findParentKey(menuItems, selectedKey);\n      if (parentKey && !collapsed) {\n        setOpenKeys([parentKey]);\n      }\n    }\n  }, [pathname, collapsed]);\n\n  // Handle menu click\n  const handleMenuClick = ({ key }: { key: string }) => {\n    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {\n      for (const item of items) {\n        if (item.key === targetKey) {\n          return item;\n        }\n        if (item.children) {\n          const childItem = findMenuItem(item.children, targetKey);\n          if (childItem) {\n            return childItem;\n          }\n        }\n      }\n      return null;\n    };\n\n    const menuItem = findMenuItem(menuItems, key);\n    if (menuItem?.path) {\n      router.push(menuItem.path);\n      \n      // Close sidebar on mobile after navigation\n      if (isMobile) {\n        onCollapse(true);\n      }\n    }\n  };\n\n  // Handle submenu open/close\n  const handleOpenChange = (keys: string[]) => {\n    setOpenKeys(keys);\n  };\n\n  // Convert menu items to Ant Design menu format\n  const convertToAntMenuItems = (items: MenuItem[]): any[] => {\n    return items.map(item => {\n      // Handle dividers\n      if (item.key.startsWith('divider')) {\n        return {\n          type: 'divider',\n          key: item.key,\n        };\n      }\n\n      // Handle regular items\n      if (item.children) {\n        return {\n          key: item.key,\n          icon: item.icon,\n          label: item.label,\n          children: convertToAntMenuItems(item.children),\n          disabled: item.disabled,\n        };\n      }\n\n      return {\n        key: item.key,\n        icon: item.icon,\n        label: item.label,\n        disabled: item.disabled,\n      };\n    });\n  };\n\n  const siderStyle: React.CSSProperties = {\n    position: 'fixed',\n    left: 0,\n    top: '64px', // Header height\n    bottom: 0,\n    zIndex: isMobile ? 1000 : 100,\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    borderRight: `1px solid ${themeStyles.getBorderColor('primary')}`,\n    overflow: 'auto',\n    ...style,\n  };\n\n  return (\n    <Sider\n      className={className}\n      style={siderStyle}\n      collapsed={collapsed}\n      collapsible={false}\n      width={250}\n      collapsedWidth={80}\n      theme=\"light\"\n    >\n      {/* Sidebar content */}\n      <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n        {/* Main navigation menu */}\n        <Menu\n          mode=\"inline\"\n          selectedKeys={selectedKeys}\n          openKeys={collapsed ? [] : openKeys}\n          onOpenChange={handleOpenChange}\n          onClick={handleMenuClick}\n          items={convertToAntMenuItems(menuItems)}\n          style={{\n            flex: 1,\n            border: 'none',\n            backgroundColor: 'transparent',\n          }}\n        />\n\n        {/* Sidebar footer */}\n        {!collapsed && (\n          <div\n            style={{\n              padding: '16px',\n              borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,\n              textAlign: 'center',\n            }}\n          >\n            <div\n              style={{\n                fontSize: '12px',\n                color: themeStyles.getTextColor('tertiary'),\n                marginBottom: '4px',\n              }}\n            >\n              APISportsGame CMS\n            </div>\n            <div\n              style={{\n                fontSize: '10px',\n                color: themeStyles.getTextColor('tertiary'),\n              }}\n            >\n              v1.0.0\n            </div>\n          </div>\n        )}\n      </div>\n    </Sider>\n  );\n}\n\n/**\n * Sidebar menu item component for custom rendering\n */\nexport interface SidebarMenuItemProps {\n  icon: React.ReactNode;\n  label: string;\n  active?: boolean;\n  collapsed?: boolean;\n  onClick?: () => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function SidebarMenuItem({\n  icon,\n  label,\n  active = false,\n  collapsed = false,\n  onClick,\n  className,\n  style,\n}: SidebarMenuItemProps) {\n  const themeStyles = useThemeStyles();\n\n  const itemStyle: React.CSSProperties = {\n    display: 'flex',\n    alignItems: 'center',\n    gap: collapsed ? '0' : '12px',\n    padding: '12px 16px',\n    cursor: 'pointer',\n    borderRadius: '6px',\n    margin: '2px 8px',\n    transition: 'all 0.2s ease',\n    backgroundColor: active ? themeStyles.getColor('primary') + '10' : 'transparent',\n    color: active ? themeStyles.getColor('primary') : themeStyles.getTextColor('primary'),\n    ...style,\n  };\n\n  return (\n    <div\n      className={className}\n      style={itemStyle}\n      onClick={onClick}\n    >\n      <div style={{ fontSize: '16px', minWidth: '16px' }}>\n        {icon}\n      </div>\n      {!collapsed && (\n        <div style={{ fontSize: '14px', fontWeight: active ? 'bold' : 'normal' }}>\n          {label}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAID;AAgBA;AACA;AAhBA;AAeA;AAdA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADA;AAHA;;;;;;;AAqBA,MAAM,EAAE,KAAK,EAAE,GAAG,kLAAA,CAAA,SAAM;AA4BjB,SAAS,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,UAAU,EACV,SAAS,EACT,KAAK,EACW;IAChB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAErD,2BAA2B;IAC3B,MAAM,YAAwB;QAC5B;YACE,KAAK;YACL,oBAAM,8OAAC,4NAAA,CAAA,oBAAiB;;;;;YACxB,OAAO;YACP,MAAM;QACR;QACA;YACE,KAAK;YACL,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;oBACtB,OAAO;oBACP,MAAM;gBACR;aACD;QACH;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;oBACrB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;oBACP,MAAM;gBACR;aACD;QACH;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;oBACP,MAAM;gBACR;aACD;QACH;QACA;YACE,KAAK;YACL,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,8OAAC,oNAAA,CAAA,gBAAa;;;;;oBACpB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,gNAAA,CAAA,cAAW;;;;;oBAClB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;oBACtB,OAAO;oBACP,MAAM;gBACR;aACD;QACH;QACA;YACE,KAAK;YACL,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,gNAAA,CAAA,cAAW;;;;;oBAClB,OAAO;oBACP,MAAM;gBACR;aACD;QACH;KACD;IAED,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,CAAC,OAAmB;YAC1C,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,KAAK,IAAI,KAAK,MAAM;oBACtB,OAAO,KAAK,GAAG;gBACjB;gBACA,IAAI,KAAK,QAAQ,EAAE;oBACjB,MAAM,WAAW,gBAAgB,KAAK,QAAQ,EAAE;oBAChD,IAAI,UAAU;wBACZ,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,MAAM,cAAc,gBAAgB,WAAW;QAC/C,IAAI,aAAa;YACf,gBAAgB;gBAAC;aAAY;YAE7B,0BAA0B;YAC1B,MAAM,gBAAgB,CAAC,OAAmB;gBACxC,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,QAAQ,EAAE;wBACjB,MAAM,WAAW,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,GAAG,KAAK;wBAC3D,IAAI,UAAU;4BACZ,OAAO,KAAK,GAAG;wBACjB;oBACF;gBACF;gBACA,OAAO;YACT;YAEA,MAAM,YAAY,cAAc,WAAW;YAC3C,IAAI,aAAa,CAAC,WAAW;gBAC3B,YAAY;oBAAC;iBAAU;YACzB;QACF;IACF,GAAG;QAAC;QAAU;KAAU;IAExB,oBAAoB;IACpB,MAAM,kBAAkB,CAAC,EAAE,GAAG,EAAmB;QAC/C,MAAM,eAAe,CAAC,OAAmB;YACvC,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,KAAK,GAAG,KAAK,WAAW;oBAC1B,OAAO;gBACT;gBACA,IAAI,KAAK,QAAQ,EAAE;oBACjB,MAAM,YAAY,aAAa,KAAK,QAAQ,EAAE;oBAC9C,IAAI,WAAW;wBACb,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,MAAM,WAAW,aAAa,WAAW;QACzC,IAAI,UAAU,MAAM;YAClB,OAAO,IAAI,CAAC,SAAS,IAAI;YAEzB,2CAA2C;YAC3C,IAAI,UAAU;gBACZ,WAAW;YACb;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,mBAAmB,CAAC;QACxB,YAAY;IACd;IAEA,+CAA+C;IAC/C,MAAM,wBAAwB,CAAC;QAC7B,OAAO,MAAM,GAAG,CAAC,CAAA;YACf,kBAAkB;YAClB,IAAI,KAAK,GAAG,CAAC,UAAU,CAAC,YAAY;gBAClC,OAAO;oBACL,MAAM;oBACN,KAAK,KAAK,GAAG;gBACf;YACF;YAEA,uBAAuB;YACvB,IAAI,KAAK,QAAQ,EAAE;gBACjB,OAAO;oBACL,KAAK,KAAK,GAAG;oBACb,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,UAAU,sBAAsB,KAAK,QAAQ;oBAC7C,UAAU,KAAK,QAAQ;gBACzB;YACF;YAEA,OAAO;gBACL,KAAK,KAAK,GAAG;gBACb,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;YACzB;QACF;IACF;IAEA,MAAM,aAAkC;QACtC,UAAU;QACV,MAAM;QACN,KAAK;QACL,QAAQ;QACR,QAAQ,WAAW,OAAO;QAC1B,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,aAAa,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;QACjE,UAAU;QACV,GAAG,KAAK;IACV;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;QACP,WAAW;QACX,aAAa;QACb,OAAO;QACP,gBAAgB;QAChB,OAAM;kBAGN,cAAA,8OAAC;YAAI,OAAO;gBAAE,QAAQ;gBAAQ,SAAS;gBAAQ,eAAe;YAAS;;8BAErE,8OAAC,8KAAA,CAAA,OAAI;oBACH,MAAK;oBACL,cAAc;oBACd,UAAU,YAAY,EAAE,GAAG;oBAC3B,cAAc;oBACd,SAAS;oBACT,OAAO,sBAAsB;oBAC7B,OAAO;wBACL,MAAM;wBACN,QAAQ;wBACR,iBAAiB;oBACnB;;;;;;gBAID,CAAC,2BACA,8OAAC;oBACC,OAAO;wBACL,SAAS;wBACT,WAAW,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;wBAC/D,WAAW;oBACb;;sCAEA,8OAAC;4BACC,OAAO;gCACL,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;gCAChC,cAAc;4BAChB;sCACD;;;;;;sCAGD,8OAAC;4BACC,OAAO;gCACL,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;4BAClC;sCACD;;;;;;;;;;;;;;;;;;;;;;;AAQb;AAeO,SAAS,gBAAgB,EAC9B,IAAI,EACJ,KAAK,EACL,SAAS,KAAK,EACd,YAAY,KAAK,EACjB,OAAO,EACP,SAAS,EACT,KAAK,EACgB;IACrB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,YAAiC;QACrC,SAAS;QACT,YAAY;QACZ,KAAK,YAAY,MAAM;QACvB,SAAS;QACT,QAAQ;QACR,cAAc;QACd,QAAQ;QACR,YAAY;QACZ,iBAAiB,SAAS,YAAY,QAAQ,CAAC,aAAa,OAAO;QACnE,OAAO,SAAS,YAAY,QAAQ,CAAC,aAAa,YAAY,YAAY,CAAC;QAC3E,GAAG,KAAK;IACV;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;QACP,SAAS;;0BAET,8OAAC;gBAAI,OAAO;oBAAE,UAAU;oBAAQ,UAAU;gBAAO;0BAC9C;;;;;;YAEF,CAAC,2BACA,8OAAC;gBAAI,OAAO;oBAAE,UAAU;oBAAQ,YAAY,SAAS,SAAS;gBAAS;0BACpE;;;;;;;;;;;;AAKX"}}, {"offset": {"line": 4279, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4285, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-footer.tsx"], "sourcesContent": ["/**\n * App Footer Component\n * Footer for the APISportsGame CMS\n */\n\n'use client';\n\nimport React from 'react';\nimport { Layout, Space, Typography, Divider } from 'antd';\nimport { \n  GithubOutlined, \n  TwitterOutlined, \n  LinkedinOutlined,\n  HeartFilled,\n  ApiOutlined,\n  GlobalOutlined,\n} from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\n\nconst { Footer } = Layout;\nconst { Text, Link } = Typography;\n\n/**\n * App footer props\n */\nexport interface AppFooterProps {\n  className?: string;\n  style?: React.CSSProperties;\n  compact?: boolean;\n}\n\n/**\n * App Footer component\n */\nexport function AppFooter({ className, style, compact = false }: AppFooterProps) {\n  const themeStyles = useThemeStyles();\n\n  const footerStyle: React.CSSProperties = {\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,\n    padding: compact ? '12px 24px' : '24px',\n    textAlign: 'center',\n    ...style,\n  };\n\n  const currentYear = new Date().getFullYear();\n\n  if (compact) {\n    return (\n      <Footer className={className} style={footerStyle}>\n        <Text\n          style={{\n            fontSize: '12px',\n            color: themeStyles.getTextColor('tertiary'),\n          }}\n        >\n          © {currentYear} APISportsGame CMS. Built with{' '}\n          <HeartFilled style={{ color: themeStyles.getColor('error') }} /> by Augment Code\n        </Text>\n      </Footer>\n    );\n  }\n\n  return (\n    <Footer className={className} style={footerStyle}>\n      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n        {/* Main footer content */}\n        <div\n          style={{\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n            gap: '32px',\n            marginBottom: '24px',\n            textAlign: 'left',\n          }}\n        >\n          {/* About section */}\n          <div>\n            <div\n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                marginBottom: '12px',\n              }}\n            >\n              <div\n                style={{\n                  width: '24px',\n                  height: '24px',\n                  backgroundColor: themeStyles.getColor('primary'),\n                  borderRadius: '4px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: 'white',\n                  fontSize: '14px',\n                  fontWeight: 'bold',\n                }}\n              >\n                ⚽\n              </div>\n              <Text\n                style={{\n                  fontSize: '16px',\n                  fontWeight: 'bold',\n                  color: themeStyles.getTextColor('primary'),\n                }}\n              >\n                APISportsGame\n              </Text>\n            </div>\n            <Text\n              style={{\n                fontSize: '14px',\n                color: themeStyles.getTextColor('secondary'),\n                lineHeight: 1.6,\n              }}\n            >\n              A comprehensive CMS for managing football data, broadcast links, and user systems.\n              Built with modern technologies for optimal performance.\n            </Text>\n          </div>\n\n          {/* Quick links */}\n          <div>\n            <Text\n              style={{\n                fontSize: '14px',\n                fontWeight: 'bold',\n                color: themeStyles.getTextColor('primary'),\n                marginBottom: '12px',\n                display: 'block',\n              }}\n            >\n              Quick Links\n            </Text>\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n              <Link\n                href=\"/\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                Dashboard\n              </Link>\n              <Link\n                href=\"/football/fixtures\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                Fixtures\n              </Link>\n              <Link\n                href=\"/broadcast/links\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                Broadcast Links\n              </Link>\n              <Link\n                href=\"/system/health\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                System Health\n              </Link>\n            </div>\n          </div>\n\n          {/* Resources */}\n          <div>\n            <Text\n              style={{\n                fontSize: '14px',\n                fontWeight: 'bold',\n                color: themeStyles.getTextColor('primary'),\n                marginBottom: '12px',\n                display: 'block',\n              }}\n            >\n              Resources\n            </Text>\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n              <Link\n                href=\"/system/api-docs\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <ApiOutlined style={{ marginRight: '4px' }} />\n                API Documentation\n              </Link>\n              <Link\n                href=\"/components-demo\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                Component Library\n              </Link>\n              <Link\n                href=\"/theme-demo\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                Theme System\n              </Link>\n              <Link\n                href=\"https://github.com/apisportsgame\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <GithubOutlined style={{ marginRight: '4px' }} />\n                GitHub Repository\n              </Link>\n            </div>\n          </div>\n\n          {/* Contact */}\n          <div>\n            <Text\n              style={{\n                fontSize: '14px',\n                fontWeight: 'bold',\n                color: themeStyles.getTextColor('primary'),\n                marginBottom: '12px',\n                display: 'block',\n              }}\n            >\n              Connect\n            </Text>\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n              <Link\n                href=\"https://github.com/apisportsgame\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <GithubOutlined style={{ marginRight: '4px' }} />\n                GitHub\n              </Link>\n              <Link\n                href=\"https://twitter.com/apisportsgame\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <TwitterOutlined style={{ marginRight: '4px' }} />\n                Twitter\n              </Link>\n              <Link\n                href=\"https://linkedin.com/company/apisportsgame\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <LinkedinOutlined style={{ marginRight: '4px' }} />\n                LinkedIn\n              </Link>\n              <Link\n                href=\"https://apisportsgame.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <GlobalOutlined style={{ marginRight: '4px' }} />\n                Website\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        <Divider style={{ margin: '24px 0 16px 0' }} />\n\n        {/* Bottom footer */}\n        <div\n          style={{\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            flexWrap: 'wrap',\n            gap: '16px',\n          }}\n        >\n          <Text\n            style={{\n              fontSize: '13px',\n              color: themeStyles.getTextColor('tertiary'),\n            }}\n          >\n            © {currentYear} APISportsGame CMS. All rights reserved. Built with{' '}\n            <HeartFilled style={{ color: themeStyles.getColor('error') }} /> by Augment Code\n          </Text>\n\n          <Space size=\"middle\">\n            <Link\n              href=\"/privacy\"\n              style={{\n                fontSize: '13px',\n                color: themeStyles.getTextColor('tertiary'),\n              }}\n            >\n              Privacy Policy\n            </Link>\n            <Link\n              href=\"/terms\"\n              style={{\n                fontSize: '13px',\n                color: themeStyles.getTextColor('tertiary'),\n              }}\n            >\n              Terms of Service\n            </Link>\n            <Text\n              style={{\n                fontSize: '13px',\n                color: themeStyles.getTextColor('tertiary'),\n              }}\n            >\n              v1.0.0\n            </Text>\n          </Space>\n        </div>\n      </div>\n    </Footer>\n  );\n}\n\n/**\n * Simple footer for minimal layouts\n */\nexport interface SimpleFooterProps {\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function SimpleFooter({ className, style }: SimpleFooterProps) {\n  const themeStyles = useThemeStyles();\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <div\n      className={className}\n      style={{\n        padding: '16px 24px',\n        textAlign: 'center',\n        backgroundColor: themeStyles.getBackgroundColor('container'),\n        borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        ...style,\n      }}\n    >\n      <Text\n        style={{\n          fontSize: '12px',\n          color: themeStyles.getTextColor('tertiary'),\n        }}\n      >\n        © {currentYear} APISportsGame CMS. Built with{' '}\n        <HeartFilled style={{ color: themeStyles.getColor('error') }} /> by Augment Code\n      </Text>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAcD;AATA;AAAA;AASA;AARA;AAAA;AAAA;AAAA;AAAA;AADA;AACA;AADA;AAHA;;;;;AAcA,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAc1B,SAAS,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,KAAK,EAAkB;IAC7E,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,cAAmC;QACvC,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,WAAW,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;QAC/D,SAAS,UAAU,cAAc;QACjC,WAAW;QACX,GAAG,KAAK;IACV;IAEA,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,IAAI,SAAS;QACX,qBACE,8OAAC;YAAO,WAAW;YAAW,OAAO;sBACnC,cAAA,8OAAC;gBACC,OAAO;oBACL,UAAU;oBACV,OAAO,YAAY,YAAY,CAAC;gBAClC;;oBACD;oBACI;oBAAY;oBAA+B;kCAC9C,8OAAC,gNAAA,CAAA,cAAW;wBAAC,OAAO;4BAAE,OAAO,YAAY,QAAQ,CAAC;wBAAS;;;;;;oBAAK;;;;;;;;;;;;IAIxE;IAEA,qBACE,8OAAC;QAAO,WAAW;QAAW,OAAO;kBACnC,cAAA,8OAAC;YAAI,OAAO;gBAAE,UAAU;gBAAU,QAAQ;YAAS;;8BAEjD,8OAAC;oBACC,OAAO;wBACL,SAAS;wBACT,qBAAqB;wBACrB,KAAK;wBACL,cAAc;wBACd,WAAW;oBACb;;sCAGA,8OAAC;;8CACC,8OAAC;oCACC,OAAO;wCACL,SAAS;wCACT,YAAY;wCACZ,KAAK;wCACL,cAAc;oCAChB;;sDAEA,8OAAC;4CACC,OAAO;gDACL,OAAO;gDACP,QAAQ;gDACR,iBAAiB,YAAY,QAAQ,CAAC;gDACtC,cAAc;gDACd,SAAS;gDACT,YAAY;gDACZ,gBAAgB;gDAChB,OAAO;gDACP,UAAU;gDACV,YAAY;4CACd;sDACD;;;;;;sDAGD,8OAAC;4CACC,OAAO;gDACL,UAAU;gDACV,YAAY;gDACZ,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;;;;;;;8CAIH,8OAAC;oCACC,OAAO;wCACL,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;wCAChC,YAAY;oCACd;8CACD;;;;;;;;;;;;sCAOH,8OAAC;;8CACC,8OAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO,YAAY,YAAY,CAAC;wCAChC,cAAc;wCACd,SAAS;oCACX;8CACD;;;;;;8CAGD,8OAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,eAAe;wCAAU,KAAK;oCAAM;;sDACjE,8OAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;;;;;;;;;;;;;sCAOL,8OAAC;;8CACC,8OAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO,YAAY,YAAY,CAAC;wCAChC,cAAc;wCACd,SAAS;oCACX;8CACD;;;;;;8CAGD,8OAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,eAAe;wCAAU,KAAK;oCAAM;;sDACjE,8OAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,8OAAC,gNAAA,CAAA,cAAW;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;sDAGhD,8OAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,8OAAC,sNAAA,CAAA,iBAAc;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;;;;;;;;;;;;;sCAOvD,8OAAC;;8CACC,8OAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO,YAAY,YAAY,CAAC;wCAChC,cAAc;wCACd,SAAS;oCACX;8CACD;;;;;;8CAGD,8OAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,eAAe;wCAAU,KAAK;oCAAM;;sDACjE,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,8OAAC,sNAAA,CAAA,iBAAc;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;sDAGnD,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,8OAAC,wNAAA,CAAA,kBAAe;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;sDAGpD,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,8OAAC,0NAAA,CAAA,mBAAgB;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;sDAGrD,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,8OAAC,sNAAA,CAAA,iBAAc;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;;;;;;;;;;;;;;;;;;;8BAOzD,8OAAC,oLAAA,CAAA,UAAO;oBAAC,OAAO;wBAAE,QAAQ;oBAAgB;;;;;;8BAG1C,8OAAC;oBACC,OAAO;wBACL,SAAS;wBACT,gBAAgB;wBAChB,YAAY;wBACZ,UAAU;wBACV,KAAK;oBACP;;sCAEA,8OAAC;4BACC,OAAO;gCACL,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;4BAClC;;gCACD;gCACI;gCAAY;gCAAoD;8CACnE,8OAAC,gNAAA,CAAA,cAAW;oCAAC,OAAO;wCAAE,OAAO,YAAY,QAAQ,CAAC;oCAAS;;;;;;gCAAK;;;;;;;sCAGlE,8OAAC,gMAAA,CAAA,QAAK;4BAAC,MAAK;;8CACV,8OAAC;oCACC,MAAK;oCACL,OAAO;wCACL,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;oCAClC;8CACD;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,OAAO;wCACL,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;oCAClC;8CACD;;;;;;8CAGD,8OAAC;oCACC,OAAO;wCACL,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;oCAClC;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;AAUO,SAAS,aAAa,EAAE,SAAS,EAAE,KAAK,EAAqB;IAClE,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,WAAW;YACX,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,WAAW,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAC/D,GAAG,KAAK;QACV;kBAEA,cAAA,8OAAC;YACC,OAAO;gBACL,UAAU;gBACV,OAAO,YAAY,YAAY,CAAC;YAClC;;gBACD;gBACI;gBAAY;gBAA+B;8BAC9C,8OAAC,gNAAA,CAAA,cAAW;oBAAC,OAAO;wBAAE,OAAO,YAAY,QAAQ,CAAC;oBAAS;;;;;;gBAAK;;;;;;;;;;;;AAIxE"}}, {"offset": {"line": 4922, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4928, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-layout.tsx"], "sourcesContent": ["/**\n * Main App Layout Component\n * Primary layout structure for the APISportsGame CMS\n */\n\n'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Layout } from 'antd';\nimport { useThemeStyles } from '@/theme';\nimport { useAppProvider } from '@/stores';\nimport { AppHeader } from './app-header';\nimport { AppSidebar } from './app-sidebar';\nimport { AppFooter } from './app-footer';\n\nconst { Content } = Layout;\n\n/**\n * App layout props\n */\nexport interface AppLayoutProps {\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Main App Layout component\n */\nexport function AppLayout({ children, className, style }: AppLayoutProps) {\n  const themeStyles = useThemeStyles();\n  const app = useAppProvider();\n  \n  // Layout state\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Handle responsive behavior\n  useEffect(() => {\n    const handleResize = () => {\n      const mobile = window.innerWidth < 768;\n      setIsMobile(mobile);\n      \n      // Auto-collapse sidebar on mobile\n      if (mobile && !sidebarCollapsed) {\n        setSidebarCollapsed(true);\n      }\n    };\n\n    // Initial check\n    handleResize();\n\n    // Add event listener\n    window.addEventListener('resize', handleResize);\n    \n    // Cleanup\n    return () => window.removeEventListener('resize', handleResize);\n  }, [sidebarCollapsed]);\n\n  // Handle sidebar toggle\n  const handleSidebarToggle = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  // Handle mobile sidebar overlay\n  const handleMobileOverlayClick = () => {\n    if (isMobile && !sidebarCollapsed) {\n      setSidebarCollapsed(true);\n    }\n  };\n\n  const layoutStyle: React.CSSProperties = {\n    minHeight: '100vh',\n    backgroundColor: themeStyles.getBackgroundColor('layout'),\n    ...style,\n  };\n\n  const contentStyle: React.CSSProperties = {\n    marginLeft: isMobile ? 0 : (sidebarCollapsed ? '80px' : '250px'),\n    transition: 'margin-left 0.2s ease',\n    minHeight: 'calc(100vh - 64px)', // Header height\n    backgroundColor: themeStyles.getBackgroundColor('layout'),\n  };\n\n  return (\n    <Layout className={className} style={layoutStyle}>\n      {/* Header */}\n      <AppHeader\n        sidebarCollapsed={sidebarCollapsed}\n        onSidebarToggle={handleSidebarToggle}\n        isMobile={isMobile}\n      />\n\n      {/* Sidebar */}\n      <AppSidebar\n        collapsed={sidebarCollapsed}\n        isMobile={isMobile}\n        onCollapse={setSidebarCollapsed}\n      />\n\n      {/* Mobile overlay */}\n      {isMobile && !sidebarCollapsed && (\n        <div\n          style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            zIndex: 999,\n          }}\n          onClick={handleMobileOverlayClick}\n        />\n      )}\n\n      {/* Main content */}\n      <Layout style={contentStyle}>\n        <Content\n          style={{\n            padding: '24px',\n            backgroundColor: themeStyles.getBackgroundColor('layout'),\n            overflow: 'auto',\n          }}\n        >\n          {children}\n        </Content>\n\n        {/* Footer */}\n        <AppFooter />\n      </Layout>\n    </Layout>\n  );\n}\n\n/**\n * Layout provider for layout state management\n */\nexport interface LayoutContextType {\n  sidebarCollapsed: boolean;\n  setSidebarCollapsed: (collapsed: boolean) => void;\n  isMobile: boolean;\n  toggleSidebar: () => void;\n}\n\nconst LayoutContext = React.createContext<LayoutContextType | undefined>(undefined);\n\nexport function LayoutProvider({ children }: { children: React.ReactNode }) {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n\n  useEffect(() => {\n    const handleResize = () => {\n      const mobile = window.innerWidth < 768;\n      setIsMobile(mobile);\n    };\n\n    handleResize();\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  const value: LayoutContextType = {\n    sidebarCollapsed,\n    setSidebarCollapsed,\n    isMobile,\n    toggleSidebar,\n  };\n\n  return (\n    <LayoutContext.Provider value={value}>\n      {children}\n    </LayoutContext.Provider>\n  );\n}\n\n/**\n * Hook to use layout context\n */\nexport function useLayout() {\n  const context = React.useContext(LayoutContext);\n  if (context === undefined) {\n    throw new Error('useLayout must be used within a LayoutProvider');\n  }\n  return context;\n}\n\n/**\n * Simple layout for pages that don't need sidebar\n */\nexport interface SimpleLayoutProps {\n  children: React.ReactNode;\n  showHeader?: boolean;\n  showFooter?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function SimpleLayout({\n  children,\n  showHeader = true,\n  showFooter = true,\n  className,\n  style,\n}: SimpleLayoutProps) {\n  const themeStyles = useThemeStyles();\n\n  const layoutStyle: React.CSSProperties = {\n    minHeight: '100vh',\n    backgroundColor: themeStyles.getBackgroundColor('layout'),\n    display: 'flex',\n    flexDirection: 'column',\n    ...style,\n  };\n\n  return (\n    <Layout className={className} style={layoutStyle}>\n      {showHeader && (\n        <AppHeader\n          sidebarCollapsed={true}\n          onSidebarToggle={() => {}}\n          isMobile={false}\n          showSidebarToggle={false}\n        />\n      )}\n\n      <Content\n        style={{\n          flex: 1,\n          padding: '24px',\n          backgroundColor: themeStyles.getBackgroundColor('layout'),\n        }}\n      >\n        {children}\n      </Content>\n\n      {showFooter && <AppFooter />}\n    </Layout>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAID;AAEA;AACA;AACA;AACA;AACA;AALA;AACA;AACA;AALA;;;;;;;;;AAUA,MAAM,EAAE,OAAO,EAAE,GAAG,kLAAA,CAAA,SAAM;AAcnB,SAAS,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAkB;IACtE,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,MAAM,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IAEzB,eAAe;IACf,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,SAAS,OAAO,UAAU,GAAG;YACnC,YAAY;YAEZ,kCAAkC;YAClC,IAAI,UAAU,CAAC,kBAAkB;gBAC/B,oBAAoB;YACtB;QACF;QAEA,gBAAgB;QAChB;QAEA,qBAAqB;QACrB,OAAO,gBAAgB,CAAC,UAAU;QAElC,UAAU;QACV,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;KAAiB;IAErB,wBAAwB;IACxB,MAAM,sBAAsB;QAC1B,oBAAoB,CAAC;IACvB;IAEA,gCAAgC;IAChC,MAAM,2BAA2B;QAC/B,IAAI,YAAY,CAAC,kBAAkB;YACjC,oBAAoB;QACtB;IACF;IAEA,MAAM,cAAmC;QACvC,WAAW;QACX,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,GAAG,KAAK;IACV;IAEA,MAAM,eAAoC;QACxC,YAAY,WAAW,IAAK,mBAAmB,SAAS;QACxD,YAAY;QACZ,WAAW;QACX,iBAAiB,YAAY,kBAAkB,CAAC;IAClD;IAEA,qBACE,8OAAC,kLAAA,CAAA,SAAM;QAAC,WAAW;QAAW,OAAO;;0BAEnC,8OAAC,6IAAA,CAAA,YAAS;gBACR,kBAAkB;gBAClB,iBAAiB;gBACjB,UAAU;;;;;;0BAIZ,8OAAC,8IAAA,CAAA,aAAU;gBACT,WAAW;gBACX,UAAU;gBACV,YAAY;;;;;;YAIb,YAAY,CAAC,kCACZ,8OAAC;gBACC,OAAO;oBACL,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,QAAQ;gBACV;gBACA,SAAS;;;;;;0BAKb,8OAAC,kLAAA,CAAA,SAAM;gBAAC,OAAO;;kCACb,8OAAC;wBACC,OAAO;4BACL,SAAS;4BACT,iBAAiB,YAAY,kBAAkB,CAAC;4BAChD,UAAU;wBACZ;kCAEC;;;;;;kCAIH,8OAAC,6IAAA,CAAA,YAAS;;;;;;;;;;;;;;;;;AAIlB;AAYA,MAAM,8BAAgB,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAgC;AAElE,SAAS,eAAe,EAAE,QAAQ,EAAiC;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,SAAS,OAAO,UAAU,GAAG;YACnC,YAAY;QACd;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,oBAAoB,CAAC;IACvB;IAEA,MAAM,QAA2B;QAC/B;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,cAAc,QAAQ;QAAC,OAAO;kBAC5B;;;;;;AAGP;AAKO,SAAS;IACd,MAAM,UAAU,qMAAA,CAAA,UAAK,CAAC,UAAU,CAAC;IACjC,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAaO,SAAS,aAAa,EAC3B,QAAQ,EACR,aAAa,IAAI,EACjB,aAAa,IAAI,EACjB,SAAS,EACT,KAAK,EACa;IAClB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,cAAmC;QACvC,WAAW;QACX,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,SAAS;QACT,eAAe;QACf,GAAG,KAAK;IACV;IAEA,qBACE,8OAAC,kLAAA,CAAA,SAAM;QAAC,WAAW;QAAW,OAAO;;YAClC,4BACC,8OAAC,6IAAA,CAAA,YAAS;gBACR,kBAAkB;gBAClB,iBAAiB,KAAO;gBACxB,UAAU;gBACV,mBAAmB;;;;;;0BAIvB,8OAAC;gBACC,OAAO;oBACL,MAAM;oBACN,SAAS;oBACT,iBAAiB,YAAY,kBAAkB,CAAC;gBAClD;0BAEC;;;;;;YAGF,4BAAc,8OAAC,6IAAA,CAAA,YAAS;;;;;;;;;;;AAG/B"}}, {"offset": {"line": 5159, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5165, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/auth-layout.tsx"], "sourcesContent": ["/**\n * Authentication Layout Component\n * Specialized layout for authentication pages\n */\n\n'use client';\n\nimport React from 'react';\nimport { Layout, Card, Typography, Space, Divider } from 'antd';\nimport { useThemeStyles } from '@/theme';\nimport { SimpleFooter } from './app-footer';\n\nconst { Content } = Layout;\nconst { Title, Text, Link } = Typography;\n\n/**\n * Auth layout props\n */\nexport interface AuthLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n  subtitle?: string;\n  showFooter?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Authentication Layout component\n */\nexport function AuthLayout({\n  children,\n  title = 'APISportsGame CMS',\n  subtitle = 'System Administration Portal',\n  showFooter = true,\n  className,\n  style,\n}: AuthLayoutProps) {\n  const themeStyles = useThemeStyles();\n\n  const layoutStyle: React.CSSProperties = {\n    minHeight: '100vh',\n    backgroundColor: themeStyles.getBackgroundColor('layout'),\n    display: 'flex',\n    flexDirection: 'column',\n    ...style,\n  };\n\n  const contentStyle: React.CSSProperties = {\n    flex: 1,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    padding: '24px',\n    backgroundImage: `linear-gradient(135deg, ${themeStyles.getColor('primary')}10 0%, ${themeStyles.getColor('primary')}05 100%)`,\n  };\n\n  return (\n    <Layout className={className} style={layoutStyle}>\n      <Content style={contentStyle}>\n        <div\n          style={{\n            width: '100%',\n            maxWidth: '400px',\n            margin: '0 auto',\n          }}\n        >\n          {/* Header Section */}\n          <div\n            style={{\n              textAlign: 'center',\n              marginBottom: '32px',\n            }}\n          >\n            {/* Logo */}\n            <div\n              style={{\n                display: 'flex',\n                justifyContent: 'center',\n                marginBottom: '16px',\n              }}\n            >\n              <div\n                style={{\n                  width: '64px',\n                  height: '64px',\n                  backgroundColor: themeStyles.getColor('primary'),\n                  borderRadius: '12px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: 'white',\n                  fontSize: '32px',\n                  fontWeight: 'bold',\n                  boxShadow: `0 8px 24px ${themeStyles.getColor('primary')}30`,\n                }}\n              >\n                ⚽\n              </div>\n            </div>\n\n            {/* Title */}\n            <Title\n              level={2}\n              style={{\n                color: themeStyles.getTextColor('primary'),\n                marginBottom: '8px',\n                fontSize: '28px',\n                fontWeight: 'bold',\n              }}\n            >\n              {title}\n            </Title>\n\n            {/* Subtitle */}\n            <Text\n              style={{\n                color: themeStyles.getTextColor('secondary'),\n                fontSize: '16px',\n              }}\n            >\n              {subtitle}\n            </Text>\n          </div>\n\n          {/* Auth Card */}\n          <Card\n            style={{\n              backgroundColor: themeStyles.getBackgroundColor('container'),\n              border: `1px solid ${themeStyles.getBorderColor('primary')}`,\n              borderRadius: '12px',\n              boxShadow: `0 8px 32px ${themeStyles.getColor('primary')}08`,\n            }}\n            bodyStyle={{\n              padding: '32px',\n            }}\n          >\n            {children}\n          </Card>\n\n          {/* Additional Links */}\n          <div\n            style={{\n              textAlign: 'center',\n              marginTop: '24px',\n            }}\n          >\n            <Space split={<Divider type=\"vertical\" />}>\n              <Link\n                href=\"/help\"\n                style={{\n                  color: themeStyles.getTextColor('secondary'),\n                  fontSize: '14px',\n                }}\n              >\n                Help & Support\n              </Link>\n              <Link\n                href=\"/privacy\"\n                style={{\n                  color: themeStyles.getTextColor('secondary'),\n                  fontSize: '14px',\n                }}\n              >\n                Privacy Policy\n              </Link>\n              <Link\n                href=\"/terms\"\n                style={{\n                  color: themeStyles.getTextColor('secondary'),\n                  fontSize: '14px',\n                }}\n              >\n                Terms of Service\n              </Link>\n            </Space>\n          </div>\n        </div>\n      </Content>\n\n      {/* Footer */}\n      {showFooter && (\n        <SimpleFooter\n          style={{\n            backgroundColor: themeStyles.getBackgroundColor('container'),\n            borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,\n          }}\n        />\n      )}\n    </Layout>\n  );\n}\n\n/**\n * Auth card wrapper for consistent styling\n */\nexport interface AuthCardProps {\n  children: React.ReactNode;\n  title?: string;\n  description?: string;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function AuthCard({\n  children,\n  title,\n  description,\n  className,\n  style,\n}: AuthCardProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div className={className} style={style}>\n      {(title || description) && (\n        <div style={{ marginBottom: '24px', textAlign: 'center' }}>\n          {title && (\n            <Title\n              level={3}\n              style={{\n                color: themeStyles.getTextColor('primary'),\n                marginBottom: description ? '8px' : '0',\n                fontSize: '24px',\n                fontWeight: 'bold',\n              }}\n            >\n              {title}\n            </Title>\n          )}\n          {description && (\n            <Text\n              style={{\n                color: themeStyles.getTextColor('secondary'),\n                fontSize: '14px',\n                lineHeight: 1.5,\n              }}\n            >\n              {description}\n            </Text>\n          )}\n        </div>\n      )}\n      {children}\n    </div>\n  );\n}\n\n/**\n * Auth form wrapper with consistent spacing\n */\nexport interface AuthFormProps {\n  children: React.ReactNode;\n  onSubmit?: (e: React.FormEvent) => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function AuthForm({\n  children,\n  onSubmit,\n  className,\n  style,\n}: AuthFormProps) {\n  return (\n    <form\n      className={className}\n      style={{\n        width: '100%',\n        ...style,\n      }}\n      onSubmit={onSubmit}\n    >\n      <Space\n        direction=\"vertical\"\n        size=\"large\"\n        style={{ width: '100%' }}\n      >\n        {children}\n      </Space>\n    </form>\n  );\n}\n\n/**\n * Auth divider with text\n */\nexport interface AuthDividerProps {\n  text?: string;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function AuthDivider({\n  text = 'OR',\n  className,\n  style,\n}: AuthDividerProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div\n      className={className}\n      style={{\n        position: 'relative',\n        textAlign: 'center',\n        margin: '24px 0',\n        ...style,\n      }}\n    >\n      <Divider\n        style={{\n          borderColor: themeStyles.getBorderColor('primary'),\n        }}\n      />\n      <span\n        style={{\n          position: 'absolute',\n          top: '50%',\n          left: '50%',\n          transform: 'translate(-50%, -50%)',\n          backgroundColor: themeStyles.getBackgroundColor('container'),\n          padding: '0 16px',\n          color: themeStyles.getTextColor('tertiary'),\n          fontSize: '12px',\n          fontWeight: 'bold',\n        }}\n      >\n        {text}\n      </span>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAMD;AACA;AAFA;AAAA;AACA;AADA;AAAA;AAAA;AAHA;;;;;AAOA,MAAM,EAAE,OAAO,EAAE,GAAG,kLAAA,CAAA,SAAM;AAC1B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAiBjC,SAAS,WAAW,EACzB,QAAQ,EACR,QAAQ,mBAAmB,EAC3B,WAAW,8BAA8B,EACzC,aAAa,IAAI,EACjB,SAAS,EACT,KAAK,EACW;IAChB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,cAAmC;QACvC,WAAW;QACX,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,SAAS;QACT,eAAe;QACf,GAAG,KAAK;IACV;IAEA,MAAM,eAAoC;QACxC,MAAM;QACN,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,SAAS;QACT,iBAAiB,CAAC,wBAAwB,EAAE,YAAY,QAAQ,CAAC,WAAW,OAAO,EAAE,YAAY,QAAQ,CAAC,WAAW,QAAQ,CAAC;IAChI;IAEA,qBACE,8OAAC,kLAAA,CAAA,SAAM;QAAC,WAAW;QAAW,OAAO;;0BACnC,8OAAC;gBAAQ,OAAO;0BACd,cAAA,8OAAC;oBACC,OAAO;wBACL,OAAO;wBACP,UAAU;wBACV,QAAQ;oBACV;;sCAGA,8OAAC;4BACC,OAAO;gCACL,WAAW;gCACX,cAAc;4BAChB;;8CAGA,8OAAC;oCACC,OAAO;wCACL,SAAS;wCACT,gBAAgB;wCAChB,cAAc;oCAChB;8CAEA,cAAA,8OAAC;wCACC,OAAO;4CACL,OAAO;4CACP,QAAQ;4CACR,iBAAiB,YAAY,QAAQ,CAAC;4CACtC,cAAc;4CACd,SAAS;4CACT,YAAY;4CACZ,gBAAgB;4CAChB,OAAO;4CACP,UAAU;4CACV,YAAY;4CACZ,WAAW,CAAC,WAAW,EAAE,YAAY,QAAQ,CAAC,WAAW,EAAE,CAAC;wCAC9D;kDACD;;;;;;;;;;;8CAMH,8OAAC;oCACC,OAAO;oCACP,OAAO;wCACL,OAAO,YAAY,YAAY,CAAC;wCAChC,cAAc;wCACd,UAAU;wCACV,YAAY;oCACd;8CAEC;;;;;;8CAIH,8OAAC;oCACC,OAAO;wCACL,OAAO,YAAY,YAAY,CAAC;wCAChC,UAAU;oCACZ;8CAEC;;;;;;;;;;;;sCAKL,8OAAC,8KAAA,CAAA,OAAI;4BACH,OAAO;gCACL,iBAAiB,YAAY,kBAAkB,CAAC;gCAChD,QAAQ,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;gCAC5D,cAAc;gCACd,WAAW,CAAC,WAAW,EAAE,YAAY,QAAQ,CAAC,WAAW,EAAE,CAAC;4BAC9D;4BACA,WAAW;gCACT,SAAS;4BACX;sCAEC;;;;;;sCAIH,8OAAC;4BACC,OAAO;gCACL,WAAW;gCACX,WAAW;4BACb;sCAEA,cAAA,8OAAC,gMAAA,CAAA,QAAK;gCAAC,qBAAO,8OAAC,oLAAA,CAAA,UAAO;oCAAC,MAAK;;;;;;;kDAC1B,8OAAC;wCACC,MAAK;wCACL,OAAO;4CACL,OAAO,YAAY,YAAY,CAAC;4CAChC,UAAU;wCACZ;kDACD;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,OAAO;4CACL,OAAO,YAAY,YAAY,CAAC;4CAChC,UAAU;wCACZ;kDACD;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,OAAO;4CACL,OAAO,YAAY,YAAY,CAAC;4CAChC,UAAU;wCACZ;kDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASR,4BACC,8OAAC,6IAAA,CAAA,eAAY;gBACX,OAAO;oBACL,iBAAiB,YAAY,kBAAkB,CAAC;oBAChD,WAAW,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;gBACjE;;;;;;;;;;;;AAKV;AAaO,SAAS,SAAS,EACvB,QAAQ,EACR,KAAK,EACL,WAAW,EACX,SAAS,EACT,KAAK,EACS;IACd,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,8OAAC;QAAI,WAAW;QAAW,OAAO;;YAC/B,CAAC,SAAS,WAAW,mBACpB,8OAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,WAAW;gBAAS;;oBACrD,uBACC,8OAAC;wBACC,OAAO;wBACP,OAAO;4BACL,OAAO,YAAY,YAAY,CAAC;4BAChC,cAAc,cAAc,QAAQ;4BACpC,UAAU;4BACV,YAAY;wBACd;kCAEC;;;;;;oBAGJ,6BACC,8OAAC;wBACC,OAAO;4BACL,OAAO,YAAY,YAAY,CAAC;4BAChC,UAAU;4BACV,YAAY;wBACd;kCAEC;;;;;;;;;;;;YAKR;;;;;;;AAGP;AAYO,SAAS,SAAS,EACvB,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,KAAK,EACS;IACd,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,OAAO;YACP,GAAG,KAAK;QACV;QACA,UAAU;kBAEV,cAAA,8OAAC,gMAAA,CAAA,QAAK;YACJ,WAAU;YACV,MAAK;YACL,OAAO;gBAAE,OAAO;YAAO;sBAEtB;;;;;;;;;;;AAIT;AAWO,SAAS,YAAY,EAC1B,OAAO,IAAI,EACX,SAAS,EACT,KAAK,EACY;IACjB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,UAAU;YACV,WAAW;YACX,QAAQ;YACR,GAAG,KAAK;QACV;;0BAEA,8OAAC,oLAAA,CAAA,UAAO;gBACN,OAAO;oBACL,aAAa,YAAY,cAAc,CAAC;gBAC1C;;;;;;0BAEF,8OAAC;gBACC,OAAO;oBACL,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,WAAW;oBACX,iBAAiB,YAAY,kBAAkB,CAAC;oBAChD,SAAS;oBACT,OAAO,YAAY,YAAY,CAAC;oBAChC,UAAU;oBACV,YAAY;gBACd;0BAEC;;;;;;;;;;;;AAIT"}}, {"offset": {"line": 5516, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5522, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/page-header.tsx"], "sourcesContent": ["/**\n * Page Header Component\n * Reusable page header with breadcrumbs, title, and actions\n */\n\n'use client';\n\nimport React from 'react';\nimport { PageHeader as AntPageHeader, Breadcrumb, Space, Divider } from 'antd';\nimport { HomeOutlined } from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Breadcrumb item interface\n */\nexport interface BreadcrumbItem {\n  title: string;\n  href?: string;\n  icon?: React.ReactNode;\n}\n\n/**\n * Page header props\n */\nexport interface PageHeaderProps {\n  title: string;\n  subtitle?: string;\n  breadcrumbs?: BreadcrumbItem[];\n  actions?: React.ReactNode[];\n  extra?: React.ReactNode;\n  children?: React.ReactNode;\n  showDivider?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Page Header component\n */\nexport function PageHeader({\n  title,\n  subtitle,\n  breadcrumbs = [],\n  actions = [],\n  extra,\n  children,\n  showDivider = true,\n  className,\n  style,\n}: PageHeaderProps) {\n  const themeStyles = useThemeStyles();\n\n  // Add home breadcrumb if not present\n  const allBreadcrumbs = breadcrumbs.length > 0 && breadcrumbs[0].href !== '/' \n    ? [{ title: 'Home', href: '/', icon: <HomeOutlined /> }, ...breadcrumbs]\n    : breadcrumbs;\n\n  return (\n    <div className={className} style={style}>\n      <div style={{\n        padding: '16px 24px',\n        backgroundColor: themeStyles.getBackgroundColor('container'),\n        borderBottom: showDivider ? `1px solid ${themeStyles.getBorderColor('primary')}` : 'none',\n      }}>\n        {/* Breadcrumbs */}\n        {allBreadcrumbs.length > 0 && (\n          <Breadcrumb style={{ marginBottom: '8px' }}>\n            {allBreadcrumbs.map((item, index) => (\n              <Breadcrumb.Item key={index} href={item.href}>\n                {item.icon && <span style={{ marginRight: '4px' }}>{item.icon}</span>}\n                {item.title}\n              </Breadcrumb.Item>\n            ))}\n          </Breadcrumb>\n        )}\n\n        {/* Header content */}\n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          flexWrap: 'wrap',\n          gap: '16px',\n        }}>\n          {/* Title section */}\n          <div style={{ flex: 1, minWidth: '200px' }}>\n            <h1 style={{\n              margin: 0,\n              fontSize: '24px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n              lineHeight: 1.2,\n            }}>\n              {title}\n            </h1>\n            {subtitle && (\n              <p style={{\n                margin: '4px 0 0 0',\n                fontSize: '14px',\n                color: themeStyles.getTextColor('secondary'),\n                lineHeight: 1.4,\n              }}>\n                {subtitle}\n              </p>\n            )}\n          </div>\n\n          {/* Actions and extra content */}\n          {(actions.length > 0 || extra) && (\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '12px',\n              flexWrap: 'wrap',\n            }}>\n              {actions.length > 0 && (\n                <Space size=\"middle\">\n                  {actions}\n                </Space>\n              )}\n              {extra}\n            </div>\n          )}\n        </div>\n\n        {/* Children content */}\n        {children && (\n          <div style={{ marginTop: '16px' }}>\n            {children}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\n/**\n * Simple page header for basic pages\n */\nexport interface SimplePageHeaderProps {\n  title: string;\n  subtitle?: string;\n  backButton?: boolean;\n  onBack?: () => void;\n  actions?: React.ReactNode[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function SimplePageHeader({\n  title,\n  subtitle,\n  backButton = false,\n  onBack,\n  actions = [],\n  className,\n  style,\n}: SimplePageHeaderProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div \n      className={className} \n      style={{\n        padding: '16px 24px',\n        backgroundColor: themeStyles.getBackgroundColor('container'),\n        borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        ...style,\n      }}\n    >\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        gap: '16px',\n      }}>\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flex: 1 }}>\n          {backButton && (\n            <button\n              onClick={onBack}\n              style={{\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                fontSize: '16px',\n                color: themeStyles.getTextColor('secondary'),\n                padding: '4px',\n              }}\n            >\n              ←\n            </button>\n          )}\n          <div>\n            <h1 style={{\n              margin: 0,\n              fontSize: '20px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n            }}>\n              {title}\n            </h1>\n            {subtitle && (\n              <p style={{\n                margin: '2px 0 0 0',\n                fontSize: '12px',\n                color: themeStyles.getTextColor('secondary'),\n              }}>\n                {subtitle}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {actions.length > 0 && (\n          <Space size=\"small\">\n            {actions}\n          </Space>\n        )}\n      </div>\n    </div>\n  );\n}\n\n/**\n * Section header for content sections\n */\nexport interface SectionHeaderProps {\n  title: string;\n  subtitle?: string;\n  actions?: React.ReactNode[];\n  divider?: boolean;\n  size?: 'small' | 'medium' | 'large';\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function SectionHeader({\n  title,\n  subtitle,\n  actions = [],\n  divider = false,\n  size = 'medium',\n  className,\n  style,\n}: SectionHeaderProps) {\n  const themeStyles = useThemeStyles();\n\n  const sizeMap = {\n    small: { fontSize: '16px', marginBottom: '12px' },\n    medium: { fontSize: '18px', marginBottom: '16px' },\n    large: { fontSize: '20px', marginBottom: '20px' },\n  };\n\n  return (\n    <div className={className} style={style}>\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        marginBottom: sizeMap[size].marginBottom,\n      }}>\n        <div>\n          <h2 style={{\n            margin: 0,\n            fontSize: sizeMap[size].fontSize,\n            fontWeight: 'bold',\n            color: themeStyles.getTextColor('primary'),\n          }}>\n            {title}\n          </h2>\n          {subtitle && (\n            <p style={{\n              margin: '4px 0 0 0',\n              fontSize: '12px',\n              color: themeStyles.getTextColor('secondary'),\n            }}>\n              {subtitle}\n            </p>\n          )}\n        </div>\n\n        {actions.length > 0 && (\n          <Space size=\"small\">\n            {actions}\n          </Space>\n        )}\n      </div>\n\n      {divider && <Divider style={{ margin: '0 0 16px 0' }} />}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAOD;AAAA;AADA;AADA;AAAA;AAAA;AAHA;;;;;AAkCO,SAAS,WAAW,EACzB,KAAK,EACL,QAAQ,EACR,cAAc,EAAE,EAChB,UAAU,EAAE,EACZ,KAAK,EACL,QAAQ,EACR,cAAc,IAAI,EAClB,SAAS,EACT,KAAK,EACW;IAChB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,qCAAqC;IACrC,MAAM,iBAAiB,YAAY,MAAM,GAAG,KAAK,WAAW,CAAC,EAAE,CAAC,IAAI,KAAK,MACrE;QAAC;YAAE,OAAO;YAAQ,MAAM;YAAK,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;QAAI;WAAM;KAAY,GACtE;IAEJ,qBACE,8OAAC;QAAI,WAAW;QAAW,OAAO;kBAChC,cAAA,8OAAC;YAAI,OAAO;gBACV,SAAS;gBACT,iBAAiB,YAAY,kBAAkB,CAAC;gBAChD,cAAc,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY,GAAG;YACrF;;gBAEG,eAAe,MAAM,GAAG,mBACvB,8OAAC,0LAAA,CAAA,aAAU;oBAAC,OAAO;wBAAE,cAAc;oBAAM;8BACtC,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC,0LAAA,CAAA,aAAU,CAAC,IAAI;4BAAa,MAAM,KAAK,IAAI;;gCACzC,KAAK,IAAI,kBAAI,8OAAC;oCAAK,OAAO;wCAAE,aAAa;oCAAM;8CAAI,KAAK,IAAI;;;;;;gCAC5D,KAAK,KAAK;;2BAFS;;;;;;;;;;8BAS5B,8OAAC;oBAAI,OAAO;wBACV,SAAS;wBACT,YAAY;wBACZ,gBAAgB;wBAChB,UAAU;wBACV,KAAK;oBACP;;sCAEE,8OAAC;4BAAI,OAAO;gCAAE,MAAM;gCAAG,UAAU;4BAAQ;;8CACvC,8OAAC;oCAAG,OAAO;wCACT,QAAQ;wCACR,UAAU;wCACV,YAAY;wCACZ,OAAO,YAAY,YAAY,CAAC;wCAChC,YAAY;oCACd;8CACG;;;;;;gCAEF,0BACC,8OAAC;oCAAE,OAAO;wCACR,QAAQ;wCACR,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;wCAChC,YAAY;oCACd;8CACG;;;;;;;;;;;;wBAMN,CAAC,QAAQ,MAAM,GAAG,KAAK,KAAK,mBAC3B,8OAAC;4BAAI,OAAO;gCACV,SAAS;gCACT,YAAY;gCACZ,KAAK;gCACL,UAAU;4BACZ;;gCACG,QAAQ,MAAM,GAAG,mBAChB,8OAAC,gMAAA,CAAA,QAAK;oCAAC,MAAK;8CACT;;;;;;gCAGJ;;;;;;;;;;;;;gBAMN,0BACC,8OAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAO;8BAC7B;;;;;;;;;;;;;;;;;AAMb;AAeO,SAAS,iBAAiB,EAC/B,KAAK,EACL,QAAQ,EACR,aAAa,KAAK,EAClB,MAAM,EACN,UAAU,EAAE,EACZ,SAAS,EACT,KAAK,EACiB;IACtB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAClE,GAAG,KAAK;QACV;kBAEA,cAAA,8OAAC;YAAI,OAAO;gBACV,SAAS;gBACT,YAAY;gBACZ,gBAAgB;gBAChB,KAAK;YACP;;8BACE,8OAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,YAAY;wBAAU,KAAK;wBAAQ,MAAM;oBAAE;;wBACvE,4BACC,8OAAC;4BACC,SAAS;4BACT,OAAO;gCACL,YAAY;gCACZ,QAAQ;gCACR,QAAQ;gCACR,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;gCAChC,SAAS;4BACX;sCACD;;;;;;sCAIH,8OAAC;;8CACC,8OAAC;oCAAG,OAAO;wCACT,QAAQ;wCACR,UAAU;wCACV,YAAY;wCACZ,OAAO,YAAY,YAAY,CAAC;oCAClC;8CACG;;;;;;gCAEF,0BACC,8OAAC;oCAAE,OAAO;wCACR,QAAQ;wCACR,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;oCAClC;8CACG;;;;;;;;;;;;;;;;;;gBAMR,QAAQ,MAAM,GAAG,mBAChB,8OAAC,gMAAA,CAAA,QAAK;oBAAC,MAAK;8BACT;;;;;;;;;;;;;;;;;AAMb;AAeO,SAAS,cAAc,EAC5B,KAAK,EACL,QAAQ,EACR,UAAU,EAAE,EACZ,UAAU,KAAK,EACf,OAAO,QAAQ,EACf,SAAS,EACT,KAAK,EACc;IACnB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,UAAU;QACd,OAAO;YAAE,UAAU;YAAQ,cAAc;QAAO;QAChD,QAAQ;YAAE,UAAU;YAAQ,cAAc;QAAO;QACjD,OAAO;YAAE,UAAU;YAAQ,cAAc;QAAO;IAClD;IAEA,qBACE,8OAAC;QAAI,WAAW;QAAW,OAAO;;0BAChC,8OAAC;gBAAI,OAAO;oBACV,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,cAAc,OAAO,CAAC,KAAK,CAAC,YAAY;gBAC1C;;kCACE,8OAAC;;0CACC,8OAAC;gCAAG,OAAO;oCACT,QAAQ;oCACR,UAAU,OAAO,CAAC,KAAK,CAAC,QAAQ;oCAChC,YAAY;oCACZ,OAAO,YAAY,YAAY,CAAC;gCAClC;0CACG;;;;;;4BAEF,0BACC,8OAAC;gCAAE,OAAO;oCACR,QAAQ;oCACR,UAAU;oCACV,OAAO,YAAY,YAAY,CAAC;gCAClC;0CACG;;;;;;;;;;;;oBAKN,QAAQ,MAAM,GAAG,mBAChB,8OAAC,gMAAA,CAAA,QAAK;wBAAC,MAAK;kCACT;;;;;;;;;;;;YAKN,yBAAW,8OAAC,oLAAA,CAAA,UAAO;gBAAC,OAAO;oBAAE,QAAQ;gBAAa;;;;;;;;;;;;AAGzD"}}, {"offset": {"line": 5887, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5893, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/content-layout.tsx"], "sourcesContent": ["/**\n * Content Layout Components\n * Reusable layout components for content organization\n */\n\n'use client';\n\nimport React from 'react';\nimport { Layout, Row, Col } from 'antd';\nimport { useThemeStyles } from '@/theme';\n\nconst { Content } = Layout;\n\n/**\n * Main content layout props\n */\nexport interface ContentLayoutProps {\n  children: React.ReactNode;\n  maxWidth?: number | string;\n  padding?: 'none' | 'small' | 'medium' | 'large';\n  centered?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Main content layout component\n */\nexport function ContentLayout({\n  children,\n  maxWidth = '1200px',\n  padding = 'medium',\n  centered = true,\n  className,\n  style,\n}: ContentLayoutProps) {\n  const themeStyles = useThemeStyles();\n\n  const paddingMap = {\n    none: '0',\n    small: '12px',\n    medium: '24px',\n    large: '32px',\n  };\n\n  const contentStyle: React.CSSProperties = {\n    maxWidth: typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth,\n    margin: centered ? '0 auto' : '0',\n    padding: paddingMap[padding],\n    backgroundColor: themeStyles.getBackgroundColor('layout'),\n    minHeight: 'calc(100vh - 64px)', // Assuming 64px header height\n    ...style,\n  };\n\n  return (\n    <Content className={className} style={contentStyle}>\n      {children}\n    </Content>\n  );\n}\n\n/**\n * Two column layout props\n */\nexport interface TwoColumnLayoutProps {\n  leftContent: React.ReactNode;\n  rightContent: React.ReactNode;\n  leftSpan?: number;\n  rightSpan?: number;\n  gutter?: number | [number, number];\n  responsive?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Two column layout component\n */\nexport function TwoColumnLayout({\n  leftContent,\n  rightContent,\n  leftSpan = 16,\n  rightSpan = 8,\n  gutter = 24,\n  responsive = true,\n  className,\n  style,\n}: TwoColumnLayoutProps) {\n  const responsiveProps = responsive ? {\n    xs: 24,\n    sm: 24,\n    md: leftSpan,\n    lg: leftSpan,\n    xl: leftSpan,\n  } : { span: leftSpan };\n\n  const rightResponsiveProps = responsive ? {\n    xs: 24,\n    sm: 24,\n    md: rightSpan,\n    lg: rightSpan,\n    xl: rightSpan,\n  } : { span: rightSpan };\n\n  return (\n    <Row gutter={gutter} className={className} style={style}>\n      <Col {...responsiveProps}>\n        {leftContent}\n      </Col>\n      <Col {...rightResponsiveProps}>\n        {rightContent}\n      </Col>\n    </Row>\n  );\n}\n\n/**\n * Three column layout props\n */\nexport interface ThreeColumnLayoutProps {\n  leftContent: React.ReactNode;\n  centerContent: React.ReactNode;\n  rightContent: React.ReactNode;\n  leftSpan?: number;\n  centerSpan?: number;\n  rightSpan?: number;\n  gutter?: number | [number, number];\n  responsive?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Three column layout component\n */\nexport function ThreeColumnLayout({\n  leftContent,\n  centerContent,\n  rightContent,\n  leftSpan = 6,\n  centerSpan = 12,\n  rightSpan = 6,\n  gutter = 24,\n  responsive = true,\n  className,\n  style,\n}: ThreeColumnLayoutProps) {\n  const getResponsiveProps = (span: number) => responsive ? {\n    xs: 24,\n    sm: 24,\n    md: span,\n    lg: span,\n    xl: span,\n  } : { span };\n\n  return (\n    <Row gutter={gutter} className={className} style={style}>\n      <Col {...getResponsiveProps(leftSpan)}>\n        {leftContent}\n      </Col>\n      <Col {...getResponsiveProps(centerSpan)}>\n        {centerContent}\n      </Col>\n      <Col {...getResponsiveProps(rightSpan)}>\n        {rightContent}\n      </Col>\n    </Row>\n  );\n}\n\n/**\n * Grid layout props\n */\nexport interface GridLayoutProps {\n  children: React.ReactNode;\n  columns?: number;\n  gutter?: number | [number, number];\n  responsive?: {\n    xs?: number;\n    sm?: number;\n    md?: number;\n    lg?: number;\n    xl?: number;\n    xxl?: number;\n  };\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Grid layout component\n */\nexport function GridLayout({\n  children,\n  columns = 3,\n  gutter = 24,\n  responsive,\n  className,\n  style,\n}: GridLayoutProps) {\n  const defaultResponsive = {\n    xs: 1,\n    sm: 1,\n    md: 2,\n    lg: columns,\n    xl: columns,\n    xxl: columns,\n  };\n\n  const responsiveConfig = responsive || defaultResponsive;\n  const span = 24 / columns;\n\n  return (\n    <Row gutter={gutter} className={className} style={style}>\n      {React.Children.map(children, (child, index) => (\n        <Col\n          key={index}\n          xs={24 / responsiveConfig.xs}\n          sm={24 / responsiveConfig.sm}\n          md={24 / responsiveConfig.md}\n          lg={24 / responsiveConfig.lg}\n          xl={24 / responsiveConfig.xl}\n          xxl={24 / responsiveConfig.xxl}\n        >\n          {child}\n        </Col>\n      ))}\n    </Row>\n  );\n}\n\n/**\n * Sidebar layout props\n */\nexport interface SidebarLayoutProps {\n  sidebar: React.ReactNode;\n  content: React.ReactNode;\n  sidebarWidth?: number;\n  sidebarPosition?: 'left' | 'right';\n  collapsible?: boolean;\n  collapsed?: boolean;\n  onCollapse?: (collapsed: boolean) => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Sidebar layout component\n */\nexport function SidebarLayout({\n  sidebar,\n  content,\n  sidebarWidth = 250,\n  sidebarPosition = 'left',\n  collapsible = false,\n  collapsed = false,\n  onCollapse,\n  className,\n  style,\n}: SidebarLayoutProps) {\n  const themeStyles = useThemeStyles();\n\n  const sidebarStyle: React.CSSProperties = {\n    width: collapsed ? '80px' : `${sidebarWidth}px`,\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    borderRight: sidebarPosition === 'left' ? `1px solid ${themeStyles.getBorderColor('primary')}` : 'none',\n    borderLeft: sidebarPosition === 'right' ? `1px solid ${themeStyles.getBorderColor('primary')}` : 'none',\n    transition: 'width 0.2s ease',\n    overflow: 'hidden',\n  };\n\n  const contentStyle: React.CSSProperties = {\n    flex: 1,\n    backgroundColor: themeStyles.getBackgroundColor('layout'),\n    overflow: 'auto',\n  };\n\n  const layoutStyle: React.CSSProperties = {\n    display: 'flex',\n    flexDirection: sidebarPosition === 'left' ? 'row' : 'row-reverse',\n    height: '100%',\n    ...style,\n  };\n\n  return (\n    <div className={className} style={layoutStyle}>\n      <div style={sidebarStyle}>\n        {collapsible && (\n          <div style={{\n            padding: '8px',\n            borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,\n            textAlign: 'center',\n          }}>\n            <button\n              onClick={() => onCollapse?.(!collapsed)}\n              style={{\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                fontSize: '16px',\n                color: themeStyles.getTextColor('secondary'),\n              }}\n            >\n              {collapsed ? '→' : '←'}\n            </button>\n          </div>\n        )}\n        {sidebar}\n      </div>\n      <div style={contentStyle}>\n        {content}\n      </div>\n    </div>\n  );\n}\n\n/**\n * Container component for consistent spacing\n */\nexport interface ContainerProps {\n  children: React.ReactNode;\n  size?: 'small' | 'medium' | 'large' | 'full';\n  padding?: boolean;\n  centered?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function Container({\n  children,\n  size = 'large',\n  padding = true,\n  centered = true,\n  className,\n  style,\n}: ContainerProps) {\n  const sizeMap = {\n    small: '600px',\n    medium: '900px',\n    large: '1200px',\n    full: '100%',\n  };\n\n  const containerStyle: React.CSSProperties = {\n    maxWidth: sizeMap[size],\n    margin: centered ? '0 auto' : '0',\n    padding: padding ? '0 24px' : '0',\n    width: '100%',\n    ...style,\n  };\n\n  return (\n    <div className={className} style={containerStyle}>\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAID;AAEA;AADA;AACA;AADA;AAAA;AAHA;;;;;AAMA,MAAM,EAAE,OAAO,EAAE,GAAG,kLAAA,CAAA,SAAM;AAiBnB,SAAS,cAAc,EAC5B,QAAQ,EACR,WAAW,QAAQ,EACnB,UAAU,QAAQ,EAClB,WAAW,IAAI,EACf,SAAS,EACT,KAAK,EACc;IACnB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,aAAa;QACjB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,eAAoC;QACxC,UAAU,OAAO,aAAa,WAAW,GAAG,SAAS,EAAE,CAAC,GAAG;QAC3D,QAAQ,WAAW,WAAW;QAC9B,SAAS,UAAU,CAAC,QAAQ;QAC5B,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,WAAW;QACX,GAAG,KAAK;IACV;IAEA,qBACE,8OAAC;QAAQ,WAAW;QAAW,OAAO;kBACnC;;;;;;AAGP;AAmBO,SAAS,gBAAgB,EAC9B,WAAW,EACX,YAAY,EACZ,WAAW,EAAE,EACb,YAAY,CAAC,EACb,SAAS,EAAE,EACX,aAAa,IAAI,EACjB,SAAS,EACT,KAAK,EACgB;IACrB,MAAM,kBAAkB,aAAa;QACnC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,IAAI;QAAE,MAAM;IAAS;IAErB,MAAM,uBAAuB,aAAa;QACxC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,IAAI;QAAE,MAAM;IAAU;IAEtB,qBACE,8OAAC,4KAAA,CAAA,MAAG;QAAC,QAAQ;QAAQ,WAAW;QAAW,OAAO;;0BAChD,8OAAC,4KAAA,CAAA,MAAG;gBAAE,GAAG,eAAe;0BACrB;;;;;;0BAEH,8OAAC,4KAAA,CAAA,MAAG;gBAAE,GAAG,oBAAoB;0BAC1B;;;;;;;;;;;;AAIT;AAqBO,SAAS,kBAAkB,EAChC,WAAW,EACX,aAAa,EACb,YAAY,EACZ,WAAW,CAAC,EACZ,aAAa,EAAE,EACf,YAAY,CAAC,EACb,SAAS,EAAE,EACX,aAAa,IAAI,EACjB,SAAS,EACT,KAAK,EACkB;IACvB,MAAM,qBAAqB,CAAC,OAAiB,aAAa;YACxD,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN,IAAI;YAAE;QAAK;IAEX,qBACE,8OAAC,4KAAA,CAAA,MAAG;QAAC,QAAQ;QAAQ,WAAW;QAAW,OAAO;;0BAChD,8OAAC,4KAAA,CAAA,MAAG;gBAAE,GAAG,mBAAmB,SAAS;0BAClC;;;;;;0BAEH,8OAAC,4KAAA,CAAA,MAAG;gBAAE,GAAG,mBAAmB,WAAW;0BACpC;;;;;;0BAEH,8OAAC,4KAAA,CAAA,MAAG;gBAAE,GAAG,mBAAmB,UAAU;0BACnC;;;;;;;;;;;;AAIT;AAwBO,SAAS,WAAW,EACzB,QAAQ,EACR,UAAU,CAAC,EACX,SAAS,EAAE,EACX,UAAU,EACV,SAAS,EACT,KAAK,EACW;IAChB,MAAM,oBAAoB;QACxB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,KAAK;IACP;IAEA,MAAM,mBAAmB,cAAc;IACvC,MAAM,OAAO,KAAK;IAElB,qBACE,8OAAC,4KAAA,CAAA,MAAG;QAAC,QAAQ;QAAQ,WAAW;QAAW,OAAO;kBAC/C,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,sBACpC,8OAAC,4KAAA,CAAA,MAAG;gBAEF,IAAI,KAAK,iBAAiB,EAAE;gBAC5B,IAAI,KAAK,iBAAiB,EAAE;gBAC5B,IAAI,KAAK,iBAAiB,EAAE;gBAC5B,IAAI,KAAK,iBAAiB,EAAE;gBAC5B,IAAI,KAAK,iBAAiB,EAAE;gBAC5B,KAAK,KAAK,iBAAiB,GAAG;0BAE7B;eARI;;;;;;;;;;AAaf;AAoBO,SAAS,cAAc,EAC5B,OAAO,EACP,OAAO,EACP,eAAe,GAAG,EAClB,kBAAkB,MAAM,EACxB,cAAc,KAAK,EACnB,YAAY,KAAK,EACjB,UAAU,EACV,SAAS,EACT,KAAK,EACc;IACnB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,eAAoC;QACxC,OAAO,YAAY,SAAS,GAAG,aAAa,EAAE,CAAC;QAC/C,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,aAAa,oBAAoB,SAAS,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY,GAAG;QACjG,YAAY,oBAAoB,UAAU,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY,GAAG;QACjG,YAAY;QACZ,UAAU;IACZ;IAEA,MAAM,eAAoC;QACxC,MAAM;QACN,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,UAAU;IACZ;IAEA,MAAM,cAAmC;QACvC,SAAS;QACT,eAAe,oBAAoB,SAAS,QAAQ;QACpD,QAAQ;QACR,GAAG,KAAK;IACV;IAEA,qBACE,8OAAC;QAAI,WAAW;QAAW,OAAO;;0BAChC,8OAAC;gBAAI,OAAO;;oBACT,6BACC,8OAAC;wBAAI,OAAO;4BACV,SAAS;4BACT,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;4BAClE,WAAW;wBACb;kCACE,cAAA,8OAAC;4BACC,SAAS,IAAM,aAAa,CAAC;4BAC7B,OAAO;gCACL,YAAY;gCACZ,QAAQ;gCACR,QAAQ;gCACR,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;4BAClC;sCAEC,YAAY,MAAM;;;;;;;;;;;oBAIxB;;;;;;;0BAEH,8OAAC;gBAAI,OAAO;0BACT;;;;;;;;;;;;AAIT;AAcO,SAAS,UAAU,EACxB,QAAQ,EACR,OAAO,OAAO,EACd,UAAU,IAAI,EACd,WAAW,IAAI,EACf,SAAS,EACT,KAAK,EACU;IACf,MAAM,UAAU;QACd,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IAEA,MAAM,iBAAsC;QAC1C,UAAU,OAAO,CAAC,KAAK;QACvB,QAAQ,WAAW,WAAW;QAC9B,SAAS,UAAU,WAAW;QAC9B,OAAO;QACP,GAAG,KAAK;IACV;IAEA,qBACE,8OAAC;QAAI,WAAW;QAAW,OAAO;kBAC/B;;;;;;AAGP"}}, {"offset": {"line": 6170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6176, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/index.ts"], "sourcesContent": ["/**\n * Layout Components Index\n * Export all layout components\n */\n\n// Main app layout components\nexport * from './app-layout';\nexport * from './app-header';\nexport * from './app-sidebar';\nexport * from './app-footer';\n\n// Authentication layout components\nexport * from './auth-layout';\n\n// Page header components\nexport * from './page-header';\n\n// Content layout components\nexport * from './content-layout';\n\n// Re-export Ant Design layout components for convenience\nexport {\n  Layout,\n  Header,\n  Footer,\n  Sider,\n  Content,\n} from 'antd';\n\n/**\n * Layout components metadata\n */\nexport const LAYOUT_COMPONENTS_VERSION = '1.0.0';\nexport const LAYOUT_COMPONENTS_NAME = 'APISportsGame Layout Components';\n\n/**\n * Setup function for layout components\n */\nexport function setupLayoutComponents() {\n  console.log(`${LAYOUT_COMPONENTS_NAME} v${LAYOUT_COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,6BAA6B;;;;;;;;;;;;;;AA2BtB,MAAM,4BAA4B;AAClC,MAAM,yBAAyB;AAK/B,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,uBAAuB,EAAE,EAAE,0BAA0B,YAAY,CAAC;AACnF"}}, {"offset": {"line": 6198, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6219, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/forms/form-wrapper.tsx"], "sourcesContent": ["/**\n * Form Wrapper Components\n * Enhanced form components with validation and theme integration\n */\n\n'use client';\n\nimport React from 'react';\nimport { Form as AntForm, FormProps as AntFormProps, Space, Divider } from 'antd';\nimport { Button, LoadingButton } from '@/components/ui';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Enhanced form props\n */\nexport interface FormProps extends AntFormProps {\n  title?: string;\n  subtitle?: string;\n  loading?: boolean;\n  submitText?: string;\n  cancelText?: string;\n  showCancel?: boolean;\n  onCancel?: () => void;\n  actions?: React.ReactNode[];\n  footer?: React.ReactNode;\n  compact?: boolean;\n}\n\n/**\n * Enhanced Form component\n */\nexport function Form({\n  title,\n  subtitle,\n  loading = false,\n  submitText = 'Submit',\n  cancelText = 'Cancel',\n  showCancel = false,\n  onCancel,\n  actions = [],\n  footer,\n  compact = false,\n  children,\n  onFinish,\n  ...props\n}: FormProps) {\n  const themeStyles = useThemeStyles();\n\n  const formStyle: React.CSSProperties = {\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    padding: compact ? '16px' : '24px',\n    borderRadius: '8px',\n    border: `1px solid ${themeStyles.getBorderColor('primary')}`,\n  };\n\n  return (\n    <div style={formStyle}>\n      {/* Form header */}\n      {(title || subtitle) && (\n        <div style={{ marginBottom: '24px' }}>\n          {title && (\n            <h2 style={{\n              margin: 0,\n              fontSize: '20px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n            }}>\n              {title}\n            </h2>\n          )}\n          {subtitle && (\n            <p style={{\n              margin: '4px 0 0 0',\n              fontSize: '14px',\n              color: themeStyles.getTextColor('secondary'),\n            }}>\n              {subtitle}\n            </p>\n          )}\n          <Divider style={{ margin: '16px 0 0 0' }} />\n        </div>\n      )}\n\n      {/* Form content */}\n      <AntForm\n        layout=\"vertical\"\n        onFinish={onFinish}\n        disabled={loading}\n        {...props}\n      >\n        {children}\n\n        {/* Form actions */}\n        <div style={{ marginTop: '24px' }}>\n          <Space>\n            <LoadingButton\n              type=\"primary\"\n              htmlType=\"submit\"\n              isLoading={loading}\n              loadingText=\"Submitting...\"\n            >\n              {submitText}\n            </LoadingButton>\n            \n            {showCancel && (\n              <Button\n                type=\"default\"\n                onClick={onCancel}\n                disabled={loading}\n              >\n                {cancelText}\n              </Button>\n            )}\n            \n            {actions}\n          </Space>\n        </div>\n      </AntForm>\n\n      {/* Custom footer */}\n      {footer && (\n        <div style={{ marginTop: '16px', paddingTop: '16px', borderTop: `1px solid ${themeStyles.getBorderColor('primary')}` }}>\n          {footer}\n        </div>\n      )}\n    </div>\n  );\n}\n\n/**\n * Inline form props\n */\nexport interface InlineFormProps extends AntFormProps {\n  submitText?: string;\n  loading?: boolean;\n  actions?: React.ReactNode[];\n}\n\n/**\n * Inline form component for simple forms\n */\nexport function InlineForm({\n  submitText = 'Submit',\n  loading = false,\n  actions = [],\n  children,\n  onFinish,\n  ...props\n}: InlineFormProps) {\n  return (\n    <AntForm\n      layout=\"inline\"\n      onFinish={onFinish}\n      disabled={loading}\n      {...props}\n    >\n      {children}\n      \n      <AntForm.Item>\n        <Space>\n          <LoadingButton\n            type=\"primary\"\n            htmlType=\"submit\"\n            isLoading={loading}\n            compact\n          >\n            {submitText}\n          </LoadingButton>\n          {actions}\n        </Space>\n      </AntForm.Item>\n    </AntForm>\n  );\n}\n\n/**\n * Search form props\n */\nexport interface SearchFormProps {\n  onSearch: (values: any) => void;\n  onReset?: () => void;\n  loading?: boolean;\n  children: React.ReactNode;\n  collapsed?: boolean;\n  onToggleCollapse?: () => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Search form component\n */\nexport function SearchForm({\n  onSearch,\n  onReset,\n  loading = false,\n  children,\n  collapsed = false,\n  onToggleCollapse,\n  className,\n  style,\n}: SearchFormProps) {\n  const themeStyles = useThemeStyles();\n\n  const [form] = AntForm.useForm();\n\n  const handleReset = () => {\n    form.resetFields();\n    onReset?.();\n  };\n\n  return (\n    <div \n      className={className}\n      style={{\n        backgroundColor: themeStyles.getBackgroundColor('container'),\n        padding: '16px',\n        borderRadius: '8px',\n        border: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        marginBottom: '16px',\n        ...style,\n      }}\n    >\n      <AntForm\n        form={form}\n        layout=\"vertical\"\n        onFinish={onSearch}\n        disabled={loading}\n      >\n        <div style={{ \n          display: collapsed ? 'none' : 'block',\n          marginBottom: '16px',\n        }}>\n          {children}\n        </div>\n\n        <div style={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n        }}>\n          <Space>\n            <LoadingButton\n              type=\"primary\"\n              htmlType=\"submit\"\n              isLoading={loading}\n              compact\n            >\n              Search\n            </LoadingButton>\n            \n            <Button\n              onClick={handleReset}\n              disabled={loading}\n              compact\n            >\n              Reset\n            </Button>\n          </Space>\n\n          {onToggleCollapse && (\n            <Button\n              type=\"text\"\n              onClick={onToggleCollapse}\n              compact\n            >\n              {collapsed ? 'Expand' : 'Collapse'}\n            </Button>\n          )}\n        </div>\n      </AntForm>\n    </div>\n  );\n}\n\n/**\n * Modal form props\n */\nexport interface ModalFormProps extends FormProps {\n  visible: boolean;\n  onClose: () => void;\n  width?: number;\n  destroyOnClose?: boolean;\n}\n\n/**\n * Modal form component\n */\nexport function ModalForm({\n  visible,\n  onClose,\n  width = 600,\n  destroyOnClose = true,\n  title,\n  loading = false,\n  submitText = 'Save',\n  cancelText = 'Cancel',\n  children,\n  onFinish,\n  ...props\n}: ModalFormProps) {\n  const { Modal } = require('antd');\n  const [form] = AntForm.useForm();\n\n  const handleFinish = async (values: any) => {\n    try {\n      await onFinish?.(values);\n      form.resetFields();\n      onClose();\n    } catch (error) {\n      // Error handling is done by the parent component\n    }\n  };\n\n  const handleCancel = () => {\n    form.resetFields();\n    onClose();\n  };\n\n  return (\n    <Modal\n      title={title}\n      open={visible}\n      onCancel={handleCancel}\n      width={width}\n      destroyOnClose={destroyOnClose}\n      footer={null}\n    >\n      <AntForm\n        form={form}\n        layout=\"vertical\"\n        onFinish={handleFinish}\n        disabled={loading}\n        {...props}\n      >\n        {children}\n\n        <div style={{ \n          marginTop: '24px',\n          textAlign: 'right',\n          borderTop: '1px solid #f0f0f0',\n          paddingTop: '16px',\n        }}>\n          <Space>\n            <Button onClick={handleCancel} disabled={loading}>\n              {cancelText}\n            </Button>\n            <LoadingButton\n              type=\"primary\"\n              htmlType=\"submit\"\n              isLoading={loading}\n            >\n              {submitText}\n            </LoadingButton>\n          </Space>\n        </div>\n      </AntForm>\n    </Modal>\n  );\n}\n\n/**\n * Drawer form props\n */\nexport interface DrawerFormProps extends FormProps {\n  visible: boolean;\n  onClose: () => void;\n  width?: number;\n  placement?: 'left' | 'right';\n  destroyOnClose?: boolean;\n}\n\n/**\n * Drawer form component\n */\nexport function DrawerForm({\n  visible,\n  onClose,\n  width = 600,\n  placement = 'right',\n  destroyOnClose = true,\n  title,\n  loading = false,\n  submitText = 'Save',\n  cancelText = 'Cancel',\n  children,\n  onFinish,\n  ...props\n}: DrawerFormProps) {\n  const { Drawer } = require('antd');\n  const [form] = AntForm.useForm();\n\n  const handleFinish = async (values: any) => {\n    try {\n      await onFinish?.(values);\n      form.resetFields();\n      onClose();\n    } catch (error) {\n      // Error handling is done by the parent component\n    }\n  };\n\n  const handleClose = () => {\n    form.resetFields();\n    onClose();\n  };\n\n  return (\n    <Drawer\n      title={title}\n      open={visible}\n      onClose={handleClose}\n      width={width}\n      placement={placement}\n      destroyOnClose={destroyOnClose}\n      footer={\n        <div style={{ textAlign: 'right' }}>\n          <Space>\n            <Button onClick={handleClose} disabled={loading}>\n              {cancelText}\n            </Button>\n            <LoadingButton\n              type=\"primary\"\n              onClick={() => form.submit()}\n              isLoading={loading}\n            >\n              {submitText}\n            </LoadingButton>\n          </Space>\n        </div>\n      }\n    >\n      <AntForm\n        form={form}\n        layout=\"vertical\"\n        onFinish={handleFinish}\n        disabled={loading}\n        {...props}\n      >\n        {children}\n      </AntForm>\n    </Drawer>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAMD;AACA;AAAA;AAFA;AAAA;AAAA;AACA;AAJA;;;;;AA0BO,SAAS,KAAK,EACnB,KAAK,EACL,QAAQ,EACR,UAAU,KAAK,EACf,aAAa,QAAQ,EACrB,aAAa,QAAQ,EACrB,aAAa,KAAK,EAClB,QAAQ,EACR,UAAU,EAAE,EACZ,MAAM,EACN,UAAU,KAAK,EACf,QAAQ,EACR,QAAQ,EACR,GAAG,OACO;IACV,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,YAAiC;QACrC,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,SAAS,UAAU,SAAS;QAC5B,cAAc;QACd,QAAQ,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;IAC9D;IAEA,qBACE,8OAAC;QAAI,OAAO;;YAET,CAAC,SAAS,QAAQ,mBACjB,8OAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;;oBAChC,uBACC,8OAAC;wBAAG,OAAO;4BACT,QAAQ;4BACR,UAAU;4BACV,YAAY;4BACZ,OAAO,YAAY,YAAY,CAAC;wBAClC;kCACG;;;;;;oBAGJ,0BACC,8OAAC;wBAAE,OAAO;4BACR,QAAQ;4BACR,UAAU;4BACV,OAAO,YAAY,YAAY,CAAC;wBAClC;kCACG;;;;;;kCAGL,8OAAC,oLAAA,CAAA,UAAO;wBAAC,OAAO;4BAAE,QAAQ;wBAAa;;;;;;;;;;;;0BAK3C,8OAAC,8KAAA,CAAA,OAAO;gBACN,QAAO;gBACP,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;;oBAER;kCAGD,8OAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAO;kCAC9B,cAAA,8OAAC,gMAAA,CAAA,QAAK;;8CACJ,8OAAC,kIAAA,CAAA,gBAAa;oCACZ,MAAK;oCACL,UAAS;oCACT,WAAW;oCACX,aAAY;8CAEX;;;;;;gCAGF,4BACC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;oCACT,UAAU;8CAET;;;;;;gCAIJ;;;;;;;;;;;;;;;;;;YAMN,wBACC,8OAAC;gBAAI,OAAO;oBAAE,WAAW;oBAAQ,YAAY;oBAAQ,WAAW,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;gBAAC;0BAClH;;;;;;;;;;;;AAKX;AAcO,SAAS,WAAW,EACzB,aAAa,QAAQ,EACrB,UAAU,KAAK,EACf,UAAU,EAAE,EACZ,QAAQ,EACR,QAAQ,EACR,GAAG,OACa;IAChB,qBACE,8OAAC,8KAAA,CAAA,OAAO;QACN,QAAO;QACP,UAAU;QACV,UAAU;QACT,GAAG,KAAK;;YAER;0BAED,8OAAC,8KAAA,CAAA,OAAO,CAAC,IAAI;0BACX,cAAA,8OAAC,gMAAA,CAAA,QAAK;;sCACJ,8OAAC,kIAAA,CAAA,gBAAa;4BACZ,MAAK;4BACL,UAAS;4BACT,WAAW;4BACX,OAAO;sCAEN;;;;;;wBAEF;;;;;;;;;;;;;;;;;;AAKX;AAmBO,SAAS,WAAW,EACzB,QAAQ,EACR,OAAO,EACP,UAAU,KAAK,EACf,QAAQ,EACR,YAAY,KAAK,EACjB,gBAAgB,EAChB,SAAS,EACT,KAAK,EACW;IAChB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAO,CAAC,OAAO;IAE9B,MAAM,cAAc;QAClB,KAAK,WAAW;QAChB;IACF;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,SAAS;YACT,cAAc;YACd,QAAQ,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAC5D,cAAc;YACd,GAAG,KAAK;QACV;kBAEA,cAAA,8OAAC,8KAAA,CAAA,OAAO;YACN,MAAM;YACN,QAAO;YACP,UAAU;YACV,UAAU;;8BAEV,8OAAC;oBAAI,OAAO;wBACV,SAAS,YAAY,SAAS;wBAC9B,cAAc;oBAChB;8BACG;;;;;;8BAGH,8OAAC;oBAAI,OAAO;wBACV,SAAS;wBACT,gBAAgB;wBAChB,YAAY;oBACd;;sCACE,8OAAC,gMAAA,CAAA,QAAK;;8CACJ,8OAAC,kIAAA,CAAA,gBAAa;oCACZ,MAAK;oCACL,UAAS;oCACT,WAAW;oCACX,OAAO;8CACR;;;;;;8CAID,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU;oCACV,OAAO;8CACR;;;;;;;;;;;;wBAKF,kCACC,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAS;4BACT,OAAO;sCAEN,YAAY,WAAW;;;;;;;;;;;;;;;;;;;;;;;AAOtC;AAeO,SAAS,UAAU,EACxB,OAAO,EACP,OAAO,EACP,QAAQ,GAAG,EACX,iBAAiB,IAAI,EACrB,KAAK,EACL,UAAU,KAAK,EACf,aAAa,MAAM,EACnB,aAAa,QAAQ,EACrB,QAAQ,EACR,QAAQ,EACR,GAAG,OACY;IACf,MAAM,EAAE,KAAK,EAAE;IACf,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAO,CAAC,OAAO;IAE9B,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW;YACjB,KAAK,WAAW;YAChB;QACF,EAAE,OAAO,OAAO;QACd,iDAAiD;QACnD;IACF;IAEA,MAAM,eAAe;QACnB,KAAK,WAAW;QAChB;IACF;IAEA,qBACE,8OAAC;QACC,OAAO;QACP,MAAM;QACN,UAAU;QACV,OAAO;QACP,gBAAgB;QAChB,QAAQ;kBAER,cAAA,8OAAC,8KAAA,CAAA,OAAO;YACN,MAAM;YACN,QAAO;YACP,UAAU;YACV,UAAU;YACT,GAAG,KAAK;;gBAER;8BAED,8OAAC;oBAAI,OAAO;wBACV,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,YAAY;oBACd;8BACE,cAAA,8OAAC,gMAAA,CAAA,QAAK;;0CACJ,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAc,UAAU;0CACtC;;;;;;0CAEH,8OAAC,kIAAA,CAAA,gBAAa;gCACZ,MAAK;gCACL,UAAS;gCACT,WAAW;0CAEV;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;AAgBO,SAAS,WAAW,EACzB,OAAO,EACP,OAAO,EACP,QAAQ,GAAG,EACX,YAAY,OAAO,EACnB,iBAAiB,IAAI,EACrB,KAAK,EACL,UAAU,KAAK,EACf,aAAa,MAAM,EACnB,aAAa,QAAQ,EACrB,QAAQ,EACR,QAAQ,EACR,GAAG,OACa;IAChB,MAAM,EAAE,MAAM,EAAE;IAChB,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAO,CAAC,OAAO;IAE9B,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW;YACjB,KAAK,WAAW;YAChB;QACF,EAAE,OAAO,OAAO;QACd,iDAAiD;QACnD;IACF;IAEA,MAAM,cAAc;QAClB,KAAK,WAAW;QAChB;IACF;IAEA,qBACE,8OAAC;QACC,OAAO;QACP,MAAM;QACN,SAAS;QACT,OAAO;QACP,WAAW;QACX,gBAAgB;QAChB,sBACE,8OAAC;YAAI,OAAO;gBAAE,WAAW;YAAQ;sBAC/B,cAAA,8OAAC,gMAAA,CAAA,QAAK;;kCACJ,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAa,UAAU;kCACrC;;;;;;kCAEH,8OAAC,kIAAA,CAAA,gBAAa;wBACZ,MAAK;wBACL,SAAS,IAAM,KAAK,MAAM;wBAC1B,WAAW;kCAEV;;;;;;;;;;;;;;;;;kBAMT,cAAA,8OAAC,8KAAA,CAAA,OAAO;YACN,MAAM;YACN,QAAO;YACP,UAAU;YACV,UAAU;YACT,GAAG,KAAK;sBAER;;;;;;;;;;;AAIT"}}, {"offset": {"line": 6667, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6673, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/forms/index.ts"], "sourcesContent": ["/**\n * Form Components Index\n * Export all form components\n */\n\n// Form wrapper components\nexport * from './form-wrapper';\n\n// Re-export Ant Design form components for convenience\nexport {\n  Form as AntForm,\n  FormInstance,\n  FormProps as AntFormProps,\n  FormItemProps,\n  FormListProps,\n} from 'antd';\n\n/**\n * Form components metadata\n */\nexport const FORM_COMPONENTS_VERSION = '1.0.0';\nexport const FORM_COMPONENTS_NAME = 'APISportsGame Form Components';\n\n/**\n * Setup function for form components\n */\nexport function setupFormComponents() {\n  console.log(`${FORM_COMPONENTS_NAME} v${FORM_COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,0BAA0B;;;;;;;;AAenB,MAAM,0BAA0B;AAChC,MAAM,uBAAuB;AAK7B,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,qBAAqB,EAAE,EAAE,wBAAwB,YAAY,CAAC;AAC/E"}}, {"offset": {"line": 6689, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6704, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/data-display/data-table.tsx"], "sourcesContent": ["/**\n * Enhanced Data Table Component\n * Extended Ant Design Table with theme integration and additional features\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { \n  Table as AntTable, \n  TableProps as AntTableProps,\n  Space,\n  Input,\n  Button,\n  Dropdown,\n  Menu,\n  Tooltip,\n  Tag,\n} from 'antd';\nimport { \n  SearchOutlined, \n  FilterOutlined, \n  DownloadOutlined,\n  ReloadOutlined,\n  SettingOutlined,\n} from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Enhanced table props\n */\nexport interface DataTableProps<T = any> extends AntTableProps<T> {\n  searchable?: boolean;\n  searchPlaceholder?: string;\n  onSearch?: (value: string) => void;\n  refreshable?: boolean;\n  onRefresh?: () => void;\n  exportable?: boolean;\n  onExport?: () => void;\n  settingsMenu?: React.ReactNode;\n  toolbar?: React.ReactNode;\n  actions?: React.ReactNode[];\n  compact?: boolean;\n  bordered?: boolean;\n}\n\n/**\n * Enhanced Data Table component\n */\nexport function DataTable<T = any>({\n  searchable = false,\n  searchPlaceholder = 'Search...',\n  onSearch,\n  refreshable = false,\n  onRefresh,\n  exportable = false,\n  onExport,\n  settingsMenu,\n  toolbar,\n  actions = [],\n  compact = false,\n  bordered = true,\n  className,\n  style,\n  ...props\n}: DataTableProps<T>) {\n  const themeStyles = useThemeStyles();\n  const [searchValue, setSearchValue] = useState('');\n\n  const handleSearch = (value: string) => {\n    setSearchValue(value);\n    onSearch?.(value);\n  };\n\n  const tableStyle: React.CSSProperties = {\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    borderRadius: '8px',\n    overflow: 'hidden',\n    ...style,\n  };\n\n  // Build toolbar actions\n  const toolbarActions = [\n    ...actions,\n    refreshable && (\n      <Tooltip title=\"Refresh\" key=\"refresh\">\n        <Button \n          icon={<ReloadOutlined />} \n          onClick={onRefresh}\n          size={compact ? 'small' : 'middle'}\n        />\n      </Tooltip>\n    ),\n    exportable && (\n      <Tooltip title=\"Export\" key=\"export\">\n        <Button \n          icon={<DownloadOutlined />} \n          onClick={onExport}\n          size={compact ? 'small' : 'middle'}\n        />\n      </Tooltip>\n    ),\n    settingsMenu && (\n      <Dropdown overlay={settingsMenu} key=\"settings\">\n        <Button \n          icon={<SettingOutlined />}\n          size={compact ? 'small' : 'middle'}\n        />\n      </Dropdown>\n    ),\n  ].filter(Boolean);\n\n  return (\n    <div className={className} style={tableStyle}>\n      {/* Table toolbar */}\n      {(searchable || toolbar || toolbarActions.length > 0) && (\n        <div style={{\n          padding: compact ? '12px' : '16px',\n          borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          gap: '12px',\n          flexWrap: 'wrap',\n        }}>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flex: 1 }}>\n            {searchable && (\n              <Input.Search\n                placeholder={searchPlaceholder}\n                value={searchValue}\n                onChange={(e) => setSearchValue(e.target.value)}\n                onSearch={handleSearch}\n                style={{ maxWidth: '300px' }}\n                size={compact ? 'small' : 'middle'}\n              />\n            )}\n            {toolbar}\n          </div>\n\n          {toolbarActions.length > 0 && (\n            <Space size=\"small\">\n              {toolbarActions}\n            </Space>\n          )}\n        </div>\n      )}\n\n      {/* Table content */}\n      <AntTable\n        size={compact ? 'small' : 'middle'}\n        bordered={bordered}\n        pagination={{\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => \n            `${range[0]}-${range[1]} of ${total} items`,\n          ...props.pagination,\n        }}\n        scroll={{ x: 'max-content' }}\n        {...props}\n      />\n    </div>\n  );\n}\n\n/**\n * Status column helper\n */\nexport interface StatusColumnProps {\n  value: string;\n  colorMap?: Record<string, string>;\n}\n\nexport function StatusColumn({ value, colorMap }: StatusColumnProps) {\n  const defaultColorMap: Record<string, string> = {\n    active: 'green',\n    inactive: 'red',\n    pending: 'orange',\n    draft: 'blue',\n    published: 'green',\n    archived: 'gray',\n  };\n\n  const colors = { ...defaultColorMap, ...colorMap };\n  const color = colors[value.toLowerCase()] || 'default';\n\n  return <Tag color={color}>{value}</Tag>;\n}\n\n/**\n * Actions column helper\n */\nexport interface ActionsColumnProps {\n  actions: Array<{\n    key: string;\n    label: string;\n    icon?: React.ReactNode;\n    onClick: () => void;\n    disabled?: boolean;\n    danger?: boolean;\n  }>;\n  compact?: boolean;\n}\n\nexport function ActionsColumn({ actions, compact = false }: ActionsColumnProps) {\n  if (actions.length <= 2) {\n    return (\n      <Space size=\"small\">\n        {actions.map((action) => (\n          <Button\n            key={action.key}\n            type=\"text\"\n            icon={action.icon}\n            onClick={action.onClick}\n            disabled={action.disabled}\n            danger={action.danger}\n            size={compact ? 'small' : 'middle'}\n          >\n            {!compact && action.label}\n          </Button>\n        ))}\n      </Space>\n    );\n  }\n\n  const menu = (\n    <Menu>\n      {actions.map((action) => (\n        <Menu.Item\n          key={action.key}\n          icon={action.icon}\n          onClick={action.onClick}\n          disabled={action.disabled}\n          danger={action.danger}\n        >\n          {action.label}\n        </Menu.Item>\n      ))}\n    </Menu>\n  );\n\n  return (\n    <Dropdown overlay={menu} trigger={['click']}>\n      <Button type=\"text\" icon={<SettingOutlined />} />\n    </Dropdown>\n  );\n}\n\n/**\n * Quick filter component\n */\nexport interface QuickFilterProps {\n  filters: Array<{\n    key: string;\n    label: string;\n    value: any;\n  }>;\n  activeFilter?: string;\n  onChange: (filterKey: string, filterValue: any) => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function QuickFilter({\n  filters,\n  activeFilter,\n  onChange,\n  className,\n  style,\n}: QuickFilterProps) {\n  return (\n    <div className={className} style={{ display: 'flex', gap: '8px', ...style }}>\n      {filters.map((filter) => (\n        <Button\n          key={filter.key}\n          type={activeFilter === filter.key ? 'primary' : 'default'}\n          size=\"small\"\n          onClick={() => onChange(filter.key, filter.value)}\n        >\n          {filter.label}\n        </Button>\n      ))}\n    </div>\n  );\n}\n\n/**\n * Table summary component\n */\nexport interface TableSummaryProps {\n  data: Array<{\n    label: string;\n    value: string | number;\n    type?: 'default' | 'primary' | 'success' | 'warning' | 'error';\n  }>;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function TableSummary({ data, className, style }: TableSummaryProps) {\n  const themeStyles = useThemeStyles();\n\n  const getTypeColor = (type: string = 'default') => {\n    switch (type) {\n      case 'primary': return themeStyles.getColor('primary');\n      case 'success': return themeStyles.getColor('success');\n      case 'warning': return themeStyles.getColor('warning');\n      case 'error': return themeStyles.getColor('error');\n      default: return themeStyles.getTextColor('primary');\n    }\n  };\n\n  return (\n    <div \n      className={className}\n      style={{\n        display: 'flex',\n        gap: '24px',\n        padding: '12px 16px',\n        backgroundColor: themeStyles.getBackgroundColor('elevated'),\n        borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        ...style,\n      }}\n    >\n      {data.map((item, index) => (\n        <div key={index} style={{ textAlign: 'center' }}>\n          <div style={{\n            fontSize: '12px',\n            color: themeStyles.getTextColor('secondary'),\n            marginBottom: '2px',\n          }}>\n            {item.label}\n          </div>\n          <div style={{\n            fontSize: '16px',\n            fontWeight: 'bold',\n            color: getTypeColor(item.type),\n          }}>\n            {item.value}\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAID;AAmBA;AAAA;AAlBA;AAAA;AAWA;AAAA;AAXA;AAWA;AAXA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;;;AA4CO,SAAS,UAAmB,EACjC,aAAa,KAAK,EAClB,oBAAoB,WAAW,EAC/B,QAAQ,EACR,cAAc,KAAK,EACnB,SAAS,EACT,aAAa,KAAK,EAClB,QAAQ,EACR,YAAY,EACZ,OAAO,EACP,UAAU,EAAE,EACZ,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,EACT,KAAK,EACL,GAAG,OACe;IAClB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,eAAe;QACf,WAAW;IACb;IAEA,MAAM,aAAkC;QACtC,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,cAAc;QACd,UAAU;QACV,GAAG,KAAK;IACV;IAEA,wBAAwB;IACxB,MAAM,iBAAiB;WAClB;QACH,6BACE,8OAAC,oLAAA,CAAA,UAAO;YAAC,OAAM;sBACb,cAAA,8OAAC,kMAAA,CAAA,SAAM;gBACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;gBACrB,SAAS;gBACT,MAAM,UAAU,UAAU;;;;;;WAJD;;;;;QAQ/B,4BACE,8OAAC,oLAAA,CAAA,UAAO;YAAC,OAAM;sBACb,cAAA,8OAAC,kMAAA,CAAA,SAAM;gBACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gBACvB,SAAS;gBACT,MAAM,UAAU,UAAU;;;;;;WAJF;;;;;QAQ9B,8BACE,8OAAC,sLAAA,CAAA,WAAQ;YAAC,SAAS;sBACjB,cAAA,8OAAC,kMAAA,CAAA,SAAM;gBACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;gBACtB,MAAM,UAAU,UAAU;;;;;;WAHO;;;;;KAOxC,CAAC,MAAM,CAAC;IAET,qBACE,8OAAC;QAAI,WAAW;QAAW,OAAO;;YAE/B,CAAC,cAAc,WAAW,eAAe,MAAM,GAAG,CAAC,mBAClD,8OAAC;gBAAI,OAAO;oBACV,SAAS,UAAU,SAAS;oBAC5B,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;oBAClE,SAAS;oBACT,gBAAgB;oBAChB,YAAY;oBACZ,KAAK;oBACL,UAAU;gBACZ;;kCACE,8OAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,YAAY;4BAAU,KAAK;4BAAQ,MAAM;wBAAE;;4BACvE,4BACC,8OAAC,gLAAA,CAAA,QAAK,CAAC,MAAM;gCACX,aAAa;gCACb,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,UAAU;gCACV,OAAO;oCAAE,UAAU;gCAAQ;gCAC3B,MAAM,UAAU,UAAU;;;;;;4BAG7B;;;;;;;oBAGF,eAAe,MAAM,GAAG,mBACvB,8OAAC,gMAAA,CAAA,QAAK;wBAAC,MAAK;kCACT;;;;;;;;;;;;0BAOT,8OAAC,gLAAA,CAAA,QAAQ;gBACP,MAAM,UAAU,UAAU;gBAC1B,UAAU;gBACV,YAAY;oBACV,iBAAiB;oBACjB,iBAAiB;oBACjB,WAAW,CAAC,OAAO,QACjB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC;oBAC7C,GAAG,MAAM,UAAU;gBACrB;gBACA,QAAQ;oBAAE,GAAG;gBAAc;gBAC1B,GAAG,KAAK;;;;;;;;;;;;AAIjB;AAUO,SAAS,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAqB;IACjE,MAAM,kBAA0C;QAC9C,QAAQ;QACR,UAAU;QACV,SAAS;QACT,OAAO;QACP,WAAW;QACX,UAAU;IACZ;IAEA,MAAM,SAAS;QAAE,GAAG,eAAe;QAAE,GAAG,QAAQ;IAAC;IACjD,MAAM,QAAQ,MAAM,CAAC,MAAM,WAAW,GAAG,IAAI;IAE7C,qBAAO,8OAAC,4KAAA,CAAA,MAAG;QAAC,OAAO;kBAAQ;;;;;;AAC7B;AAiBO,SAAS,cAAc,EAAE,OAAO,EAAE,UAAU,KAAK,EAAsB;IAC5E,IAAI,QAAQ,MAAM,IAAI,GAAG;QACvB,qBACE,8OAAC,gMAAA,CAAA,QAAK;YAAC,MAAK;sBACT,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,kMAAA,CAAA,SAAM;oBAEL,MAAK;oBACL,MAAM,OAAO,IAAI;oBACjB,SAAS,OAAO,OAAO;oBACvB,UAAU,OAAO,QAAQ;oBACzB,QAAQ,OAAO,MAAM;oBACrB,MAAM,UAAU,UAAU;8BAEzB,CAAC,WAAW,OAAO,KAAK;mBARpB,OAAO,GAAG;;;;;;;;;;IAazB;IAEA,MAAM,qBACJ,8OAAC,8KAAA,CAAA,OAAI;kBACF,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gBAER,MAAM,OAAO,IAAI;gBACjB,SAAS,OAAO,OAAO;gBACvB,UAAU,OAAO,QAAQ;gBACzB,QAAQ,OAAO,MAAM;0BAEpB,OAAO,KAAK;eANR,OAAO,GAAG;;;;;;;;;;IAYvB,qBACE,8OAAC,sLAAA,CAAA,WAAQ;QAAC,SAAS;QAAM,SAAS;YAAC;SAAQ;kBACzC,cAAA,8OAAC,kMAAA,CAAA,SAAM;YAAC,MAAK;YAAO,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;;;;;;;;;;;AAGhD;AAiBO,SAAS,YAAY,EAC1B,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,SAAS,EACT,KAAK,EACY;IACjB,qBACE,8OAAC;QAAI,WAAW;QAAW,OAAO;YAAE,SAAS;YAAQ,KAAK;YAAO,GAAG,KAAK;QAAC;kBACvE,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,kMAAA,CAAA,SAAM;gBAEL,MAAM,iBAAiB,OAAO,GAAG,GAAG,YAAY;gBAChD,MAAK;gBACL,SAAS,IAAM,SAAS,OAAO,GAAG,EAAE,OAAO,KAAK;0BAE/C,OAAO,KAAK;eALR,OAAO,GAAG;;;;;;;;;;AAUzB;AAeO,SAAS,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAqB;IACxE,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,eAAe,CAAC,OAAe,SAAS;QAC5C,OAAQ;YACN,KAAK;gBAAW,OAAO,YAAY,QAAQ,CAAC;YAC5C,KAAK;gBAAW,OAAO,YAAY,QAAQ,CAAC;YAC5C,KAAK;gBAAW,OAAO,YAAY,QAAQ,CAAC;YAC5C,KAAK;gBAAS,OAAO,YAAY,QAAQ,CAAC;YAC1C;gBAAS,OAAO,YAAY,YAAY,CAAC;QAC3C;IACF;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,KAAK;YACL,SAAS;YACT,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,WAAW,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAC/D,GAAG,KAAK;QACV;kBAEC,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,8OAAC;gBAAgB,OAAO;oBAAE,WAAW;gBAAS;;kCAC5C,8OAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,OAAO,YAAY,YAAY,CAAC;4BAChC,cAAc;wBAChB;kCACG,KAAK,KAAK;;;;;;kCAEb,8OAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,YAAY;4BACZ,OAAO,aAAa,KAAK,IAAI;wBAC/B;kCACG,KAAK,KAAK;;;;;;;eAbL;;;;;;;;;;AAmBlB"}}, {"offset": {"line": 7071, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7077, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/data-display/index.ts"], "sourcesContent": ["/**\n * Data Display Components Index\n * Export all data display components\n */\n\n// Data table components\nexport * from './data-table';\n\n// Re-export Ant Design data display components for convenience\nexport {\n  Table,\n  List,\n  Descriptions,\n  Tree,\n  Timeline,\n  Collapse,\n  Tabs,\n  Carousel,\n  Image,\n  Calendar,\n  Statistic,\n} from 'antd';\n\n/**\n * Data display components metadata\n */\nexport const DATA_DISPLAY_COMPONENTS_VERSION = '1.0.0';\nexport const DATA_DISPLAY_COMPONENTS_NAME = 'APISportsGame Data Display Components';\n\n/**\n * Setup function for data display components\n */\nexport function setupDataDisplayComponents() {\n  console.log(`${DATA_DISPLAY_COMPONENTS_NAME} v${DATA_DISPLAY_COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB;;;;;;;;AAqBjB,MAAM,kCAAkC;AACxC,MAAM,+BAA+B;AAKrC,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,6BAA6B,EAAE,EAAE,gCAAgC,YAAY,CAAC;AAC/F"}}, {"offset": {"line": 7093, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7108, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/feedback/loading.tsx"], "sourcesContent": ["/**\n * Loading Components\n * Various loading states and spinners with theme integration\n */\n\n'use client';\n\nimport React from 'react';\nimport { Spin, Skeleton, Empty, Result } from 'antd';\nimport { LoadingOutlined, InboxOutlined } from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Loading spinner props\n */\nexport interface LoadingSpinnerProps {\n  size?: 'small' | 'default' | 'large';\n  tip?: string;\n  spinning?: boolean;\n  children?: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Loading spinner component\n */\nexport function LoadingSpinner({\n  size = 'default',\n  tip,\n  spinning = true,\n  children,\n  className,\n  style,\n}: LoadingSpinnerProps) {\n  const themeStyles = useThemeStyles();\n\n  const indicator = <LoadingOutlined style={{ fontSize: 24, color: themeStyles.getColor('primary') }} spin />;\n\n  if (children) {\n    return (\n      <Spin \n        indicator={indicator}\n        size={size}\n        tip={tip}\n        spinning={spinning}\n        className={className}\n        style={style}\n      >\n        {children}\n      </Spin>\n    );\n  }\n\n  return (\n    <div \n      className={className}\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        padding: '40px',\n        ...style,\n      }}\n    >\n      <Spin indicator={indicator} size={size} />\n      {tip && (\n        <div style={{\n          marginTop: '12px',\n          color: themeStyles.getTextColor('secondary'),\n          fontSize: '14px',\n        }}>\n          {tip}\n        </div>\n      )}\n    </div>\n  );\n}\n\n/**\n * Page loading props\n */\nexport interface PageLoadingProps {\n  message?: string;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Full page loading component\n */\nexport function PageLoading({\n  message = 'Loading...',\n  className,\n  style,\n}: PageLoadingProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div \n      className={className}\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '60vh',\n        backgroundColor: themeStyles.getBackgroundColor('layout'),\n        ...style,\n      }}\n    >\n      <LoadingSpinner size=\"large\" tip={message} />\n    </div>\n  );\n}\n\n/**\n * Content loading props\n */\nexport interface ContentLoadingProps {\n  rows?: number;\n  avatar?: boolean;\n  title?: boolean;\n  paragraph?: boolean;\n  active?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Content loading skeleton component\n */\nexport function ContentLoading({\n  rows = 3,\n  avatar = false,\n  title = true,\n  paragraph = true,\n  active = true,\n  className,\n  style,\n}: ContentLoadingProps) {\n  return (\n    <div className={className} style={style}>\n      <Skeleton\n        avatar={avatar}\n        title={title}\n        paragraph={{ rows }}\n        active={active}\n      />\n    </div>\n  );\n}\n\n/**\n * List loading props\n */\nexport interface ListLoadingProps {\n  count?: number;\n  avatar?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * List loading skeleton component\n */\nexport function ListLoading({\n  count = 5,\n  avatar = true,\n  className,\n  style,\n}: ListLoadingProps) {\n  return (\n    <div className={className} style={style}>\n      {Array.from({ length: count }).map((_, index) => (\n        <div key={index} style={{ marginBottom: '16px' }}>\n          <Skeleton\n            avatar={avatar}\n            title={{ width: '60%' }}\n            paragraph={{ rows: 2, width: ['100%', '80%'] }}\n            active\n          />\n        </div>\n      ))}\n    </div>\n  );\n}\n\n/**\n * Card loading props\n */\nexport interface CardLoadingProps {\n  count?: number;\n  columns?: number;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Card loading skeleton component\n */\nexport function CardLoading({\n  count = 6,\n  columns = 3,\n  className,\n  style,\n}: CardLoadingProps) {\n  return (\n    <div \n      className={className}\n      style={{\n        display: 'grid',\n        gridTemplateColumns: `repeat(${columns}, 1fr)`,\n        gap: '16px',\n        ...style,\n      }}\n    >\n      {Array.from({ length: count }).map((_, index) => (\n        <div key={index} style={{ padding: '16px', border: '1px solid #f0f0f0', borderRadius: '8px' }}>\n          <Skeleton\n            title={{ width: '80%' }}\n            paragraph={{ rows: 3, width: ['100%', '90%', '70%'] }}\n            active\n          />\n        </div>\n      ))}\n    </div>\n  );\n}\n\n/**\n * Empty state props\n */\nexport interface EmptyStateProps {\n  title?: string;\n  description?: string;\n  image?: React.ReactNode;\n  actions?: React.ReactNode[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Empty state component\n */\nexport function EmptyState({\n  title = 'No data',\n  description,\n  image,\n  actions = [],\n  className,\n  style,\n}: EmptyStateProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div \n      className={className}\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        padding: '40px 20px',\n        backgroundColor: themeStyles.getBackgroundColor('container'),\n        borderRadius: '8px',\n        border: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        ...style,\n      }}\n    >\n      <Empty\n        image={image || <InboxOutlined style={{ fontSize: '64px', color: themeStyles.getTextColor('tertiary') }} />}\n        description={\n          <div>\n            <div style={{\n              fontSize: '16px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n              marginBottom: '4px',\n            }}>\n              {title}\n            </div>\n            {description && (\n              <div style={{\n                fontSize: '14px',\n                color: themeStyles.getTextColor('secondary'),\n              }}>\n                {description}\n              </div>\n            )}\n          </div>\n        }\n      >\n        {actions.length > 0 && (\n          <div style={{ marginTop: '16px', display: 'flex', gap: '8px', justifyContent: 'center' }}>\n            {actions}\n          </div>\n        )}\n      </Empty>\n    </div>\n  );\n}\n\n/**\n * Error state props\n */\nexport interface ErrorStateProps {\n  title?: string;\n  subtitle?: string;\n  error?: Error | string;\n  showError?: boolean;\n  actions?: React.ReactNode[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Error state component\n */\nexport function ErrorState({\n  title = 'Something went wrong',\n  subtitle = 'An error occurred while loading the content.',\n  error,\n  showError = false,\n  actions = [],\n  className,\n  style,\n}: ErrorStateProps) {\n  const themeStyles = useThemeStyles();\n\n  const errorMessage = error instanceof Error ? error.message : String(error);\n\n  return (\n    <div className={className} style={style}>\n      <Result\n        status=\"error\"\n        title={title}\n        subTitle={subtitle}\n        extra={actions}\n      >\n        {showError && error && (\n          <div style={{\n            marginTop: '16px',\n            padding: '12px',\n            backgroundColor: themeStyles.getBackgroundColor('elevated'),\n            border: `1px solid ${themeStyles.getColor('error')}`,\n            borderRadius: '6px',\n            textAlign: 'left',\n          }}>\n            <div style={{\n              fontSize: '12px',\n              fontWeight: 'bold',\n              color: themeStyles.getColor('error'),\n              marginBottom: '4px',\n            }}>\n              Error Details:\n            </div>\n            <div style={{\n              fontSize: '12px',\n              color: themeStyles.getTextColor('secondary'),\n              fontFamily: 'monospace',\n              whiteSpace: 'pre-wrap',\n            }}>\n              {errorMessage}\n            </div>\n          </div>\n        )}\n      </Result>\n    </div>\n  );\n}\n\n/**\n * Loading overlay props\n */\nexport interface LoadingOverlayProps {\n  visible: boolean;\n  message?: string;\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Loading overlay component\n */\nexport function LoadingOverlay({\n  visible,\n  message = 'Loading...',\n  children,\n  className,\n  style,\n}: LoadingOverlayProps) {\n  return (\n    <div className={className} style={{ position: 'relative', ...style }}>\n      {children}\n      {visible && (\n        <div style={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(255, 255, 255, 0.8)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n        }}>\n          <LoadingSpinner tip={message} />\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;AAOD;AAAA;AADA;AADA;AAAA;AAAA;AACA;AADA;AAHA;;;;;AAsBO,SAAS,eAAe,EAC7B,OAAO,SAAS,EAChB,GAAG,EACH,WAAW,IAAI,EACf,QAAQ,EACR,SAAS,EACT,KAAK,EACe;IACpB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,0BAAY,8OAAC,wNAAA,CAAA,kBAAe;QAAC,OAAO;YAAE,UAAU;YAAI,OAAO,YAAY,QAAQ,CAAC;QAAW;QAAG,IAAI;;;;;;IAExG,IAAI,UAAU;QACZ,qBACE,8OAAC,8KAAA,CAAA,OAAI;YACH,WAAW;YACX,MAAM;YACN,KAAK;YACL,UAAU;YACV,WAAW;YACX,OAAO;sBAEN;;;;;;IAGP;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,SAAS;YACT,GAAG,KAAK;QACV;;0BAEA,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAW;gBAAW,MAAM;;;;;;YACjC,qBACC,8OAAC;gBAAI,OAAO;oBACV,WAAW;oBACX,OAAO,YAAY,YAAY,CAAC;oBAChC,UAAU;gBACZ;0BACG;;;;;;;;;;;;AAKX;AAcO,SAAS,YAAY,EAC1B,UAAU,YAAY,EACtB,SAAS,EACT,KAAK,EACY;IACjB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,GAAG,KAAK;QACV;kBAEA,cAAA,8OAAC;YAAe,MAAK;YAAQ,KAAK;;;;;;;;;;;AAGxC;AAkBO,SAAS,eAAe,EAC7B,OAAO,CAAC,EACR,SAAS,KAAK,EACd,QAAQ,IAAI,EACZ,YAAY,IAAI,EAChB,SAAS,IAAI,EACb,SAAS,EACT,KAAK,EACe;IACpB,qBACE,8OAAC;QAAI,WAAW;QAAW,OAAO;kBAChC,cAAA,8OAAC,sLAAA,CAAA,WAAQ;YACP,QAAQ;YACR,OAAO;YACP,WAAW;gBAAE;YAAK;YAClB,QAAQ;;;;;;;;;;;AAIhB;AAeO,SAAS,YAAY,EAC1B,QAAQ,CAAC,EACT,SAAS,IAAI,EACb,SAAS,EACT,KAAK,EACY;IACjB,qBACE,8OAAC;QAAI,WAAW;QAAW,OAAO;kBAC/B,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,8OAAC;gBAAgB,OAAO;oBAAE,cAAc;gBAAO;0BAC7C,cAAA,8OAAC,sLAAA,CAAA,WAAQ;oBACP,QAAQ;oBACR,OAAO;wBAAE,OAAO;oBAAM;oBACtB,WAAW;wBAAE,MAAM;wBAAG,OAAO;4BAAC;4BAAQ;yBAAM;oBAAC;oBAC7C,MAAM;;;;;;eALA;;;;;;;;;;AAWlB;AAeO,SAAS,YAAY,EAC1B,QAAQ,CAAC,EACT,UAAU,CAAC,EACX,SAAS,EACT,KAAK,EACY;IACjB,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,qBAAqB,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC;YAC9C,KAAK;YACL,GAAG,KAAK;QACV;kBAEC,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,8OAAC;gBAAgB,OAAO;oBAAE,SAAS;oBAAQ,QAAQ;oBAAqB,cAAc;gBAAM;0BAC1F,cAAA,8OAAC,sLAAA,CAAA,WAAQ;oBACP,OAAO;wBAAE,OAAO;oBAAM;oBACtB,WAAW;wBAAE,MAAM;wBAAG,OAAO;4BAAC;4BAAQ;4BAAO;yBAAM;oBAAC;oBACpD,MAAM;;;;;;eAJA;;;;;;;;;;AAUlB;AAiBO,SAAS,WAAW,EACzB,QAAQ,SAAS,EACjB,WAAW,EACX,KAAK,EACL,UAAU,EAAE,EACZ,SAAS,EACT,KAAK,EACW;IAChB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,SAAS;YACT,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,cAAc;YACd,QAAQ,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAC5D,GAAG,KAAK;QACV;kBAEA,cAAA,8OAAC,gLAAA,CAAA,QAAK;YACJ,OAAO,uBAAS,8OAAC,oNAAA,CAAA,gBAAa;gBAAC,OAAO;oBAAE,UAAU;oBAAQ,OAAO,YAAY,YAAY,CAAC;gBAAY;;;;;;YACtG,2BACE,8OAAC;;kCACC,8OAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,YAAY;4BACZ,OAAO,YAAY,YAAY,CAAC;4BAChC,cAAc;wBAChB;kCACG;;;;;;oBAEF,6BACC,8OAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,OAAO,YAAY,YAAY,CAAC;wBAClC;kCACG;;;;;;;;;;;;sBAMR,QAAQ,MAAM,GAAG,mBAChB,8OAAC;gBAAI,OAAO;oBAAE,WAAW;oBAAQ,SAAS;oBAAQ,KAAK;oBAAO,gBAAgB;gBAAS;0BACpF;;;;;;;;;;;;;;;;AAMb;AAkBO,SAAS,WAAW,EACzB,QAAQ,sBAAsB,EAC9B,WAAW,8CAA8C,EACzD,KAAK,EACL,YAAY,KAAK,EACjB,UAAU,EAAE,EACZ,SAAS,EACT,KAAK,EACW;IAChB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;IAErE,qBACE,8OAAC;QAAI,WAAW;QAAW,OAAO;kBAChC,cAAA,8OAAC,kLAAA,CAAA,SAAM;YACL,QAAO;YACP,OAAO;YACP,UAAU;YACV,OAAO;sBAEN,aAAa,uBACZ,8OAAC;gBAAI,OAAO;oBACV,WAAW;oBACX,SAAS;oBACT,iBAAiB,YAAY,kBAAkB,CAAC;oBAChD,QAAQ,CAAC,UAAU,EAAE,YAAY,QAAQ,CAAC,UAAU;oBACpD,cAAc;oBACd,WAAW;gBACb;;kCACE,8OAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,YAAY;4BACZ,OAAO,YAAY,QAAQ,CAAC;4BAC5B,cAAc;wBAChB;kCAAG;;;;;;kCAGH,8OAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,OAAO,YAAY,YAAY,CAAC;4BAChC,YAAY;4BACZ,YAAY;wBACd;kCACG;;;;;;;;;;;;;;;;;;;;;;AAOf;AAgBO,SAAS,eAAe,EAC7B,OAAO,EACP,UAAU,YAAY,EACtB,QAAQ,EACR,SAAS,EACT,KAAK,EACe;IACpB,qBACE,8OAAC;QAAI,WAAW;QAAW,OAAO;YAAE,UAAU;YAAY,GAAG,KAAK;QAAC;;YAChE;YACA,yBACC,8OAAC;gBAAI,OAAO;oBACV,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;gBACV;0BACE,cAAA,8OAAC;oBAAe,KAAK;;;;;;;;;;;;;;;;;AAK/B"}}, {"offset": {"line": 7522, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7528, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/feedback/index.ts"], "sourcesContent": ["/**\n * Feedback Components Index\n * Export all feedback components\n */\n\n// Loading components\nexport * from './loading';\n\n// Re-export Ant Design feedback components for convenience\nexport {\n  Alert,\n  Message,\n  Notification,\n  Progress,\n  Result,\n  Skeleton,\n  Spin,\n  Empty,\n} from 'antd';\n\n/**\n * Feedback components metadata\n */\nexport const FEEDBACK_COMPONENTS_VERSION = '1.0.0';\nexport const FEEDBACK_COMPONENTS_NAME = 'APISportsGame Feedback Components';\n\n/**\n * Setup function for feedback components\n */\nexport function setupFeedbackComponents() {\n  console.log(`${FEEDBACK_COMPONENTS_NAME} v${FEEDBACK_COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,qBAAqB;;;;;;;;AAkBd,MAAM,8BAA8B;AACpC,MAAM,2BAA2B;AAKjC,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,yBAAyB,EAAE,EAAE,4BAA4B,YAAY,CAAC;AACvF"}}, {"offset": {"line": 7544, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7559, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/auth/auth-guard.tsx"], "sourcesContent": ["/**\n * Auth Guard Component\n * Protects routes and handles authentication redirects\n */\n\n'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { Spin, Alert, Button } from 'antd';\nimport { LoadingOutlined, LockOutlined } from '@ant-design/icons';\nimport { useAuth } from '@/hooks/api';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Auth guard props\n */\nexport interface AuthGuardProps {\n  children: React.ReactNode;\n  requireAuth?: boolean;\n  requiredRoles?: string[];\n  fallback?: React.ReactNode;\n  redirectTo?: string;\n}\n\n/**\n * Auth Guard component\n */\nexport function AuthGuard({\n  children,\n  requireAuth = true,\n  requiredRoles = [],\n  fallback,\n  redirectTo,\n}: AuthGuardProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const auth = useAuth();\n  const themeStyles = useThemeStyles();\n  \n  const [isChecking, setIsChecking] = useState(true);\n\n  useEffect(() => {\n    // Skip auth check in development mode if authentication is disabled\n    if (process.env.NODE_ENV === 'development' && !requireAuth) {\n      setIsChecking(false);\n      return;\n    }\n\n    // Check authentication status\n    const checkAuth = async () => {\n      try {\n        // Wait for auth state to be determined\n        if (auth.isLoading) {\n          return;\n        }\n\n        setIsChecking(false);\n\n        // If authentication is required but user is not authenticated\n        if (requireAuth && !auth.isAuthenticated) {\n          const loginUrl = `/login?redirect=${encodeURIComponent(pathname)}`;\n          router.push(redirectTo || loginUrl);\n          return;\n        }\n\n        // If user is authenticated but doesn't have required roles\n        if (auth.isAuthenticated && requiredRoles.length > 0) {\n          const userRole = auth.user?.role;\n          if (!userRole || !requiredRoles.includes(userRole)) {\n            router.push('/unauthorized');\n            return;\n          }\n        }\n      } catch (error) {\n        console.error('Auth guard error:', error);\n        setIsChecking(false);\n      }\n    };\n\n    checkAuth();\n  }, [auth.isAuthenticated, auth.isLoading, auth.user, requireAuth, requiredRoles, router, pathname, redirectTo]);\n\n  // Show loading state while checking authentication\n  if (isChecking || auth.isLoading) {\n    return fallback || <AuthLoadingFallback />;\n  }\n\n  // In development mode, allow access without authentication\n  if (process.env.NODE_ENV === 'development' && !requireAuth) {\n    return <>{children}</>;\n  }\n\n  // If authentication is required but user is not authenticated\n  if (requireAuth && !auth.isAuthenticated) {\n    return <AuthRequiredFallback />;\n  }\n\n  // If user is authenticated but doesn't have required roles\n  if (auth.isAuthenticated && requiredRoles.length > 0) {\n    const userRole = auth.user?.role;\n    if (!userRole || !requiredRoles.includes(userRole)) {\n      return <InsufficientPermissionsFallback requiredRoles={requiredRoles} userRole={userRole} />;\n    }\n  }\n\n  // All checks passed, render children\n  return <>{children}</>;\n}\n\n/**\n * Loading fallback component\n */\nfunction AuthLoadingFallback() {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '100vh',\n        backgroundColor: themeStyles.getBackgroundColor('layout'),\n        gap: '16px',\n      }}\n    >\n      <Spin\n        indicator={\n          <LoadingOutlined\n            style={{\n              fontSize: '48px',\n              color: themeStyles.getColor('primary'),\n            }}\n          />\n        }\n      />\n      <div\n        style={{\n          color: themeStyles.getTextColor('secondary'),\n          fontSize: '16px',\n        }}\n      >\n        Checking authentication...\n      </div>\n    </div>\n  );\n}\n\n/**\n * Authentication required fallback component\n */\nfunction AuthRequiredFallback() {\n  const router = useRouter();\n  const pathname = usePathname();\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '100vh',\n        backgroundColor: themeStyles.getBackgroundColor('layout'),\n        padding: '24px',\n      }}\n    >\n      <div\n        style={{\n          maxWidth: '400px',\n          textAlign: 'center',\n        }}\n      >\n        <LockOutlined\n          style={{\n            fontSize: '64px',\n            color: themeStyles.getColor('warning'),\n            marginBottom: '24px',\n          }}\n        />\n        \n        <Alert\n          message=\"Authentication Required\"\n          description=\"You need to sign in to access this page. Please log in with your administrator credentials.\"\n          type=\"warning\"\n          showIcon\n          style={{ marginBottom: '24px' }}\n        />\n\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          onClick={() => {\n            const loginUrl = `/login?redirect=${encodeURIComponent(pathname)}`;\n            router.push(loginUrl);\n          }}\n          style={{\n            borderRadius: '8px',\n            height: '48px',\n            fontSize: '16px',\n            fontWeight: 'bold',\n          }}\n        >\n          Go to Login\n        </Button>\n      </div>\n    </div>\n  );\n}\n\n/**\n * Insufficient permissions fallback component\n */\ninterface InsufficientPermissionsFallbackProps {\n  requiredRoles: string[];\n  userRole?: string;\n}\n\nfunction InsufficientPermissionsFallback({\n  requiredRoles,\n  userRole,\n}: InsufficientPermissionsFallbackProps) {\n  const router = useRouter();\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '100vh',\n        backgroundColor: themeStyles.getBackgroundColor('layout'),\n        padding: '24px',\n      }}\n    >\n      <div\n        style={{\n          maxWidth: '400px',\n          textAlign: 'center',\n        }}\n      >\n        <LockOutlined\n          style={{\n            fontSize: '64px',\n            color: themeStyles.getColor('error'),\n            marginBottom: '24px',\n          }}\n        />\n        \n        <Alert\n          message=\"Insufficient Permissions\"\n          description={\n            <div>\n              <p>You don't have permission to access this page.</p>\n              <p><strong>Required roles:</strong> {requiredRoles.join(', ')}</p>\n              {userRole && <p><strong>Your role:</strong> {userRole}</p>}\n            </div>\n          }\n          type=\"error\"\n          showIcon\n          style={{ marginBottom: '24px' }}\n        />\n\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          onClick={() => router.push('/')}\n          style={{\n            borderRadius: '8px',\n            height: '48px',\n            fontSize: '16px',\n            fontWeight: 'bold',\n          }}\n        >\n          Go to Dashboard\n        </Button>\n      </div>\n    </div>\n  );\n}\n\n/**\n * HOC to wrap components with auth guard\n */\nexport function withAuthGuard<P extends object>(\n  Component: React.ComponentType<P>,\n  options: Omit<AuthGuardProps, 'children'> = {}\n) {\n  const WrappedComponent = (props: P) => {\n    return (\n      <AuthGuard {...options}>\n        <Component {...props} />\n      </AuthGuard>\n    );\n  };\n\n  WrappedComponent.displayName = `withAuthGuard(${Component.displayName || Component.name})`;\n  \n  return WrappedComponent;\n}\n\n/**\n * Hook to check if user has required permissions\n */\nexport function usePermissions(requiredRoles: string[] = []) {\n  const auth = useAuth();\n\n  const hasPermission = () => {\n    if (!auth.isAuthenticated || !auth.user) {\n      return false;\n    }\n\n    if (requiredRoles.length === 0) {\n      return true;\n    }\n\n    const userRole = auth.user.role;\n    return userRole && requiredRoles.includes(userRole);\n  };\n\n  return {\n    hasPermission: hasPermission(),\n    userRole: auth.user?.role,\n    isAuthenticated: auth.isAuthenticated,\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAID;AACA;AAGA;AACA;AADA;AACA;AAHA;AACA;AAAA;AADA;AAAA;AAJA;;;;;;;;AAuBO,SAAS,UAAU,EACxB,QAAQ,EACR,cAAc,IAAI,EAClB,gBAAgB,EAAE,EAClB,QAAQ,EACR,UAAU,EACK;IACf,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,OAAO,CAAA,GAAA,oIAAA,CAAA,UAAO,AAAD;IACnB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oEAAoE;QACpE,IAAI,oDAAyB,iBAAiB,CAAC,aAAa;YAC1D,cAAc;YACd;QACF;QAEA,8BAA8B;QAC9B,MAAM,YAAY;YAChB,IAAI;gBACF,uCAAuC;gBACvC,IAAI,KAAK,SAAS,EAAE;oBAClB;gBACF;gBAEA,cAAc;gBAEd,8DAA8D;gBAC9D,IAAI,eAAe,CAAC,KAAK,eAAe,EAAE;oBACxC,MAAM,WAAW,CAAC,gBAAgB,EAAE,mBAAmB,WAAW;oBAClE,OAAO,IAAI,CAAC,cAAc;oBAC1B;gBACF;gBAEA,2DAA2D;gBAC3D,IAAI,KAAK,eAAe,IAAI,cAAc,MAAM,GAAG,GAAG;oBACpD,MAAM,WAAW,KAAK,IAAI,EAAE;oBAC5B,IAAI,CAAC,YAAY,CAAC,cAAc,QAAQ,CAAC,WAAW;wBAClD,OAAO,IAAI,CAAC;wBACZ;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,cAAc;YAChB;QACF;QAEA;IACF,GAAG;QAAC,KAAK,eAAe;QAAE,KAAK,SAAS;QAAE,KAAK,IAAI;QAAE;QAAa;QAAe;QAAQ;QAAU;KAAW;IAE9G,mDAAmD;IACnD,IAAI,cAAc,KAAK,SAAS,EAAE;QAChC,OAAO,0BAAY,8OAAC;;;;;IACtB;IAEA,2DAA2D;IAC3D,IAAI,oDAAyB,iBAAiB,CAAC,aAAa;QAC1D,qBAAO;sBAAG;;IACZ;IAEA,8DAA8D;IAC9D,IAAI,eAAe,CAAC,KAAK,eAAe,EAAE;QACxC,qBAAO,8OAAC;;;;;IACV;IAEA,2DAA2D;IAC3D,IAAI,KAAK,eAAe,IAAI,cAAc,MAAM,GAAG,GAAG;QACpD,MAAM,WAAW,KAAK,IAAI,EAAE;QAC5B,IAAI,CAAC,YAAY,CAAC,cAAc,QAAQ,CAAC,WAAW;YAClD,qBAAO,8OAAC;gBAAgC,eAAe;gBAAe,UAAU;;;;;;QAClF;IACF;IAEA,qCAAqC;IACrC,qBAAO;kBAAG;;AACZ;AAEA;;CAEC,GACD,SAAS;IACP,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,8OAAC;QACC,OAAO;YACL,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,KAAK;QACP;;0BAEA,8OAAC,8KAAA,CAAA,OAAI;gBACH,yBACE,8OAAC,wNAAA,CAAA,kBAAe;oBACd,OAAO;wBACL,UAAU;wBACV,OAAO,YAAY,QAAQ,CAAC;oBAC9B;;;;;;;;;;;0BAIN,8OAAC;gBACC,OAAO;oBACL,OAAO,YAAY,YAAY,CAAC;oBAChC,UAAU;gBACZ;0BACD;;;;;;;;;;;;AAKP;AAEA;;CAEC,GACD,SAAS;IACP,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,8OAAC;QACC,OAAO;YACL,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,SAAS;QACX;kBAEA,cAAA,8OAAC;YACC,OAAO;gBACL,UAAU;gBACV,WAAW;YACb;;8BAEA,8OAAC,kNAAA,CAAA,eAAY;oBACX,OAAO;wBACL,UAAU;wBACV,OAAO,YAAY,QAAQ,CAAC;wBAC5B,cAAc;oBAChB;;;;;;8BAGF,8OAAC,gLAAA,CAAA,QAAK;oBACJ,SAAQ;oBACR,aAAY;oBACZ,MAAK;oBACL,QAAQ;oBACR,OAAO;wBAAE,cAAc;oBAAO;;;;;;8BAGhC,8OAAC,kMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,MAAK;oBACL,SAAS;wBACP,MAAM,WAAW,CAAC,gBAAgB,EAAE,mBAAmB,WAAW;wBAClE,OAAO,IAAI,CAAC;oBACd;oBACA,OAAO;wBACL,cAAc;wBACd,QAAQ;wBACR,UAAU;wBACV,YAAY;oBACd;8BACD;;;;;;;;;;;;;;;;;AAMT;AAUA,SAAS,gCAAgC,EACvC,aAAa,EACb,QAAQ,EAC6B;IACrC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,8OAAC;QACC,OAAO;YACL,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,SAAS;QACX;kBAEA,cAAA,8OAAC;YACC,OAAO;gBACL,UAAU;gBACV,WAAW;YACb;;8BAEA,8OAAC,kNAAA,CAAA,eAAY;oBACX,OAAO;wBACL,UAAU;wBACV,OAAO,YAAY,QAAQ,CAAC;wBAC5B,cAAc;oBAChB;;;;;;8BAGF,8OAAC,gLAAA,CAAA,QAAK;oBACJ,SAAQ;oBACR,2BACE,8OAAC;;0CACC,8OAAC;0CAAE;;;;;;0CACH,8OAAC;;kDAAE,8OAAC;kDAAO;;;;;;oCAAwB;oCAAE,cAAc,IAAI,CAAC;;;;;;;4BACvD,0BAAY,8OAAC;;kDAAE,8OAAC;kDAAO;;;;;;oCAAmB;oCAAE;;;;;;;;;;;;;oBAGjD,MAAK;oBACL,QAAQ;oBACR,OAAO;wBAAE,cAAc;oBAAO;;;;;;8BAGhC,8OAAC,kMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,MAAK;oBACL,SAAS,IAAM,OAAO,IAAI,CAAC;oBAC3B,OAAO;wBACL,cAAc;wBACd,QAAQ;wBACR,UAAU;wBACV,YAAY;oBACd;8BACD;;;;;;;;;;;;;;;;;AAMT;AAKO,SAAS,cACd,SAAiC,EACjC,UAA4C,CAAC,CAAC;IAE9C,MAAM,mBAAmB,CAAC;QACxB,qBACE,8OAAC;YAAW,GAAG,OAAO;sBACpB,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAG1B;IAEA,iBAAiB,WAAW,GAAG,CAAC,cAAc,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAE1F,OAAO;AACT;AAKO,SAAS,eAAe,gBAA0B,EAAE;IACzD,MAAM,OAAO,CAAA,GAAA,oIAAA,CAAA,UAAO,AAAD;IAEnB,MAAM,gBAAgB;QACpB,IAAI,CAAC,KAAK,eAAe,IAAI,CAAC,KAAK,IAAI,EAAE;YACvC,OAAO;QACT;QAEA,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,OAAO;QACT;QAEA,MAAM,WAAW,KAAK,IAAI,CAAC,IAAI;QAC/B,OAAO,YAAY,cAAc,QAAQ,CAAC;IAC5C;IAEA,OAAO;QACL,eAAe;QACf,UAAU,KAAK,IAAI,EAAE;QACrB,iBAAiB,KAAK,eAAe;IACvC;AACF"}}, {"offset": {"line": 7961, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7967, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/auth/index.ts"], "sourcesContent": ["/**\n * Auth Components Index\n * Export all authentication-related components\n */\n\n// Auth guard components\nexport * from './auth-guard';\n\n// Auth component metadata\nexport const authComponentsMetadata = {\n  version: '1.0.0',\n  components: [\n    'AuthGuard',\n    'withAuthGuard',\n    'usePermissions',\n  ],\n  description: 'Authentication and authorization components for route protection',\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB;;;;;AAIjB,MAAM,yBAAyB;IACpC,SAAS;IACT,YAAY;QACV;QACA;QACA;KACD;IACD,aAAa;AACf"}}, {"offset": {"line": 7984, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7999, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/users/user-form.tsx"], "sourcesContent": ["/**\n * User Form Components\n * Forms for creating and editing SystemUser accounts\n */\n\n'use client';\n\nimport React, { useEffect } from 'react';\nimport { Form, Input, Select, Switch, Button, Space, Alert, Typography, Divider } from 'antd';\nimport { UserOutlined, MailOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';\nimport { \n  SystemUser, \n  CreateUserRequest, \n  UpdateUserRequest,\n  SystemUserRole,\n  UserStatus,\n  ROLE_LABELS,\n  STATUS_LABELS,\n  USER_VALIDATION,\n} from '@/types/user';\nimport { useThemeStyles } from '@/theme';\n\nconst { Text } = Typography;\n\n/**\n * User form props\n */\nexport interface UserFormProps {\n  user?: SystemUser;\n  onSubmit: (data: CreateUserRequest | UpdateUserRequest) => void;\n  loading?: boolean;\n  mode: 'create' | 'edit';\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * User Form component\n */\nexport function UserForm({\n  user,\n  onSubmit,\n  loading = false,\n  mode,\n  className,\n  style,\n}: UserFormProps) {\n  const [form] = Form.useForm();\n  const themeStyles = useThemeStyles();\n\n  // Initialize form with user data for edit mode\n  useEffect(() => {\n    if (mode === 'edit' && user) {\n      form.setFieldsValue({\n        username: user.username,\n        email: user.email,\n        firstName: user.firstName,\n        lastName: user.lastName,\n        role: user.role,\n        status: user.status,\n        isActive: user.status === 'active',\n      });\n    }\n  }, [form, mode, user]);\n\n  // Handle form submission\n  const handleSubmit = (values: any) => {\n    const formData = {\n      ...values,\n      status: values.isActive ? 'active' : 'inactive',\n    };\n    delete formData.isActive;\n    delete formData.confirmPassword;\n    \n    onSubmit(formData);\n  };\n\n  // Handle form validation failure\n  const handleSubmitFailed = (errorInfo: any) => {\n    console.log('Form validation failed:', errorInfo);\n  };\n\n  return (\n    <div className={className} style={style}>\n      <Form\n        form={form}\n        layout=\"vertical\"\n        onFinish={handleSubmit}\n        onFinishFailed={handleSubmitFailed}\n        autoComplete=\"off\"\n        requiredMark={false}\n      >\n        {/* Basic Information */}\n        <div style={{ marginBottom: '24px' }}>\n          <Text strong style={{ fontSize: '16px', color: themeStyles.getTextColor('primary') }}>\n            Basic Information\n          </Text>\n          <Divider style={{ margin: '12px 0 24px 0' }} />\n\n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>\n            {/* Username */}\n            <Form.Item\n              name=\"username\"\n              label=\"Username\"\n              rules={[\n                { required: true, message: 'Please enter username' },\n                { min: USER_VALIDATION.username.min, message: USER_VALIDATION.username.message },\n                { max: USER_VALIDATION.username.max, message: USER_VALIDATION.username.message },\n                { pattern: USER_VALIDATION.username.pattern, message: USER_VALIDATION.username.message },\n              ]}\n            >\n              <Input\n                prefix={<UserOutlined />}\n                placeholder=\"Enter username\"\n                disabled={mode === 'edit'} // Username cannot be changed\n              />\n            </Form.Item>\n\n            {/* Email */}\n            <Form.Item\n              name=\"email\"\n              label=\"Email Address\"\n              rules={[\n                { required: true, message: 'Please enter email address' },\n                { type: 'email', message: USER_VALIDATION.email.message },\n              ]}\n            >\n              <Input\n                prefix={<MailOutlined />}\n                placeholder=\"Enter email address\"\n              />\n            </Form.Item>\n\n            {/* First Name */}\n            <Form.Item\n              name=\"firstName\"\n              label=\"First Name\"\n              rules={[\n                { max: USER_VALIDATION.firstName.max, message: USER_VALIDATION.firstName.message },\n              ]}\n            >\n              <Input placeholder=\"Enter first name\" />\n            </Form.Item>\n\n            {/* Last Name */}\n            <Form.Item\n              name=\"lastName\"\n              label=\"Last Name\"\n              rules={[\n                { max: USER_VALIDATION.lastName.max, message: USER_VALIDATION.lastName.message },\n              ]}\n            >\n              <Input placeholder=\"Enter last name\" />\n            </Form.Item>\n          </div>\n        </div>\n\n        {/* Password Section (Create mode only) */}\n        {mode === 'create' && (\n          <div style={{ marginBottom: '24px' }}>\n            <Text strong style={{ fontSize: '16px', color: themeStyles.getTextColor('primary') }}>\n              Password\n            </Text>\n            <Divider style={{ margin: '12px 0 24px 0' }} />\n\n            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>\n              {/* Password */}\n              <Form.Item\n                name=\"password\"\n                label=\"Password\"\n                rules={[\n                  { required: true, message: 'Please enter password' },\n                  { min: USER_VALIDATION.password.min, message: USER_VALIDATION.password.message },\n                  { pattern: USER_VALIDATION.password.pattern, message: USER_VALIDATION.password.message },\n                ]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder=\"Enter password\"\n                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                />\n              </Form.Item>\n\n              {/* Confirm Password */}\n              <Form.Item\n                name=\"confirmPassword\"\n                label=\"Confirm Password\"\n                dependencies={['password']}\n                rules={[\n                  { required: true, message: 'Please confirm password' },\n                  ({ getFieldValue }) => ({\n                    validator(_, value) {\n                      if (!value || getFieldValue('password') === value) {\n                        return Promise.resolve();\n                      }\n                      return Promise.reject(new Error('Passwords do not match'));\n                    },\n                  }),\n                ]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder=\"Confirm password\"\n                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                />\n              </Form.Item>\n            </div>\n\n            <Alert\n              message=\"Password Requirements\"\n              description=\"Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character.\"\n              type=\"info\"\n              showIcon\n              style={{ marginTop: '16px' }}\n            />\n          </div>\n        )}\n\n        {/* Role and Status */}\n        <div style={{ marginBottom: '24px' }}>\n          <Text strong style={{ fontSize: '16px', color: themeStyles.getTextColor('primary') }}>\n            Role & Status\n          </Text>\n          <Divider style={{ margin: '12px 0 24px 0' }} />\n\n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>\n            {/* Role */}\n            <Form.Item\n              name=\"role\"\n              label=\"Role\"\n              rules={[\n                { required: true, message: 'Please select a role' },\n              ]}\n            >\n              <Select placeholder=\"Select role\">\n                <Select.Option value=\"admin\">{ROLE_LABELS.admin}</Select.Option>\n                <Select.Option value=\"editor\">{ROLE_LABELS.editor}</Select.Option>\n                <Select.Option value=\"moderator\">{ROLE_LABELS.moderator}</Select.Option>\n              </Select>\n            </Form.Item>\n\n            {/* Status */}\n            <Form.Item\n              name=\"isActive\"\n              label=\"Status\"\n              valuePropName=\"checked\"\n              initialValue={true}\n            >\n              <Switch\n                checkedChildren=\"Active\"\n                unCheckedChildren=\"Inactive\"\n              />\n            </Form.Item>\n          </div>\n\n          {/* Role Descriptions */}\n          <div style={{ marginTop: '16px' }}>\n            <Alert\n              message=\"Role Permissions\"\n              description={\n                <div>\n                  <div><strong>Administrator:</strong> Full access to all features including user management, system settings, and logs.</div>\n                  <div><strong>Editor:</strong> Can manage football data, broadcast links, and view users.</div>\n                  <div><strong>Moderator:</strong> Read-only access to most features with limited broadcast link management.</div>\n                </div>\n              }\n              type=\"info\"\n              showIcon\n            />\n          </div>\n        </div>\n\n        {/* Form Actions */}\n        <Form.Item style={{ marginBottom: 0 }}>\n          <Space>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              size=\"large\"\n            >\n              {mode === 'create' ? 'Create User' : 'Update User'}\n            </Button>\n            \n            <Button\n              size=\"large\"\n              onClick={() => form.resetFields()}\n            >\n              Reset\n            </Button>\n          </Space>\n        </Form.Item>\n      </Form>\n    </div>\n  );\n}\n\n/**\n * Quick user form for modal/drawer usage\n */\nexport interface QuickUserFormProps {\n  onSubmit: (data: CreateUserRequest) => void;\n  loading?: boolean;\n  onCancel?: () => void;\n}\n\nexport function QuickUserForm({ onSubmit, loading = false, onCancel }: QuickUserFormProps) {\n  const [form] = Form.useForm();\n\n  const handleSubmit = (values: any) => {\n    const formData = {\n      ...values,\n      status: 'active' as UserStatus,\n    };\n    delete formData.confirmPassword;\n    onSubmit(formData);\n  };\n\n  return (\n    <Form\n      form={form}\n      layout=\"vertical\"\n      onFinish={handleSubmit}\n      autoComplete=\"off\"\n      requiredMark={false}\n    >\n      {/* Username */}\n      <Form.Item\n        name=\"username\"\n        label=\"Username\"\n        rules={[\n          { required: true, message: 'Please enter username' },\n          { min: USER_VALIDATION.username.min, message: USER_VALIDATION.username.message },\n          { max: USER_VALIDATION.username.max, message: USER_VALIDATION.username.message },\n          { pattern: USER_VALIDATION.username.pattern, message: USER_VALIDATION.username.message },\n        ]}\n      >\n        <Input prefix={<UserOutlined />} placeholder=\"Enter username\" />\n      </Form.Item>\n\n      {/* Email */}\n      <Form.Item\n        name=\"email\"\n        label=\"Email Address\"\n        rules={[\n          { required: true, message: 'Please enter email address' },\n          { type: 'email', message: USER_VALIDATION.email.message },\n        ]}\n      >\n        <Input prefix={<MailOutlined />} placeholder=\"Enter email address\" />\n      </Form.Item>\n\n      {/* Password */}\n      <Form.Item\n        name=\"password\"\n        label=\"Password\"\n        rules={[\n          { required: true, message: 'Please enter password' },\n          { min: USER_VALIDATION.password.min, message: USER_VALIDATION.password.message },\n        ]}\n      >\n        <Input.Password prefix={<LockOutlined />} placeholder=\"Enter password\" />\n      </Form.Item>\n\n      {/* Role */}\n      <Form.Item\n        name=\"role\"\n        label=\"Role\"\n        rules={[{ required: true, message: 'Please select a role' }]}\n      >\n        <Select placeholder=\"Select role\">\n          <Select.Option value=\"admin\">{ROLE_LABELS.admin}</Select.Option>\n          <Select.Option value=\"editor\">{ROLE_LABELS.editor}</Select.Option>\n          <Select.Option value=\"moderator\">{ROLE_LABELS.moderator}</Select.Option>\n        </Select>\n      </Form.Item>\n\n      {/* Actions */}\n      <Form.Item style={{ marginBottom: 0, marginTop: '24px' }}>\n        <Space style={{ width: '100%', justifyContent: 'flex-end' }}>\n          {onCancel && (\n            <Button onClick={onCancel}>\n              Cancel\n            </Button>\n          )}\n          <Button type=\"primary\" htmlType=\"submit\" loading={loading}>\n            Create User\n          </Button>\n        </Space>\n      </Form.Item>\n    </Form>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAID;AAGA;AAUA;AAZA;AAAA;AAYA;AAZA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AADA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;;;;AAiBA,MAAM,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAiBpB,SAAS,SAAS,EACvB,IAAI,EACJ,QAAQ,EACR,UAAU,KAAK,EACf,IAAI,EACJ,SAAS,EACT,KAAK,EACS;IACd,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAC3B,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,UAAU,MAAM;YAC3B,KAAK,cAAc,CAAC;gBAClB,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,KAAK;gBACjB,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;gBACvB,MAAM,KAAK,IAAI;gBACf,QAAQ,KAAK,MAAM;gBACnB,UAAU,KAAK,MAAM,KAAK;YAC5B;QACF;IACF,GAAG;QAAC;QAAM;QAAM;KAAK;IAErB,yBAAyB;IACzB,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW;YACf,GAAG,MAAM;YACT,QAAQ,OAAO,QAAQ,GAAG,WAAW;QACvC;QACA,OAAO,SAAS,QAAQ;QACxB,OAAO,SAAS,eAAe;QAE/B,SAAS;IACX;IAEA,iCAAiC;IACjC,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,2BAA2B;IACzC;IAEA,qBACE,8OAAC;QAAI,WAAW;QAAW,OAAO;kBAChC,cAAA,8OAAC,8KAAA,CAAA,OAAI;YACH,MAAM;YACN,QAAO;YACP,UAAU;YACV,gBAAgB;YAChB,cAAa;YACb,cAAc;;8BAGd,8OAAC;oBAAI,OAAO;wBAAE,cAAc;oBAAO;;sCACjC,8OAAC;4BAAK,MAAM;4BAAC,OAAO;gCAAE,UAAU;gCAAQ,OAAO,YAAY,YAAY,CAAC;4BAAW;sCAAG;;;;;;sCAGtF,8OAAC,oLAAA,CAAA,UAAO;4BAAC,OAAO;gCAAE,QAAQ;4BAAgB;;;;;;sCAE1C,8OAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,qBAAqB;gCAAW,KAAK;4BAAO;;8CAEzE,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAwB;wCACnD;4CAAE,KAAK,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,GAAG;4CAAE,SAAS,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;wCAAC;wCAC/E;4CAAE,KAAK,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,GAAG;4CAAE,SAAS,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;wCAAC;wCAC/E;4CAAE,SAAS,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;4CAAE,SAAS,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;wCAAC;qCACxF;8CAED,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCACJ,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;wCACZ,UAAU,SAAS;;;;;;;;;;;8CAKvB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAA6B;wCACxD;4CAAE,MAAM;4CAAS,SAAS,oHAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,OAAO;wCAAC;qCACzD;8CAED,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCACJ,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;;;;;;;;;;;8CAKhB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,KAAK,oHAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,GAAG;4CAAE,SAAS,oHAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,OAAO;wCAAC;qCAClF;8CAED,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCAAC,aAAY;;;;;;;;;;;8CAIrB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,KAAK,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,GAAG;4CAAE,SAAS,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;wCAAC;qCAChF;8CAED,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCAAC,aAAY;;;;;;;;;;;;;;;;;;;;;;;gBAMxB,SAAS,0BACR,8OAAC;oBAAI,OAAO;wBAAE,cAAc;oBAAO;;sCACjC,8OAAC;4BAAK,MAAM;4BAAC,OAAO;gCAAE,UAAU;gCAAQ,OAAO,YAAY,YAAY,CAAC;4BAAW;sCAAG;;;;;;sCAGtF,8OAAC,oLAAA,CAAA,UAAO;4BAAC,OAAO;gCAAE,QAAQ;4BAAgB;;;;;;sCAE1C,8OAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,qBAAqB;gCAAW,KAAK;4BAAO;;8CAEzE,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAwB;wCACnD;4CAAE,KAAK,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,GAAG;4CAAE,SAAS,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;wCAAC;wCAC/E;4CAAE,SAAS,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;4CAAE,SAAS,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;wCAAC;qCACxF;8CAED,cAAA,8OAAC,gLAAA,CAAA,QAAK,CAAC,QAAQ;wCACb,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;wCACZ,YAAY,CAAC,UAAa,wBAAU,8OAAC,8MAAA,CAAA,aAAU;;;;uEAAM,8OAAC,kOAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;8CAK9E,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,cAAc;wCAAC;qCAAW;oCAC1B,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAA0B;wCACrD,CAAC,EAAE,aAAa,EAAE,GAAK,CAAC;gDACtB,WAAU,CAAC,EAAE,KAAK;oDAChB,IAAI,CAAC,SAAS,cAAc,gBAAgB,OAAO;wDACjD,OAAO,QAAQ,OAAO;oDACxB;oDACA,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;gDAClC;4CACF,CAAC;qCACF;8CAED,cAAA,8OAAC,gLAAA,CAAA,QAAK,CAAC,QAAQ;wCACb,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;wCACZ,YAAY,CAAC,UAAa,wBAAU,8OAAC,8MAAA,CAAA,aAAU;;;;uEAAM,8OAAC,kOAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;;;;;;;sCAKhF,8OAAC,gLAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,aAAY;4BACZ,MAAK;4BACL,QAAQ;4BACR,OAAO;gCAAE,WAAW;4BAAO;;;;;;;;;;;;8BAMjC,8OAAC;oBAAI,OAAO;wBAAE,cAAc;oBAAO;;sCACjC,8OAAC;4BAAK,MAAM;4BAAC,OAAO;gCAAE,UAAU;gCAAQ,OAAO,YAAY,YAAY,CAAC;4BAAW;sCAAG;;;;;;sCAGtF,8OAAC,oLAAA,CAAA,UAAO;4BAAC,OAAO;gCAAE,QAAQ;4BAAgB;;;;;;sCAE1C,8OAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,qBAAqB;gCAAW,KAAK;4BAAO;;8CAEzE,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAuB;qCACnD;8CAED,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCAAC,aAAY;;0DAClB,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAS,oHAAA,CAAA,cAAW,CAAC,KAAK;;;;;;0DAC/C,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAU,oHAAA,CAAA,cAAW,CAAC,MAAM;;;;;;0DACjD,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAa,oHAAA,CAAA,cAAW,CAAC,SAAS;;;;;;;;;;;;;;;;;8CAK3D,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,eAAc;oCACd,cAAc;8CAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCACL,iBAAgB;wCAChB,mBAAkB;;;;;;;;;;;;;;;;;sCAMxB,8OAAC;4BAAI,OAAO;gCAAE,WAAW;4BAAO;sCAC9B,cAAA,8OAAC,gLAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,2BACE,8OAAC;;sDACC,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAuB;;;;;;;sDACpC,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAgB;;;;;;;sDAC7B,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAmB;;;;;;;;;;;;;gCAGpC,MAAK;gCACL,QAAQ;;;;;;;;;;;;;;;;;8BAMd,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oBAAC,OAAO;wBAAE,cAAc;oBAAE;8BAClC,cAAA,8OAAC,gMAAA,CAAA,QAAK;;0CACJ,8OAAC,kMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,UAAS;gCACT,SAAS;gCACT,MAAK;0CAEJ,SAAS,WAAW,gBAAgB;;;;;;0CAGvC,8OAAC,kMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS,IAAM,KAAK,WAAW;0CAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;AAWO,SAAS,cAAc,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAsB;IACvF,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW;YACf,GAAG,MAAM;YACT,QAAQ;QACV;QACA,OAAO,SAAS,eAAe;QAC/B,SAAS;IACX;IAEA,qBACE,8OAAC,8KAAA,CAAA,OAAI;QACH,MAAM;QACN,QAAO;QACP,UAAU;QACV,cAAa;QACb,cAAc;;0BAGd,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;gBACN,OAAO;oBACL;wBAAE,UAAU;wBAAM,SAAS;oBAAwB;oBACnD;wBAAE,KAAK,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,GAAG;wBAAE,SAAS,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;oBAAC;oBAC/E;wBAAE,KAAK,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,GAAG;wBAAE,SAAS,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;oBAAC;oBAC/E;wBAAE,SAAS,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;wBAAE,SAAS,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;oBAAC;iBACxF;0BAED,cAAA,8OAAC,gLAAA,CAAA,QAAK;oBAAC,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;oBAAK,aAAY;;;;;;;;;;;0BAI/C,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;gBACN,OAAO;oBACL;wBAAE,UAAU;wBAAM,SAAS;oBAA6B;oBACxD;wBAAE,MAAM;wBAAS,SAAS,oHAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,OAAO;oBAAC;iBACzD;0BAED,cAAA,8OAAC,gLAAA,CAAA,QAAK;oBAAC,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;oBAAK,aAAY;;;;;;;;;;;0BAI/C,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;gBACN,OAAO;oBACL;wBAAE,UAAU;wBAAM,SAAS;oBAAwB;oBACnD;wBAAE,KAAK,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,GAAG;wBAAE,SAAS,oHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;oBAAC;iBAChF;0BAED,cAAA,8OAAC,gLAAA,CAAA,QAAK,CAAC,QAAQ;oBAAC,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;oBAAK,aAAY;;;;;;;;;;;0BAIxD,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;gBACN,OAAO;oBAAC;wBAAE,UAAU;wBAAM,SAAS;oBAAuB;iBAAE;0BAE5D,cAAA,8OAAC,kLAAA,CAAA,SAAM;oBAAC,aAAY;;sCAClB,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;4BAAC,OAAM;sCAAS,oHAAA,CAAA,cAAW,CAAC,KAAK;;;;;;sCAC/C,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;4BAAC,OAAM;sCAAU,oHAAA,CAAA,cAAW,CAAC,MAAM;;;;;;sCACjD,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;4BAAC,OAAM;sCAAa,oHAAA,CAAA,cAAW,CAAC,SAAS;;;;;;;;;;;;;;;;;0BAK3D,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gBAAC,OAAO;oBAAE,cAAc;oBAAG,WAAW;gBAAO;0BACrD,cAAA,8OAAC,gMAAA,CAAA,QAAK;oBAAC,OAAO;wBAAE,OAAO;wBAAQ,gBAAgB;oBAAW;;wBACvD,0BACC,8OAAC,kMAAA,CAAA,SAAM;4BAAC,SAAS;sCAAU;;;;;;sCAI7B,8OAAC,kMAAA,CAAA,SAAM;4BAAC,MAAK;4BAAU,UAAS;4BAAS,SAAS;sCAAS;;;;;;;;;;;;;;;;;;;;;;;AAOrE"}}, {"offset": {"line": 8824, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8830, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/users/index.ts"], "sourcesContent": ["/**\n * User Components Index\n * Export all user management components\n */\n\n// User form components\nexport * from './user-form';\n\n// User component metadata\nexport const userComponentsMetadata = {\n  version: '1.0.0',\n  components: [\n    'UserForm',\n    'QuickUserForm',\n  ],\n  description: 'User management components for SystemUser CRUD operations',\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,uBAAuB;;;;;AAIhB,MAAM,yBAAyB;IACpC,SAAS;IACT,YAAY;QACV;QACA;KACD;IACD,aAAa;AACf"}}, {"offset": {"line": 8846, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8861, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/index.ts"], "sourcesContent": ["/**\n * Component Library Index\n * Central export for all reusable components\n */\n\n// Common UI Components\nexport * from './ui';\n\n// Layout Components\nexport * from './layout';\n\n// Form Components\nexport * from './forms';\n\n// Data Display Components\nexport * from './data-display';\n\n// Feedback Components\nexport * from './feedback';\n\n// Authentication components\nexport * from './auth';\n\n// User management components\nexport * from './users';\n\n// Future component categories:\n// export * from './navigation';\n// export * from './charts';\n// export * from './media';\n\n/**\n * Component library metadata\n */\nexport const COMPONENTS_VERSION = '1.0.0';\nexport const COMPONENTS_NAME = 'APISportsGame Component Library';\n\n/**\n * Setup function for component library\n */\nexport function setupComponents() {\n  console.log(`${COMPONENTS_NAME} v${COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,uBAAuB;;;;;;;;;;;;;AA6BhB,MAAM,qBAAqB;AAC3B,MAAM,kBAAkB;AAKxB,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,gBAAgB,EAAE,EAAE,mBAAmB,YAAY,CAAC;AACrE"}}, {"offset": {"line": 8882, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8903, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  AppLayout,\n  PageHeader,\n  Container,\n  TwoColumnLayout,\n  Card,\n  StatCard,\n  Button,\n  Space,\n  Typography,\n  Alert,\n} from '@/components';\nimport {\n  DashboardOutlined,\n  UserOutlined,\n  TrophyOutlined,\n  CalendarOutlined,\n  <PERSON>Outlined,\n  Bar<PERSON><PERSON>Outlined,\n  ApiOutlined,\n  SettingOutlined,\n} from '@ant-design/icons';\n\nconst { Title, Text, Paragraph } = Typography;\n\nexport default function Home() {\n  return (\n    <AppLayout>\n      <PageHeader\n        title=\"Dashboard\"\n        subtitle=\"Welcome to APISportsGame CMS - Your central hub for managing football data and broadcast links\"\n        actions={[\n          <Button key=\"settings\" icon={<SettingOutlined />}>\n            Settings\n          </Button>,\n        ]}\n      />\n\n      <Container>\n        {/* Welcome Alert */}\n        <Alert\n          message=\"Welcome to APISportsGame CMS!\"\n          description=\"This is your central dashboard for managing football leagues, teams, fixtures, broadcast links, and system users. Navigate using the sidebar menu to access different sections.\"\n          type=\"success\"\n          showIcon\n          style={{ marginBottom: '24px' }}\n        />\n\n        {/* Stats Overview */}\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '16px',\n          marginBottom: '24px'\n        }}>\n          <StatCard\n            title=\"Total Leagues\"\n            value=\"25\"\n            subtitle=\"Active football leagues\"\n            icon={<TrophyOutlined />}\n            trend={{ value: 8, isPositive: true }}\n          />\n\n          <StatCard\n            title=\"Teams\"\n            value=\"500+\"\n            subtitle=\"Registered teams\"\n            icon={<UserOutlined />}\n            trend={{ value: 12, isPositive: true }}\n          />\n\n          <StatCard\n            title=\"Fixtures\"\n            value=\"1,250\"\n            subtitle=\"Total fixtures\"\n            icon={<CalendarOutlined />}\n            trend={{ value: 5, isPositive: true }}\n          />\n\n          <StatCard\n            title=\"Broadcast Links\"\n            value=\"850\"\n            subtitle=\"Active links\"\n            icon={<LinkOutlined />}\n            trend={{ value: 15, isPositive: true }}\n          />\n        </div>\n\n        {/* Main Content */}\n        <TwoColumnLayout\n          leftContent={\n            <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\n              <Card title=\"Quick Actions\">\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <Button type=\"primary\" icon={<CalendarOutlined />} block>\n                    Sync Latest Fixtures\n                  </Button>\n                  <Button icon={<LinkOutlined />} block>\n                    Add Broadcast Link\n                  </Button>\n                  <Button icon={<UserOutlined />} block>\n                    Create System User\n                  </Button>\n                  <Button icon={<BarChartOutlined />} block>\n                    View Reports\n                  </Button>\n                </Space>\n              </Card>\n\n              <Card title=\"System Overview\">\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <div>\n                    <Text strong>API Status:</Text>\n                    <Text style={{ color: '#52c41a', marginLeft: '8px' }}>● Online</Text>\n                  </div>\n                  <div>\n                    <Text strong>Database:</Text>\n                    <Text style={{ color: '#52c41a', marginLeft: '8px' }}>● Connected</Text>\n                  </div>\n                  <div>\n                    <Text strong>External API:</Text>\n                    <Text style={{ color: '#52c41a', marginLeft: '8px' }}>● Syncing</Text>\n                  </div>\n                  <div>\n                    <Text strong>Last Sync:</Text>\n                    <Text style={{ marginLeft: '8px' }}>2 minutes ago</Text>\n                  </div>\n                </Space>\n              </Card>\n            </Space>\n          }\n          rightContent={\n            <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\n              <Card title=\"Recent Activity\">\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <div>\n                    <Text strong>Fixture sync completed</Text>\n                    <br />\n                    <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                      2 minutes ago\n                    </Text>\n                  </div>\n                  <div>\n                    <Text strong>New broadcast link added</Text>\n                    <br />\n                    <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                      5 minutes ago\n                    </Text>\n                  </div>\n                  <div>\n                    <Text strong>User John Doe logged in</Text>\n                    <br />\n                    <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                      10 minutes ago\n                    </Text>\n                  </div>\n                  <div>\n                    <Text strong>System backup completed</Text>\n                    <br />\n                    <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                      1 hour ago\n                    </Text>\n                  </div>\n                </Space>\n              </Card>\n\n              <Card title=\"Demo Pages\">\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <Button type=\"link\" href=\"/layout-demo\" icon={<DashboardOutlined />}>\n                    Layout Demo\n                  </Button>\n                  <Button type=\"link\" href=\"/components-demo\" icon={<BarChartOutlined />}>\n                    Components Demo\n                  </Button>\n                  <Button type=\"link\" href=\"/theme-demo\" icon={<SettingOutlined />}>\n                    Theme Demo\n                  </Button>\n                  <Button type=\"link\" href=\"/api-hooks-demo\" icon={<ApiOutlined />}>\n                    API Hooks Demo\n                  </Button>\n                </Space>\n              </Card>\n            </Space>\n          }\n        />\n\n        {/* Getting Started */}\n        <Card title=\"Getting Started\" style={{ marginTop: '24px' }}>\n          <Paragraph>\n            Welcome to the APISportsGame CMS! This dashboard provides you with a comprehensive overview\n            of your football data management system. Here's what you can do:\n          </Paragraph>\n\n          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>\n            <div>\n              <Title level={5}>\n                <TrophyOutlined /> Football Data Management\n              </Title>\n              <Text>\n                Manage leagues, teams, and fixtures. Sync data from external APIs and\n                keep your football database up to date.\n              </Text>\n            </div>\n\n            <div>\n              <Title level={5}>\n                <LinkOutlined /> Broadcast Links\n              </Title>\n              <Text>\n                Add and manage broadcast links for fixtures. Control quality settings\n                and ensure reliable streaming sources.\n              </Text>\n            </div>\n\n            <div>\n              <Title level={5}>\n                <UserOutlined /> User System\n              </Title>\n              <Text>\n                Manage system users, roles, and permissions. Control access to different\n                parts of the CMS based on user roles.\n              </Text>\n            </div>\n\n            <div>\n              <Title level={5}>\n                <BarChartOutlined /> System Monitoring\n              </Title>\n              <Text>\n                Monitor API health, view system logs, and track performance metrics\n                to ensure optimal system operation.\n              </Text>\n            </div>\n          </div>\n        </Card>\n      </Container>\n    </AppLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAYA;AAZA;AAAA;AAAA;AAYA;AAAA;AAAA;AAAA;AAZA;AAYA;AAAA;AAAA;AAfA;;;;AA0BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,0LAAA,CAAA,aAAU;AAE9B,SAAS;IACtB,qBACE,8OAAC,6IAAA,CAAA,YAAS;;0BACR,8OAAC,8IAAA,CAAA,aAAU;gBACT,OAAM;gBACN,UAAS;gBACT,SAAS;kCACP,8OAAC,kIAAA,CAAA,SAAM;wBAAgB,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;kCAAK;uBAAtC;;;;;iBAGb;;;;;;0BAGH,8OAAC,iJAAA,CAAA,YAAS;;kCAER,8OAAC,gLAAA,CAAA,QAAK;wBACJ,SAAQ;wBACR,aAAY;wBACZ,MAAK;wBACL,QAAQ;wBACR,OAAO;4BAAE,cAAc;wBAAO;;;;;;kCAIhC,8OAAC;wBAAI,OAAO;4BACV,SAAS;4BACT,qBAAqB;4BACrB,KAAK;4BACL,cAAc;wBAChB;;0CACE,8OAAC,gIAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,OAAM;gCACN,UAAS;gCACT,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;gCACrB,OAAO;oCAAE,OAAO;oCAAG,YAAY;gCAAK;;;;;;0CAGtC,8OAAC,gIAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,OAAM;gCACN,UAAS;gCACT,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gCACnB,OAAO;oCAAE,OAAO;oCAAI,YAAY;gCAAK;;;;;;0CAGvC,8OAAC,gIAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,OAAM;gCACN,UAAS;gCACT,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gCACvB,OAAO;oCAAE,OAAO;oCAAG,YAAY;gCAAK;;;;;;0CAGtC,8OAAC,gIAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,OAAM;gCACN,UAAS;gCACT,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gCACnB,OAAO;oCAAE,OAAO;oCAAI,YAAY;gCAAK;;;;;;;;;;;;kCAKzC,8OAAC,iJAAA,CAAA,kBAAe;wBACd,2BACE,8OAAC,gMAAA,CAAA,QAAK;4BAAC,WAAU;4BAAW,OAAO;gCAAE,OAAO;4BAAO;4BAAG,MAAK;;8CACzD,8OAAC,gIAAA,CAAA,OAAI;oCAAC,OAAM;8CACV,cAAA,8OAAC,gMAAA,CAAA,QAAK;wCAAC,WAAU;wCAAW,OAAO;4CAAE,OAAO;wCAAO;;0DACjD,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAU,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gDAAK,KAAK;0DAAC;;;;;;0DAGzD,8OAAC,kIAAA,CAAA,SAAM;gDAAC,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gDAAK,KAAK;0DAAC;;;;;;0DAGtC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gDAAK,KAAK;0DAAC;;;;;;0DAGtC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gDAAK,KAAK;0DAAC;;;;;;;;;;;;;;;;;8CAM9C,8OAAC,gIAAA,CAAA,OAAI;oCAAC,OAAM;8CACV,cAAA,8OAAC,gMAAA,CAAA,QAAK;wCAAC,WAAU;wCAAW,OAAO;4CAAE,OAAO;wCAAO;;0DACjD,8OAAC;;kEACC,8OAAC;wDAAK,MAAM;kEAAC;;;;;;kEACb,8OAAC;wDAAK,OAAO;4DAAE,OAAO;4DAAW,YAAY;wDAAM;kEAAG;;;;;;;;;;;;0DAExD,8OAAC;;kEACC,8OAAC;wDAAK,MAAM;kEAAC;;;;;;kEACb,8OAAC;wDAAK,OAAO;4DAAE,OAAO;4DAAW,YAAY;wDAAM;kEAAG;;;;;;;;;;;;0DAExD,8OAAC;;kEACC,8OAAC;wDAAK,MAAM;kEAAC;;;;;;kEACb,8OAAC;wDAAK,OAAO;4DAAE,OAAO;4DAAW,YAAY;wDAAM;kEAAG;;;;;;;;;;;;0DAExD,8OAAC;;kEACC,8OAAC;wDAAK,MAAM;kEAAC;;;;;;kEACb,8OAAC;wDAAK,OAAO;4DAAE,YAAY;wDAAM;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAM9C,4BACE,8OAAC,gMAAA,CAAA,QAAK;4BAAC,WAAU;4BAAW,OAAO;gCAAE,OAAO;4BAAO;4BAAG,MAAK;;8CACzD,8OAAC,gIAAA,CAAA,OAAI;oCAAC,OAAM;8CACV,cAAA,8OAAC,gMAAA,CAAA,QAAK;wCAAC,WAAU;wCAAW,OAAO;4CAAE,OAAO;wCAAO;;0DACjD,8OAAC;;kEACC,8OAAC;wDAAK,MAAM;kEAAC;;;;;;kEACb,8OAAC;;;;;kEACD,8OAAC;wDAAK,MAAK;wDAAY,OAAO;4DAAE,UAAU;wDAAO;kEAAG;;;;;;;;;;;;0DAItD,8OAAC;;kEACC,8OAAC;wDAAK,MAAM;kEAAC;;;;;;kEACb,8OAAC;;;;;kEACD,8OAAC;wDAAK,MAAK;wDAAY,OAAO;4DAAE,UAAU;wDAAO;kEAAG;;;;;;;;;;;;0DAItD,8OAAC;;kEACC,8OAAC;wDAAK,MAAM;kEAAC;;;;;;kEACb,8OAAC;;;;;kEACD,8OAAC;wDAAK,MAAK;wDAAY,OAAO;4DAAE,UAAU;wDAAO;kEAAG;;;;;;;;;;;;0DAItD,8OAAC;;kEACC,8OAAC;wDAAK,MAAM;kEAAC;;;;;;kEACb,8OAAC;;;;;kEACD,8OAAC;wDAAK,MAAK;wDAAY,OAAO;4DAAE,UAAU;wDAAO;kEAAG;;;;;;;;;;;;;;;;;;;;;;;8CAO1D,8OAAC,gIAAA,CAAA,OAAI;oCAAC,OAAM;8CACV,cAAA,8OAAC,gMAAA,CAAA,QAAK;wCAAC,WAAU;wCAAW,OAAO;4CAAE,OAAO;wCAAO;;0DACjD,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAO,MAAK;gDAAe,oBAAM,8OAAC,4NAAA,CAAA,oBAAiB;;;;;0DAAK;;;;;;0DAGrE,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAO,MAAK;gDAAmB,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;0DAAK;;;;;;0DAGxE,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAO,MAAK;gDAAc,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;0DAAK;;;;;;0DAGlE,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAO,MAAK;gDAAkB,oBAAM,8OAAC,gNAAA,CAAA,cAAW;;;;;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU5E,8OAAC,gIAAA,CAAA,OAAI;wBAAC,OAAM;wBAAkB,OAAO;4BAAE,WAAW;wBAAO;;0CACvD,8OAAC;0CAAU;;;;;;0CAKX,8OAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,qBAAqB;oCAAwC,KAAK;gCAAO;;kDACtG,8OAAC;;0DACC,8OAAC;gDAAM,OAAO;;kEACZ,8OAAC,sNAAA,CAAA,iBAAc;;;;;oDAAG;;;;;;;0DAEpB,8OAAC;0DAAK;;;;;;;;;;;;kDAMR,8OAAC;;0DACC,8OAAC;gDAAM,OAAO;;kEACZ,8OAAC,kNAAA,CAAA,eAAY;;;;;oDAAG;;;;;;;0DAElB,8OAAC;0DAAK;;;;;;;;;;;;kDAMR,8OAAC;;0DACC,8OAAC;gDAAM,OAAO;;kEACZ,8OAAC,kNAAA,CAAA,eAAY;;;;;oDAAG;;;;;;;0DAElB,8OAAC;0DAAK;;;;;;;;;;;;kDAMR,8OAAC;;0DACC,8OAAC;gDAAM,OAAO;;kEACZ,8OAAC,0NAAA,CAAA,mBAAgB;;;;;oDAAG;;;;;;;0DAEtB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpB"}}, {"offset": {"line": 9672, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}