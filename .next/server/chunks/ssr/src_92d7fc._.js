module.exports = {

"[project]/src/stores/types.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Types and Interfaces
 * Defines TypeScript types for all store modules
 */ // ============================================================================
// Base Store Types
// ============================================================================
/**
 * Base store state interface
 * All stores should extend this interface
 */ __turbopack_esm__({});
;
}}),
"[project]/src/stores/auth-utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Authentication Utilities
 * Helper functions for authentication operations
 */ __turbopack_esm__({
    "authenticatedApiRequest": (()=>authenticatedApiRequest),
    "authenticatedFetch": (()=>authenticatedFetch),
    "canAccessRoute": (()=>canAccessRoute),
    "canAdmin": (()=>canAdmin),
    "canEdit": (()=>canEdit),
    "canModerate": (()=>canModerate),
    "checkSession": (()=>checkSession),
    "clearAuthError": (()=>clearAuthError),
    "getAccessToken": (()=>getAccessToken),
    "getAuthError": (()=>getAuthError),
    "getAuthHeader": (()=>getAuthHeader),
    "getAuthSessionRemainingTime": (()=>getAuthSessionRemainingTime),
    "getAuthState": (()=>getAuthState),
    "getCurrentUser": (()=>getCurrentUser),
    "getCurrentUserRole": (()=>getCurrentUserRole),
    "getLoginRedirectPath": (()=>getLoginRedirectPath),
    "getPostLoginRedirectPath": (()=>getPostLoginRedirectPath),
    "getRefreshToken": (()=>getRefreshToken),
    "hasAnyRole": (()=>hasAnyRole),
    "hasAuthError": (()=>hasAuthError),
    "hasRole": (()=>hasRole),
    "isAdmin": (()=>isAdmin),
    "isAuthenticated": (()=>isAuthenticated),
    "isEditor": (()=>isEditor),
    "isModerator": (()=>isModerator),
    "isSessionExpiringSoon": (()=>isSessionExpiringSoon),
    "login": (()=>login),
    "logout": (()=>logout),
    "logoutAll": (()=>logoutAll),
    "mockLogin": (()=>mockLogin),
    "refreshTokens": (()=>refreshTokens),
    "resetAuthState": (()=>resetAuthState),
    "updateActivity": (()=>updateActivity),
    "updateProfile": (()=>updateProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-store.ts [app-ssr] (ecmascript)");
;
const getAccessToken = ()=>{
    const tokens = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState().tokens;
    return tokens?.accessToken || null;
};
const getRefreshToken = ()=>{
    const tokens = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState().tokens;
    return tokens?.refreshToken || null;
};
const getAuthHeader = ()=>{
    const token = getAccessToken();
    return token ? {
        Authorization: `Bearer ${token}`
    } : {};
};
const isAuthenticated = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState().isAuthenticated;
};
const getCurrentUser = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState().user;
};
const getCurrentUserRole = ()=>{
    const user = getCurrentUser();
    return user?.role || null;
};
const hasRole = (role)=>{
    const currentRole = getCurrentUserRole();
    return currentRole === role;
};
const hasAnyRole = (roles)=>{
    const currentRole = getCurrentUserRole();
    return currentRole ? roles.includes(currentRole) : false;
};
const isAdmin = ()=>{
    return hasRole('Admin');
};
const isEditor = ()=>{
    return hasRole('Editor');
};
const isModerator = ()=>{
    return hasRole('Moderator');
};
const canEdit = ()=>{
    return hasAnyRole([
        'Admin',
        'Editor'
    ]);
};
const canAdmin = ()=>{
    return isAdmin();
};
const canModerate = ()=>{
    return hasAnyRole([
        'Admin',
        'Editor',
        'Moderator'
    ]);
};
const checkSession = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState().checkSession();
};
const updateActivity = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState().updateLastActivity();
};
const getAuthSessionRemainingTime = ()=>{
    const { lastActivity, sessionTimeout } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState();
    const now = Date.now();
    const timeoutMs = sessionTimeout * 60 * 1000;
    const elapsed = now - lastActivity;
    const remaining = timeoutMs - elapsed;
    return Math.max(0, Math.floor(remaining / (60 * 1000)));
};
const isSessionExpiringSoon = ()=>{
    return getAuthSessionRemainingTime() <= 5;
};
const login = async (email, password)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState().login(email, password);
};
const logout = async ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState().logout();
};
const logoutAll = async ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState().logoutAll();
};
const updateProfile = async (data)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState().updateProfile(data);
};
const refreshTokens = async ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState().refreshTokens();
};
const getAuthError = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState().error;
};
const clearAuthError = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState().clearError();
};
const hasAuthError = ()=>{
    return !!getAuthError();
};
const authenticatedFetch = async (url, options = {})=>{
    const authHeaders = getAuthHeader();
    const config = {
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...authHeaders,
            ...options.headers
        }
    };
    const response = await fetch(url, config);
    // Handle token expiration
    if (response.status === 401) {
        const isAuth = isAuthenticated();
        if (isAuth) {
            // Try to refresh tokens
            try {
                await refreshTokens();
                // Retry the request with new token
                const newAuthHeaders = getAuthHeader();
                const retryConfig = {
                    ...config,
                    headers: {
                        ...config.headers,
                        ...newAuthHeaders
                    }
                };
                return fetch(url, retryConfig);
            } catch (error) {
                // Refresh failed, logout user
                await logout();
                throw new Error('Authentication expired. Please log in again.');
            }
        }
    }
    return response;
};
const authenticatedApiRequest = async (url, options = {})=>{
    const response = await authenticatedFetch(url, options);
    if (!response.ok) {
        const errorData = await response.json().catch(()=>({}));
        throw new Error(errorData.message || `Request failed with status ${response.status}`);
    }
    return response.json();
};
const canAccessRoute = (requiredRoles)=>{
    if (!isAuthenticated()) return false;
    if (!requiredRoles || requiredRoles.length === 0) return true;
    return hasAnyRole(requiredRoles);
};
const getLoginRedirectPath = (currentPath)=>{
    const loginPath = '/login';
    if (currentPath && currentPath !== '/') {
        return `${loginPath}?redirect=${encodeURIComponent(currentPath)}`;
    }
    return loginPath;
};
const getPostLoginRedirectPath = (searchParams)=>{
    const redirectParam = searchParams?.get('redirect');
    return redirectParam || '/dashboard';
};
const getAuthState = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState();
};
const mockLogin = (user, tokens)=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState().setUser(user);
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState().setTokens(tokens);
};
const resetAuthState = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"].getState().logout();
};
}}),
"[project]/src/stores/app-utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Application Utilities
 * Helper functions for application operations
 */ __turbopack_esm__({
    "addNotification": (()=>addNotification),
    "applyThemeToDocument": (()=>applyThemeToDocument),
    "clearGlobalError": (()=>clearGlobalError),
    "clearNotifications": (()=>clearNotifications),
    "getActiveMenuKey": (()=>getActiveMenuKey),
    "getAppState": (()=>getAppState),
    "getAppVersion": (()=>getAppVersion),
    "getBreadcrumbs": (()=>getBreadcrumbs),
    "getBuildTime": (()=>getBuildTime),
    "getCurrentBreakpoint": (()=>getCurrentBreakpoint),
    "getCurrentNavigation": (()=>getCurrentNavigation),
    "getCurrentPath": (()=>getCurrentPath),
    "getCurrentSettings": (()=>getCurrentSettings),
    "getCurrentTheme": (()=>getCurrentTheme),
    "getCurrentThemeMode": (()=>getCurrentThemeMode),
    "getCurrentUIState": (()=>getCurrentUIState),
    "getEnvironment": (()=>getEnvironment),
    "getGlobalError": (()=>getGlobalError),
    "getModalData": (()=>getModalData),
    "getModalsState": (()=>getModalsState),
    "getNotifications": (()=>getNotifications),
    "getSetting": (()=>getSetting),
    "hideAllModals": (()=>hideAllModals),
    "hideModal": (()=>hideModal),
    "isDarkMode": (()=>isDarkMode),
    "isDesktop": (()=>isDesktop),
    "isDevelopment": (()=>isDevelopment),
    "isGlobalLoading": (()=>isGlobalLoading),
    "isMobile": (()=>isMobile),
    "isModalVisible": (()=>isModalVisible),
    "isProduction": (()=>isProduction),
    "isSidebarCollapsed": (()=>isSidebarCollapsed),
    "isTablet": (()=>isTablet),
    "notifyError": (()=>notifyError),
    "notifyInfo": (()=>notifyInfo),
    "notifySuccess": (()=>notifySuccess),
    "notifyWarning": (()=>notifyWarning),
    "removeNotification": (()=>removeNotification),
    "resetAppState": (()=>resetAppState),
    "resetSettings": (()=>resetSettings),
    "setActiveMenu": (()=>setActiveMenu),
    "setBreadcrumbs": (()=>setBreadcrumbs),
    "setCurrentPath": (()=>setCurrentPath),
    "setGlobalError": (()=>setGlobalError),
    "setGlobalLoading": (()=>setGlobalLoading),
    "setThemeMode": (()=>setThemeMode),
    "showModal": (()=>showModal),
    "toggleSidebar": (()=>toggleSidebar),
    "toggleTheme": (()=>toggleTheme),
    "updateSettings": (()=>updateSettings)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-store.ts [app-ssr] (ecmascript)");
;
const getCurrentTheme = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().theme;
};
const getCurrentThemeMode = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().theme.mode;
};
const isDarkMode = ()=>{
    return getCurrentThemeMode() === 'dark';
};
const toggleTheme = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().toggleTheme();
};
const setThemeMode = (mode)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().setTheme({
        mode
    });
};
const applyThemeToDocument = ()=>{
    if (typeof document === 'undefined') return;
    const theme = getCurrentTheme();
    const { mode, primaryColor, borderRadius } = theme;
    // Apply theme class to body
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${mode}`);
    // Apply CSS custom properties
    const root = document.documentElement;
    root.style.setProperty('--primary-color', primaryColor);
    root.style.setProperty('--border-radius', `${borderRadius}px`);
};
const getCurrentSettings = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().settings;
};
const getSetting = (key)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().settings[key];
};
const updateSettings = (settings)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().updateSettings(settings);
};
const resetSettings = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().resetSettings();
};
const getCurrentNavigation = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().navigation;
};
const getCurrentPath = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().navigation.currentPath;
};
const setCurrentPath = (path)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().setCurrentPath(path);
};
const getBreadcrumbs = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().navigation.breadcrumbs;
};
const setBreadcrumbs = (breadcrumbs)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().setBreadcrumbs(breadcrumbs);
};
const isSidebarCollapsed = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().navigation.sidebarCollapsed;
};
const toggleSidebar = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().toggleSidebar();
};
const getActiveMenuKey = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().navigation.activeMenuKey;
};
const setActiveMenu = (key)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().setActiveMenu(key);
};
const getCurrentUIState = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui;
};
const isGlobalLoading = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.globalLoading;
};
const setGlobalLoading = (loading, message)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().setGlobalLoading(loading, message);
};
const getGlobalError = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.globalError;
};
const setGlobalError = (error, details)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().setGlobalError(error, details);
};
const clearGlobalError = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().clearGlobalError();
};
const getNotifications = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.notifications;
};
const addNotification = (notification)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification(notification);
};
const removeNotification = (id)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().removeNotification(id);
};
const clearNotifications = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().clearNotifications();
};
const notifySuccess = (message, title)=>{
    addNotification({
        type: 'success',
        title: title || 'Success',
        message
    });
};
const notifyError = (message, title)=>{
    addNotification({
        type: 'error',
        title: title || 'Error',
        message
    });
};
const notifyWarning = (message, title)=>{
    addNotification({
        type: 'warning',
        title: title || 'Warning',
        message
    });
};
const notifyInfo = (message, title)=>{
    addNotification({
        type: 'info',
        title: title || 'Info',
        message
    });
};
const getModalsState = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.modals;
};
const isModalVisible = (key)=>{
    const modal = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.modals[key];
    return modal?.visible || false;
};
const getModalData = (key)=>{
    const modal = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.modals[key];
    return modal?.data;
};
const showModal = (key, data)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().showModal(key, data);
};
const hideModal = (key)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().hideModal(key);
};
const hideAllModals = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().hideAllModals();
};
const getAppVersion = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().version;
};
const getBuildTime = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().buildTime;
};
const getEnvironment = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().environment;
};
const isDevelopment = ()=>{
    return getEnvironment() === 'development';
};
const isProduction = ()=>{
    return getEnvironment() === 'production';
};
const isMobile = ()=>{
    if ("TURBOPACK compile-time truthy", 1) return false;
    "TURBOPACK unreachable";
};
const isTablet = ()=>{
    if ("TURBOPACK compile-time truthy", 1) return false;
    "TURBOPACK unreachable";
};
const isDesktop = ()=>{
    if ("TURBOPACK compile-time truthy", 1) return false;
    "TURBOPACK unreachable";
};
const getCurrentBreakpoint = ()=>{
    if (isMobile()) return 'mobile';
    if (isTablet()) return 'tablet';
    return 'desktop';
};
const getAppState = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState();
};
const resetAppState = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    const store = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState();
    store.resetSettings();
    store.clearNotifications();
    store.hideAllModals();
    store.clearGlobalError();
    store.setGlobalLoading(false);
};
}}),
"[project]/src/stores/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Index - Central export for all stores
 * Provides barrel exports for all store modules
 */ // Store types and interfaces
__turbopack_esm__({});
;
;
;
;
;
;
;
;
;
;
;
}}),
"[project]/src/stores/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/types.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-store.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-store.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/store-provider.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/store-context.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/provider-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/src/stores/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/app/store-demo/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Demo Page - Test page for Store Provider functionality
 * Demonstrates usage of stores through providers
 */ __turbopack_esm__({
    "default": (()=>StoreDemoPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/stores/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/provider-hooks.ts [app-ssr] (ecmascript)");
'use client';
;
;
function StoreDemoPage() {
    const auth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthProvider"])();
    const app = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppProvider"])();
    const { isAvailable } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useStoreAvailability"])();
    const debug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useStoreDebug"])();
    const handleLogin = async ()=>{
        try {
            await auth.login({
                username: 'admin',
                password: 'admin123456'
            });
        } catch (error) {
            console.error('Login failed:', error);
        }
    };
    const handleLogout = async ()=>{
        try {
            await auth.logout();
        } catch (error) {
            console.error('Logout failed:', error);
        }
    };
    const handleThemeToggle = ()=>{
        app.toggleTheme();
    };
    const handleSidebarToggle = ()=>{
        app.toggleSidebar();
    };
    const handleShowNotification = ()=>{
        app.showNotification({
            type: 'success',
            message: 'Test Notification',
            description: 'This is a test notification from the store provider demo.'
        });
    };
    const handleLanguageChange = ()=>{
        const newLanguage = app.language === 'en' ? 'vi' : 'en';
        app.setLanguage(newLanguage);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            padding: '20px',
            fontFamily: 'Arial, sans-serif'
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                children: "Store Provider Demo"
            }, void 0, false, {
                fileName: "[project]/src/app/store-demo/page.tsx",
                lineNumber: 59,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginBottom: '20px',
                    padding: '10px',
                    border: '1px solid #ccc',
                    borderRadius: '5px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: "Store Availability"
                    }, void 0, false, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 63,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Store Available: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: isAvailable ? 'Yes' : 'No'
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 64,
                                columnNumber: 29
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 64,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Auth Initialized: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: auth.isInitialized ? 'Yes' : 'No'
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 65,
                                columnNumber: 30
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 65,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "App Initialized: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: app.isInitialized ? 'Yes' : 'No'
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 66,
                                columnNumber: 29
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 66,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/store-demo/page.tsx",
                lineNumber: 62,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginBottom: '20px',
                    padding: '10px',
                    border: '1px solid #ccc',
                    borderRadius: '5px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: "Authentication State"
                    }, void 0, false, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 71,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Authenticated: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: auth.isAuthenticated ? 'Yes' : 'No'
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 72,
                                columnNumber: 27
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 72,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Loading: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: auth.isLoading ? 'Yes' : 'No'
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 73,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 73,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "User: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: auth.user ? `${auth.user.username} (${auth.user.role})` : 'None'
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 74,
                                columnNumber: 18
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 74,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Error: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: auth.error || 'None'
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 75,
                                columnNumber: 19
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 75,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            marginTop: '10px'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleLogin,
                                disabled: auth.isLoading || auth.isAuthenticated,
                                style: {
                                    marginRight: '10px',
                                    padding: '5px 10px'
                                },
                                children: "Login (Demo)"
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 78,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleLogout,
                                disabled: auth.isLoading || !auth.isAuthenticated,
                                style: {
                                    marginRight: '10px',
                                    padding: '5px 10px'
                                },
                                children: "Logout"
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 85,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: auth.clearError,
                                disabled: !auth.error,
                                style: {
                                    padding: '5px 10px'
                                },
                                children: "Clear Error"
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 92,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 77,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/store-demo/page.tsx",
                lineNumber: 70,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginBottom: '20px',
                    padding: '10px',
                    border: '1px solid #ccc',
                    borderRadius: '5px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: "Application State"
                    }, void 0, false, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 104,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Theme: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: app.theme
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 105,
                                columnNumber: 19
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 105,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Language: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: app.language
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 106,
                                columnNumber: 22
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 106,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Sidebar Collapsed: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: app.sidebarCollapsed ? 'Yes' : 'No'
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 107,
                                columnNumber: 31
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 107,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Loading: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: app.isLoading ? 'Yes' : 'No'
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 108,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 108,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Notification: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: app.notification ? `${app.notification.type}: ${app.notification.message}` : 'None'
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 109,
                                columnNumber: 26
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 109,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            marginTop: '10px'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleThemeToggle,
                                style: {
                                    marginRight: '10px',
                                    padding: '5px 10px'
                                },
                                children: "Toggle Theme"
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 112,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleSidebarToggle,
                                style: {
                                    marginRight: '10px',
                                    padding: '5px 10px'
                                },
                                children: "Toggle Sidebar"
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 118,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleLanguageChange,
                                style: {
                                    marginRight: '10px',
                                    padding: '5px 10px'
                                },
                                children: "Change Language"
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 124,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleShowNotification,
                                style: {
                                    marginRight: '10px',
                                    padding: '5px 10px'
                                },
                                children: "Show Notification"
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 130,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: app.hideNotification,
                                disabled: !app.notification,
                                style: {
                                    padding: '5px 10px'
                                },
                                children: "Hide Notification"
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 136,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 111,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/store-demo/page.tsx",
                lineNumber: 103,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginBottom: '20px',
                    padding: '10px',
                    border: '1px solid #ccc',
                    borderRadius: '5px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: "Debug Information"
                    }, void 0, false, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 148,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("details", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("summary", {
                                children: "Auth State (Click to expand)"
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 150,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                                style: {
                                    background: '#f5f5f5',
                                    padding: '10px',
                                    overflow: 'auto'
                                },
                                children: JSON.stringify(debug.authState, null, 2)
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 151,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 149,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("details", {
                        style: {
                            marginTop: '10px'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("summary", {
                                children: "App State (Click to expand)"
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 157,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                                style: {
                                    background: '#f5f5f5',
                                    padding: '10px',
                                    overflow: 'auto'
                                },
                                children: JSON.stringify(debug.appState, null, 2)
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 158,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 156,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            marginTop: '10px'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: debug.actions.resetAuth,
                                style: {
                                    marginRight: '10px',
                                    padding: '5px 10px'
                                },
                                children: "Reset Auth Store"
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 164,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: debug.actions.resetApp,
                                style: {
                                    padding: '5px 10px'
                                },
                                children: "Reset App Store"
                            }, void 0, false, {
                                fileName: "[project]/src/app/store-demo/page.tsx",
                                lineNumber: 170,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 163,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/store-demo/page.tsx",
                lineNumber: 147,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginTop: '30px',
                    padding: '10px',
                    border: '1px solid #ccc',
                    borderRadius: '5px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: "Navigation"
                    }, void 0, false, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 181,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "This demo page shows the Store Provider functionality."
                    }, void 0, false, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 182,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                        href: "/",
                        style: {
                            color: 'blue',
                            textDecoration: 'underline'
                        },
                        children: "← Back to Home"
                    }, void 0, false, {
                        fileName: "[project]/src/app/store-demo/page.tsx",
                        lineNumber: 183,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/store-demo/page.tsx",
                lineNumber: 180,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/store-demo/page.tsx",
        lineNumber: 58,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/store-demo/page.tsx [app-rsc] (ecmascript, Next.js server component, client modules ssr)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),

};

//# sourceMappingURL=src_92d7fc._.js.map