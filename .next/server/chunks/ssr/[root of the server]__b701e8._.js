module.exports = {

"[externals]/next/dist/compiled/next-server/app-page.runtime.dev.js [external] (next/dist/compiled/next-server/app-page.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/compiled/next-server/app-page.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/stores/utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Utilities
 * Helper functions and utilities for store management
 */ __turbopack_esm__({
    "clearStoredTokens": (()=>clearStoredTokens),
    "createBaseStoreActions": (()=>createBaseStoreActions),
    "createBaseStoreState": (()=>createBaseStoreState),
    "createErrorObject": (()=>createErrorObject),
    "createStoreWithMiddleware": (()=>createStoreWithMiddleware),
    "extractErrorMessage": (()=>extractErrorMessage),
    "generateNotificationId": (()=>generateNotificationId),
    "getDefaultNotificationDuration": (()=>getDefaultNotificationDuration),
    "getSessionRemainingTime": (()=>getSessionRemainingTime),
    "getTokenExpiration": (()=>getTokenExpiration),
    "isSessionValid": (()=>isSessionValid),
    "isTokenExpired": (()=>isTokenExpired),
    "logStoreAction": (()=>logStoreAction),
    "storage": (()=>storage),
    "validateStoreState": (()=>validateStoreState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
;
function createStoreWithMiddleware(storeCreator, config) {
    let store = storeCreator;
    // Apply persistence middleware if configured
    if (config.persist) {
        store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])(store, {
            name: config.persist.name,
            version: config.persist.version,
            storage: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createJSONStorage"])(()=>localStorage),
            partialize: config.persist.partialize || ((state)=>state),
            skipHydration: config.persist.skipHydration || false,
            onRehydrateStorage: ()=>(state)=>{
                    if (state) {
                        state.setHasHydrated(true);
                    }
                }
        });
    }
    // Apply devtools middleware if configured
    if (config.devtools) {
        store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["devtools"])(store, {
            name: config.devtools.name,
            enabled: config.devtools.enabled && ("TURBOPACK compile-time value", "development") === 'development'
        });
    }
    return store;
}
function createBaseStoreState() {
    return {
        _hasHydrated: false,
        setHasHydrated: (hasHydrated)=>{
        // This will be implemented by the actual store
        }
    };
}
function createBaseStoreActions(set) {
    return {
        setHasHydrated: (hasHydrated)=>{
            set({
                _hasHydrated: hasHydrated
            });
        }
    };
}
function isTokenExpired(expiresAt) {
    return Date.now() >= expiresAt;
}
function getTokenExpiration(token, defaultMinutes = 60) {
    try {
        // Try to decode JWT token to get expiration
        const payload = JSON.parse(atob(token.split('.')[1]));
        if (payload.exp) {
            return payload.exp * 1000; // Convert to milliseconds
        }
    } catch (error) {
    // If JWT parsing fails, use default expiration
    }
    // Default expiration: current time + defaultMinutes
    return Date.now() + defaultMinutes * 60 * 1000;
}
function clearStoredTokens() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
}
function isSessionValid(lastActivity, sessionTimeout) {
    const now = Date.now();
    const timeoutMs = sessionTimeout * 60 * 1000; // Convert minutes to milliseconds
    return now - lastActivity < timeoutMs;
}
function getSessionRemainingTime(lastActivity, sessionTimeout) {
    const now = Date.now();
    const timeoutMs = sessionTimeout * 60 * 1000;
    const elapsed = now - lastActivity;
    const remaining = timeoutMs - elapsed;
    return Math.max(0, Math.floor(remaining / (60 * 1000)));
}
function extractErrorMessage(error) {
    if (typeof error === 'string') {
        return error;
    }
    if (error?.response?.data?.message) {
        return error.response.data.message;
    }
    if (error?.message) {
        return error.message;
    }
    if (error?.error) {
        return error.error;
    }
    return 'An unexpected error occurred';
}
function createErrorObject(message, details) {
    return {
        message,
        details: details || null
    };
}
function generateNotificationId() {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
function getDefaultNotificationDuration(type) {
    switch(type){
        case 'success':
            return 3000; // 3 seconds
        case 'error':
            return 5000; // 5 seconds
        case 'warning':
            return 4000; // 4 seconds
        case 'info':
            return 3000; // 3 seconds
        default:
            return 3000;
    }
}
const storage = {
    get: (key)=>{
        if ("TURBOPACK compile-time truthy", 1) return null;
        "TURBOPACK unreachable";
    },
    set: (key, value)=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    },
    remove: (key)=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    },
    clear: ()=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    }
};
function logStoreAction(storeName, actionName, payload) {
    if ("TURBOPACK compile-time truthy", 1) {
        console.group(`🐻 [${storeName}] ${actionName}`);
        if (payload !== undefined) {
            console.log('Payload:', payload);
        }
        console.log('Timestamp:', new Date().toISOString());
        console.groupEnd();
    }
}
function validateStoreState(state, requiredKeys) {
    for (const key of requiredKeys){
        if (!(key in state)) {
            console.error(`Missing required store state key: ${String(key)}`);
            return false;
        }
    }
    return true;
}
}}),
"[project]/src/stores/constants.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Constants
 * Defines constants used across all stores
 */ // ============================================================================
// Store Names
// ============================================================================
__turbopack_esm__({
    "ACTIVITY_TRACKING_INTERVAL": (()=>ACTIVITY_TRACKING_INTERVAL),
    "DEFAULT_APP_SETTINGS": (()=>DEFAULT_APP_SETTINGS),
    "DEFAULT_NAVIGATION": (()=>DEFAULT_NAVIGATION),
    "DEFAULT_THEME": (()=>DEFAULT_THEME),
    "DEFAULT_UI_STATE": (()=>DEFAULT_UI_STATE),
    "DEV_CONFIG": (()=>DEV_CONFIG),
    "ERROR_MESSAGES": (()=>ERROR_MESSAGES),
    "FEATURE_FLAGS": (()=>FEATURE_FLAGS),
    "SESSION_TIMEOUT": (()=>SESSION_TIMEOUT),
    "STORAGE_KEYS": (()=>STORAGE_KEYS),
    "STORE_API_ENDPOINTS": (()=>STORE_API_ENDPOINTS),
    "STORE_NAMES": (()=>STORE_NAMES),
    "STORE_VERSIONS": (()=>STORE_VERSIONS),
    "SUCCESS_MESSAGES": (()=>SUCCESS_MESSAGES),
    "TOKEN_REFRESH_THRESHOLD": (()=>TOKEN_REFRESH_THRESHOLD),
    "VALIDATION_RULES": (()=>VALIDATION_RULES)
});
const STORE_NAMES = {
    AUTH: 'auth-store',
    APP: 'app-store'
};
const STORAGE_KEYS = {
    AUTH: 'auth-storage',
    APP: 'app-storage',
    THEME: 'theme-storage',
    SETTINGS: 'settings-storage'
};
const DEFAULT_THEME = {
    mode: 'light',
    primaryColor: '#1890ff',
    borderRadius: 6,
    compactMode: false
};
const DEFAULT_APP_SETTINGS = {
    language: 'en',
    timezone: 'UTC',
    dateFormat: 'YYYY-MM-DD',
    pageSize: 20,
    autoRefresh: true,
    refreshInterval: 30,
    features: {
        darkMode: true,
        notifications: true,
        autoSave: true,
        advancedFilters: true
    }
};
const DEFAULT_NAVIGATION = {
    currentPath: '/',
    breadcrumbs: [],
    sidebarCollapsed: false,
    activeMenuKey: 'dashboard'
};
const DEFAULT_UI_STATE = {
    globalLoading: false,
    loadingMessage: '',
    globalError: null,
    errorDetails: null,
    notifications: [],
    modals: {}
};
const SESSION_TIMEOUT = 60; // 1 hour
const TOKEN_REFRESH_THRESHOLD = 5; // 5 minutes
const ACTIVITY_TRACKING_INTERVAL = 60000; // 1 minute
const STORE_API_ENDPOINTS = {
    AUTH: {
        LOGIN: '/api/system-auth/login',
        LOGOUT: '/api/system-auth/logout',
        LOGOUT_ALL: '/api/system-auth/logout-all',
        PROFILE: '/api/system-auth/profile',
        REFRESH: '/api/system-auth/refresh'
    }
};
const ERROR_MESSAGES = {
    AUTH: {
        LOGIN_FAILED: 'Login failed. Please check your credentials.',
        LOGOUT_FAILED: 'Logout failed. Please try again.',
        SESSION_EXPIRED: 'Your session has expired. Please log in again.',
        TOKEN_REFRESH_FAILED: 'Failed to refresh authentication token.',
        PROFILE_UPDATE_FAILED: 'Failed to update profile. Please try again.',
        UNAUTHORIZED: 'You are not authorized to perform this action.'
    },
    APP: {
        SETTINGS_SAVE_FAILED: 'Failed to save settings. Please try again.',
        THEME_LOAD_FAILED: 'Failed to load theme configuration.',
        NETWORK_ERROR: 'Network error. Please check your connection.',
        UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.'
    }
};
const SUCCESS_MESSAGES = {
    AUTH: {
        LOGIN_SUCCESS: 'Successfully logged in.',
        LOGOUT_SUCCESS: 'Successfully logged out.',
        PROFILE_UPDATED: 'Profile updated successfully.'
    },
    APP: {
        SETTINGS_SAVED: 'Settings saved successfully.',
        THEME_UPDATED: 'Theme updated successfully.'
    }
};
const STORE_VERSIONS = {
    AUTH: 1,
    APP: 1
};
const DEV_CONFIG = {
    ENABLE_DEVTOOLS: ("TURBOPACK compile-time value", "development") === 'development',
    ENABLE_LOGGING: ("TURBOPACK compile-time value", "development") === 'development',
    MOCK_API_DELAY: 1000
};
const FEATURE_FLAGS = {
    ENABLE_DARK_MODE: true,
    ENABLE_NOTIFICATIONS: true,
    ENABLE_AUTO_SAVE: true,
    ENABLE_ADVANCED_FILTERS: true,
    ENABLE_REAL_TIME_UPDATES: false,
    ENABLE_OFFLINE_MODE: false
};
const VALIDATION_RULES = {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    PASSWORD_MIN_LENGTH: 8,
    NAME_MIN_LENGTH: 2,
    NAME_MAX_LENGTH: 50,
    PAGE_SIZE_MIN: 5,
    PAGE_SIZE_MAX: 100,
    REFRESH_INTERVAL_MIN: 10,
    REFRESH_INTERVAL_MAX: 300
};
}}),
"[project]/src/stores/auth-store.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Authentication Store
 * Manages user authentication state, tokens, and session
 */ __turbopack_esm__({
    "createAuthStore": (()=>createAuthStore),
    "useAuthStore": (()=>useAuthStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/constants.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
;
;
;
// ============================================================================
// Initial State
// ============================================================================
const initialAuthState = {
    // Base store state
    _hasHydrated: false,
    // User data
    user: null,
    tokens: null,
    // Authentication status
    isAuthenticated: false,
    isLoading: false,
    // Error handling
    error: null,
    // Session management
    lastActivity: Date.now(),
    sessionTimeout: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SESSION_TIMEOUT"]
};
// ============================================================================
// Store Implementation
// ============================================================================
/**
 * Authentication Store Creator
 */ const createAuthStore = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createStoreWithMiddleware"])((set, get)=>({
            ...initialAuthState,
            // Base store actions
            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createBaseStoreActions"])(set),
            // ========================================================================
            // Authentication Actions
            // ========================================================================
            /**
         * Login user with email and password
         */ login: async (email, password)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'login', {
                    email
                });
                set({
                    isLoading: true,
                    error: null
                });
                try {
                    const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.LOGIN, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            email,
                            password
                        })
                    });
                    if (!response.ok) {
                        const errorData = await response.json().catch(()=>({}));
                        throw new Error(errorData.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.LOGIN_FAILED);
                    }
                    const data = await response.json();
                    if (data.success && data.data) {
                        const { user, accessToken, refreshToken } = data.data;
                        // Create tokens object
                        const tokens = {
                            accessToken,
                            refreshToken,
                            expiresAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTokenExpiration"])(accessToken)
                        };
                        // Update store state
                        set({
                            user,
                            tokens,
                            isAuthenticated: true,
                            isLoading: false,
                            error: null,
                            lastActivity: Date.now()
                        });
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'login_success', {
                            userId: user.id
                        });
                    } else {
                        throw new Error(data.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.LOGIN_FAILED);
                    }
                } catch (error) {
                    const errorMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'login_error', {
                        error: errorMessage
                    });
                    set({
                        isLoading: false,
                        error: errorMessage,
                        isAuthenticated: false,
                        user: null,
                        tokens: null
                    });
                    throw error;
                }
            },
            /**
         * Logout user from current session
         */ logout: async ()=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout');
                const { tokens } = get();
                try {
                    // Call logout API if we have tokens
                    if (tokens?.accessToken) {
                        await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.LOGOUT, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${tokens.accessToken}`
                            }
                        });
                    }
                } catch (error) {
                    // Log error but don't prevent logout
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_api_error', {
                        error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error)
                    });
                }
                // Clear local state regardless of API call result
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clearStoredTokens"])();
                set({
                    user: null,
                    tokens: null,
                    isAuthenticated: false,
                    error: null,
                    lastActivity: Date.now()
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_success');
            },
            /**
         * Logout user from all devices
         */ logoutAll: async ()=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_all');
                const { tokens } = get();
                try {
                    if (tokens?.accessToken) {
                        await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.LOGOUT_ALL, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${tokens.accessToken}`
                            }
                        });
                    }
                } catch (error) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_all_api_error', {
                        error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error)
                    });
                }
                // Clear local state
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clearStoredTokens"])();
                set({
                    user: null,
                    tokens: null,
                    isAuthenticated: false,
                    error: null,
                    lastActivity: Date.now()
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_all_success');
            },
            // ========================================================================
            // User Profile Actions
            // ========================================================================
            /**
         * Update user profile
         */ updateProfile: async (data)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'update_profile', data);
                const { tokens, user } = get();
                if (!tokens?.accessToken || !user) {
                    throw new Error(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.UNAUTHORIZED);
                }
                set({
                    isLoading: true,
                    error: null
                });
                try {
                    const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.PROFILE, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${tokens.accessToken}`
                        },
                        body: JSON.stringify(data)
                    });
                    if (!response.ok) {
                        const errorData = await response.json().catch(()=>({}));
                        throw new Error(errorData.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.PROFILE_UPDATE_FAILED);
                    }
                    const responseData = await response.json();
                    if (responseData.success && responseData.data) {
                        set({
                            user: {
                                ...user,
                                ...responseData.data
                            },
                            isLoading: false,
                            error: null,
                            lastActivity: Date.now()
                        });
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'update_profile_success');
                    } else {
                        throw new Error(responseData.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.PROFILE_UPDATE_FAILED);
                    }
                } catch (error) {
                    const errorMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'update_profile_error', {
                        error: errorMessage
                    });
                    set({
                        isLoading: false,
                        error: errorMessage
                    });
                    throw error;
                }
            },
            /**
         * Refresh authentication tokens
         */ refreshTokens: async ()=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'refresh_tokens');
                const { tokens } = get();
                if (!tokens?.refreshToken) {
                    throw new Error(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.TOKEN_REFRESH_FAILED);
                }
                try {
                    const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.REFRESH, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            refreshToken: tokens.refreshToken
                        })
                    });
                    if (!response.ok) {
                        throw new Error(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.TOKEN_REFRESH_FAILED);
                    }
                    const data = await response.json();
                    if (data.success && data.data) {
                        const { accessToken, refreshToken } = data.data;
                        const newTokens = {
                            accessToken,
                            refreshToken,
                            expiresAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTokenExpiration"])(accessToken)
                        };
                        set({
                            tokens: newTokens,
                            lastActivity: Date.now()
                        });
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'refresh_tokens_success');
                    } else {
                        throw new Error(data.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.TOKEN_REFRESH_FAILED);
                    }
                } catch (error) {
                    const errorMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'refresh_tokens_error', {
                        error: errorMessage
                    });
                    // If refresh fails, logout user
                    get().logout();
                    throw error;
                }
            },
            // ========================================================================
            // State Management Actions
            // ========================================================================
            /**
         * Set user data
         */ setUser: (user)=>{
                set({
                    user,
                    isAuthenticated: !!user
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'set_user', {
                    userId: user?.id
                });
            },
            /**
         * Set authentication tokens
         */ setTokens: (tokens)=>{
                set({
                    tokens
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'set_tokens', {
                    hasTokens: !!tokens
                });
            },
            /**
         * Set loading state
         */ setLoading: (loading)=>{
                set({
                    isLoading: loading
                });
            },
            /**
         * Set error message
         */ setError: (error)=>{
                set({
                    error
                });
                if (error) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'set_error', {
                        error
                    });
                }
            },
            /**
         * Clear error message
         */ clearError: ()=>{
                set({
                    error: null
                });
            },
            // ========================================================================
            // Session Management Actions
            // ========================================================================
            /**
         * Update last activity timestamp
         */ updateLastActivity: ()=>{
                set({
                    lastActivity: Date.now()
                });
            },
            /**
         * Check if current session is valid
         */ checkSession: ()=>{
                const { lastActivity, sessionTimeout, tokens } = get();
                // Check session timeout
                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isSessionValid"])(lastActivity, sessionTimeout)) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'session_expired');
                    get().logout();
                    return false;
                }
                // Check token expiration
                if (tokens && (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTokenExpired"])(tokens.expiresAt)) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'token_expired');
                    // Try to refresh tokens
                    get().refreshTokens().catch(()=>{
                    // If refresh fails, logout will be called automatically
                    });
                    return false;
                }
                return true;
            },
            /**
         * Hydrate store from persisted state
         */ hydrate: ()=>{
                const state = get();
                // Validate persisted session
                if (state.isAuthenticated && state.user && state.tokens) {
                    const isValid = state.checkSession();
                    if (!isValid) {
                        // Session is invalid, clear state
                        set({
                            user: null,
                            tokens: null,
                            isAuthenticated: false,
                            error: null
                        });
                    }
                }
                set({
                    _hasHydrated: true
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'hydrated');
            }
        }), {
        persist: {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].AUTH,
            version: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_VERSIONS"].AUTH,
            partialize: (state)=>({
                    user: state.user,
                    tokens: state.tokens,
                    isAuthenticated: state.isAuthenticated,
                    lastActivity: state.lastActivity,
                    sessionTimeout: state.sessionTimeout
                })
        },
        devtools: {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH,
            enabled: ("TURBOPACK compile-time value", "development") === 'development'
        }
    }));
};
const useAuthStore = createAuthStore();
;
}}),
"[project]/src/stores/auth-hooks.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Authentication Hooks
 * Custom hooks for easy authentication state access
 */ __turbopack_esm__({
    "useAuth": (()=>useAuth),
    "useAuthDebug": (()=>useAuthDebug),
    "useAuthError": (()=>useAuthError),
    "useAuthLoading": (()=>useAuthLoading),
    "useAuthTokens": (()=>useAuthTokens),
    "useAuthWithSession": (()=>useAuthWithSession),
    "useCanAdmin": (()=>useCanAdmin),
    "useCanEdit": (()=>useCanEdit),
    "useCheckSession": (()=>useCheckSession),
    "useClearAuthError": (()=>useClearAuthError),
    "useHasRole": (()=>useHasRole),
    "useIsAdmin": (()=>useIsAdmin),
    "useIsAuthenticated": (()=>useIsAuthenticated),
    "useIsEditor": (()=>useIsEditor),
    "useIsModerator": (()=>useIsModerator),
    "useLogin": (()=>useLogin),
    "useLogout": (()=>useLogout),
    "useLogoutAll": (()=>useLogoutAll),
    "usePermissions": (()=>usePermissions),
    "useRefreshTokens": (()=>useRefreshTokens),
    "useRouteProtection": (()=>useRouteProtection),
    "useUpdateActivity": (()=>useUpdateActivity),
    "useUpdateProfile": (()=>useUpdateProfile),
    "useUser": (()=>useUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-store.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/constants.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const useUser = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"])((state)=>state.user);
};
const useIsAuthenticated = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"])((state)=>state.isAuthenticated);
};
const useAuthLoading = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"])((state)=>state.isLoading);
};
const useAuthError = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"])((state)=>state.error);
};
const useAuthTokens = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"])((state)=>state.tokens);
};
const useLogin = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"])((state)=>state.login);
};
const useLogout = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"])((state)=>state.logout);
};
const useLogoutAll = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"])((state)=>state.logoutAll);
};
const useUpdateProfile = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"])((state)=>state.updateProfile);
};
const useRefreshTokens = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"])((state)=>state.refreshTokens);
};
const useClearAuthError = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"])((state)=>state.clearError);
};
const useCheckSession = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"])((state)=>state.checkSession);
};
const useUpdateActivity = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"])((state)=>state.updateLastActivity);
};
const useHasRole = (role)=>{
    const user = useUser();
    return user?.role === role;
};
const useIsAdmin = ()=>{
    return useHasRole('Admin');
};
const useIsEditor = ()=>{
    return useHasRole('Editor');
};
const useIsModerator = ()=>{
    return useHasRole('Moderator');
};
const useCanEdit = ()=>{
    const user = useUser();
    return user?.role === 'Admin' || user?.role === 'Editor';
};
const useCanAdmin = ()=>{
    return useIsAdmin();
};
const useAuth = ()=>{
    const user = useUser();
    const isAuthenticated = useIsAuthenticated();
    const isLoading = useAuthLoading();
    const error = useAuthError();
    const tokens = useAuthTokens();
    const login = useLogin();
    const logout = useLogout();
    const logoutAll = useLogoutAll();
    const updateProfile = useUpdateProfile();
    const clearError = useClearAuthError();
    return {
        // State
        user,
        isAuthenticated,
        isLoading,
        error,
        tokens,
        // Actions
        login,
        logout,
        logoutAll,
        updateProfile,
        clearError,
        // Role checks
        isAdmin: useIsAdmin(),
        isEditor: useIsEditor(),
        isModerator: useIsModerator(),
        canEdit: useCanEdit(),
        canAdmin: useCanAdmin()
    };
};
const useAuthWithSession = ()=>{
    const auth = useAuth();
    const checkSession = useCheckSession();
    const updateActivity = useUpdateActivity();
    // Auto-check session validity
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (auth.isAuthenticated) {
            const interval = setInterval(()=>{
                checkSession();
            }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ACTIVITY_TRACKING_INTERVAL"]);
            return ()=>clearInterval(interval);
        }
    }, [
        auth.isAuthenticated,
        checkSession
    ]);
    // Update activity on user interaction
    const handleUserActivity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (auth.isAuthenticated) {
            updateActivity();
        }
    }, [
        auth.isAuthenticated,
        updateActivity
    ]);
    // Auto-update activity on mouse/keyboard events
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (auth.isAuthenticated) {
            const events = [
                'mousedown',
                'mousemove',
                'keypress',
                'scroll',
                'touchstart'
            ];
            events.forEach((event)=>{
                document.addEventListener(event, handleUserActivity, true);
            });
            return ()=>{
                events.forEach((event)=>{
                    document.removeEventListener(event, handleUserActivity, true);
                });
            };
        }
    }, [
        auth.isAuthenticated,
        handleUserActivity
    ]);
    return {
        ...auth,
        checkSession,
        updateActivity
    };
};
const usePermissions = (requiredRoles)=>{
    const user = useUser();
    const hasPermission = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((roles)=>{
        if (!user) return false;
        return roles.includes(user.role);
    }, [
        user
    ]);
    const hasAnyPermission = hasPermission(requiredRoles);
    return {
        hasPermission: hasAnyPermission,
        userRole: user?.role,
        checkRole: hasPermission
    };
};
const useRouteProtection = (requiredRoles)=>{
    const isAuthenticated = useIsAuthenticated();
    const user = useUser();
    const isLoading = useAuthLoading();
    const hasAccess = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (!isAuthenticated) return false;
        if (!requiredRoles || requiredRoles.length === 0) return true;
        if (!user) return false;
        return requiredRoles.includes(user.role);
    }, [
        isAuthenticated,
        user,
        requiredRoles
    ]);
    return {
        isAuthenticated,
        hasAccess: hasAccess(),
        isLoading,
        user,
        shouldRedirect: !isLoading && !isAuthenticated,
        shouldShowUnauthorized: !isLoading && isAuthenticated && !hasAccess()
    };
};
const useAuthDebug = ()=>{
    const state = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"])((state)=>state);
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return {
        fullState: state,
        hasHydrated: state._hasHydrated,
        lastActivity: new Date(state.lastActivity).toISOString(),
        sessionTimeout: state.sessionTimeout,
        tokenExpiry: state.tokens ? new Date(state.tokens.expiresAt).toISOString() : null
    };
};
}}),
"[project]/src/stores/app-store.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Application Store
 * Manages application state, theme, settings, navigation, and UI state
 */ __turbopack_esm__({
    "createAppStore": (()=>createAppStore),
    "useAppStore": (()=>useAppStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/constants.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
;
;
;
// ============================================================================
// Initial State
// ============================================================================
const initialAppState = {
    // Base store state
    _hasHydrated: false,
    // Configuration
    theme: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_THEME"],
    settings: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_APP_SETTINGS"],
    // Navigation
    navigation: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_NAVIGATION"],
    // UI state
    ui: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_UI_STATE"],
    // System info
    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
    buildTime: process.env.NEXT_PUBLIC_BUILD_TIME || new Date().toISOString(),
    environment: ("TURBOPACK compile-time value", "development") || 'development'
};
// ============================================================================
// Store Implementation
// ============================================================================
/**
 * Application Store Creator
 */ const createAppStore = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createStoreWithMiddleware"])((set, get)=>({
            ...initialAppState,
            // Base store actions
            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createBaseStoreActions"])(set),
            // ========================================================================
            // Theme Management Actions
            // ========================================================================
            /**
         * Set theme configuration
         */ setTheme: (themeUpdate)=>{
                const currentTheme = get().theme;
                const newTheme = {
                    ...currentTheme,
                    ...themeUpdate
                };
                set({
                    theme: newTheme
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_theme', themeUpdate);
            },
            /**
         * Toggle between light and dark mode
         */ toggleTheme: ()=>{
                const currentMode = get().theme.mode;
                const newMode = currentMode === 'light' ? 'dark' : 'light';
                get().setTheme({
                    mode: newMode
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'toggle_theme', {
                    newMode
                });
            },
            // ========================================================================
            // Settings Management Actions
            // ========================================================================
            /**
         * Update application settings
         */ updateSettings: (settingsUpdate)=>{
                const currentSettings = get().settings;
                const newSettings = {
                    ...currentSettings,
                    ...settingsUpdate
                };
                set({
                    settings: newSettings
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'update_settings', settingsUpdate);
            },
            /**
         * Reset settings to default values
         */ resetSettings: ()=>{
                set({
                    settings: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_APP_SETTINGS"]
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'reset_settings');
            },
            // ========================================================================
            // Navigation Actions
            // ========================================================================
            /**
         * Set current path
         */ setCurrentPath: (path)=>{
                const currentNavigation = get().navigation;
                const newNavigation = {
                    ...currentNavigation,
                    currentPath: path
                };
                set({
                    navigation: newNavigation
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_current_path', {
                    path
                });
            },
            /**
         * Set breadcrumbs
         */ setBreadcrumbs: (breadcrumbs)=>{
                const currentNavigation = get().navigation;
                const newNavigation = {
                    ...currentNavigation,
                    breadcrumbs
                };
                set({
                    navigation: newNavigation
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_breadcrumbs', {
                    count: breadcrumbs.length
                });
            },
            /**
         * Toggle sidebar collapsed state
         */ toggleSidebar: ()=>{
                const currentNavigation = get().navigation;
                const newCollapsed = !currentNavigation.sidebarCollapsed;
                const newNavigation = {
                    ...currentNavigation,
                    sidebarCollapsed: newCollapsed
                };
                set({
                    navigation: newNavigation
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'toggle_sidebar', {
                    collapsed: newCollapsed
                });
            },
            /**
         * Set active menu key
         */ setActiveMenu: (key)=>{
                const currentNavigation = get().navigation;
                const newNavigation = {
                    ...currentNavigation,
                    activeMenuKey: key
                };
                set({
                    navigation: newNavigation
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_active_menu', {
                    key
                });
            },
            // ========================================================================
            // UI State Management Actions
            // ========================================================================
            /**
         * Set global loading state
         */ setGlobalLoading: (loading, message)=>{
                const currentUI = get().ui;
                const newUI = {
                    ...currentUI,
                    globalLoading: loading,
                    loadingMessage: message || ''
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_global_loading', {
                    loading,
                    message
                });
            },
            /**
         * Set global error
         */ setGlobalError: (error, details)=>{
                const currentUI = get().ui;
                const newUI = {
                    ...currentUI,
                    globalError: error,
                    errorDetails: details || null
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_global_error', {
                    error,
                    hasDetails: !!details
                });
            },
            /**
         * Clear global error
         */ clearGlobalError: ()=>{
                const currentUI = get().ui;
                const newUI = {
                    ...currentUI,
                    globalError: null,
                    errorDetails: null
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'clear_global_error');
            },
            // ========================================================================
            // Notifications Actions
            // ========================================================================
            /**
         * Add notification
         */ addNotification: (notification)=>{
                const id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generateNotificationId"])();
                const timestamp = Date.now();
                const duration = notification.duration || (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDefaultNotificationDuration"])(notification.type);
                const newNotification = {
                    ...notification,
                    id,
                    timestamp,
                    duration
                };
                const currentUI = get().ui;
                const newNotifications = [
                    ...currentUI.notifications,
                    newNotification
                ];
                const newUI = {
                    ...currentUI,
                    notifications: newNotifications
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'add_notification', {
                    type: notification.type,
                    id
                });
                // Auto-remove notification after duration
                if (duration > 0) {
                    setTimeout(()=>{
                        get().removeNotification(id);
                    }, duration);
                }
            },
            /**
         * Remove notification
         */ removeNotification: (id)=>{
                const currentUI = get().ui;
                const newNotifications = currentUI.notifications.filter((n)=>n.id !== id);
                const newUI = {
                    ...currentUI,
                    notifications: newNotifications
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'remove_notification', {
                    id
                });
            },
            /**
         * Clear all notifications
         */ clearNotifications: ()=>{
                const currentUI = get().ui;
                const newUI = {
                    ...currentUI,
                    notifications: []
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'clear_notifications');
            },
            // ========================================================================
            // Modals Actions
            // ========================================================================
            /**
         * Show modal
         */ showModal: (key, data)=>{
                const currentUI = get().ui;
                const newModals = {
                    ...currentUI.modals,
                    [key]: {
                        visible: true,
                        data
                    }
                };
                const newUI = {
                    ...currentUI,
                    modals: newModals
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'show_modal', {
                    key,
                    hasData: !!data
                });
            },
            /**
         * Hide modal
         */ hideModal: (key)=>{
                const currentUI = get().ui;
                const newModals = {
                    ...currentUI.modals,
                    [key]: {
                        visible: false,
                        data: undefined
                    }
                };
                const newUI = {
                    ...currentUI,
                    modals: newModals
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'hide_modal', {
                    key
                });
            },
            /**
         * Hide all modals
         */ hideAllModals: ()=>{
                const currentUI = get().ui;
                const newModals = {};
                // Set all modals to hidden
                Object.keys(currentUI.modals).forEach((key)=>{
                    newModals[key] = {
                        visible: false,
                        data: undefined
                    };
                });
                const newUI = {
                    ...currentUI,
                    modals: newModals
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'hide_all_modals');
            }
        }), {
        persist: {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].APP,
            version: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_VERSIONS"].APP,
            partialize: (state)=>({
                    theme: state.theme,
                    settings: state.settings,
                    navigation: {
                        sidebarCollapsed: state.navigation.sidebarCollapsed,
                        activeMenuKey: state.navigation.activeMenuKey
                    }
                })
        },
        devtools: {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP,
            enabled: ("TURBOPACK compile-time value", "development") === 'development'
        }
    }));
};
const useAppStore = createAppStore();
;
}}),
"[project]/src/stores/app-hooks.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Application Hooks
 * Custom hooks for easy application state access
 */ __turbopack_esm__({
    "useActiveMenu": (()=>useActiveMenu),
    "useApp": (()=>useApp),
    "useAppSettings": (()=>useAppSettings),
    "useAppVersion": (()=>useAppVersion),
    "useBreadcrumbs": (()=>useBreadcrumbs),
    "useBuildTime": (()=>useBuildTime),
    "useCurrentPath": (()=>useCurrentPath),
    "useEnvironment": (()=>useEnvironment),
    "useGlobalError": (()=>useGlobalError),
    "useGlobalLoading": (()=>useGlobalLoading),
    "useIsDarkMode": (()=>useIsDarkMode),
    "useModal": (()=>useModal),
    "useModalActions": (()=>useModalActions),
    "useModals": (()=>useModals),
    "useNavigation": (()=>useNavigation),
    "useNavigationActions": (()=>useNavigationActions),
    "useNotificationActions": (()=>useNotificationActions),
    "useNotifications": (()=>useNotifications),
    "useNotify": (()=>useNotify),
    "useResponsive": (()=>useResponsive),
    "useSetting": (()=>useSetting),
    "useSettingsActions": (()=>useSettingsActions),
    "useSidebarState": (()=>useSidebarState),
    "useSystemInfo": (()=>useSystemInfo),
    "useTheme": (()=>useTheme),
    "useThemeActions": (()=>useThemeActions),
    "useThemeMode": (()=>useThemeMode),
    "useUIActions": (()=>useUIActions),
    "useUIState": (()=>useUIState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-store.ts [app-ssr] (ecmascript)");
'use client';
;
;
const useTheme = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.theme);
};
const useThemeMode = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.theme.mode);
};
const useIsDarkMode = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.theme.mode === 'dark');
};
const useThemeActions = ()=>{
    const setTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.setTheme);
    const toggleTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.toggleTheme);
    return {
        setTheme,
        toggleTheme
    };
};
const useAppSettings = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.settings);
};
const useSettingsActions = ()=>{
    const updateSettings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.updateSettings);
    const resetSettings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.resetSettings);
    return {
        updateSettings,
        resetSettings
    };
};
const useSetting = (key)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.settings[key]);
};
const useNavigation = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.navigation);
};
const useCurrentPath = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.navigation.currentPath);
};
const useBreadcrumbs = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.navigation.breadcrumbs);
};
const useSidebarState = ()=>{
    const collapsed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.navigation.sidebarCollapsed);
    const toggleSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.toggleSidebar);
    return {
        collapsed,
        toggleSidebar
    };
};
const useActiveMenu = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.navigation.activeMenuKey);
};
const useNavigationActions = ()=>{
    const setCurrentPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.setCurrentPath);
    const setBreadcrumbs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.setBreadcrumbs);
    const toggleSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.toggleSidebar);
    const setActiveMenu = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.setActiveMenu);
    return {
        setCurrentPath,
        setBreadcrumbs,
        toggleSidebar,
        setActiveMenu
    };
};
const useUIState = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.ui);
};
const useGlobalLoading = ()=>{
    const loading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.ui.globalLoading);
    const message = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.ui.loadingMessage);
    return {
        loading,
        message
    };
};
const useGlobalError = ()=>{
    const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.ui.globalError);
    const details = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.ui.errorDetails);
    return {
        error,
        details
    };
};
const useUIActions = ()=>{
    const setGlobalLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.setGlobalLoading);
    const setGlobalError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.setGlobalError);
    const clearGlobalError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.clearGlobalError);
    return {
        setGlobalLoading,
        setGlobalError,
        clearGlobalError
    };
};
const useNotifications = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.ui.notifications);
};
const useNotificationActions = ()=>{
    const addNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.addNotification);
    const removeNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.removeNotification);
    const clearNotifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.clearNotifications);
    return {
        addNotification,
        removeNotification,
        clearNotifications
    };
};
const useNotify = ()=>{
    const addNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.addNotification);
    const notify = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>({
            success: (message, title)=>{
                addNotification({
                    type: 'success',
                    title: title || 'Success',
                    message
                });
            },
            error: (message, title)=>{
                addNotification({
                    type: 'error',
                    title: title || 'Error',
                    message
                });
            },
            warning: (message, title)=>{
                addNotification({
                    type: 'warning',
                    title: title || 'Warning',
                    message
                });
            },
            info: (message, title)=>{
                addNotification({
                    type: 'info',
                    title: title || 'Info',
                    message
                });
            }
        }), [
        addNotification
    ]);
    return notify();
};
const useModals = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.ui.modals);
};
const useModal = (key)=>{
    const modal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.ui.modals[key]);
    const showModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.showModal);
    const hideModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.hideModal);
    const show = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((data)=>{
        showModal(key, data);
    }, [
        showModal,
        key
    ]);
    const hide = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        hideModal(key);
    }, [
        hideModal,
        key
    ]);
    return {
        visible: modal?.visible || false,
        data: modal?.data,
        show,
        hide
    };
};
const useModalActions = ()=>{
    const showModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.showModal);
    const hideModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.hideModal);
    const hideAllModals = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.hideAllModals);
    return {
        showModal,
        hideModal,
        hideAllModals
    };
};
const useAppVersion = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.version);
};
const useBuildTime = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.buildTime);
};
const useEnvironment = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.environment);
};
const useSystemInfo = ()=>{
    const version = useAppVersion();
    const buildTime = useBuildTime();
    const environment = useEnvironment();
    return {
        version,
        buildTime,
        environment,
        isDevelopment: environment === 'development',
        isProduction: environment === 'production'
    };
};
const useApp = ()=>{
    const theme = useTheme();
    const settings = useAppSettings();
    const navigation = useNavigation();
    const ui = useUIState();
    const systemInfo = useSystemInfo();
    const themeActions = useThemeActions();
    const settingsActions = useSettingsActions();
    const navigationActions = useNavigationActions();
    const uiActions = useUIActions();
    const notificationActions = useNotificationActions();
    const modalActions = useModalActions();
    return {
        // State
        theme,
        settings,
        navigation,
        ui,
        systemInfo,
        // Actions
        ...themeActions,
        ...settingsActions,
        ...navigationActions,
        ...uiActions,
        ...notificationActions,
        ...modalActions
    };
};
const useResponsive = ()=>{
    const sidebarCollapsed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.navigation.sidebarCollapsed);
    const toggleSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.toggleSidebar);
    // Auto-collapse sidebar on mobile
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleResize = ()=>{
            const isMobile = window.innerWidth < 768;
            if (isMobile && !sidebarCollapsed) {
                toggleSidebar();
            }
        };
        window.addEventListener('resize', handleResize);
        handleResize(); // Check on mount
        return ()=>window.removeEventListener('resize', handleResize);
    }, [
        sidebarCollapsed,
        toggleSidebar
    ]);
    return {
        isMobile: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : false,
        isTablet: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : false,
        isDesktop: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : false
    };
};
}}),
"[project]/src/stores/store-context.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Context - React context for accessing stores
 * Provides centralized access to all Zustand stores
 */ __turbopack_esm__({
    "StoreContextProvider": (()=>StoreContextProvider),
    "useAppStoreContext": (()=>useAppStoreContext),
    "useAuthStoreContext": (()=>useAuthStoreContext),
    "useIsStoreContextAvailable": (()=>useIsStoreContextAvailable),
    "useStoreContext": (()=>useStoreContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-hooks.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
/**
 * Store context
 */ const StoreContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(null);
function StoreContextProvider({ children }) {
    // Get store instances
    const authStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const appStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApp"])();
    const contextValue = {
        authStore,
        appStore
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(StoreContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/stores/store-context.tsx",
        lineNumber: 48,
        columnNumber: 5
    }, this);
}
function useStoreContext() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(StoreContext);
    if (!context) {
        throw new Error('useStoreContext must be used within a StoreContextProvider');
    }
    return context;
}
function useAuthStoreContext() {
    const { authStore } = useStoreContext();
    return authStore;
}
function useAppStoreContext() {
    const { appStore } = useStoreContext();
    return appStore;
}
function useIsStoreContextAvailable() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(StoreContext);
    return context !== null;
}
}}),
"[project]/src/stores/store-provider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Provider - Main provider component for all stores
 * Wraps the application with necessary store providers
 */ __turbopack_esm__({
    "StoreProvider": (()=>StoreProvider),
    "StoreProviderUtils": (()=>StoreProviderUtils),
    "withStoreProvider": (()=>withStoreProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/store-context.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/constants.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
/**
 * Store initialization component
 * Handles store initialization and hydration
 */ function StoreInitializer({ children }) {
    const authStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const appStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApp"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Initialize stores on mount
        const initializeStores = async ()=>{
            try {
                console.log('✅ Stores initialized successfully');
            } catch (error) {
                console.error('❌ Failed to initialize stores:', error);
            }
        };
        initializeStores();
    }, []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
function StoreProvider({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StoreContextProvider"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(StoreInitializer, {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/stores/store-provider.tsx",
            lineNumber: 52,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/stores/store-provider.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
}
function withStoreProvider(Component) {
    const WrappedComponent = (props)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(StoreProvider, {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
                ...props
            }, void 0, false, {
                fileName: "[project]/src/stores/store-provider.tsx",
                lineNumber: 67,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/stores/store-provider.tsx",
            lineNumber: 66,
            columnNumber: 5
        }, this);
    WrappedComponent.displayName = `withStoreProvider(${Component.displayName || Component.name})`;
    return WrappedComponent;
}
const StoreProviderUtils = {
    /**
   * Check if stores are properly initialized
   */ checkStoreInitialization: ()=>{
        try {
            return {
                auth: true,
                app: true,
                all: true
            };
        } catch (error) {
            console.error('Failed to check store initialization:', error);
            return {
                auth: false,
                app: false,
                all: false
            };
        }
    },
    /**
   * Reset all stores to initial state
   */ resetAllStores: ()=>{
        try {
            console.log('✅ All stores reset successfully');
        } catch (error) {
            console.error('❌ Failed to reset stores:', error);
        }
    },
    /**
   * Clear all persisted store data
   */ clearPersistedData: ()=>{
        try {
            Object.values(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"]).forEach((key)=>{
                localStorage.removeItem(key);
            });
            console.log('✅ All persisted store data cleared');
        } catch (error) {
            console.error('❌ Failed to clear persisted data:', error);
        }
    }
};
}}),
"[project]/src/stores/provider-hooks.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Provider Hooks - Custom hooks for using stores with providers
 * Provides convenient access to stores through context
 */ __turbopack_esm__({
    "useAppProvider": (()=>useAppProvider),
    "useAuthProvider": (()=>useAuthProvider),
    "useStoreAvailability": (()=>useStoreAvailability),
    "useStoreDebug": (()=>useStoreDebug),
    "useStores": (()=>useStores)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/store-context.tsx [app-ssr] (ecmascript)");
'use client';
;
;
function useAuthProvider() {
    const authStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStoreContext"])();
    const login = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (credentials)=>{
        return authStore.login(credentials);
    }, [
        authStore
    ]);
    const logout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        return authStore.logout();
    }, [
        authStore
    ]);
    const logoutAll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        return authStore.logoutAll();
    }, [
        authStore
    ]);
    const refreshToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        return authStore.refreshToken();
    }, [
        authStore
    ]);
    const updateProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (data)=>{
        return authStore.updateProfile(data);
    }, [
        authStore
    ]);
    const changePassword = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (data)=>{
        return authStore.changePassword(data);
    }, [
        authStore
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            // State
            user: authStore.user,
            token: authStore.token,
            refreshToken: authStore.refreshToken,
            isAuthenticated: authStore.isAuthenticated,
            isLoading: authStore.isLoading,
            error: authStore.error,
            isInitialized: authStore.isInitialized,
            // Actions
            login,
            logout,
            logoutAll,
            refreshToken: refreshToken,
            updateProfile,
            changePassword,
            clearError: authStore.clearError,
            reset: authStore.reset
        }), [
        authStore.user,
        authStore.token,
        authStore.refreshToken,
        authStore.isAuthenticated,
        authStore.isLoading,
        authStore.error,
        authStore.isInitialized,
        login,
        logout,
        logoutAll,
        refreshToken,
        updateProfile,
        changePassword,
        authStore.clearError,
        authStore.reset
    ]);
}
function useAppProvider() {
    const appStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStoreContext"])();
    const setTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((theme)=>{
        appStore.setTheme(theme);
    }, [
        appStore
    ]);
    const toggleTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        appStore.toggleTheme();
    }, [
        appStore
    ]);
    const setLanguage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((language)=>{
        appStore.setLanguage(language);
    }, [
        appStore
    ]);
    const setSidebarCollapsed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((collapsed)=>{
        appStore.setSidebarCollapsed(collapsed);
    }, [
        appStore
    ]);
    const toggleSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        appStore.toggleSidebar();
    }, [
        appStore
    ]);
    const setLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((loading)=>{
        appStore.setLoading(loading);
    }, [
        appStore
    ]);
    const showNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((notification)=>{
        appStore.showNotification(notification);
    }, [
        appStore
    ]);
    const hideNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        appStore.hideNotification();
    }, [
        appStore
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            // State
            theme: appStore.theme,
            language: appStore.language,
            sidebarCollapsed: appStore.sidebarCollapsed,
            isLoading: appStore.isLoading,
            notification: appStore.notification,
            isInitialized: appStore.isInitialized,
            // Actions
            setTheme,
            toggleTheme,
            setLanguage,
            setSidebarCollapsed,
            toggleSidebar,
            setLoading,
            showNotification,
            hideNotification,
            reset: appStore.reset
        }), [
        appStore.theme,
        appStore.language,
        appStore.sidebarCollapsed,
        appStore.isLoading,
        appStore.notification,
        appStore.isInitialized,
        setTheme,
        toggleTheme,
        setLanguage,
        setSidebarCollapsed,
        toggleSidebar,
        setLoading,
        showNotification,
        hideNotification,
        appStore.reset
    ]);
}
function useStoreAvailability() {
    const isAvailable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsStoreContextAvailable"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            isAvailable,
            isStoreReady: isAvailable
        }), [
        isAvailable
    ]);
}
function useStores() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useStoreContext"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            authStore: context.authStore,
            appStore: context.appStore
        }), [
        context.authStore,
        context.appStore
    ]);
}
function useStoreDebug() {
    const { authStore, appStore } = useStores();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            authState: {
                user: authStore.user,
                isAuthenticated: authStore.isAuthenticated,
                isLoading: authStore.isLoading,
                error: authStore.error,
                isInitialized: authStore.isInitialized
            },
            appState: {
                theme: appStore.theme,
                language: appStore.language,
                sidebarCollapsed: appStore.sidebarCollapsed,
                isLoading: appStore.isLoading,
                notification: appStore.notification,
                isInitialized: appStore.isInitialized
            },
            actions: {
                resetAuth: authStore.reset,
                resetApp: appStore.reset,
                clearAuthError: authStore.clearError,
                hideNotification: appStore.hideNotification
            }
        }), [
        authStore,
        appStore
    ]);
}
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component, client modules ssr)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
"use strict";
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        if ("TURBOPACK compile-time truthy", 1) {
            module.exports = __turbopack_require__("[externals]/next/dist/compiled/next-server/app-page.runtime.dev.js [external] (next/dist/compiled/next-server/app-page.runtime.dev.js, cjs)");
        } else {
            "TURBOPACK unreachable";
        }
    }
} //# sourceMappingURL=module.compiled.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_require__("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].React; //# sourceMappingURL=react.js.map
}}),
"[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "combine": (()=>combine),
    "createJSONStorage": (()=>createJSONStorage),
    "devtools": (()=>devtools),
    "persist": (()=>persist),
    "redux": (()=>redux),
    "subscribeWithSelector": (()=>subscribeWithSelector)
});
const __TURBOPACK__import$2e$meta__ = {
    get url () {
        return `file://${__turbopack_resolve_absolute_path__("node_modules/zustand/esm/middleware.mjs")}`;
    }
};
const reduxImpl = (reducer, initial)=>(set, _get, api)=>{
        api.dispatch = (action)=>{
            set((state)=>reducer(state, action), false, action);
            return action;
        };
        api.dispatchFromDevtools = true;
        return {
            dispatch: (...args)=>api.dispatch(...args),
            ...initial
        };
    };
const redux = reduxImpl;
const trackedConnections = /* @__PURE__ */ new Map();
const getTrackedConnectionState = (name)=>{
    const api = trackedConnections.get(name);
    if (!api) return {};
    return Object.fromEntries(Object.entries(api.stores).map(([key, api2])=>[
            key,
            api2.getState()
        ]));
};
const extractConnectionInformation = (store, extensionConnector, options)=>{
    if (store === void 0) {
        return {
            type: "untracked",
            connection: extensionConnector.connect(options)
        };
    }
    const existingConnection = trackedConnections.get(options.name);
    if (existingConnection) {
        return {
            type: "tracked",
            store,
            ...existingConnection
        };
    }
    const newConnection = {
        connection: extensionConnector.connect(options),
        stores: {}
    };
    trackedConnections.set(options.name, newConnection);
    return {
        type: "tracked",
        store,
        ...newConnection
    };
};
const removeStoreFromTrackedConnections = (name, store)=>{
    if (store === void 0) return;
    const connectionInfo = trackedConnections.get(name);
    if (!connectionInfo) return;
    delete connectionInfo.stores[store];
    if (Object.keys(connectionInfo.stores).length === 0) {
        trackedConnections.delete(name);
    }
};
const findCallerName = (stack)=>{
    var _a, _b;
    if (!stack) return void 0;
    const traceLines = stack.split("\n");
    const apiSetStateLineIndex = traceLines.findIndex((traceLine)=>traceLine.includes("api.setState"));
    if (apiSetStateLineIndex < 0) return void 0;
    const callerLine = ((_a = traceLines[apiSetStateLineIndex + 1]) == null ? void 0 : _a.trim()) || "";
    return (_b = /.+ (.+) .+/.exec(callerLine)) == null ? void 0 : _b[1];
};
const devtoolsImpl = (fn, devtoolsOptions = {})=>(set, get, api)=>{
        const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;
        let extensionConnector;
        try {
            extensionConnector = (enabled != null ? enabled : (__TURBOPACK__import$2e$meta__.env ? __TURBOPACK__import$2e$meta__.env.MODE : void 0) !== "production") && window.__REDUX_DEVTOOLS_EXTENSION__;
        } catch (e) {}
        if (!extensionConnector) {
            return fn(set, get, api);
        }
        const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);
        let isRecording = true;
        api.setState = (state, replace, nameOrAction)=>{
            const r = set(state, replace);
            if (!isRecording) return r;
            const inferredActionType = findCallerName(new Error().stack);
            const action = nameOrAction === void 0 ? {
                type: anonymousActionType || inferredActionType || "anonymous"
            } : typeof nameOrAction === "string" ? {
                type: nameOrAction
            } : nameOrAction;
            if (store === void 0) {
                connection == null ? void 0 : connection.send(action, get());
                return r;
            }
            connection == null ? void 0 : connection.send({
                ...action,
                type: `${store}/${action.type}`
            }, {
                ...getTrackedConnectionState(options.name),
                [store]: api.getState()
            });
            return r;
        };
        api.devtools = {
            cleanup: ()=>{
                if (connection && typeof connection.unsubscribe === "function") {
                    connection.unsubscribe();
                }
                removeStoreFromTrackedConnections(options.name, store);
            }
        };
        const setStateFromDevtools = (...a)=>{
            const originalIsRecording = isRecording;
            isRecording = false;
            set(...a);
            isRecording = originalIsRecording;
        };
        const initialState = fn(api.setState, get, api);
        if (connectionInformation.type === "untracked") {
            connection == null ? void 0 : connection.init(initialState);
        } else {
            connectionInformation.stores[connectionInformation.store] = api;
            connection == null ? void 0 : connection.init(Object.fromEntries(Object.entries(connectionInformation.stores).map(([key, store2])=>[
                    key,
                    key === connectionInformation.store ? initialState : store2.getState()
                ])));
        }
        if (api.dispatchFromDevtools && typeof api.dispatch === "function") {
            let didWarnAboutReservedActionType = false;
            const originalDispatch = api.dispatch;
            api.dispatch = (...args)=>{
                if ((__TURBOPACK__import$2e$meta__.env ? __TURBOPACK__import$2e$meta__.env.MODE : void 0) !== "production" && args[0].type === "__setState" && !didWarnAboutReservedActionType) {
                    console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.');
                    didWarnAboutReservedActionType = true;
                }
                originalDispatch(...args);
            };
        }
        connection.subscribe((message)=>{
            var _a;
            switch(message.type){
                case "ACTION":
                    if (typeof message.payload !== "string") {
                        console.error("[zustand devtools middleware] Unsupported action format");
                        return;
                    }
                    return parseJsonThen(message.payload, (action)=>{
                        if (action.type === "__setState") {
                            if (store === void 0) {
                                setStateFromDevtools(action.state);
                                return;
                            }
                            if (Object.keys(action.state).length !== 1) {
                                console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);
                            }
                            const stateFromDevtools = action.state[store];
                            if (stateFromDevtools === void 0 || stateFromDevtools === null) {
                                return;
                            }
                            if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {
                                setStateFromDevtools(stateFromDevtools);
                            }
                            return;
                        }
                        if (!api.dispatchFromDevtools) return;
                        if (typeof api.dispatch !== "function") return;
                        api.dispatch(action);
                    });
                case "DISPATCH":
                    switch(message.payload.type){
                        case "RESET":
                            setStateFromDevtools(initialState);
                            if (store === void 0) {
                                return connection == null ? void 0 : connection.init(api.getState());
                            }
                            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));
                        case "COMMIT":
                            if (store === void 0) {
                                connection == null ? void 0 : connection.init(api.getState());
                                return;
                            }
                            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));
                        case "ROLLBACK":
                            return parseJsonThen(message.state, (state)=>{
                                if (store === void 0) {
                                    setStateFromDevtools(state);
                                    connection == null ? void 0 : connection.init(api.getState());
                                    return;
                                }
                                setStateFromDevtools(state[store]);
                                connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));
                            });
                        case "JUMP_TO_STATE":
                        case "JUMP_TO_ACTION":
                            return parseJsonThen(message.state, (state)=>{
                                if (store === void 0) {
                                    setStateFromDevtools(state);
                                    return;
                                }
                                if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {
                                    setStateFromDevtools(state[store]);
                                }
                            });
                        case "IMPORT_STATE":
                            {
                                const { nextLiftedState } = message.payload;
                                const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;
                                if (!lastComputedState) return;
                                if (store === void 0) {
                                    setStateFromDevtools(lastComputedState);
                                } else {
                                    setStateFromDevtools(lastComputedState[store]);
                                }
                                connection == null ? void 0 : connection.send(null, // FIXME no-any
                                nextLiftedState);
                                return;
                            }
                        case "PAUSE_RECORDING":
                            return isRecording = !isRecording;
                    }
                    return;
            }
        });
        return initialState;
    };
const devtools = devtoolsImpl;
const parseJsonThen = (stringified, fn)=>{
    let parsed;
    try {
        parsed = JSON.parse(stringified);
    } catch (e) {
        console.error("[zustand devtools middleware] Could not parse the received json", e);
    }
    if (parsed !== void 0) fn(parsed);
};
const subscribeWithSelectorImpl = (fn)=>(set, get, api)=>{
        const origSubscribe = api.subscribe;
        api.subscribe = (selector, optListener, options)=>{
            let listener = selector;
            if (optListener) {
                const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;
                let currentSlice = selector(api.getState());
                listener = (state)=>{
                    const nextSlice = selector(state);
                    if (!equalityFn(currentSlice, nextSlice)) {
                        const previousSlice = currentSlice;
                        optListener(currentSlice = nextSlice, previousSlice);
                    }
                };
                if (options == null ? void 0 : options.fireImmediately) {
                    optListener(currentSlice, currentSlice);
                }
            }
            return origSubscribe(listener);
        };
        const initialState = fn(set, get, api);
        return initialState;
    };
const subscribeWithSelector = subscribeWithSelectorImpl;
function combine(initialState, create) {
    return (...args)=>Object.assign({}, initialState, create(...args));
}
function createJSONStorage(getStorage, options) {
    let storage;
    try {
        storage = getStorage();
    } catch (e) {
        return;
    }
    const persistStorage = {
        getItem: (name)=>{
            var _a;
            const parse = (str2)=>{
                if (str2 === null) {
                    return null;
                }
                return JSON.parse(str2, options == null ? void 0 : options.reviver);
            };
            const str = (_a = storage.getItem(name)) != null ? _a : null;
            if (str instanceof Promise) {
                return str.then(parse);
            }
            return parse(str);
        },
        setItem: (name, newValue)=>storage.setItem(name, JSON.stringify(newValue, options == null ? void 0 : options.replacer)),
        removeItem: (name)=>storage.removeItem(name)
    };
    return persistStorage;
}
const toThenable = (fn)=>(input)=>{
        try {
            const result = fn(input);
            if (result instanceof Promise) {
                return result;
            }
            return {
                then (onFulfilled) {
                    return toThenable(onFulfilled)(result);
                },
                catch (_onRejected) {
                    return this;
                }
            };
        } catch (e) {
            return {
                then (_onFulfilled) {
                    return this;
                },
                catch (onRejected) {
                    return toThenable(onRejected)(e);
                }
            };
        }
    };
const persistImpl = (config, baseOptions)=>(set, get, api)=>{
        let options = {
            storage: createJSONStorage(()=>localStorage),
            partialize: (state)=>state,
            version: 0,
            merge: (persistedState, currentState)=>({
                    ...currentState,
                    ...persistedState
                }),
            ...baseOptions
        };
        let hasHydrated = false;
        const hydrationListeners = /* @__PURE__ */ new Set();
        const finishHydrationListeners = /* @__PURE__ */ new Set();
        let storage = options.storage;
        if (!storage) {
            return config((...args)=>{
                console.warn(`[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`);
                set(...args);
            }, get, api);
        }
        const setItem = ()=>{
            const state = options.partialize({
                ...get()
            });
            return storage.setItem(options.name, {
                state,
                version: options.version
            });
        };
        const savedSetState = api.setState;
        api.setState = (state, replace)=>{
            savedSetState(state, replace);
            void setItem();
        };
        const configResult = config((...args)=>{
            set(...args);
            void setItem();
        }, get, api);
        api.getInitialState = ()=>configResult;
        let stateFromStorage;
        const hydrate = ()=>{
            var _a, _b;
            if (!storage) return;
            hasHydrated = false;
            hydrationListeners.forEach((cb)=>{
                var _a2;
                return cb((_a2 = get()) != null ? _a2 : configResult);
            });
            const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;
            return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue)=>{
                if (deserializedStorageValue) {
                    if (typeof deserializedStorageValue.version === "number" && deserializedStorageValue.version !== options.version) {
                        if (options.migrate) {
                            const migration = options.migrate(deserializedStorageValue.state, deserializedStorageValue.version);
                            if (migration instanceof Promise) {
                                return migration.then((result)=>[
                                        true,
                                        result
                                    ]);
                            }
                            return [
                                true,
                                migration
                            ];
                        }
                        console.error(`State loaded from storage couldn't be migrated since no migrate function was provided`);
                    } else {
                        return [
                            false,
                            deserializedStorageValue.state
                        ];
                    }
                }
                return [
                    false,
                    void 0
                ];
            }).then((migrationResult)=>{
                var _a2;
                const [migrated, migratedState] = migrationResult;
                stateFromStorage = options.merge(migratedState, (_a2 = get()) != null ? _a2 : configResult);
                set(stateFromStorage, true);
                if (migrated) {
                    return setItem();
                }
            }).then(()=>{
                postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);
                stateFromStorage = get();
                hasHydrated = true;
                finishHydrationListeners.forEach((cb)=>cb(stateFromStorage));
            }).catch((e)=>{
                postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);
            });
        };
        api.persist = {
            setOptions: (newOptions)=>{
                options = {
                    ...options,
                    ...newOptions
                };
                if (newOptions.storage) {
                    storage = newOptions.storage;
                }
            },
            clearStorage: ()=>{
                storage == null ? void 0 : storage.removeItem(options.name);
            },
            getOptions: ()=>options,
            rehydrate: ()=>hydrate(),
            hasHydrated: ()=>hasHydrated,
            onHydrate: (cb)=>{
                hydrationListeners.add(cb);
                return ()=>{
                    hydrationListeners.delete(cb);
                };
            },
            onFinishHydration: (cb)=>{
                finishHydrationListeners.add(cb);
                return ()=>{
                    finishHydrationListeners.delete(cb);
                };
            }
        };
        if (!options.skipHydration) {
            hydrate();
        }
        return stateFromStorage || configResult;
    };
const persist = persistImpl;
;
}}),
"[project]/node_modules/zustand/esm/vanilla.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "createStore": (()=>createStore)
});
const createStoreImpl = (createState)=>{
    let state;
    const listeners = /* @__PURE__ */ new Set();
    const setState = (partial, replace)=>{
        const nextState = typeof partial === "function" ? partial(state) : partial;
        if (!Object.is(nextState, state)) {
            const previousState = state;
            state = (replace != null ? replace : typeof nextState !== "object" || nextState === null) ? nextState : Object.assign({}, state, nextState);
            listeners.forEach((listener)=>listener(state, previousState));
        }
    };
    const getState = ()=>state;
    const getInitialState = ()=>initialState;
    const subscribe = (listener)=>{
        listeners.add(listener);
        return ()=>listeners.delete(listener);
    };
    const api = {
        setState,
        getState,
        getInitialState,
        subscribe
    };
    const initialState = state = createState(setState, getState, api);
    return api;
};
const createStore = (createState)=>createState ? createStoreImpl(createState) : createStoreImpl;
;
}}),
"[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "create": (()=>create),
    "useStore": (()=>useStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$vanilla$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/vanilla.mjs [app-ssr] (ecmascript)");
;
;
const identity = (arg)=>arg;
function useStore(api, selector = identity) {
    const slice = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useSyncExternalStore(api.subscribe, ()=>selector(api.getState()), ()=>selector(api.getInitialState()));
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useDebugValue(slice);
    return slice;
}
const createImpl = (createState)=>{
    const api = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$vanilla$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createStore"])(createState);
    const useBoundStore = (selector)=>useStore(api, selector);
    Object.assign(useBoundStore, api);
    return useBoundStore;
};
const create = (createState)=>createState ? createImpl(createState) : createImpl;
;
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_require__("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__b701e8._.js.map