module.exports = {

"[project]/src/lib/query-error-handler.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Query Error Handler
 * Centralized error handling for TanStack Query
 */ __turbopack_esm__({
    "ErrorType": (()=>ErrorType),
    "createApiError": (()=>createApiError),
    "createGlobalErrorHandler": (()=>createGlobalErrorHandler),
    "errorUtils": (()=>errorUtils),
    "getErrorType": (()=>getErrorType),
    "handleApiError": (()=>handleApiError),
    "isApiError": (()=>isApiError),
    "parseErrorResponse": (()=>parseErrorResponse),
    "setupQueryErrorHandling": (()=>setupQueryErrorHandling)
});
var ErrorType = /*#__PURE__*/ function(ErrorType) {
    ErrorType["NETWORK"] = "NETWORK";
    ErrorType["AUTHENTICATION"] = "AUTHENTICATION";
    ErrorType["AUTHORIZATION"] = "AUTHORIZATION";
    ErrorType["VALIDATION"] = "VALIDATION";
    ErrorType["SERVER"] = "SERVER";
    ErrorType["UNKNOWN"] = "UNKNOWN";
    return ErrorType;
}({});
function getErrorType(status) {
    if (status === 401) return "AUTHENTICATION";
    if (status === 403) return "AUTHORIZATION";
    if (status >= 400 && status < 500) return "VALIDATION";
    if (status >= 500) return "SERVER";
    if (status === 0) return "NETWORK";
    return "UNKNOWN";
}
function createApiError(status, statusText, message, details) {
    return {
        status,
        statusText,
        message,
        details,
        timestamp: new Date().toISOString()
    };
}
async function parseErrorResponse(response) {
    let message = response.statusText || 'An error occurred';
    let details = null;
    try {
        const errorData = await response.json();
        message = errorData.message || errorData.error || message;
        details = errorData.details || errorData;
    } catch  {
    // If response is not JSON, use status text
    }
    return createApiError(response.status, response.statusText, message, details);
}
function createGlobalErrorHandler() {
    return (error)=>{
        console.error('[Query Error]', error);
        // Handle different types of errors
        if (error instanceof Error) {
            // Network errors, parsing errors, etc.
            console.error('Error details:', {
                name: error.name,
                message: error.message,
                stack: error.stack
            });
        }
        // Handle API errors
        if (isApiError(error)) {
            handleApiError(error);
        }
    };
}
function isApiError(error) {
    return typeof error === 'object' && error !== null && 'status' in error && 'message' in error;
}
function handleApiError(error) {
    const errorType = getErrorType(error.status);
    switch(errorType){
        case "AUTHENTICATION":
            handleAuthenticationError(error);
            break;
        case "AUTHORIZATION":
            handleAuthorizationError(error);
            break;
        case "VALIDATION":
            handleValidationError(error);
            break;
        case "SERVER":
            handleServerError(error);
            break;
        case "NETWORK":
            handleNetworkError(error);
            break;
        default:
            handleUnknownError(error);
    }
}
/**
 * Handle authentication errors (401)
 */ function handleAuthenticationError(error) {
    console.warn('[Auth Error]', error.message);
    // In development mode, authentication is disabled
    if ("TURBOPACK compile-time truthy", 1) {
        console.log('[Dev Mode] Authentication error ignored');
        return;
    }
// In production, redirect to login or refresh token
// This will be implemented when auth system is ready
}
/**
 * Handle authorization errors (403)
 */ function handleAuthorizationError(error) {
    console.warn('[Authorization Error]', error.message);
// Show user-friendly message about insufficient permissions
// This will be integrated with notification system
}
/**
 * Handle validation errors (400-499)
 */ function handleValidationError(error) {
    console.warn('[Validation Error]', error.message);
// These are usually handled by individual components
// Global handler just logs for debugging
}
/**
 * Handle server errors (500+)
 */ function handleServerError(error) {
    console.error('[Server Error]', error.message);
// Show generic error message to user
// Log detailed error for debugging
}
/**
 * Handle network errors
 */ function handleNetworkError(error) {
    console.error('[Network Error]', error.message);
// Show network connectivity message
// Suggest retry or check connection
}
/**
 * Handle unknown errors
 */ function handleUnknownError(error) {
    console.error('[Unknown Error]', error);
// Show generic error message
// Log for investigation
}
function setupQueryErrorHandling(queryClient) {
    // Set up global error handler
    queryClient.setDefaultOptions({
        queries: {
            ...queryClient.getDefaultOptions().queries,
            throwOnError: false
        },
        mutations: {
            ...queryClient.getDefaultOptions().mutations,
            throwOnError: false
        }
    });
    // Set up global error handler
    queryClient.setMutationDefaults([
        'mutation'
    ], {
        onError: createGlobalErrorHandler()
    });
}
const errorUtils = {
    /**
   * Check if error should trigger retry
   */ shouldRetry: (error)=>{
        if (isApiError(error)) {
            const errorType = getErrorType(error.status);
            // Don't retry client errors (4xx)
            return errorType !== "VALIDATION" && errorType !== "AUTHENTICATION" && errorType !== "AUTHORIZATION";
        }
        return true; // Retry network and unknown errors
    },
    /**
   * Get user-friendly error message
   */ getUserMessage: (error)=>{
        if (isApiError(error)) {
            const errorType = getErrorType(error.status);
            switch(errorType){
                case "AUTHENTICATION":
                    return 'Please log in to continue';
                case "AUTHORIZATION":
                    return 'You do not have permission to perform this action';
                case "VALIDATION":
                    return error.message || 'Please check your input and try again';
                case "SERVER":
                    return 'Server error occurred. Please try again later';
                case "NETWORK":
                    return 'Network error. Please check your connection';
                default:
                    return 'An unexpected error occurred';
            }
        }
        return 'An unexpected error occurred';
    },
    /**
   * Check if error is retryable
   */ isRetryable: (error)=>{
        if (isApiError(error)) {
            return error.status >= 500 || error.status === 0;
        }
        return true;
    }
};
}}),
"[project]/src/lib/query-utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Query Utilities and Helpers
 * Common utilities for working with TanStack Query
 */ __turbopack_esm__({
    "cacheUtils": (()=>cacheUtils),
    "devUtils": (()=>devUtils),
    "mutationOptionsBuilder": (()=>mutationOptionsBuilder),
    "queryErrorUtils": (()=>queryErrorUtils),
    "queryOptionsBuilder": (()=>queryOptionsBuilder),
    "queryStateUtils": (()=>queryStateUtils),
    "typeGuards": (()=>typeGuards)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-error-handler.ts [app-ssr] (ecmascript)");
;
;
const queryOptionsBuilder = {
    /**
   * Build options for real-time data (short cache)
   */ realTime: (options)=>({
            staleTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.SHORT,
            gcTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.MEDIUM,
            refetchInterval: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].REFETCH_INTERVAL.FAST,
            ...options
        }),
    /**
   * Build options for static data (long cache)
   */ static: (options)=>({
            staleTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.VERY_LONG,
            gcTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.VERY_LONG,
            refetchOnWindowFocus: false,
            refetchOnReconnect: false,
            ...options
        }),
    /**
   * Build options for user-specific data
   */ userSpecific: (options)=>({
            staleTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.MEDIUM,
            gcTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.LONG,
            refetchOnWindowFocus: true,
            ...options
        }),
    /**
   * Build options for background sync data
   */ backgroundSync: (options)=>({
            staleTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.LONG,
            gcTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.VERY_LONG,
            refetchInterval: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].REFETCH_INTERVAL.SLOW,
            refetchIntervalInBackground: true,
            ...options
        })
};
const mutationOptionsBuilder = {
    /**
   * Build options for optimistic updates
   */ optimistic: (options)=>({
            retry: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].RETRY.ONCE,
            ...options
        }),
    /**
   * Build options for critical operations
   */ critical: (options)=>({
            retry: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].RETRY.DEFAULT,
            retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
            ...options
        }),
    /**
   * Build options for background operations
   */ background: (options)=>({
            retry: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].RETRY.TWICE,
            ...options
        })
};
const cacheUtils = {
    /**
   * Invalidate queries by pattern
   */ invalidateByPattern: async (queryClient, pattern)=>{
        await queryClient.invalidateQueries({
            queryKey: pattern
        });
    },
    /**
   * Remove queries by pattern
   */ removeByPattern: (queryClient, pattern)=>{
        queryClient.removeQueries({
            queryKey: pattern
        });
    },
    /**
   * Update query data
   */ updateQueryData: (queryClient, queryKey, updater)=>{
        queryClient.setQueryData(queryKey, updater);
    },
    /**
   * Optimistically update list data
   */ optimisticListUpdate: (queryClient, queryKey, item, operation)=>{
        queryClient.setQueryData(queryKey, (oldData)=>{
            if (!oldData) return operation === 'add' ? [
                item
            ] : [];
            switch(operation){
                case 'add':
                    return [
                        ...oldData,
                        item
                    ];
                case 'update':
                    return oldData.map((existing)=>existing.id === item.id ? {
                            ...existing,
                            ...item
                        } : existing);
                case 'remove':
                    return oldData.filter((existing)=>existing.id !== item.id);
                default:
                    return oldData;
            }
        });
    },
    /**
   * Optimistically update paginated data
   */ optimisticPaginatedUpdate: (queryClient, queryKey, item, operation)=>{
        queryClient.setQueryData(queryKey, (oldData)=>{
            if (!oldData) return oldData;
            const updatedData = cacheUtils.optimisticListUpdate(queryClient, [
                'temp'
            ], item, operation);
            return {
                ...oldData,
                data: updatedData || oldData.data
            };
        });
    }
};
const queryStateUtils = {
    /**
   * Check if any queries are loading
   */ isAnyLoading: (queryClient, queryKeys)=>{
        return queryKeys.some((key)=>{
            const query = queryClient.getQueryState(key);
            return query?.fetchStatus === 'fetching';
        });
    },
    /**
   * Check if any queries have errors
   */ hasAnyErrors: (queryClient, queryKeys)=>{
        return queryKeys.some((key)=>{
            const query = queryClient.getQueryState(key);
            return query?.status === 'error';
        });
    },
    /**
   * Get all errors from queries
   */ getAllErrors: (queryClient, queryKeys)=>{
        return queryKeys.map((key)=>{
            const query = queryClient.getQueryState(key);
            return query?.error;
        }).filter((error)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isApiError"])(error));
    },
    /**
   * Check if data is stale
   */ isStale: (queryClient, queryKey)=>{
        const query = queryClient.getQueryState(queryKey);
        return query ? query.isStale : true;
    }
};
const devUtils = {
    /**
   * Log query cache state
   */ logCacheState: (queryClient)=>{
        if ("TURBOPACK compile-time truthy", 1) {
            const cache = queryClient.getQueryCache();
            console.log('[Query Cache]', {
                queries: cache.getAll().length,
                state: cache.getAll().map((query)=>({
                        key: query.queryKey,
                        status: query.state.status,
                        dataUpdatedAt: query.state.dataUpdatedAt,
                        error: query.state.error
                    }))
            });
        }
    },
    /**
   * Clear all cache (development only)
   */ clearAllCache: (queryClient)=>{
        if ("TURBOPACK compile-time truthy", 1) {
            queryClient.clear();
            console.log('[Dev] Query cache cleared');
        }
    },
    /**
   * Force refetch all queries (development only)
   */ refetchAll: async (queryClient)=>{
        if ("TURBOPACK compile-time truthy", 1) {
            await queryClient.refetchQueries();
            console.log('[Dev] All queries refetched');
        }
    }
};
const queryErrorUtils = {
    /**
   * Handle query error with user feedback
   */ handleQueryError: (error, context)=>{
        const message = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["errorUtils"].getUserMessage(error);
        console.error(`[Query Error${context ? ` - ${context}` : ''}]`, error);
        // This will be integrated with notification system
        // For now, just log the user-friendly message
        console.log('[User Message]', message);
        return message;
    },
    /**
   * Handle mutation error with user feedback
   */ handleMutationError: (error, context)=>{
        const message = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["errorUtils"].getUserMessage(error);
        console.error(`[Mutation Error${context ? ` - ${context}` : ''}]`, error);
        // This will be integrated with notification system
        // For now, just log the user-friendly message
        console.log('[User Message]', message);
        return message;
    }
};
const typeGuards = {
    /**
   * Check if response is a valid API response
   */ isApiResponse: (data)=>{
        return typeof data === 'object' && data !== null && 'data' in data && 'success' in data && 'timestamp' in data;
    },
    /**
   * Check if response is a paginated response
   */ isPaginatedResponse: (data)=>{
        return typeGuards.isApiResponse(data) && 'pagination' in data && typeof data.pagination === 'object';
    }
};
}}),
"[project]/src/hooks/api/base-hooks.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Base API Hooks
 * Foundation hooks for API operations with TanStack Query
 */ __turbopack_esm__({
    "useApiHookUtils": (()=>useApiHookUtils),
    "useApiStatus": (()=>useApiStatus),
    "useBackgroundMutation": (()=>useBackgroundMutation),
    "useBackgroundSyncQuery": (()=>useBackgroundSyncQuery),
    "useBaseMutation": (()=>useBaseMutation),
    "useBaseQuery": (()=>useBaseQuery),
    "useCriticalMutation": (()=>useCriticalMutation),
    "useOptimisticMutation": (()=>useOptimisticMutation),
    "usePaginatedQuery": (()=>usePaginatedQuery),
    "useRealTimeQuery": (()=>useRealTimeQuery),
    "useStaticQuery": (()=>useStaticQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-error-handler.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
'use client';
;
;
;
function useBaseQuery(queryKey, queryFn, options) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        ...options,
        onError: (error)=>{
            console.error(`[Query Error] ${queryKey.join(' → ')}:`, error);
            if (options?.onError) {
                options.onError(error);
            }
        }
    });
}
function useBaseMutation(mutationFn, options) {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn,
        ...options,
        onError: (error, variables, context)=>{
            console.error('[Mutation Error]:', error);
            if (options?.onError) {
                options.onError(error, variables, context);
            }
        },
        onSuccess: (data, variables, context)=>{
            if (options?.onSuccess) {
                options.onSuccess(data, variables, context);
            }
        }
    });
}
function usePaginatedQuery(queryKey, queryFn, options) {
    return useBaseQuery(queryKey, queryFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryOptionsBuilder"].userSpecific(),
        ...options
    });
}
function useRealTimeQuery(queryKey, queryFn, options) {
    return useBaseQuery(queryKey, queryFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryOptionsBuilder"].realTime(),
        ...options
    });
}
function useStaticQuery(queryKey, queryFn, options) {
    return useBaseQuery(queryKey, queryFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryOptionsBuilder"].static(),
        ...options
    });
}
function useBackgroundSyncQuery(queryKey, queryFn, options) {
    return useBaseQuery(queryKey, queryFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryOptionsBuilder"].backgroundSync(),
        ...options
    });
}
function useOptimisticMutation(mutationFn, options) {
    return useBaseMutation(mutationFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mutationOptionsBuilder"].optimistic(),
        ...options
    });
}
function useCriticalMutation(mutationFn, options) {
    return useBaseMutation(mutationFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mutationOptionsBuilder"].critical(),
        ...options
    });
}
function useBackgroundMutation(mutationFn, options) {
    return useBaseMutation(mutationFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mutationOptionsBuilder"].background(),
        ...options
    });
}
const useApiHookUtils = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return {
        /**
     * Invalidate queries by pattern
     */ invalidateQueries: (queryKey)=>{
            return queryClient.invalidateQueries({
                queryKey
            });
        },
        /**
     * Remove queries from cache
     */ removeQueries: (queryKey)=>{
            return queryClient.removeQueries({
                queryKey
            });
        },
        /**
     * Update query data optimistically
     */ updateQueryData: (queryKey, updater)=>{
            queryClient.setQueryData(queryKey, updater);
        },
        /**
     * Get cached query data
     */ getQueryData: (queryKey)=>{
            return queryClient.getQueryData(queryKey);
        },
        /**
     * Prefetch query data
     */ prefetchQuery: (queryKey, queryFn)=>{
            return queryClient.prefetchQuery({
                queryKey,
                queryFn
            });
        },
        /**
     * Check if query is loading
     */ isQueryLoading: (queryKey)=>{
            const query = queryClient.getQueryState(queryKey);
            return query?.fetchStatus === 'fetching';
        },
        /**
     * Get query error
     */ getQueryError: (queryKey)=>{
            const query = queryClient.getQueryState(queryKey);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isApiError"])(query?.error) ? query.error : null;
        },
        /**
     * Handle API error with user feedback
     */ handleApiError: (error, context)=>{
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["errorUtils"].getUserMessage(error);
        }
    };
};
const useApiStatus = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return {
        /**
     * Get overall API status
     */ getApiStatus: ()=>{
            const queries = queryClient.getQueryCache().getAll();
            const totalQueries = queries.length;
            const loadingQueries = queries.filter((q)=>q.state.fetchStatus === 'fetching').length;
            const errorQueries = queries.filter((q)=>q.state.status === 'error').length;
            const successQueries = queries.filter((q)=>q.state.status === 'success').length;
            return {
                total: totalQueries,
                loading: loadingQueries,
                error: errorQueries,
                success: successQueries,
                isLoading: loadingQueries > 0,
                hasErrors: errorQueries > 0,
                healthScore: totalQueries > 0 ? successQueries / totalQueries * 100 : 100
            };
        },
        /**
     * Get queries by status
     */ getQueriesByStatus: (status)=>{
            const queries = queryClient.getQueryCache().getAll();
            switch(status){
                case 'loading':
                    return queries.filter((q)=>q.state.fetchStatus === 'fetching');
                case 'error':
                    return queries.filter((q)=>q.state.status === 'error');
                case 'success':
                    return queries.filter((q)=>q.state.status === 'success');
                case 'idle':
                    return queries.filter((q)=>q.state.fetchStatus === 'idle');
                default:
                    return [];
            }
        },
        /**
     * Clear all errors
     */ clearAllErrors: ()=>{
            const errorQueries = queryClient.getQueryCache().getAll().filter((q)=>q.state.status === 'error');
            errorQueries.forEach((query)=>{
                queryClient.resetQueries({
                    queryKey: query.queryKey
                });
            });
        }
    };
};
}}),
"[project]/src/hooks/api/auth-hooks.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Authentication API Hooks
 * Hooks for system authentication operations
 */ __turbopack_esm__({
    "useAuth": (()=>useAuth),
    "useChangePassword": (()=>useChangePassword),
    "useCreateUser": (()=>useCreateUser),
    "useLogin": (()=>useLogin),
    "useLogout": (()=>useLogout),
    "useLogoutAll": (()=>useLogoutAll),
    "useProfile": (()=>useProfile),
    "useSystemUser": (()=>useSystemUser),
    "useSystemUsers": (()=>useSystemUsers),
    "useUpdateProfile": (()=>useUpdateProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/base-hooks.ts [app-ssr] (ecmascript)");
'use client';
;
;
function useLogin() {
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseMutation"])(async (credentials)=>{
        const response = await fetch('/api/system-auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(credentials)
        });
        if (!response.ok) {
            throw new Error(`Login failed: ${response.statusText}`);
        }
        return response.json();
    }, {
        onSuccess: (data)=>{
            // Invalidate auth queries on successful login
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].auth.all);
            console.log('✅ Login successful:', data.user.username);
        },
        onError: (error)=>{
            console.error('❌ Login failed:', error);
        }
    });
}
function useLogout() {
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseMutation"])(async ()=>{
        const response = await fetch('/api/system-auth/logout', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error(`Logout failed: ${response.statusText}`);
        }
        return response.json();
    }, {
        onSuccess: ()=>{
            // Clear all auth-related queries on logout
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].auth.all);
            console.log('✅ Logout successful');
        },
        onError: (error)=>{
            console.error('❌ Logout failed:', error);
        }
    });
}
function useLogoutAll() {
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseMutation"])(async ()=>{
        const response = await fetch('/api/system-auth/logout-all', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error(`Logout all failed: ${response.statusText}`);
        }
        return response.json();
    }, {
        onSuccess: ()=>{
            // Clear all auth-related queries on logout all
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].auth.all);
            console.log('✅ Logout all successful');
        },
        onError: (error)=>{
            console.error('❌ Logout all failed:', error);
        }
    });
}
function useProfile() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].auth.profile(), async ()=>{
        const response = await fetch('/api/system-auth/profile');
        if (!response.ok) {
            throw new Error(`Failed to fetch profile: ${response.statusText}`);
        }
        return response.json();
    }, {
        enabled: false,
        staleTime: 5 * 60 * 1000,
        retry: (failureCount, error)=>{
            // Don't retry on 401 (unauthorized)
            if (error?.status === 401) return false;
            return failureCount < 2;
        }
    });
}
function useUpdateProfile() {
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseMutation"])(async (data)=>{
        const response = await fetch('/api/system-auth/profile', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        if (!response.ok) {
            throw new Error(`Failed to update profile: ${response.statusText}`);
        }
        return response.json();
    }, {
        onSuccess: ()=>{
            // Invalidate profile query to refetch updated data
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].auth.profile());
            console.log('✅ Profile updated successfully');
        },
        onError: (error)=>{
            console.error('❌ Profile update failed:', error);
        }
    });
}
function useChangePassword() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseMutation"])(async (data)=>{
        const response = await fetch('/api/system-auth/change-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        if (!response.ok) {
            throw new Error(`Failed to change password: ${response.statusText}`);
        }
        return response.json();
    }, {
        onSuccess: ()=>{
            console.log('✅ Password changed successfully');
        },
        onError: (error)=>{
            console.error('❌ Password change failed:', error);
        }
    });
}
function useCreateUser() {
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseMutation"])(async (data)=>{
        const response = await fetch('/api/system-auth/users', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        if (!response.ok) {
            throw new Error(`Failed to create user: ${response.statusText}`);
        }
        return response.json();
    }, {
        onSuccess: ()=>{
            // Invalidate users list to show new user
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].auth.users());
            console.log('✅ User created successfully');
        },
        onError: (error)=>{
            console.error('❌ User creation failed:', error);
        }
    });
}
function useSystemUsers() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].auth.users(), async ()=>{
        const response = await fetch('/api/system-auth/users');
        if (!response.ok) {
            throw new Error(`Failed to fetch users: ${response.statusText}`);
        }
        return response.json();
    }, {
        enabled: false,
        staleTime: 2 * 60 * 1000
    });
}
function useSystemUser(userId) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].auth.user(userId), async ()=>{
        const response = await fetch(`/api/system-auth/users/${userId}`);
        if (!response.ok) {
            throw new Error(`Failed to fetch user: ${response.statusText}`);
        }
        return response.json();
    }, {
        enabled: !!userId,
        staleTime: 5 * 60 * 1000
    });
}
function useAuth() {
    const login = useLogin();
    const logout = useLogout();
    const logoutAll = useLogoutAll();
    const profile = useProfile();
    const updateProfile = useUpdateProfile();
    const changePassword = useChangePassword();
    const createUser = useCreateUser();
    return {
        // Queries
        profile,
        // Mutations
        login,
        logout,
        logoutAll,
        updateProfile,
        changePassword,
        createUser,
        // Computed state
        isAuthenticated: !!profile.data,
        user: profile.data,
        isLoading: profile.isLoading || login.isPending || logout.isPending,
        error: profile.error || login.error || logout.error,
        // Actions
        loginUser: login.mutate,
        logoutUser: logout.mutate,
        logoutAllDevices: logoutAll.mutate,
        updateUserProfile: updateProfile.mutate,
        changeUserPassword: changePassword.mutate,
        createNewUser: createUser.mutate
    };
}
}}),
"[project]/src/hooks/api/football-hooks.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Football Data API Hooks
 * Hooks for football leagues, teams, and fixtures operations
 */ __turbopack_esm__({
    "useDailySync": (()=>useDailySync),
    "useFixture": (()=>useFixture),
    "useFixtures": (()=>useFixtures),
    "useFootball": (()=>useFootball),
    "useLeague": (()=>useLeague),
    "useLeagues": (()=>useLeagues),
    "useLiveFixtures": (()=>useLiveFixtures),
    "useSyncFixtures": (()=>useSyncFixtures),
    "useSyncStatus": (()=>useSyncStatus),
    "useTeam": (()=>useTeam),
    "useTeams": (()=>useTeams),
    "useTodayFixtures": (()=>useTodayFixtures),
    "useUpcomingFixtures": (()=>useUpcomingFixtures)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/base-hooks.ts [app-ssr] (ecmascript)");
'use client';
;
;
function useLeagues(params) {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());
    if (params?.country) queryParams.set('country', params.country);
    if (params?.isActive !== undefined) queryParams.set('isActive', params.isActive.toString());
    if (params?.query) queryParams.set('query', params.query);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePaginatedQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].football.leagues(),
        params
    ], async ()=>{
        const response = await fetch(`/api/football/leagues?${queryParams.toString()}`);
        if (!response.ok) {
            throw new Error(`Failed to fetch leagues: ${response.statusText}`);
        }
        return response.json();
    }, {
        staleTime: 10 * 60 * 1000
    });
}
function useLeague(leagueId) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].football.league(leagueId), async ()=>{
        const response = await fetch(`/api/football/leagues/${leagueId}`);
        if (!response.ok) {
            throw new Error(`Failed to fetch league: ${response.statusText}`);
        }
        return response.json();
    }, {
        enabled: !!leagueId,
        staleTime: 10 * 60 * 1000
    });
}
function useTeams(params) {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());
    if (params?.leagueId) queryParams.set('leagueId', params.leagueId);
    if (params?.country) queryParams.set('country', params.country);
    if (params?.query) queryParams.set('query', params.query);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePaginatedQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].football.teams(),
        params
    ], async ()=>{
        const response = await fetch(`/api/football/teams?${queryParams.toString()}`);
        if (!response.ok) {
            throw new Error(`Failed to fetch teams: ${response.statusText}`);
        }
        return response.json();
    }, {
        staleTime: 5 * 60 * 1000
    });
}
function useTeam(teamId) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].football.team(teamId), async ()=>{
        const response = await fetch(`/api/football/teams/${teamId}`);
        if (!response.ok) {
            throw new Error(`Failed to fetch team: ${response.statusText}`);
        }
        return response.json();
    }, {
        enabled: !!teamId,
        staleTime: 5 * 60 * 1000
    });
}
function useFixtures(params) {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());
    if (params?.leagueId) queryParams.set('leagueId', params.leagueId);
    if (params?.teamId) queryParams.set('teamId', params.teamId);
    if (params?.status) queryParams.set('status', params.status);
    if (params?.dateFrom) queryParams.set('dateFrom', params.dateFrom);
    if (params?.dateTo) queryParams.set('dateTo', params.dateTo);
    if (params?.query) queryParams.set('query', params.query);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePaginatedQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].football.fixtures(),
        params
    ], async ()=>{
        const response = await fetch(`/api/football/fixtures?${queryParams.toString()}`);
        if (!response.ok) {
            throw new Error(`Failed to fetch fixtures: ${response.statusText}`);
        }
        return response.json();
    }, {
        staleTime: 1 * 60 * 1000
    });
}
function useFixture(fixtureId) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].football.fixture(fixtureId), async ()=>{
        const response = await fetch(`/api/football/fixtures/${fixtureId}`);
        if (!response.ok) {
            throw new Error(`Failed to fetch fixture: ${response.statusText}`);
        }
        return response.json();
    }, {
        enabled: !!fixtureId,
        staleTime: 30 * 1000
    });
}
function useSyncStatus() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBackgroundSyncQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].football.syncStatus(), async ()=>{
        const response = await fetch('/api/football/fixtures/sync/status');
        if (!response.ok) {
            throw new Error(`Failed to fetch sync status: ${response.statusText}`);
        }
        return response.json();
    }, {
        staleTime: 30 * 1000,
        refetchInterval: 60 * 1000
    });
}
function useSyncFixtures() {
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseMutation"])(async ()=>{
        const response = await fetch('/api/football/fixtures/sync', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error(`Failed to start sync: ${response.statusText}`);
        }
        return response.json();
    }, {
        onSuccess: ()=>{
            // Invalidate sync status and fixtures to show updated data
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].football.syncStatus());
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].football.fixtures());
            console.log('✅ Fixtures sync started');
        },
        onError: (error)=>{
            console.error('❌ Fixtures sync failed:', error);
        }
    });
}
function useDailySync() {
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseMutation"])(async ()=>{
        const response = await fetch('/api/football/fixtures/sync/daily', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error(`Failed to start daily sync: ${response.statusText}`);
        }
        return response.json();
    }, {
        onSuccess: ()=>{
            // Invalidate sync status and fixtures
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].football.syncStatus());
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].football.fixtures());
            console.log('✅ Daily sync started');
        },
        onError: (error)=>{
            console.error('❌ Daily sync failed:', error);
        }
    });
}
function useFootball() {
    const syncFixtures = useSyncFixtures();
    const dailySync = useDailySync();
    const syncStatus = useSyncStatus();
    return {
        // Sync operations
        syncFixtures,
        dailySync,
        syncStatus,
        // Sync actions
        startSync: syncFixtures.mutate,
        startDailySync: dailySync.mutate,
        // Sync state
        isSyncing: syncFixtures.isPending || dailySync.isPending,
        syncError: syncFixtures.error || dailySync.error,
        lastSyncStatus: syncStatus.data
    };
}
function useLiveFixtures() {
    return useFixtures({
        status: 'live',
        limit: 50
    });
}
function useTodayFixtures() {
    const today = new Date().toISOString().split('T')[0];
    return useFixtures({
        dateFrom: today,
        dateTo: today,
        limit: 100
    });
}
function useUpcomingFixtures(days = 7) {
    const today = new Date();
    const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);
    return useFixtures({
        dateFrom: today.toISOString().split('T')[0],
        dateTo: futureDate.toISOString().split('T')[0],
        status: 'scheduled',
        limit: 100
    });
}
}}),
"[project]/src/types/user.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * User Types & Interfaces
 * SystemUser management types for APISportsGame CMS
 */ /**
 * System user roles
 */ __turbopack_esm__({
    "DEFAULT_USER_PARAMS": (()=>DEFAULT_USER_PARAMS),
    "ROLE_COLORS": (()=>ROLE_COLORS),
    "ROLE_LABELS": (()=>ROLE_LABELS),
    "ROLE_PERMISSIONS": (()=>ROLE_PERMISSIONS),
    "STATUS_COLORS": (()=>STATUS_COLORS),
    "STATUS_LABELS": (()=>STATUS_LABELS),
    "USER_VALIDATION": (()=>USER_VALIDATION),
    "userHelpers": (()=>userHelpers)
});
const ROLE_PERMISSIONS = {
    admin: [
        'users.create',
        'users.read',
        'users.update',
        'users.delete',
        'users.manage_roles',
        'football.create',
        'football.read',
        'football.update',
        'football.delete',
        'football.sync',
        'broadcast.create',
        'broadcast.read',
        'broadcast.update',
        'broadcast.delete',
        'system.settings',
        'system.logs',
        'system.health'
    ],
    editor: [
        'users.read',
        'football.create',
        'football.read',
        'football.update',
        'football.sync',
        'broadcast.create',
        'broadcast.read',
        'broadcast.update',
        'broadcast.delete'
    ],
    moderator: [
        'users.read',
        'football.read',
        'broadcast.read',
        'broadcast.update'
    ]
};
const ROLE_LABELS = {
    admin: 'Administrator',
    editor: 'Editor',
    moderator: 'Moderator'
};
const STATUS_LABELS = {
    active: 'Active',
    inactive: 'Inactive',
    suspended: 'Suspended'
};
const ROLE_COLORS = {
    admin: '#ff4d4f',
    editor: '#1890ff',
    moderator: '#52c41a'
};
const STATUS_COLORS = {
    active: '#52c41a',
    inactive: '#d9d9d9',
    suspended: '#ff4d4f'
};
const USER_VALIDATION = {
    username: {
        min: 3,
        max: 50,
        pattern: /^[a-zA-Z0-9_-]+$/,
        message: 'Username must be 3-50 characters and contain only letters, numbers, hyphens, and underscores'
    },
    email: {
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: 'Please enter a valid email address'
    },
    password: {
        min: 8,
        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
        message: 'Password must be at least 8 characters with uppercase, lowercase, number, and special character'
    },
    firstName: {
        max: 50,
        message: 'First name must not exceed 50 characters'
    },
    lastName: {
        max: 50,
        message: 'Last name must not exceed 50 characters'
    }
};
const DEFAULT_USER_PARAMS = {
    page: 1,
    limit: 20,
    sortBy: 'createdAt',
    sortOrder: 'desc'
};
const userHelpers = {
    /**
   * Get user full name
   */ getFullName: (user)=>{
        if (user.firstName && user.lastName) {
            return `${user.firstName} ${user.lastName}`;
        }
        if (user.firstName) {
            return user.firstName;
        }
        if (user.lastName) {
            return user.lastName;
        }
        return user.username;
    },
    /**
   * Get user display name
   */ getDisplayName: (user)=>{
        const fullName = userHelpers.getFullName(user);
        return fullName !== user.username ? `${fullName} (${user.username})` : user.username;
    },
    /**
   * Check if user has permission
   */ hasPermission: (user, permission)=>{
        const rolePermissions = ROLE_PERMISSIONS[user.role] || [];
        return rolePermissions.includes(permission);
    },
    /**
   * Check if user is active
   */ isActive: (user)=>{
        return user.status === 'active';
    },
    /**
   * Get user avatar URL or initials
   */ getAvatarDisplay: (user)=>{
        if (user.avatar) {
            return {
                type: 'url',
                value: user.avatar
            };
        }
        const fullName = userHelpers.getFullName(user);
        const initials = fullName.split(' ').map((name)=>name.charAt(0).toUpperCase()).slice(0, 2).join('');
        return {
            type: 'initials',
            value: initials || user.username.charAt(0).toUpperCase()
        };
    },
    /**
   * Format last login time
   */ formatLastLogin: (lastLogin)=>{
        if (!lastLogin) return 'Never';
        const date = new Date(lastLogin);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        if (diffDays === 0) return 'Today';
        if (diffDays === 1) return 'Yesterday';
        if (diffDays < 7) return `${diffDays} days ago`;
        if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
        if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
        return `${Math.floor(diffDays / 365)} years ago`;
    }
};
}}),
"[project]/src/hooks/api/users.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * User API Hooks
 * TanStack Query hooks for SystemUser management
 */ __turbopack_esm__({
    "useCreateUser": (()=>useCreateUser),
    "useDeleteUser": (()=>useDeleteUser),
    "useUpdateUser": (()=>useUpdateUser),
    "useUser": (()=>useUser),
    "useUserStatistics": (()=>useUserStatistics),
    "useUsers": (()=>useUsers),
    "userQueryKeys": (()=>userQueryKeys)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$user$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/types/user.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/message/index.js [app-ssr] (ecmascript) <export default as message>");
;
;
;
/**
 * API endpoints
 */ const API_ENDPOINTS = {
    users: '/api/system-auth/users',
    userById: (id)=>`/api/system-auth/users/${id}`,
    userStats: '/api/system-auth/users/statistics',
    userActivity: (id)=>`/api/system-auth/users/${id}/activity`,
    userSessions: (id)=>`/api/system-auth/users/${id}/sessions`,
    changePassword: (id)=>`/api/system-auth/users/${id}/change-password`,
    resetPassword: (id)=>`/api/system-auth/users/${id}/reset-password`
};
const userQueryKeys = {
    all: [
        'users'
    ],
    lists: ()=>[
            ...userQueryKeys.all,
            'list'
        ],
    list: (params)=>[
            ...userQueryKeys.lists(),
            params
        ],
    details: ()=>[
            ...userQueryKeys.all,
            'detail'
        ],
    detail: (id)=>[
            ...userQueryKeys.details(),
            id
        ],
    statistics: ()=>[
            ...userQueryKeys.all,
            'statistics'
        ],
    activity: (id)=>[
            ...userQueryKeys.all,
            'activity',
            id
        ],
    sessions: (id)=>[
            ...userQueryKeys.all,
            'sessions',
            id
        ]
};
/**
 * Mock data for development
 */ const mockUsers = [
    {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        firstName: 'System',
        lastName: 'Administrator',
        role: 'admin',
        status: 'active',
        lastLogin: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'system'
    },
    {
        id: '2',
        username: 'editor1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Editor',
        role: 'editor',
        status: 'active',
        lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: '1'
    },
    {
        id: '3',
        username: 'moderator1',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Moderator',
        role: 'moderator',
        status: 'active',
        lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: '1'
    },
    {
        id: '4',
        username: 'inactive_user',
        email: '<EMAIL>',
        firstName: 'Inactive',
        lastName: 'User',
        role: 'editor',
        status: 'inactive',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 60).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: '1'
    }
];
/**
 * Mock API functions
 */ const mockAPI = {
    getUsers: async (params)=>{
        await new Promise((resolve)=>setTimeout(resolve, 500)); // Simulate network delay
        let filteredUsers = [
            ...mockUsers
        ];
        // Apply filters
        if (params.search) {
            const search = params.search.toLowerCase();
            filteredUsers = filteredUsers.filter((user)=>user.username.toLowerCase().includes(search) || user.email.toLowerCase().includes(search) || user.firstName?.toLowerCase().includes(search) || user.lastName?.toLowerCase().includes(search));
        }
        if (params.role) {
            filteredUsers = filteredUsers.filter((user)=>user.role === params.role);
        }
        if (params.status) {
            filteredUsers = filteredUsers.filter((user)=>user.status === params.status);
        }
        // Apply sorting
        if (params.sortBy) {
            filteredUsers.sort((a, b)=>{
                const aValue = a[params.sortBy] || '';
                const bValue = b[params.sortBy] || '';
                const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
                return params.sortOrder === 'desc' ? -comparison : comparison;
            });
        }
        // Apply pagination
        const page = params.page || 1;
        const limit = params.limit || 20;
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
        return {
            users: paginatedUsers,
            total: filteredUsers.length,
            page,
            limit,
            totalPages: Math.ceil(filteredUsers.length / limit)
        };
    },
    getUser: async (id)=>{
        await new Promise((resolve)=>setTimeout(resolve, 300));
        const user = mockUsers.find((u)=>u.id === id);
        if (!user) throw new Error('User not found');
        return user;
    },
    createUser: async (data)=>{
        await new Promise((resolve)=>setTimeout(resolve, 1000));
        const newUser = {
            id: String(mockUsers.length + 1),
            ...data,
            status: data.status || 'active',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: '1'
        };
        mockUsers.push(newUser);
        return newUser;
    },
    updateUser: async (id, data)=>{
        await new Promise((resolve)=>setTimeout(resolve, 800));
        const userIndex = mockUsers.findIndex((u)=>u.id === id);
        if (userIndex === -1) throw new Error('User not found');
        mockUsers[userIndex] = {
            ...mockUsers[userIndex],
            ...data,
            updatedAt: new Date().toISOString()
        };
        return mockUsers[userIndex];
    },
    deleteUser: async (id)=>{
        await new Promise((resolve)=>setTimeout(resolve, 500));
        const userIndex = mockUsers.findIndex((u)=>u.id === id);
        if (userIndex === -1) throw new Error('User not found');
        mockUsers.splice(userIndex, 1);
    },
    getStatistics: async ()=>{
        await new Promise((resolve)=>setTimeout(resolve, 400));
        const total = mockUsers.length;
        const active = mockUsers.filter((u)=>u.status === 'active').length;
        const inactive = mockUsers.filter((u)=>u.status === 'inactive').length;
        const suspended = mockUsers.filter((u)=>u.status === 'suspended').length;
        const byRole = {
            admin: mockUsers.filter((u)=>u.role === 'admin').length,
            editor: mockUsers.filter((u)=>u.role === 'editor').length,
            moderator: mockUsers.filter((u)=>u.role === 'moderator').length
        };
        const recentLogins = mockUsers.filter((u)=>{
            if (!u.lastLogin) return false;
            const lastLogin = new Date(u.lastLogin);
            const dayAgo = new Date(Date.now() - 1000 * 60 * 60 * 24);
            return lastLogin > dayAgo;
        }).length;
        const monthAgo = new Date(Date.now() - 1000 * 60 * 60 * 24 * 30);
        const newThisMonth = mockUsers.filter((u)=>{
            const created = new Date(u.createdAt);
            return created > monthAgo;
        }).length;
        return {
            total,
            active,
            inactive,
            suspended,
            byRole,
            recentLogins,
            newThisMonth
        };
    }
};
function useUsers(params = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$user$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_USER_PARAMS"]) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: userQueryKeys.list(params),
        queryFn: ()=>mockAPI.getUsers(params),
        staleTime: 5 * 60 * 1000
    });
}
function useUser(id) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: userQueryKeys.detail(id),
        queryFn: ()=>mockAPI.getUser(id),
        enabled: !!id,
        staleTime: 5 * 60 * 1000
    });
}
function useUserStatistics() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: userQueryKeys.statistics(),
        queryFn: ()=>mockAPI.getStatistics(),
        staleTime: 2 * 60 * 1000
    });
}
function useCreateUser() {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (data)=>mockAPI.createUser(data),
        onSuccess: ()=>{
            queryClient.invalidateQueries({
                queryKey: userQueryKeys.lists()
            });
            queryClient.invalidateQueries({
                queryKey: userQueryKeys.statistics()
            });
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success('User created successfully');
        },
        onError: (error)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error(`Failed to create user: ${error.message}`);
        }
    });
}
function useUpdateUser() {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: ({ id, data })=>mockAPI.updateUser(id, data),
        onSuccess: (updatedUser)=>{
            queryClient.invalidateQueries({
                queryKey: userQueryKeys.lists()
            });
            queryClient.invalidateQueries({
                queryKey: userQueryKeys.detail(updatedUser.id)
            });
            queryClient.invalidateQueries({
                queryKey: userQueryKeys.statistics()
            });
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success('User updated successfully');
        },
        onError: (error)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error(`Failed to update user: ${error.message}`);
        }
    });
}
function useDeleteUser() {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (id)=>mockAPI.deleteUser(id),
        onSuccess: ()=>{
            queryClient.invalidateQueries({
                queryKey: userQueryKeys.lists()
            });
            queryClient.invalidateQueries({
                queryKey: userQueryKeys.statistics()
            });
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success('User deleted successfully');
        },
        onError: (error)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error(`Failed to delete user: ${error.message}`);
        }
    });
}
}}),
"[project]/src/hooks/api/broadcast-hooks.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Broadcast Links API Hooks
 * Hooks for broadcast links management operations
 */ __turbopack_esm__({
    "useActiveBroadcastLinks": (()=>useActiveBroadcastLinks),
    "useBroadcastLink": (()=>useBroadcastLink),
    "useBroadcastLinks": (()=>useBroadcastLinks),
    "useBroadcastLinksByLanguage": (()=>useBroadcastLinksByLanguage),
    "useBroadcastLinksByQuality": (()=>useBroadcastLinksByQuality),
    "useBroadcastLinksManager": (()=>useBroadcastLinksManager),
    "useCreateBroadcastLink": (()=>useCreateBroadcastLink),
    "useDeleteBroadcastLink": (()=>useDeleteBroadcastLink),
    "useFixtureBroadcastLinks": (()=>useFixtureBroadcastLinks),
    "useToggleBroadcastLinkStatus": (()=>useToggleBroadcastLinkStatus),
    "useUpdateBroadcastLink": (()=>useUpdateBroadcastLink)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/base-hooks.ts [app-ssr] (ecmascript)");
'use client';
;
;
function useBroadcastLinks(params) {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());
    if (params?.fixtureId) queryParams.set('fixtureId', params.fixtureId);
    if (params?.quality) queryParams.set('quality', params.quality);
    if (params?.language) queryParams.set('language', params.language);
    if (params?.isActive !== undefined) queryParams.set('isActive', params.isActive.toString());
    if (params?.query) queryParams.set('query', params.query);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePaginatedQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.links(),
        params
    ], async ()=>{
        const response = await fetch(`/api/broadcast-links?${queryParams.toString()}`);
        if (!response.ok) {
            throw new Error(`Failed to fetch broadcast links: ${response.statusText}`);
        }
        return response.json();
    }, {
        staleTime: 2 * 60 * 1000
    });
}
function useBroadcastLink(linkId) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.link(linkId), async ()=>{
        const response = await fetch(`/api/broadcast-links/${linkId}`);
        if (!response.ok) {
            throw new Error(`Failed to fetch broadcast link: ${response.statusText}`);
        }
        return response.json();
    }, {
        enabled: !!linkId,
        staleTime: 2 * 60 * 1000
    });
}
function useFixtureBroadcastLinks(fixtureId) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.fixture(fixtureId), async ()=>{
        const response = await fetch(`/api/broadcast-links/fixture/${fixtureId}`);
        if (!response.ok) {
            throw new Error(`Failed to fetch fixture broadcast links: ${response.statusText}`);
        }
        return response.json();
    }, {
        enabled: !!fixtureId,
        staleTime: 1 * 60 * 1000
    });
}
function useCreateBroadcastLink() {
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useOptimisticMutation"])(async (data)=>{
        const response = await fetch('/api/broadcast-links', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        if (!response.ok) {
            throw new Error(`Failed to create broadcast link: ${response.statusText}`);
        }
        return response.json();
    }, {
        onSuccess: (data)=>{
            // Invalidate broadcast links queries
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.links());
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.fixture(data.fixtureId));
            console.log('✅ Broadcast link created successfully');
        },
        onError: (error)=>{
            console.error('❌ Failed to create broadcast link:', error);
        }
    });
}
function useUpdateBroadcastLink() {
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useOptimisticMutation"])(async ({ id, data })=>{
        const response = await fetch(`/api/broadcast-links/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        if (!response.ok) {
            throw new Error(`Failed to update broadcast link: ${response.statusText}`);
        }
        return response.json();
    }, {
        onSuccess: (data)=>{
            // Invalidate specific link and related queries
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.link(data.id));
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.links());
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.fixture(data.fixtureId));
            console.log('✅ Broadcast link updated successfully');
        },
        onError: (error)=>{
            console.error('❌ Failed to update broadcast link:', error);
        }
    });
}
function useDeleteBroadcastLink() {
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseMutation"])(async (linkId)=>{
        const response = await fetch(`/api/broadcast-links/${linkId}`, {
            method: 'DELETE'
        });
        if (!response.ok) {
            throw new Error(`Failed to delete broadcast link: ${response.statusText}`);
        }
        return response.json();
    }, {
        onSuccess: (_, linkId)=>{
            // Invalidate broadcast links queries
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.links());
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.link(linkId));
            console.log('✅ Broadcast link deleted successfully');
        },
        onError: (error)=>{
            console.error('❌ Failed to delete broadcast link:', error);
        }
    });
}
function useToggleBroadcastLinkStatus() {
    const { invalidateQueries, updateQueryData } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useOptimisticMutation"])(async ({ id, isActive })=>{
        const response = await fetch(`/api/broadcast-links/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                isActive
            })
        });
        if (!response.ok) {
            throw new Error(`Failed to toggle broadcast link status: ${response.statusText}`);
        }
        return response.json();
    }, {
        onMutate: async ({ id, isActive })=>{
            // Optimistically update the link status
            const linkQueryKey = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.link(id);
            const previousLink = updateQueryData(linkQueryKey, (old)=>old ? {
                    ...old,
                    isActive
                } : old);
            return {
                previousLink,
                linkQueryKey
            };
        },
        onError: (error, variables, context)=>{
            // Revert optimistic update on error
            if (context?.previousLink && context?.linkQueryKey) {
                updateQueryData(context.linkQueryKey, ()=>context.previousLink);
            }
            console.error('❌ Failed to toggle broadcast link status:', error);
        },
        onSuccess: (data)=>{
            // Invalidate related queries
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.links());
            invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.fixture(data.fixtureId));
            console.log('✅ Broadcast link status toggled successfully');
        }
    });
}
function useBroadcastLinksManager() {
    const createLink = useCreateBroadcastLink();
    const updateLink = useUpdateBroadcastLink();
    const deleteLink = useDeleteBroadcastLink();
    const toggleStatus = useToggleBroadcastLinkStatus();
    return {
        // Mutations
        createLink,
        updateLink,
        deleteLink,
        toggleStatus,
        // Actions
        createBroadcastLink: createLink.mutate,
        updateBroadcastLink: updateLink.mutate,
        deleteBroadcastLink: deleteLink.mutate,
        toggleLinkStatus: toggleStatus.mutate,
        // State
        isCreating: createLink.isPending,
        isUpdating: updateLink.isPending,
        isDeleting: deleteLink.isPending,
        isToggling: toggleStatus.isPending,
        isLoading: createLink.isPending || updateLink.isPending || deleteLink.isPending || toggleStatus.isPending,
        // Errors
        createError: createLink.error,
        updateError: updateLink.error,
        deleteError: deleteLink.error,
        toggleError: toggleStatus.error
    };
}
function useBroadcastLinksByQuality(quality) {
    return useBroadcastLinks({
        quality,
        isActive: true
    });
}
function useBroadcastLinksByLanguage(language) {
    return useBroadcastLinks({
        language,
        isActive: true
    });
}
function useActiveBroadcastLinks() {
    return useBroadcastLinks({
        isActive: true
    });
}
}}),
"[project]/src/hooks/api/health-hooks.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Health Check API Hooks
 * Hooks for API health monitoring operations
 */ __turbopack_esm__({
    "useApiHealth": (()=>useApiHealth),
    "useApiPerformance": (()=>useApiPerformance),
    "useDatabaseHealth": (()=>useDatabaseHealth),
    "useExternalApiHealth": (()=>useExternalApiHealth),
    "useHealthDashboard": (()=>useHealthDashboard),
    "useSystemHealth": (()=>useSystemHealth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/base-hooks.ts [app-ssr] (ecmascript)");
'use client';
;
;
function useApiHealth() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBackgroundSyncQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].health.api(), async ()=>{
        const response = await fetch('/api/health');
        if (!response.ok) {
            throw new Error(`Health check failed: ${response.statusText}`);
        }
        return response.json();
    }, {
        staleTime: 30 * 1000,
        refetchInterval: 60 * 1000,
        retry: 3,
        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 10000)
    });
}
function useDatabaseHealth() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].health.all,
        'database'
    ], async ()=>{
        const startTime = performance.now();
        const response = await fetch('/api/health/database');
        const endTime = performance.now();
        if (!response.ok) {
            throw new Error(`Database health check failed: ${response.statusText}`);
        }
        const data = await response.json();
        return {
            ...data,
            responseTime: endTime - startTime
        };
    }, {
        staleTime: 30 * 1000,
        retry: 2
    });
}
function useExternalApiHealth() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBaseQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryKeys"].health.all,
        'external-api'
    ], async ()=>{
        const startTime = performance.now();
        const response = await fetch('/api/health/external-api');
        const endTime = performance.now();
        if (!response.ok) {
            throw new Error(`External API health check failed: ${response.statusText}`);
        }
        const data = await response.json();
        return {
            ...data,
            responseTime: endTime - startTime
        };
    }, {
        staleTime: 60 * 1000,
        retry: 2
    });
}
function useSystemHealth() {
    const apiHealth = useApiHealth();
    const dbHealth = useDatabaseHealth();
    const externalApiHealth = useExternalApiHealth();
    const isLoading = apiHealth.isLoading || dbHealth.isLoading || externalApiHealth.isLoading;
    const hasErrors = apiHealth.isError || dbHealth.isError || externalApiHealth.isError;
    // Calculate overall health status
    const getOverallStatus = ()=>{
        if (hasErrors) return 'unhealthy';
        const apiStatus = apiHealth.data?.status;
        const dbStatus = dbHealth.data?.status;
        const externalStatus = externalApiHealth.data?.status;
        if (apiStatus === 'healthy' && dbStatus === 'up' && externalStatus === 'up') {
            return 'healthy';
        }
        if (apiStatus === 'unhealthy' || dbStatus === 'down') {
            return 'unhealthy';
        }
        return 'degraded';
    };
    // Calculate average response time
    const getAverageResponseTime = ()=>{
        const times = [
            dbHealth.data?.responseTime,
            externalApiHealth.data?.responseTime
        ].filter((time)=>typeof time === 'number');
        if (times.length === 0) return 0;
        return times.reduce((sum, time)=>sum + time, 0) / times.length;
    };
    return {
        // Individual health checks
        api: apiHealth,
        database: dbHealth,
        externalApi: externalApiHealth,
        // Overall status
        isLoading,
        hasErrors,
        overallStatus: getOverallStatus(),
        averageResponseTime: getAverageResponseTime(),
        // Health data
        healthData: {
            api: apiHealth.data,
            database: dbHealth.data,
            externalApi: externalApiHealth.data
        },
        // Error information
        errors: {
            api: apiHealth.error,
            database: dbHealth.error,
            externalApi: externalApiHealth.error
        },
        // Refetch functions
        refetchAll: ()=>{
            apiHealth.refetch();
            dbHealth.refetch();
            externalApiHealth.refetch();
        }
    };
}
function useApiPerformance() {
    const systemHealth = useSystemHealth();
    const getPerformanceMetrics = ()=>{
        const { healthData } = systemHealth;
        return {
            uptime: healthData.api?.uptime || 0,
            responseTime: systemHealth.averageResponseTime,
            status: systemHealth.overallStatus,
            services: {
                database: healthData.database?.status || 'unknown',
                externalApi: healthData.externalApi?.status || 'unknown'
            },
            lastCheck: new Date().toISOString()
        };
    };
    return {
        ...systemHealth,
        performanceMetrics: getPerformanceMetrics(),
        // Performance indicators
        isPerformanceGood: systemHealth.averageResponseTime < 1000,
        isPerformanceFair: systemHealth.averageResponseTime < 3000,
        isPerformancePoor: systemHealth.averageResponseTime >= 3000
    };
}
function useHealthDashboard() {
    const performance1 = useApiPerformance();
    const getDashboardData = ()=>{
        const { healthData, overallStatus, averageResponseTime } = performance1;
        return {
            status: overallStatus,
            uptime: healthData.api?.uptime || 0,
            version: healthData.api?.version || 'unknown',
            responseTime: averageResponseTime,
            services: [
                {
                    name: 'Database',
                    status: healthData.database?.status || 'unknown',
                    responseTime: healthData.database?.responseTime || 0
                },
                {
                    name: 'External API',
                    status: healthData.externalApi?.status || 'unknown',
                    responseTime: healthData.externalApi?.responseTime || 0
                }
            ],
            lastUpdated: new Date().toISOString()
        };
    };
    return {
        ...performance1,
        dashboardData: getDashboardData(),
        // Dashboard actions
        refreshDashboard: performance1.refetchAll,
        // Status indicators
        statusColor: ({
            healthy: '#10b981',
            degraded: '#f59e0b',
            unhealthy: '#ef4444'
        })[performance1.overallStatus],
        statusIcon: ({
            healthy: '✅',
            degraded: '⚠️',
            unhealthy: '❌'
        })[performance1.overallStatus]
    };
}
}}),
"[project]/src/hooks/api/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * API Hooks Index
 * Central export for all API hooks
 */ // Base hooks and utilities
__turbopack_esm__({
    "API_HOOKS_NAME": (()=>API_HOOKS_NAME),
    "API_HOOKS_VERSION": (()=>API_HOOKS_VERSION),
    "setupApiHooks": (()=>setupApiHooks)
});
;
;
;
;
;
;
;
const API_HOOKS_VERSION = '1.0.0';
const API_HOOKS_NAME = 'APISportsGame API Hooks';
function setupApiHooks() {
    console.log(`${API_HOOKS_NAME} v${API_HOOKS_VERSION} initialized`);
}
}}),
"[project]/src/hooks/api/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/base-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$auth$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/auth-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$football$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/football-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$users$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/users.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$broadcast$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/broadcast-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$health$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/health-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/src/hooks/api/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/app/api-hooks-demo/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * API Hooks Demo Page - Test page for API Hooks functionality
 * Demonstrates usage of various API hooks
 */ __turbopack_esm__({
    "default": (()=>ApiHooksDemoPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/hooks/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$health$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/health-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$auth$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/auth-hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/base-hooks.ts [app-ssr] (ecmascript)");
'use client';
;
;
function ApiHooksDemoPage() {
    const apiHealth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$health$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiHealth"])();
    const systemHealth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$health$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSystemHealth"])();
    const healthDashboard = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$health$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useHealthDashboard"])();
    const auth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$auth$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const apiUtils = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    const apiStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiStatus"])();
    const handleTestLogin = ()=>{
        auth.loginUser({
            username: 'admin',
            password: 'admin123456'
        });
    };
    const handleRefreshHealth = ()=>{
        healthDashboard.refreshDashboard();
    };
    const handleClearErrors = ()=>{
        apiStatus.clearAllErrors();
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            padding: '20px',
            fontFamily: 'Arial, sans-serif'
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                children: "API Hooks Demo"
            }, void 0, false, {
                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                lineNumber: 43,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginBottom: '20px',
                    padding: '10px',
                    border: '1px solid #ccc',
                    borderRadius: '5px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: "API Status Overview"
                    }, void 0, false, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 47,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Total Queries: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: apiStatus.getApiStatus().total
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 48,
                                columnNumber: 27
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 48,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Loading Queries: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: apiStatus.getApiStatus().loading
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 49,
                                columnNumber: 29
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 49,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Error Queries: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: apiStatus.getApiStatus().error
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 50,
                                columnNumber: 27
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 50,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Success Queries: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: apiStatus.getApiStatus().success
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 51,
                                columnNumber: 29
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 51,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Health Score: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: [
                                    apiStatus.getApiStatus().healthScore.toFixed(1),
                                    "%"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 52,
                                columnNumber: 26
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 52,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Is Loading: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: apiStatus.getApiStatus().isLoading ? 'Yes' : 'No'
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 53,
                                columnNumber: 24
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 53,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Has Errors: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: apiStatus.getApiStatus().hasErrors ? 'Yes' : 'No'
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 54,
                                columnNumber: 24
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 54,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleClearErrors,
                        style: {
                            marginTop: '10px',
                            padding: '5px 10px'
                        },
                        children: "Clear All Errors"
                    }, void 0, false, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 56,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                lineNumber: 46,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginBottom: '20px',
                    padding: '10px',
                    border: '1px solid #ccc',
                    borderRadius: '5px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: "Health Dashboard"
                    }, void 0, false, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 66,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Overall Status: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                style: {
                                    color: healthDashboard.statusColor
                                },
                                children: [
                                    healthDashboard.statusIcon,
                                    " ",
                                    healthDashboard.overallStatus
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 67,
                                columnNumber: 28
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 67,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Average Response Time: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: [
                                    healthDashboard.averageResponseTime.toFixed(2),
                                    "ms"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 70,
                                columnNumber: 35
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 70,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Performance: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: healthDashboard.isPerformanceGood ? 'Good' : healthDashboard.isPerformanceFair ? 'Fair' : 'Poor'
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 71,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 71,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        children: "Services Status"
                    }, void 0, false, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 76,
                        columnNumber: 9
                    }, this),
                    healthDashboard.dashboardData.services.map((service, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                marginLeft: '20px'
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: [
                                    service.name,
                                    ": ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                        children: service.status
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                        lineNumber: 79,
                                        columnNumber: 32
                                    }, this),
                                    " (",
                                    service.responseTime.toFixed(2),
                                    "ms)"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 79,
                                columnNumber: 13
                            }, this)
                        }, index, false, {
                            fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                            lineNumber: 78,
                            columnNumber: 11
                        }, this)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleRefreshHealth,
                        disabled: systemHealth.isLoading,
                        style: {
                            marginTop: '10px',
                            padding: '5px 10px'
                        },
                        children: systemHealth.isLoading ? 'Refreshing...' : 'Refresh Health'
                    }, void 0, false, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 83,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                lineNumber: 65,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginBottom: '20px',
                    padding: '10px',
                    border: '1px solid #ccc',
                    borderRadius: '5px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: "API Health Details"
                    }, void 0, false, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 94,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Status: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: apiHealth.data?.status || 'Unknown'
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 95,
                                columnNumber: 20
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 95,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Loading: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: apiHealth.isLoading ? 'Yes' : 'No'
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 96,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 96,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Error: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: apiHealth.error ? String(apiHealth.error) : 'None'
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 97,
                                columnNumber: 19
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 97,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Last Updated: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: apiHealth.dataUpdatedAt ? new Date(apiHealth.dataUpdatedAt).toLocaleTimeString() : 'Never'
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 98,
                                columnNumber: 26
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 98,
                        columnNumber: 9
                    }, this),
                    apiHealth.data && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("details", {
                        style: {
                            marginTop: '10px'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("summary", {
                                children: "Health Data"
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 104,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                                style: {
                                    background: '#f5f5f5',
                                    padding: '10px',
                                    overflow: 'auto'
                                },
                                children: JSON.stringify(apiHealth.data, null, 2)
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 105,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 103,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                lineNumber: 93,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginBottom: '20px',
                    padding: '10px',
                    border: '1px solid #ccc',
                    borderRadius: '5px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: "Authentication Demo"
                    }, void 0, false, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 114,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Authenticated: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: auth.isAuthenticated ? 'Yes' : 'No'
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 115,
                                columnNumber: 27
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 115,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "User: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: auth.user ? `${auth.user.username} (${auth.user.role})` : 'None'
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 116,
                                columnNumber: 18
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 116,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Loading: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: auth.isLoading ? 'Yes' : 'No'
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 117,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 117,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Error: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: auth.error ? String(auth.error) : 'None'
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 118,
                                columnNumber: 19
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 118,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            marginTop: '10px'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleTestLogin,
                                disabled: auth.isLoading || auth.isAuthenticated,
                                style: {
                                    marginRight: '10px',
                                    padding: '5px 10px'
                                },
                                children: "Test Login"
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 121,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>auth.logoutUser(),
                                disabled: auth.isLoading || !auth.isAuthenticated,
                                style: {
                                    padding: '5px 10px'
                                },
                                children: "Logout"
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 128,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 120,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                lineNumber: 113,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginBottom: '20px',
                    padding: '10px',
                    border: '1px solid #ccc',
                    borderRadius: '5px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: "System Health Summary"
                    }, void 0, false, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 140,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Overall Status: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: systemHealth.overallStatus
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 141,
                                columnNumber: 28
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 141,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Is Loading: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: systemHealth.isLoading ? 'Yes' : 'No'
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 142,
                                columnNumber: 24
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 142,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Has Errors: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: systemHealth.hasErrors ? 'Yes' : 'No'
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 143,
                                columnNumber: 24
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 143,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Average Response Time: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: [
                                    systemHealth.averageResponseTime.toFixed(2),
                                    "ms"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 144,
                                columnNumber: 35
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 144,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        children: "Individual Services"
                    }, void 0, false, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 146,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            marginLeft: '20px'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: [
                                    "API: ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                        children: systemHealth.api.data?.status || 'Unknown'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                        lineNumber: 148,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 148,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: [
                                    "Database: ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                        children: systemHealth.database.data?.status || 'Unknown'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                        lineNumber: 149,
                                        columnNumber: 24
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 149,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: [
                                    "External API: ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                        children: systemHealth.externalApi.data?.status || 'Unknown'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                        lineNumber: 150,
                                        columnNumber: 28
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 150,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 147,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                lineNumber: 139,
                columnNumber: 7
            }, this),
            ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginBottom: '20px',
                    padding: '10px',
                    border: '1px solid #ccc',
                    borderRadius: '5px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: "Development Info"
                    }, void 0, false, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 157,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Environment: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: ("TURBOPACK compile-time value", "development")
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 158,
                                columnNumber: 27
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 158,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "API Hooks: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: "Available"
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 159,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 159,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Query Client: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: "Connected"
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 160,
                                columnNumber: 28
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 160,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Real-time Updates: ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                children: "Active"
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 161,
                                columnNumber: 33
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 161,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                lineNumber: 156,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginTop: '30px',
                    padding: '10px',
                    border: '1px solid #ccc',
                    borderRadius: '5px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: "Navigation"
                    }, void 0, false, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 167,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "This demo page shows the API Hooks functionality."
                    }, void 0, false, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 168,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            display: 'flex',
                            gap: '10px'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                href: "/",
                                style: {
                                    color: 'blue',
                                    textDecoration: 'underline'
                                },
                                children: "← Back to Home"
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 170,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                href: "/simple-query-demo",
                                style: {
                                    color: 'blue',
                                    textDecoration: 'underline'
                                },
                                children: "Simple Query Demo"
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 173,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                href: "/store-demo",
                                style: {
                                    color: 'blue',
                                    textDecoration: 'underline'
                                },
                                children: "Store Demo"
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                                lineNumber: 176,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                        lineNumber: 169,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/api-hooks-demo/page.tsx",
                lineNumber: 166,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/api-hooks-demo/page.tsx",
        lineNumber: 42,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/api-hooks-demo/page.tsx [app-rsc] (ecmascript, Next.js server component, client modules ssr)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),

};

//# sourceMappingURL=src_ecffce._.js.map