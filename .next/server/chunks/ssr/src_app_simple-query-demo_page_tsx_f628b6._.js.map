{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/simple-query-demo/page.tsx"], "sourcesContent": ["/**\n * Simple Query Demo Page - Basic test for Query Provider\n */\n\n'use client';\n\nimport React from 'react';\nimport { useQueryClient } from '@tanstack/react-query';\n\nexport default function SimpleQueryDemoPage() {\n  const queryClient = useQueryClient();\n\n  return (\n    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>\n      <h1>Simple Query Provider Demo</h1>\n      \n      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>Query Client Status</h2>\n        <p>Query Client Available: <strong>{queryClient ? 'Yes' : 'No'}</strong></p>\n        <p>Cache Size: <strong>{queryClient ? queryClient.getQueryCache().getAll().length : 0} queries</strong></p>\n      </div>\n\n      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>Environment Info</h2>\n        <p>Environment: <strong>{process.env.NODE_ENV}</strong></p>\n        <p>Query Provider: <strong>Working</strong></p>\n      </div>\n\n      <div style={{ marginTop: '30px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>Navigation</h2>\n        <a href=\"/\" style={{ color: 'blue', textDecoration: 'underline' }}>\n          ← Back to Home\n        </a>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAKD;AAHA;;;AAKe,SAAS;IACtB,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,8OAAC;QAAI,OAAO;YAAE,SAAS;YAAQ,YAAY;QAAoB;;0BAC7D,8OAAC;0BAAG;;;;;;0BAEJ,8OAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCACjG,8OAAC;kCAAG;;;;;;kCACJ,8OAAC;;4BAAE;0CAAwB,8OAAC;0CAAQ,cAAc,QAAQ;;;;;;;;;;;;kCAC1D,8OAAC;;4BAAE;0CAAY,8OAAC;;oCAAQ,cAAc,YAAY,aAAa,GAAG,MAAM,GAAG,MAAM,GAAG;oCAAE;;;;;;;;;;;;;;;;;;;0BAGxF,8OAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCACjG,8OAAC;kCAAG;;;;;;kCACJ,8OAAC;;4BAAE;0CAAa,8OAAC;;;;;;;;;;;;;kCACjB,8OAAC;;4BAAE;0CAAgB,8OAAC;0CAAO;;;;;;;;;;;;;;;;;;0BAG7B,8OAAC;gBAAI,OAAO;oBAAE,WAAW;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCAC9F,8OAAC;kCAAG;;;;;;kCACJ,8OAAC;wBAAE,MAAK;wBAAI,OAAO;4BAAE,OAAO;4BAAQ,gBAAgB;wBAAY;kCAAG;;;;;;;;;;;;;;;;;;AAM3E"}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}