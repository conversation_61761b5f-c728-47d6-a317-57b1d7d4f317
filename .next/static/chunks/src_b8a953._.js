(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_b8a953._.js", {

"[project]/src/stores/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Utilities
 * Helper functions and utilities for store management
 */ __turbopack_esm__({
    "clearStoredTokens": (()=>clearStoredTokens),
    "createBaseStoreActions": (()=>createBaseStoreActions),
    "createBaseStoreState": (()=>createBaseStoreState),
    "createErrorObject": (()=>createErrorObject),
    "createStoreWithMiddleware": (()=>createStoreWithMiddleware),
    "extractErrorMessage": (()=>extractErrorMessage),
    "generateNotificationId": (()=>generateNotificationId),
    "getDefaultNotificationDuration": (()=>getDefaultNotificationDuration),
    "getSessionRemainingTime": (()=>getSessionRemainingTime),
    "getTokenExpiration": (()=>getTokenExpiration),
    "isSessionValid": (()=>isSessionValid),
    "isTokenExpired": (()=>isTokenExpired),
    "logStoreAction": (()=>logStoreAction),
    "storage": (()=>storage),
    "validateStoreState": (()=>validateStoreState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
;
function createStoreWithMiddleware(storeCreator, config) {
    let store = storeCreator;
    // Apply persistence middleware if configured
    if (config.persist) {
        store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])(store, {
            name: config.persist.name,
            version: config.persist.version,
            storage: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createJSONStorage"])(()=>localStorage),
            partialize: config.persist.partialize || ((state)=>state),
            skipHydration: config.persist.skipHydration || false,
            onRehydrateStorage: ()=>(state)=>{
                    if (state) {
                        state.setHasHydrated(true);
                    }
                }
        });
    }
    // Apply devtools middleware if configured
    if (config.devtools) {
        store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devtools"])(store, {
            name: config.devtools.name,
            enabled: config.devtools.enabled && ("TURBOPACK compile-time value", "development") === 'development'
        });
    }
    return store;
}
function createBaseStoreState() {
    return {
        _hasHydrated: false,
        setHasHydrated: (hasHydrated)=>{
        // This will be implemented by the actual store
        }
    };
}
function createBaseStoreActions(set) {
    return {
        setHasHydrated: (hasHydrated)=>{
            set({
                _hasHydrated: hasHydrated
            });
        }
    };
}
function isTokenExpired(expiresAt) {
    return Date.now() >= expiresAt;
}
function getTokenExpiration(token, defaultMinutes = 60) {
    try {
        // Try to decode JWT token to get expiration
        const payload = JSON.parse(atob(token.split('.')[1]));
        if (payload.exp) {
            return payload.exp * 1000; // Convert to milliseconds
        }
    } catch (error) {
    // If JWT parsing fails, use default expiration
    }
    // Default expiration: current time + defaultMinutes
    return Date.now() + defaultMinutes * 60 * 1000;
}
function clearStoredTokens() {
    if ("TURBOPACK compile-time truthy", 1) {
        localStorage.removeItem('auth-storage');
        sessionStorage.removeItem('auth-storage');
    }
}
function isSessionValid(lastActivity, sessionTimeout) {
    const now = Date.now();
    const timeoutMs = sessionTimeout * 60 * 1000; // Convert minutes to milliseconds
    return now - lastActivity < timeoutMs;
}
function getSessionRemainingTime(lastActivity, sessionTimeout) {
    const now = Date.now();
    const timeoutMs = sessionTimeout * 60 * 1000;
    const elapsed = now - lastActivity;
    const remaining = timeoutMs - elapsed;
    return Math.max(0, Math.floor(remaining / (60 * 1000)));
}
function extractErrorMessage(error) {
    if (typeof error === 'string') {
        return error;
    }
    if (error?.response?.data?.message) {
        return error.response.data.message;
    }
    if (error?.message) {
        return error.message;
    }
    if (error?.error) {
        return error.error;
    }
    return 'An unexpected error occurred';
}
function createErrorObject(message, details) {
    return {
        message,
        details: details || null
    };
}
function generateNotificationId() {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
function getDefaultNotificationDuration(type) {
    switch(type){
        case 'success':
            return 3000; // 3 seconds
        case 'error':
            return 5000; // 5 seconds
        case 'warning':
            return 4000; // 4 seconds
        case 'info':
            return 3000; // 3 seconds
        default:
            return 3000;
    }
}
const storage = {
    get: (key)=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : null;
        } catch (error) {
            console.warn(`Error reading from localStorage key "${key}":`, error);
            return null;
        }
    },
    set: (key, value)=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.warn(`Error writing to localStorage key "${key}":`, error);
        }
    },
    remove: (key)=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.warn(`Error removing localStorage key "${key}":`, error);
        }
    },
    clear: ()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        try {
            localStorage.clear();
        } catch (error) {
            console.warn('Error clearing localStorage:', error);
        }
    }
};
function logStoreAction(storeName, actionName, payload) {
    if ("TURBOPACK compile-time truthy", 1) {
        console.group(`🐻 [${storeName}] ${actionName}`);
        if (payload !== undefined) {
            console.log('Payload:', payload);
        }
        console.log('Timestamp:', new Date().toISOString());
        console.groupEnd();
    }
}
function validateStoreState(state, requiredKeys) {
    for (const key of requiredKeys){
        if (!(key in state)) {
            console.error(`Missing required store state key: ${String(key)}`);
            return false;
        }
    }
    return true;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/constants.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Constants
 * Defines constants used across all stores
 */ // ============================================================================
// Store Names
// ============================================================================
__turbopack_esm__({
    "ACTIVITY_TRACKING_INTERVAL": (()=>ACTIVITY_TRACKING_INTERVAL),
    "DEFAULT_APP_SETTINGS": (()=>DEFAULT_APP_SETTINGS),
    "DEFAULT_NAVIGATION": (()=>DEFAULT_NAVIGATION),
    "DEFAULT_THEME": (()=>DEFAULT_THEME),
    "DEFAULT_UI_STATE": (()=>DEFAULT_UI_STATE),
    "DEV_CONFIG": (()=>DEV_CONFIG),
    "ERROR_MESSAGES": (()=>ERROR_MESSAGES),
    "FEATURE_FLAGS": (()=>FEATURE_FLAGS),
    "SESSION_TIMEOUT": (()=>SESSION_TIMEOUT),
    "STORAGE_KEYS": (()=>STORAGE_KEYS),
    "STORE_API_ENDPOINTS": (()=>STORE_API_ENDPOINTS),
    "STORE_NAMES": (()=>STORE_NAMES),
    "STORE_VERSIONS": (()=>STORE_VERSIONS),
    "SUCCESS_MESSAGES": (()=>SUCCESS_MESSAGES),
    "TOKEN_REFRESH_THRESHOLD": (()=>TOKEN_REFRESH_THRESHOLD),
    "VALIDATION_RULES": (()=>VALIDATION_RULES)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const STORE_NAMES = {
    AUTH: 'auth-store',
    APP: 'app-store'
};
const STORAGE_KEYS = {
    AUTH: 'auth-storage',
    APP: 'app-storage',
    THEME: 'theme-storage',
    SETTINGS: 'settings-storage'
};
const DEFAULT_THEME = {
    mode: 'light',
    primaryColor: '#1890ff',
    borderRadius: 6,
    compactMode: false
};
const DEFAULT_APP_SETTINGS = {
    language: 'en',
    timezone: 'UTC',
    dateFormat: 'YYYY-MM-DD',
    pageSize: 20,
    autoRefresh: true,
    refreshInterval: 30,
    features: {
        darkMode: true,
        notifications: true,
        autoSave: true,
        advancedFilters: true
    }
};
const DEFAULT_NAVIGATION = {
    currentPath: '/',
    breadcrumbs: [],
    sidebarCollapsed: false,
    activeMenuKey: 'dashboard'
};
const DEFAULT_UI_STATE = {
    globalLoading: false,
    loadingMessage: '',
    globalError: null,
    errorDetails: null,
    notifications: [],
    modals: {}
};
const SESSION_TIMEOUT = 60; // 1 hour
const TOKEN_REFRESH_THRESHOLD = 5; // 5 minutes
const ACTIVITY_TRACKING_INTERVAL = 60000; // 1 minute
const STORE_API_ENDPOINTS = {
    AUTH: {
        LOGIN: '/api/system-auth/login',
        LOGOUT: '/api/system-auth/logout',
        LOGOUT_ALL: '/api/system-auth/logout-all',
        PROFILE: '/api/system-auth/profile',
        REFRESH: '/api/system-auth/refresh'
    }
};
const ERROR_MESSAGES = {
    AUTH: {
        LOGIN_FAILED: 'Login failed. Please check your credentials.',
        LOGOUT_FAILED: 'Logout failed. Please try again.',
        SESSION_EXPIRED: 'Your session has expired. Please log in again.',
        TOKEN_REFRESH_FAILED: 'Failed to refresh authentication token.',
        PROFILE_UPDATE_FAILED: 'Failed to update profile. Please try again.',
        UNAUTHORIZED: 'You are not authorized to perform this action.'
    },
    APP: {
        SETTINGS_SAVE_FAILED: 'Failed to save settings. Please try again.',
        THEME_LOAD_FAILED: 'Failed to load theme configuration.',
        NETWORK_ERROR: 'Network error. Please check your connection.',
        UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.'
    }
};
const SUCCESS_MESSAGES = {
    AUTH: {
        LOGIN_SUCCESS: 'Successfully logged in.',
        LOGOUT_SUCCESS: 'Successfully logged out.',
        PROFILE_UPDATED: 'Profile updated successfully.'
    },
    APP: {
        SETTINGS_SAVED: 'Settings saved successfully.',
        THEME_UPDATED: 'Theme updated successfully.'
    }
};
const STORE_VERSIONS = {
    AUTH: 1,
    APP: 1
};
const DEV_CONFIG = {
    ENABLE_DEVTOOLS: ("TURBOPACK compile-time value", "development") === 'development',
    ENABLE_LOGGING: ("TURBOPACK compile-time value", "development") === 'development',
    MOCK_API_DELAY: 1000
};
const FEATURE_FLAGS = {
    ENABLE_DARK_MODE: true,
    ENABLE_NOTIFICATIONS: true,
    ENABLE_AUTO_SAVE: true,
    ENABLE_ADVANCED_FILTERS: true,
    ENABLE_REAL_TIME_UPDATES: false,
    ENABLE_OFFLINE_MODE: false
};
const VALIDATION_RULES = {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    PASSWORD_MIN_LENGTH: 8,
    NAME_MIN_LENGTH: 2,
    NAME_MAX_LENGTH: 50,
    PAGE_SIZE_MIN: 5,
    PAGE_SIZE_MAX: 100,
    REFRESH_INTERVAL_MIN: 10,
    REFRESH_INTERVAL_MAX: 300
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/auth-store.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Authentication Store
 * Manages user authentication state, tokens, and session
 */ __turbopack_esm__({
    "createAuthStore": (()=>createAuthStore),
    "useAuthStore": (()=>useAuthStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
;
;
;
// ============================================================================
// Initial State
// ============================================================================
const initialAuthState = {
    // Base store state
    _hasHydrated: false,
    // User data
    user: null,
    tokens: null,
    // Authentication status
    isAuthenticated: false,
    isLoading: false,
    // Error handling
    error: null,
    // Session management
    lastActivity: Date.now(),
    sessionTimeout: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SESSION_TIMEOUT"]
};
// ============================================================================
// Store Implementation
// ============================================================================
/**
 * Authentication Store Creator
 */ const createAuthStore = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createStoreWithMiddleware"])((set, get)=>({
            ...initialAuthState,
            // Base store actions
            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createBaseStoreActions"])(set),
            // ========================================================================
            // Authentication Actions
            // ========================================================================
            /**
         * Login user with email and password
         */ login: async (email, password)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'login', {
                    email
                });
                set({
                    isLoading: true,
                    error: null
                });
                try {
                    const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.LOGIN, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            email,
                            password
                        })
                    });
                    if (!response.ok) {
                        const errorData = await response.json().catch(()=>({}));
                        throw new Error(errorData.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.LOGIN_FAILED);
                    }
                    const data = await response.json();
                    if (data.success && data.data) {
                        const { user, accessToken, refreshToken } = data.data;
                        // Create tokens object
                        const tokens = {
                            accessToken,
                            refreshToken,
                            expiresAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTokenExpiration"])(accessToken)
                        };
                        // Update store state
                        set({
                            user,
                            tokens,
                            isAuthenticated: true,
                            isLoading: false,
                            error: null,
                            lastActivity: Date.now()
                        });
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'login_success', {
                            userId: user.id
                        });
                    } else {
                        throw new Error(data.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.LOGIN_FAILED);
                    }
                } catch (error) {
                    const errorMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'login_error', {
                        error: errorMessage
                    });
                    set({
                        isLoading: false,
                        error: errorMessage,
                        isAuthenticated: false,
                        user: null,
                        tokens: null
                    });
                    throw error;
                }
            },
            /**
         * Logout user from current session
         */ logout: async ()=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout');
                const { tokens } = get();
                try {
                    // Call logout API if we have tokens
                    if (tokens?.accessToken) {
                        await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.LOGOUT, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${tokens.accessToken}`
                            }
                        });
                    }
                } catch (error) {
                    // Log error but don't prevent logout
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_api_error', {
                        error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error)
                    });
                }
                // Clear local state regardless of API call result
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearStoredTokens"])();
                set({
                    user: null,
                    tokens: null,
                    isAuthenticated: false,
                    error: null,
                    lastActivity: Date.now()
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_success');
            },
            /**
         * Logout user from all devices
         */ logoutAll: async ()=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_all');
                const { tokens } = get();
                try {
                    if (tokens?.accessToken) {
                        await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.LOGOUT_ALL, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${tokens.accessToken}`
                            }
                        });
                    }
                } catch (error) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_all_api_error', {
                        error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error)
                    });
                }
                // Clear local state
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearStoredTokens"])();
                set({
                    user: null,
                    tokens: null,
                    isAuthenticated: false,
                    error: null,
                    lastActivity: Date.now()
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_all_success');
            },
            // ========================================================================
            // User Profile Actions
            // ========================================================================
            /**
         * Update user profile
         */ updateProfile: async (data)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'update_profile', data);
                const { tokens, user } = get();
                if (!tokens?.accessToken || !user) {
                    throw new Error(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.UNAUTHORIZED);
                }
                set({
                    isLoading: true,
                    error: null
                });
                try {
                    const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.PROFILE, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${tokens.accessToken}`
                        },
                        body: JSON.stringify(data)
                    });
                    if (!response.ok) {
                        const errorData = await response.json().catch(()=>({}));
                        throw new Error(errorData.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.PROFILE_UPDATE_FAILED);
                    }
                    const responseData = await response.json();
                    if (responseData.success && responseData.data) {
                        set({
                            user: {
                                ...user,
                                ...responseData.data
                            },
                            isLoading: false,
                            error: null,
                            lastActivity: Date.now()
                        });
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'update_profile_success');
                    } else {
                        throw new Error(responseData.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.PROFILE_UPDATE_FAILED);
                    }
                } catch (error) {
                    const errorMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'update_profile_error', {
                        error: errorMessage
                    });
                    set({
                        isLoading: false,
                        error: errorMessage
                    });
                    throw error;
                }
            },
            /**
         * Refresh authentication tokens
         */ refreshTokens: async ()=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'refresh_tokens');
                const { tokens } = get();
                if (!tokens?.refreshToken) {
                    throw new Error(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.TOKEN_REFRESH_FAILED);
                }
                try {
                    const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.REFRESH, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            refreshToken: tokens.refreshToken
                        })
                    });
                    if (!response.ok) {
                        throw new Error(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.TOKEN_REFRESH_FAILED);
                    }
                    const data = await response.json();
                    if (data.success && data.data) {
                        const { accessToken, refreshToken } = data.data;
                        const newTokens = {
                            accessToken,
                            refreshToken,
                            expiresAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTokenExpiration"])(accessToken)
                        };
                        set({
                            tokens: newTokens,
                            lastActivity: Date.now()
                        });
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'refresh_tokens_success');
                    } else {
                        throw new Error(data.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.TOKEN_REFRESH_FAILED);
                    }
                } catch (error) {
                    const errorMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'refresh_tokens_error', {
                        error: errorMessage
                    });
                    // If refresh fails, logout user
                    get().logout();
                    throw error;
                }
            },
            // ========================================================================
            // State Management Actions
            // ========================================================================
            /**
         * Set user data
         */ setUser: (user)=>{
                set({
                    user,
                    isAuthenticated: !!user
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'set_user', {
                    userId: user?.id
                });
            },
            /**
         * Set authentication tokens
         */ setTokens: (tokens)=>{
                set({
                    tokens
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'set_tokens', {
                    hasTokens: !!tokens
                });
            },
            /**
         * Set loading state
         */ setLoading: (loading)=>{
                set({
                    isLoading: loading
                });
            },
            /**
         * Set error message
         */ setError: (error)=>{
                set({
                    error
                });
                if (error) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'set_error', {
                        error
                    });
                }
            },
            /**
         * Clear error message
         */ clearError: ()=>{
                set({
                    error: null
                });
            },
            // ========================================================================
            // Session Management Actions
            // ========================================================================
            /**
         * Update last activity timestamp
         */ updateLastActivity: ()=>{
                set({
                    lastActivity: Date.now()
                });
            },
            /**
         * Check if current session is valid
         */ checkSession: ()=>{
                const { lastActivity, sessionTimeout, tokens } = get();
                // Check session timeout
                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSessionValid"])(lastActivity, sessionTimeout)) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'session_expired');
                    get().logout();
                    return false;
                }
                // Check token expiration
                if (tokens && (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTokenExpired"])(tokens.expiresAt)) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'token_expired');
                    // Try to refresh tokens
                    get().refreshTokens().catch(()=>{
                    // If refresh fails, logout will be called automatically
                    });
                    return false;
                }
                return true;
            },
            /**
         * Hydrate store from persisted state
         */ hydrate: ()=>{
                const state = get();
                // Validate persisted session
                if (state.isAuthenticated && state.user && state.tokens) {
                    const isValid = state.checkSession();
                    if (!isValid) {
                        // Session is invalid, clear state
                        set({
                            user: null,
                            tokens: null,
                            isAuthenticated: false,
                            error: null
                        });
                    }
                }
                set({
                    _hasHydrated: true
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'hydrated');
            }
        }), {
        persist: {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].AUTH,
            version: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_VERSIONS"].AUTH,
            partialize: (state)=>({
                    user: state.user,
                    tokens: state.tokens,
                    isAuthenticated: state.isAuthenticated,
                    lastActivity: state.lastActivity,
                    sessionTimeout: state.sessionTimeout
                })
        },
        devtools: {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH,
            enabled: ("TURBOPACK compile-time value", "development") === 'development'
        }
    }));
};
const useAuthStore = createAuthStore();
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/auth-hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Authentication Hooks
 * Custom hooks for easy authentication state access
 */ __turbopack_esm__({
    "useAuth": (()=>useAuth),
    "useAuthDebug": (()=>useAuthDebug),
    "useAuthError": (()=>useAuthError),
    "useAuthLoading": (()=>useAuthLoading),
    "useAuthTokens": (()=>useAuthTokens),
    "useAuthWithSession": (()=>useAuthWithSession),
    "useCanAdmin": (()=>useCanAdmin),
    "useCanEdit": (()=>useCanEdit),
    "useCheckSession": (()=>useCheckSession),
    "useClearAuthError": (()=>useClearAuthError),
    "useHasRole": (()=>useHasRole),
    "useIsAdmin": (()=>useIsAdmin),
    "useIsAuthenticated": (()=>useIsAuthenticated),
    "useIsEditor": (()=>useIsEditor),
    "useIsModerator": (()=>useIsModerator),
    "useLogin": (()=>useLogin),
    "useLogout": (()=>useLogout),
    "useLogoutAll": (()=>useLogoutAll),
    "usePermissions": (()=>usePermissions),
    "useRefreshTokens": (()=>useRefreshTokens),
    "useRouteProtection": (()=>useRouteProtection),
    "useUpdateActivity": (()=>useUpdateActivity),
    "useUpdateProfile": (()=>useUpdateProfile),
    "useUser": (()=>useUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature(), _s5 = __turbopack_refresh__.signature(), _s6 = __turbopack_refresh__.signature(), _s7 = __turbopack_refresh__.signature(), _s8 = __turbopack_refresh__.signature(), _s9 = __turbopack_refresh__.signature(), _s10 = __turbopack_refresh__.signature(), _s11 = __turbopack_refresh__.signature(), _s12 = __turbopack_refresh__.signature(), _s13 = __turbopack_refresh__.signature(), _s14 = __turbopack_refresh__.signature(), _s15 = __turbopack_refresh__.signature(), _s16 = __turbopack_refresh__.signature(), _s17 = __turbopack_refresh__.signature(), _s18 = __turbopack_refresh__.signature(), _s19 = __turbopack_refresh__.signature(), _s20 = __turbopack_refresh__.signature(), _s21 = __turbopack_refresh__.signature(), _s22 = __turbopack_refresh__.signature(), _s23 = __turbopack_refresh__.signature();
'use client';
;
;
;
const useUser = ()=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useUser.useAuthStore": (state)=>state.user
    }["useUser.useAuthStore"]);
};
_s(useUser, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useIsAuthenticated = ()=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useIsAuthenticated.useAuthStore": (state)=>state.isAuthenticated
    }["useIsAuthenticated.useAuthStore"]);
};
_s1(useIsAuthenticated, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useAuthLoading = ()=>{
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useAuthLoading.useAuthStore": (state)=>state.isLoading
    }["useAuthLoading.useAuthStore"]);
};
_s2(useAuthLoading, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useAuthError = ()=>{
    _s3();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useAuthError.useAuthStore": (state)=>state.error
    }["useAuthError.useAuthStore"]);
};
_s3(useAuthError, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useAuthTokens = ()=>{
    _s4();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useAuthTokens.useAuthStore": (state)=>state.tokens
    }["useAuthTokens.useAuthStore"]);
};
_s4(useAuthTokens, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useLogin = ()=>{
    _s5();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useLogin.useAuthStore": (state)=>state.login
    }["useLogin.useAuthStore"]);
};
_s5(useLogin, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useLogout = ()=>{
    _s6();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useLogout.useAuthStore": (state)=>state.logout
    }["useLogout.useAuthStore"]);
};
_s6(useLogout, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useLogoutAll = ()=>{
    _s7();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useLogoutAll.useAuthStore": (state)=>state.logoutAll
    }["useLogoutAll.useAuthStore"]);
};
_s7(useLogoutAll, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useUpdateProfile = ()=>{
    _s8();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useUpdateProfile.useAuthStore": (state)=>state.updateProfile
    }["useUpdateProfile.useAuthStore"]);
};
_s8(useUpdateProfile, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useRefreshTokens = ()=>{
    _s9();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useRefreshTokens.useAuthStore": (state)=>state.refreshTokens
    }["useRefreshTokens.useAuthStore"]);
};
_s9(useRefreshTokens, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useClearAuthError = ()=>{
    _s10();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useClearAuthError.useAuthStore": (state)=>state.clearError
    }["useClearAuthError.useAuthStore"]);
};
_s10(useClearAuthError, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useCheckSession = ()=>{
    _s11();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useCheckSession.useAuthStore": (state)=>state.checkSession
    }["useCheckSession.useAuthStore"]);
};
_s11(useCheckSession, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useUpdateActivity = ()=>{
    _s12();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useUpdateActivity.useAuthStore": (state)=>state.updateLastActivity
    }["useUpdateActivity.useAuthStore"]);
};
_s12(useUpdateActivity, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useHasRole = (role)=>{
    _s13();
    const user = useUser();
    return user?.role === role;
};
_s13(useHasRole, "BPnln+wUpxLjLAxQmw7xYz9C+QI=", false, function() {
    return [
        useUser
    ];
});
const useIsAdmin = ()=>{
    _s14();
    return useHasRole('Admin');
};
_s14(useIsAdmin, "jeHUSpWuvMoZ10WaPJ/up4tFqRo=", false, function() {
    return [
        useHasRole
    ];
});
const useIsEditor = ()=>{
    _s15();
    return useHasRole('Editor');
};
_s15(useIsEditor, "jeHUSpWuvMoZ10WaPJ/up4tFqRo=", false, function() {
    return [
        useHasRole
    ];
});
const useIsModerator = ()=>{
    _s16();
    return useHasRole('Moderator');
};
_s16(useIsModerator, "jeHUSpWuvMoZ10WaPJ/up4tFqRo=", false, function() {
    return [
        useHasRole
    ];
});
const useCanEdit = ()=>{
    _s17();
    const user = useUser();
    return user?.role === 'Admin' || user?.role === 'Editor';
};
_s17(useCanEdit, "BPnln+wUpxLjLAxQmw7xYz9C+QI=", false, function() {
    return [
        useUser
    ];
});
const useCanAdmin = ()=>{
    _s18();
    return useIsAdmin();
};
_s18(useCanAdmin, "Dqqt7lFNmzjkMdLJsNoq64CBQOo=", false, function() {
    return [
        useIsAdmin
    ];
});
const useAuth = ()=>{
    _s19();
    const user = useUser();
    const isAuthenticated = useIsAuthenticated();
    const isLoading = useAuthLoading();
    const error = useAuthError();
    const tokens = useAuthTokens();
    const login = useLogin();
    const logout = useLogout();
    const logoutAll = useLogoutAll();
    const updateProfile = useUpdateProfile();
    const clearError = useClearAuthError();
    return {
        // State
        user,
        isAuthenticated,
        isLoading,
        error,
        tokens,
        // Actions
        login,
        logout,
        logoutAll,
        updateProfile,
        clearError,
        // Role checks
        isAdmin: useIsAdmin(),
        isEditor: useIsEditor(),
        isModerator: useIsModerator(),
        canEdit: useCanEdit(),
        canAdmin: useCanAdmin()
    };
};
_s19(useAuth, "h91lhtaB2wbjWbd8Or0OBXynugQ=", false, function() {
    return [
        useUser,
        useIsAuthenticated,
        useAuthLoading,
        useAuthError,
        useAuthTokens,
        useLogin,
        useLogout,
        useLogoutAll,
        useUpdateProfile,
        useClearAuthError,
        useIsAdmin,
        useIsEditor,
        useIsModerator,
        useCanEdit,
        useCanAdmin
    ];
});
const useAuthWithSession = ()=>{
    _s20();
    const auth = useAuth();
    const checkSession = useCheckSession();
    const updateActivity = useUpdateActivity();
    // Auto-check session validity
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useAuthWithSession.useEffect": ()=>{
            if (auth.isAuthenticated) {
                const interval = setInterval({
                    "useAuthWithSession.useEffect.interval": ()=>{
                        checkSession();
                    }
                }["useAuthWithSession.useEffect.interval"], __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTIVITY_TRACKING_INTERVAL"]);
                return ({
                    "useAuthWithSession.useEffect": ()=>clearInterval(interval)
                })["useAuthWithSession.useEffect"];
            }
        }
    }["useAuthWithSession.useEffect"], [
        auth.isAuthenticated,
        checkSession
    ]);
    // Update activity on user interaction
    const handleUserActivity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthWithSession.useCallback[handleUserActivity]": ()=>{
            if (auth.isAuthenticated) {
                updateActivity();
            }
        }
    }["useAuthWithSession.useCallback[handleUserActivity]"], [
        auth.isAuthenticated,
        updateActivity
    ]);
    // Auto-update activity on mouse/keyboard events
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useAuthWithSession.useEffect": ()=>{
            if (auth.isAuthenticated) {
                const events = [
                    'mousedown',
                    'mousemove',
                    'keypress',
                    'scroll',
                    'touchstart'
                ];
                events.forEach({
                    "useAuthWithSession.useEffect": (event)=>{
                        document.addEventListener(event, handleUserActivity, true);
                    }
                }["useAuthWithSession.useEffect"]);
                return ({
                    "useAuthWithSession.useEffect": ()=>{
                        events.forEach({
                            "useAuthWithSession.useEffect": (event)=>{
                                document.removeEventListener(event, handleUserActivity, true);
                            }
                        }["useAuthWithSession.useEffect"]);
                    }
                })["useAuthWithSession.useEffect"];
            }
        }
    }["useAuthWithSession.useEffect"], [
        auth.isAuthenticated,
        handleUserActivity
    ]);
    return {
        ...auth,
        checkSession,
        updateActivity
    };
};
_s20(useAuthWithSession, "AZhya6d4LQ2m9d2CT9VfOC+ZQ+4=", false, function() {
    return [
        useAuth,
        useCheckSession,
        useUpdateActivity
    ];
});
const usePermissions = (requiredRoles)=>{
    _s21();
    const user = useUser();
    const hasPermission = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePermissions.useCallback[hasPermission]": (roles)=>{
            if (!user) return false;
            return roles.includes(user.role);
        }
    }["usePermissions.useCallback[hasPermission]"], [
        user
    ]);
    const hasAnyPermission = hasPermission(requiredRoles);
    return {
        hasPermission: hasAnyPermission,
        userRole: user?.role,
        checkRole: hasPermission
    };
};
_s21(usePermissions, "Lzp/G+M7rv7dScEztGTaLkLHqDw=", false, function() {
    return [
        useUser
    ];
});
const useRouteProtection = (requiredRoles)=>{
    _s22();
    const isAuthenticated = useIsAuthenticated();
    const user = useUser();
    const isLoading = useAuthLoading();
    const hasAccess = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRouteProtection.useCallback[hasAccess]": ()=>{
            if (!isAuthenticated) return false;
            if (!requiredRoles || requiredRoles.length === 0) return true;
            if (!user) return false;
            return requiredRoles.includes(user.role);
        }
    }["useRouteProtection.useCallback[hasAccess]"], [
        isAuthenticated,
        user,
        requiredRoles
    ]);
    return {
        isAuthenticated,
        hasAccess: hasAccess(),
        isLoading,
        user,
        shouldRedirect: !isLoading && !isAuthenticated,
        shouldShowUnauthorized: !isLoading && isAuthenticated && !hasAccess()
    };
};
_s22(useRouteProtection, "zSxlCGxBMi2FdpU3LspmENoF2lA=", false, function() {
    return [
        useIsAuthenticated,
        useUser,
        useAuthLoading
    ];
});
const useAuthDebug = ()=>{
    _s23();
    const state = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useAuthDebug.useAuthStore[state]": (state)=>state
    }["useAuthDebug.useAuthStore[state]"]);
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return {
        fullState: state,
        hasHydrated: state._hasHydrated,
        lastActivity: new Date(state.lastActivity).toISOString(),
        sessionTimeout: state.sessionTimeout,
        tokenExpiry: state.tokens ? new Date(state.tokens.expiresAt).toISOString() : null
    };
};
_s23(useAuthDebug, "u52TyCeu374WlgENmxbZ5P3kg3w=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/app-store.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Application Store
 * Manages application state, theme, settings, navigation, and UI state
 */ __turbopack_esm__({
    "createAppStore": (()=>createAppStore),
    "useAppStore": (()=>useAppStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
;
;
;
// ============================================================================
// Initial State
// ============================================================================
const initialAppState = {
    // Base store state
    _hasHydrated: false,
    // Configuration
    theme: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_THEME"],
    settings: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_APP_SETTINGS"],
    // Navigation
    navigation: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_NAVIGATION"],
    // UI state
    ui: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_UI_STATE"],
    // System info
    version: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
    buildTime: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_BUILD_TIME || new Date().toISOString(),
    environment: ("TURBOPACK compile-time value", "development") || 'development'
};
// ============================================================================
// Store Implementation
// ============================================================================
/**
 * Application Store Creator
 */ const createAppStore = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createStoreWithMiddleware"])((set, get)=>({
            ...initialAppState,
            // Base store actions
            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createBaseStoreActions"])(set),
            // ========================================================================
            // Theme Management Actions
            // ========================================================================
            /**
         * Set theme configuration
         */ setTheme: (themeUpdate)=>{
                const currentTheme = get().theme;
                const newTheme = {
                    ...currentTheme,
                    ...themeUpdate
                };
                set({
                    theme: newTheme
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_theme', themeUpdate);
            },
            /**
         * Toggle between light and dark mode
         */ toggleTheme: ()=>{
                const currentMode = get().theme.mode;
                const newMode = currentMode === 'light' ? 'dark' : 'light';
                get().setTheme({
                    mode: newMode
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'toggle_theme', {
                    newMode
                });
            },
            // ========================================================================
            // Settings Management Actions
            // ========================================================================
            /**
         * Update application settings
         */ updateSettings: (settingsUpdate)=>{
                const currentSettings = get().settings;
                const newSettings = {
                    ...currentSettings,
                    ...settingsUpdate
                };
                set({
                    settings: newSettings
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'update_settings', settingsUpdate);
            },
            /**
         * Reset settings to default values
         */ resetSettings: ()=>{
                set({
                    settings: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_APP_SETTINGS"]
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'reset_settings');
            },
            // ========================================================================
            // Navigation Actions
            // ========================================================================
            /**
         * Set current path
         */ setCurrentPath: (path)=>{
                const currentNavigation = get().navigation;
                const newNavigation = {
                    ...currentNavigation,
                    currentPath: path
                };
                set({
                    navigation: newNavigation
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_current_path', {
                    path
                });
            },
            /**
         * Set breadcrumbs
         */ setBreadcrumbs: (breadcrumbs)=>{
                const currentNavigation = get().navigation;
                const newNavigation = {
                    ...currentNavigation,
                    breadcrumbs
                };
                set({
                    navigation: newNavigation
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_breadcrumbs', {
                    count: breadcrumbs.length
                });
            },
            /**
         * Toggle sidebar collapsed state
         */ toggleSidebar: ()=>{
                const currentNavigation = get().navigation;
                const newCollapsed = !currentNavigation.sidebarCollapsed;
                const newNavigation = {
                    ...currentNavigation,
                    sidebarCollapsed: newCollapsed
                };
                set({
                    navigation: newNavigation
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'toggle_sidebar', {
                    collapsed: newCollapsed
                });
            },
            /**
         * Set active menu key
         */ setActiveMenu: (key)=>{
                const currentNavigation = get().navigation;
                const newNavigation = {
                    ...currentNavigation,
                    activeMenuKey: key
                };
                set({
                    navigation: newNavigation
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_active_menu', {
                    key
                });
            },
            // ========================================================================
            // UI State Management Actions
            // ========================================================================
            /**
         * Set global loading state
         */ setGlobalLoading: (loading, message)=>{
                const currentUI = get().ui;
                const newUI = {
                    ...currentUI,
                    globalLoading: loading,
                    loadingMessage: message || ''
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_global_loading', {
                    loading,
                    message
                });
            },
            /**
         * Set global error
         */ setGlobalError: (error, details)=>{
                const currentUI = get().ui;
                const newUI = {
                    ...currentUI,
                    globalError: error,
                    errorDetails: details || null
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_global_error', {
                    error,
                    hasDetails: !!details
                });
            },
            /**
         * Clear global error
         */ clearGlobalError: ()=>{
                const currentUI = get().ui;
                const newUI = {
                    ...currentUI,
                    globalError: null,
                    errorDetails: null
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'clear_global_error');
            },
            // ========================================================================
            // Notifications Actions
            // ========================================================================
            /**
         * Add notification
         */ addNotification: (notification)=>{
                const id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateNotificationId"])();
                const timestamp = Date.now();
                const duration = notification.duration || (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDefaultNotificationDuration"])(notification.type);
                const newNotification = {
                    ...notification,
                    id,
                    timestamp,
                    duration
                };
                const currentUI = get().ui;
                const newNotifications = [
                    ...currentUI.notifications,
                    newNotification
                ];
                const newUI = {
                    ...currentUI,
                    notifications: newNotifications
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'add_notification', {
                    type: notification.type,
                    id
                });
                // Auto-remove notification after duration
                if (duration > 0) {
                    setTimeout(()=>{
                        get().removeNotification(id);
                    }, duration);
                }
            },
            /**
         * Remove notification
         */ removeNotification: (id)=>{
                const currentUI = get().ui;
                const newNotifications = currentUI.notifications.filter((n)=>n.id !== id);
                const newUI = {
                    ...currentUI,
                    notifications: newNotifications
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'remove_notification', {
                    id
                });
            },
            /**
         * Clear all notifications
         */ clearNotifications: ()=>{
                const currentUI = get().ui;
                const newUI = {
                    ...currentUI,
                    notifications: []
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'clear_notifications');
            },
            // ========================================================================
            // Modals Actions
            // ========================================================================
            /**
         * Show modal
         */ showModal: (key, data)=>{
                const currentUI = get().ui;
                const newModals = {
                    ...currentUI.modals,
                    [key]: {
                        visible: true,
                        data
                    }
                };
                const newUI = {
                    ...currentUI,
                    modals: newModals
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'show_modal', {
                    key,
                    hasData: !!data
                });
            },
            /**
         * Hide modal
         */ hideModal: (key)=>{
                const currentUI = get().ui;
                const newModals = {
                    ...currentUI.modals,
                    [key]: {
                        visible: false,
                        data: undefined
                    }
                };
                const newUI = {
                    ...currentUI,
                    modals: newModals
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'hide_modal', {
                    key
                });
            },
            /**
         * Hide all modals
         */ hideAllModals: ()=>{
                const currentUI = get().ui;
                const newModals = {};
                // Set all modals to hidden
                Object.keys(currentUI.modals).forEach((key)=>{
                    newModals[key] = {
                        visible: false,
                        data: undefined
                    };
                });
                const newUI = {
                    ...currentUI,
                    modals: newModals
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'hide_all_modals');
            }
        }), {
        persist: {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].APP,
            version: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_VERSIONS"].APP,
            partialize: (state)=>({
                    theme: state.theme,
                    settings: state.settings,
                    navigation: {
                        sidebarCollapsed: state.navigation.sidebarCollapsed,
                        activeMenuKey: state.navigation.activeMenuKey
                    }
                })
        },
        devtools: {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP,
            enabled: ("TURBOPACK compile-time value", "development") === 'development'
        }
    }));
};
const useAppStore = createAppStore();
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/app-hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Application Hooks
 * Custom hooks for easy application state access
 */ __turbopack_esm__({
    "useActiveMenu": (()=>useActiveMenu),
    "useApp": (()=>useApp),
    "useAppSettings": (()=>useAppSettings),
    "useAppVersion": (()=>useAppVersion),
    "useBreadcrumbs": (()=>useBreadcrumbs),
    "useBuildTime": (()=>useBuildTime),
    "useCurrentPath": (()=>useCurrentPath),
    "useEnvironment": (()=>useEnvironment),
    "useGlobalError": (()=>useGlobalError),
    "useGlobalLoading": (()=>useGlobalLoading),
    "useIsDarkMode": (()=>useIsDarkMode),
    "useModal": (()=>useModal),
    "useModalActions": (()=>useModalActions),
    "useModals": (()=>useModals),
    "useNavigation": (()=>useNavigation),
    "useNavigationActions": (()=>useNavigationActions),
    "useNotificationActions": (()=>useNotificationActions),
    "useNotifications": (()=>useNotifications),
    "useNotify": (()=>useNotify),
    "useResponsive": (()=>useResponsive),
    "useSetting": (()=>useSetting),
    "useSettingsActions": (()=>useSettingsActions),
    "useSidebarState": (()=>useSidebarState),
    "useSystemInfo": (()=>useSystemInfo),
    "useTheme": (()=>useTheme),
    "useThemeActions": (()=>useThemeActions),
    "useThemeMode": (()=>useThemeMode),
    "useUIActions": (()=>useUIActions),
    "useUIState": (()=>useUIState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-store.ts [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature(), _s5 = __turbopack_refresh__.signature(), _s6 = __turbopack_refresh__.signature(), _s7 = __turbopack_refresh__.signature(), _s8 = __turbopack_refresh__.signature(), _s9 = __turbopack_refresh__.signature(), _s10 = __turbopack_refresh__.signature(), _s11 = __turbopack_refresh__.signature(), _s12 = __turbopack_refresh__.signature(), _s13 = __turbopack_refresh__.signature(), _s14 = __turbopack_refresh__.signature(), _s15 = __turbopack_refresh__.signature(), _s16 = __turbopack_refresh__.signature(), _s17 = __turbopack_refresh__.signature(), _s18 = __turbopack_refresh__.signature(), _s19 = __turbopack_refresh__.signature(), _s20 = __turbopack_refresh__.signature(), _s21 = __turbopack_refresh__.signature(), _s22 = __turbopack_refresh__.signature(), _s23 = __turbopack_refresh__.signature(), _s24 = __turbopack_refresh__.signature(), _s25 = __turbopack_refresh__.signature(), _s26 = __turbopack_refresh__.signature(), _s27 = __turbopack_refresh__.signature(), _s28 = __turbopack_refresh__.signature();
'use client';
;
;
const useTheme = ()=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useTheme.useAppStore": (state)=>state.theme
    }["useTheme.useAppStore"]);
};
_s(useTheme, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useThemeMode = ()=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useThemeMode.useAppStore": (state)=>state.theme.mode
    }["useThemeMode.useAppStore"]);
};
_s1(useThemeMode, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useIsDarkMode = ()=>{
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useIsDarkMode.useAppStore": (state)=>state.theme.mode === 'dark'
    }["useIsDarkMode.useAppStore"]);
};
_s2(useIsDarkMode, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useThemeActions = ()=>{
    _s3();
    const setTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useThemeActions.useAppStore[setTheme]": (state)=>state.setTheme
    }["useThemeActions.useAppStore[setTheme]"]);
    const toggleTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useThemeActions.useAppStore[toggleTheme]": (state)=>state.toggleTheme
    }["useThemeActions.useAppStore[toggleTheme]"]);
    return {
        setTheme,
        toggleTheme
    };
};
_s3(useThemeActions, "fktaLG1y+yrwp1eeIN6PtDjRE5o=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useAppSettings = ()=>{
    _s4();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useAppSettings.useAppStore": (state)=>state.settings
    }["useAppSettings.useAppStore"]);
};
_s4(useAppSettings, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useSettingsActions = ()=>{
    _s5();
    const updateSettings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useSettingsActions.useAppStore[updateSettings]": (state)=>state.updateSettings
    }["useSettingsActions.useAppStore[updateSettings]"]);
    const resetSettings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useSettingsActions.useAppStore[resetSettings]": (state)=>state.resetSettings
    }["useSettingsActions.useAppStore[resetSettings]"]);
    return {
        updateSettings,
        resetSettings
    };
};
_s5(useSettingsActions, "YRCJBTyYwTrRohP2UON80zn4N1w=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useSetting = (key)=>{
    _s6();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useSetting.useAppStore": (state)=>state.settings[key]
    }["useSetting.useAppStore"]);
};
_s6(useSetting, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useNavigation = ()=>{
    _s7();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNavigation.useAppStore": (state)=>state.navigation
    }["useNavigation.useAppStore"]);
};
_s7(useNavigation, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useCurrentPath = ()=>{
    _s8();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useCurrentPath.useAppStore": (state)=>state.navigation.currentPath
    }["useCurrentPath.useAppStore"]);
};
_s8(useCurrentPath, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useBreadcrumbs = ()=>{
    _s9();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useBreadcrumbs.useAppStore": (state)=>state.navigation.breadcrumbs
    }["useBreadcrumbs.useAppStore"]);
};
_s9(useBreadcrumbs, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useSidebarState = ()=>{
    _s10();
    const collapsed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useSidebarState.useAppStore[collapsed]": (state)=>state.navigation.sidebarCollapsed
    }["useSidebarState.useAppStore[collapsed]"]);
    const toggleSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useSidebarState.useAppStore[toggleSidebar]": (state)=>state.toggleSidebar
    }["useSidebarState.useAppStore[toggleSidebar]"]);
    return {
        collapsed,
        toggleSidebar
    };
};
_s10(useSidebarState, "0LbuncB40cF4m6ds8NIvdt0eHO0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useActiveMenu = ()=>{
    _s11();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useActiveMenu.useAppStore": (state)=>state.navigation.activeMenuKey
    }["useActiveMenu.useAppStore"]);
};
_s11(useActiveMenu, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useNavigationActions = ()=>{
    _s12();
    const setCurrentPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNavigationActions.useAppStore[setCurrentPath]": (state)=>state.setCurrentPath
    }["useNavigationActions.useAppStore[setCurrentPath]"]);
    const setBreadcrumbs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNavigationActions.useAppStore[setBreadcrumbs]": (state)=>state.setBreadcrumbs
    }["useNavigationActions.useAppStore[setBreadcrumbs]"]);
    const toggleSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNavigationActions.useAppStore[toggleSidebar]": (state)=>state.toggleSidebar
    }["useNavigationActions.useAppStore[toggleSidebar]"]);
    const setActiveMenu = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNavigationActions.useAppStore[setActiveMenu]": (state)=>state.setActiveMenu
    }["useNavigationActions.useAppStore[setActiveMenu]"]);
    return {
        setCurrentPath,
        setBreadcrumbs,
        toggleSidebar,
        setActiveMenu
    };
};
_s12(useNavigationActions, "q6iR5zAz0svK3rwVC1m07g+0co4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useUIState = ()=>{
    _s13();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useUIState.useAppStore": (state)=>state.ui
    }["useUIState.useAppStore"]);
};
_s13(useUIState, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useGlobalLoading = ()=>{
    _s14();
    const loading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useGlobalLoading.useAppStore[loading]": (state)=>state.ui.globalLoading
    }["useGlobalLoading.useAppStore[loading]"]);
    const message = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useGlobalLoading.useAppStore[message]": (state)=>state.ui.loadingMessage
    }["useGlobalLoading.useAppStore[message]"]);
    return {
        loading,
        message
    };
};
_s14(useGlobalLoading, "qDIZx7g2h4o/l0FydxK1Za5p7xE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useGlobalError = ()=>{
    _s15();
    const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useGlobalError.useAppStore[error]": (state)=>state.ui.globalError
    }["useGlobalError.useAppStore[error]"]);
    const details = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useGlobalError.useAppStore[details]": (state)=>state.ui.errorDetails
    }["useGlobalError.useAppStore[details]"]);
    return {
        error,
        details
    };
};
_s15(useGlobalError, "JhhG5vdITFJAjWeyDjp+ZkKz8Ww=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useUIActions = ()=>{
    _s16();
    const setGlobalLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useUIActions.useAppStore[setGlobalLoading]": (state)=>state.setGlobalLoading
    }["useUIActions.useAppStore[setGlobalLoading]"]);
    const setGlobalError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useUIActions.useAppStore[setGlobalError]": (state)=>state.setGlobalError
    }["useUIActions.useAppStore[setGlobalError]"]);
    const clearGlobalError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useUIActions.useAppStore[clearGlobalError]": (state)=>state.clearGlobalError
    }["useUIActions.useAppStore[clearGlobalError]"]);
    return {
        setGlobalLoading,
        setGlobalError,
        clearGlobalError
    };
};
_s16(useUIActions, "itumUNF25Atg29v/X4BqO+jDoaE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useNotifications = ()=>{
    _s17();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNotifications.useAppStore": (state)=>state.ui.notifications
    }["useNotifications.useAppStore"]);
};
_s17(useNotifications, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useNotificationActions = ()=>{
    _s18();
    const addNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNotificationActions.useAppStore[addNotification]": (state)=>state.addNotification
    }["useNotificationActions.useAppStore[addNotification]"]);
    const removeNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNotificationActions.useAppStore[removeNotification]": (state)=>state.removeNotification
    }["useNotificationActions.useAppStore[removeNotification]"]);
    const clearNotifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNotificationActions.useAppStore[clearNotifications]": (state)=>state.clearNotifications
    }["useNotificationActions.useAppStore[clearNotifications]"]);
    return {
        addNotification,
        removeNotification,
        clearNotifications
    };
};
_s18(useNotificationActions, "8N02qsN1AIM6T9BNrU2RbFNs0kE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useNotify = ()=>{
    _s19();
    const addNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNotify.useAppStore[addNotification]": (state)=>state.addNotification
    }["useNotify.useAppStore[addNotification]"]);
    const notify = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNotify.useCallback[notify]": ()=>({
                success: ({
                    "useNotify.useCallback[notify]": (message, title)=>{
                        addNotification({
                            type: 'success',
                            title: title || 'Success',
                            message
                        });
                    }
                })["useNotify.useCallback[notify]"],
                error: ({
                    "useNotify.useCallback[notify]": (message, title)=>{
                        addNotification({
                            type: 'error',
                            title: title || 'Error',
                            message
                        });
                    }
                })["useNotify.useCallback[notify]"],
                warning: ({
                    "useNotify.useCallback[notify]": (message, title)=>{
                        addNotification({
                            type: 'warning',
                            title: title || 'Warning',
                            message
                        });
                    }
                })["useNotify.useCallback[notify]"],
                info: ({
                    "useNotify.useCallback[notify]": (message, title)=>{
                        addNotification({
                            type: 'info',
                            title: title || 'Info',
                            message
                        });
                    }
                })["useNotify.useCallback[notify]"]
            })
    }["useNotify.useCallback[notify]"], [
        addNotification
    ]);
    return notify();
};
_s19(useNotify, "fqIrf568d5dpBBISc0jJBqkr4Tw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useModals = ()=>{
    _s20();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useModals.useAppStore": (state)=>state.ui.modals
    }["useModals.useAppStore"]);
};
_s20(useModals, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useModal = (key)=>{
    _s21();
    const modal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useModal.useAppStore[modal]": (state)=>state.ui.modals[key]
    }["useModal.useAppStore[modal]"]);
    const showModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useModal.useAppStore[showModal]": (state)=>state.showModal
    }["useModal.useAppStore[showModal]"]);
    const hideModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useModal.useAppStore[hideModal]": (state)=>state.hideModal
    }["useModal.useAppStore[hideModal]"]);
    const show = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[show]": (data)=>{
            showModal(key, data);
        }
    }["useModal.useCallback[show]"], [
        showModal,
        key
    ]);
    const hide = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[hide]": ()=>{
            hideModal(key);
        }
    }["useModal.useCallback[hide]"], [
        hideModal,
        key
    ]);
    return {
        visible: modal?.visible || false,
        data: modal?.data,
        show,
        hide
    };
};
_s21(useModal, "+xze8a4MsG4hK71Dr9tsD0e+ECY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useModalActions = ()=>{
    _s22();
    const showModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useModalActions.useAppStore[showModal]": (state)=>state.showModal
    }["useModalActions.useAppStore[showModal]"]);
    const hideModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useModalActions.useAppStore[hideModal]": (state)=>state.hideModal
    }["useModalActions.useAppStore[hideModal]"]);
    const hideAllModals = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useModalActions.useAppStore[hideAllModals]": (state)=>state.hideAllModals
    }["useModalActions.useAppStore[hideAllModals]"]);
    return {
        showModal,
        hideModal,
        hideAllModals
    };
};
_s22(useModalActions, "SCo+OhxHAqnk/it92onpXUpbgKc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useAppVersion = ()=>{
    _s23();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useAppVersion.useAppStore": (state)=>state.version
    }["useAppVersion.useAppStore"]);
};
_s23(useAppVersion, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useBuildTime = ()=>{
    _s24();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useBuildTime.useAppStore": (state)=>state.buildTime
    }["useBuildTime.useAppStore"]);
};
_s24(useBuildTime, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useEnvironment = ()=>{
    _s25();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useEnvironment.useAppStore": (state)=>state.environment
    }["useEnvironment.useAppStore"]);
};
_s25(useEnvironment, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useSystemInfo = ()=>{
    _s26();
    const version = useAppVersion();
    const buildTime = useBuildTime();
    const environment = useEnvironment();
    return {
        version,
        buildTime,
        environment,
        isDevelopment: environment === 'development',
        isProduction: environment === 'production'
    };
};
_s26(useSystemInfo, "v8xdnOj/+I5EMQ1Nln1Qw2VHX9A=", false, function() {
    return [
        useAppVersion,
        useBuildTime,
        useEnvironment
    ];
});
const useApp = ()=>{
    _s27();
    const theme = useTheme();
    const settings = useAppSettings();
    const navigation = useNavigation();
    const ui = useUIState();
    const systemInfo = useSystemInfo();
    const themeActions = useThemeActions();
    const settingsActions = useSettingsActions();
    const navigationActions = useNavigationActions();
    const uiActions = useUIActions();
    const notificationActions = useNotificationActions();
    const modalActions = useModalActions();
    return {
        // State
        theme,
        settings,
        navigation,
        ui,
        systemInfo,
        // Actions
        ...themeActions,
        ...settingsActions,
        ...navigationActions,
        ...uiActions,
        ...notificationActions,
        ...modalActions
    };
};
_s27(useApp, "8QCu3HIXL9yiBaXIAYGB5ZqCeKw=", false, function() {
    return [
        useTheme,
        useAppSettings,
        useNavigation,
        useUIState,
        useSystemInfo,
        useThemeActions,
        useSettingsActions,
        useNavigationActions,
        useUIActions,
        useNotificationActions,
        useModalActions
    ];
});
const useResponsive = ()=>{
    _s28();
    const sidebarCollapsed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useResponsive.useAppStore[sidebarCollapsed]": (state)=>state.navigation.sidebarCollapsed
    }["useResponsive.useAppStore[sidebarCollapsed]"]);
    const toggleSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useResponsive.useAppStore[toggleSidebar]": (state)=>state.toggleSidebar
    }["useResponsive.useAppStore[toggleSidebar]"]);
    // Auto-collapse sidebar on mobile
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useResponsive.useEffect": ()=>{
            const handleResize = {
                "useResponsive.useEffect.handleResize": ()=>{
                    const isMobile = window.innerWidth < 768;
                    if (isMobile && !sidebarCollapsed) {
                        toggleSidebar();
                    }
                }
            }["useResponsive.useEffect.handleResize"];
            window.addEventListener('resize', handleResize);
            handleResize(); // Check on mount
            return ({
                "useResponsive.useEffect": ()=>window.removeEventListener('resize', handleResize)
            })["useResponsive.useEffect"];
        }
    }["useResponsive.useEffect"], [
        sidebarCollapsed,
        toggleSidebar
    ]);
    return {
        isMobile: ("TURBOPACK compile-time truthy", 1) ? window.innerWidth < 768 : ("TURBOPACK unreachable", undefined),
        isTablet: ("TURBOPACK compile-time truthy", 1) ? window.innerWidth >= 768 && window.innerWidth < 1024 : ("TURBOPACK unreachable", undefined),
        isDesktop: ("TURBOPACK compile-time truthy", 1) ? window.innerWidth >= 1024 : ("TURBOPACK unreachable", undefined)
    };
};
_s28(useResponsive, "cS1R8GfG/99EpyfAhhG7oTdEmgs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/store-context.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Context - React context for accessing stores
 * Provides centralized access to all Zustand stores
 */ __turbopack_esm__({
    "StoreContextProvider": (()=>StoreContextProvider),
    "useAppStoreContext": (()=>useAppStoreContext),
    "useAuthStoreContext": (()=>useAuthStoreContext),
    "useIsStoreContextAvailable": (()=>useIsStoreContextAvailable),
    "useStoreContext": (()=>useStoreContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-hooks.ts [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature();
'use client';
;
;
;
/**
 * Store context
 */ const StoreContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
function StoreContextProvider({ children }) {
    _s();
    // Get store instances
    const authStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const appStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApp"])();
    const contextValue = {
        authStore,
        appStore
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(StoreContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/stores/store-context.tsx",
        lineNumber: 48,
        columnNumber: 5
    }, this);
}
_s(StoreContextProvider, "OicTdOWBIqHnugywUtDndIumbeM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApp"]
    ];
});
_c = StoreContextProvider;
function useStoreContext() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(StoreContext);
    if (!context) {
        throw new Error('useStoreContext must be used within a StoreContextProvider');
    }
    return context;
}
_s1(useStoreContext, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function useAuthStoreContext() {
    _s2();
    const { authStore } = useStoreContext();
    return authStore;
}
_s2(useAuthStoreContext, "oXAYGMcwWuCUpHlPw3viwr0fwMY=", false, function() {
    return [
        useStoreContext
    ];
});
function useAppStoreContext() {
    _s3();
    const { appStore } = useStoreContext();
    return appStore;
}
_s3(useAppStoreContext, "ndF6xaHZzaktCbjtePbTt23CuEo=", false, function() {
    return [
        useStoreContext
    ];
});
function useIsStoreContextAvailable() {
    _s4();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(StoreContext);
    return context !== null;
}
_s4(useIsStoreContextAvailable, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_refresh__.register(_c, "StoreContextProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/store-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Provider - Main provider component for all stores
 * Wraps the application with necessary store providers
 */ __turbopack_esm__({
    "StoreProvider": (()=>StoreProvider),
    "StoreProviderUtils": (()=>StoreProviderUtils),
    "withStoreProvider": (()=>withStoreProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/store-context.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/constants.ts [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
'use client';
;
;
;
;
;
/**
 * Store initialization component
 * Handles store initialization and hydration
 */ function StoreInitializer({ children }) {
    _s();
    const authStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const appStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApp"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "StoreInitializer.useEffect": ()=>{
            // Initialize stores on mount
            const initializeStores = {
                "StoreInitializer.useEffect.initializeStores": async ()=>{
                    try {
                        console.log('✅ Stores initialized successfully');
                    } catch (error) {
                        console.error('❌ Failed to initialize stores:', error);
                    }
                }
            }["StoreInitializer.useEffect.initializeStores"];
            initializeStores();
        }
    }["StoreInitializer.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(StoreInitializer, "1EJnJaUr5kHmJgPxnLzBwxNnRWU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApp"]
    ];
});
_c = StoreInitializer;
function StoreProvider({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["StoreContextProvider"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(StoreInitializer, {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/stores/store-provider.tsx",
            lineNumber: 52,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/stores/store-provider.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
}
_c1 = StoreProvider;
function withStoreProvider(Component) {
    const WrappedComponent = (props)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(StoreProvider, {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
                ...props
            }, void 0, false, {
                fileName: "[project]/src/stores/store-provider.tsx",
                lineNumber: 67,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/stores/store-provider.tsx",
            lineNumber: 66,
            columnNumber: 5
        }, this);
    WrappedComponent.displayName = `withStoreProvider(${Component.displayName || Component.name})`;
    return WrappedComponent;
}
const StoreProviderUtils = {
    /**
   * Check if stores are properly initialized
   */ checkStoreInitialization: ()=>{
        try {
            return {
                auth: true,
                app: true,
                all: true
            };
        } catch (error) {
            console.error('Failed to check store initialization:', error);
            return {
                auth: false,
                app: false,
                all: false
            };
        }
    },
    /**
   * Reset all stores to initial state
   */ resetAllStores: ()=>{
        try {
            console.log('✅ All stores reset successfully');
        } catch (error) {
            console.error('❌ Failed to reset stores:', error);
        }
    },
    /**
   * Clear all persisted store data
   */ clearPersistedData: ()=>{
        try {
            Object.values(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORAGE_KEYS"]).forEach((key)=>{
                localStorage.removeItem(key);
            });
            console.log('✅ All persisted store data cleared');
        } catch (error) {
            console.error('❌ Failed to clear persisted data:', error);
        }
    }
};
var _c, _c1;
__turbopack_refresh__.register(_c, "StoreInitializer");
__turbopack_refresh__.register(_c1, "StoreProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/provider-hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Provider Hooks - Custom hooks for using stores with providers
 * Provides convenient access to stores through context
 */ __turbopack_esm__({
    "useAppProvider": (()=>useAppProvider),
    "useAuthProvider": (()=>useAuthProvider),
    "useStoreAvailability": (()=>useStoreAvailability),
    "useStoreDebug": (()=>useStoreDebug),
    "useStores": (()=>useStores)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/store-context.tsx [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature();
'use client';
;
;
function useAuthProvider() {
    _s();
    const authStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStoreContext"])();
    const login = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthProvider.useCallback[login]": async (credentials)=>{
            return authStore.login(credentials);
        }
    }["useAuthProvider.useCallback[login]"], [
        authStore
    ]);
    const logout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthProvider.useCallback[logout]": async ()=>{
            return authStore.logout();
        }
    }["useAuthProvider.useCallback[logout]"], [
        authStore
    ]);
    const logoutAll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthProvider.useCallback[logoutAll]": async ()=>{
            return authStore.logoutAll();
        }
    }["useAuthProvider.useCallback[logoutAll]"], [
        authStore
    ]);
    const refreshToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthProvider.useCallback[refreshToken]": async ()=>{
            return authStore.refreshToken();
        }
    }["useAuthProvider.useCallback[refreshToken]"], [
        authStore
    ]);
    const updateProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthProvider.useCallback[updateProfile]": async (data)=>{
            return authStore.updateProfile(data);
        }
    }["useAuthProvider.useCallback[updateProfile]"], [
        authStore
    ]);
    const changePassword = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthProvider.useCallback[changePassword]": async (data)=>{
            return authStore.changePassword(data);
        }
    }["useAuthProvider.useCallback[changePassword]"], [
        authStore
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useAuthProvider.useMemo": ()=>({
                // State
                user: authStore.user,
                token: authStore.token,
                refreshToken: authStore.refreshToken,
                isAuthenticated: authStore.isAuthenticated,
                isLoading: authStore.isLoading,
                error: authStore.error,
                isInitialized: authStore.isInitialized,
                // Actions
                login,
                logout,
                logoutAll,
                refreshToken: refreshToken,
                updateProfile,
                changePassword,
                clearError: authStore.clearError,
                reset: authStore.reset
            })
    }["useAuthProvider.useMemo"], [
        authStore.user,
        authStore.token,
        authStore.refreshToken,
        authStore.isAuthenticated,
        authStore.isLoading,
        authStore.error,
        authStore.isInitialized,
        login,
        logout,
        logoutAll,
        refreshToken,
        updateProfile,
        changePassword,
        authStore.clearError,
        authStore.reset
    ]);
}
_s(useAuthProvider, "oS85YpiiN5ktu11jWhhZl/tW8I8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStoreContext"]
    ];
});
function useAppProvider() {
    _s1();
    const appStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStoreContext"])();
    const setTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAppProvider.useCallback[setTheme]": (theme)=>{
            appStore.setTheme(theme);
        }
    }["useAppProvider.useCallback[setTheme]"], [
        appStore
    ]);
    const toggleTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAppProvider.useCallback[toggleTheme]": ()=>{
            appStore.toggleTheme();
        }
    }["useAppProvider.useCallback[toggleTheme]"], [
        appStore
    ]);
    const setLanguage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAppProvider.useCallback[setLanguage]": (language)=>{
            appStore.setLanguage(language);
        }
    }["useAppProvider.useCallback[setLanguage]"], [
        appStore
    ]);
    const setSidebarCollapsed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAppProvider.useCallback[setSidebarCollapsed]": (collapsed)=>{
            appStore.setSidebarCollapsed(collapsed);
        }
    }["useAppProvider.useCallback[setSidebarCollapsed]"], [
        appStore
    ]);
    const toggleSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAppProvider.useCallback[toggleSidebar]": ()=>{
            appStore.toggleSidebar();
        }
    }["useAppProvider.useCallback[toggleSidebar]"], [
        appStore
    ]);
    const setLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAppProvider.useCallback[setLoading]": (loading)=>{
            appStore.setLoading(loading);
        }
    }["useAppProvider.useCallback[setLoading]"], [
        appStore
    ]);
    const showNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAppProvider.useCallback[showNotification]": (notification)=>{
            appStore.showNotification(notification);
        }
    }["useAppProvider.useCallback[showNotification]"], [
        appStore
    ]);
    const hideNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAppProvider.useCallback[hideNotification]": ()=>{
            appStore.hideNotification();
        }
    }["useAppProvider.useCallback[hideNotification]"], [
        appStore
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useAppProvider.useMemo": ()=>({
                // State
                theme: appStore.theme,
                language: appStore.language,
                sidebarCollapsed: appStore.sidebarCollapsed,
                isLoading: appStore.isLoading,
                notification: appStore.notification,
                isInitialized: appStore.isInitialized,
                // Actions
                setTheme,
                toggleTheme,
                setLanguage,
                setSidebarCollapsed,
                toggleSidebar,
                setLoading,
                showNotification,
                hideNotification,
                reset: appStore.reset
            })
    }["useAppProvider.useMemo"], [
        appStore.theme,
        appStore.language,
        appStore.sidebarCollapsed,
        appStore.isLoading,
        appStore.notification,
        appStore.isInitialized,
        setTheme,
        toggleTheme,
        setLanguage,
        setSidebarCollapsed,
        toggleSidebar,
        setLoading,
        showNotification,
        hideNotification,
        appStore.reset
    ]);
}
_s1(useAppProvider, "RJH3h6LZsbGfJqGOhGTmHOOoXrU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStoreContext"]
    ];
});
function useStoreAvailability() {
    _s2();
    const isAvailable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useIsStoreContextAvailable"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useStoreAvailability.useMemo": ()=>({
                isAvailable,
                isStoreReady: isAvailable
            })
    }["useStoreAvailability.useMemo"], [
        isAvailable
    ]);
}
_s2(useStoreAvailability, "dOJZ4eMDH9j7B8nYKB/wGZfEsQ0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useIsStoreContextAvailable"]
    ];
});
function useStores() {
    _s3();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useStoreContext"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useStores.useMemo": ()=>({
                authStore: context.authStore,
                appStore: context.appStore
            })
    }["useStores.useMemo"], [
        context.authStore,
        context.appStore
    ]);
}
_s3(useStores, "fx9+AZvIqkZTvgQB7YH+XLZ4Q/k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useStoreContext"]
    ];
});
function useStoreDebug() {
    _s4();
    const { authStore, appStore } = useStores();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useStoreDebug.useMemo": ()=>({
                authState: {
                    user: authStore.user,
                    isAuthenticated: authStore.isAuthenticated,
                    isLoading: authStore.isLoading,
                    error: authStore.error,
                    isInitialized: authStore.isInitialized
                },
                appState: {
                    theme: appStore.theme,
                    language: appStore.language,
                    sidebarCollapsed: appStore.sidebarCollapsed,
                    isLoading: appStore.isLoading,
                    notification: appStore.notification,
                    isInitialized: appStore.isInitialized
                },
                actions: {
                    resetAuth: authStore.reset,
                    resetApp: appStore.reset,
                    clearAuthError: authStore.clearError,
                    hideNotification: appStore.hideNotification
                }
            })
    }["useStoreDebug.useMemo"], [
        authStore,
        appStore
    ]);
}
_s4(useStoreDebug, "QXXhmk12UY1uOXk8cVJ5CTrq4jY=", false, function() {
    return [
        useStores
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=src_b8a953._.js.map