{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/query-error-handler.ts"], "sourcesContent": ["/**\n * Query Error Handler\n * Centralized error handling for TanStack Query\n */\n\nimport { QueryClient } from '@tanstack/react-query';\n\n/**\n * API Error interface\n */\nexport interface ApiError {\n  status: number;\n  statusText: string;\n  message: string;\n  details?: any;\n  timestamp: string;\n}\n\n/**\n * Error types for different scenarios\n */\nexport enum ErrorType {\n  NETWORK = 'NETWORK',\n  AUTHENTICATION = 'AUTHENTICATION',\n  AUTHORIZATION = 'AUTHORIZATION',\n  VALIDATION = 'VALIDATION',\n  SERVER = 'SERVER',\n  UNKNOWN = 'UNKNOWN',\n}\n\n/**\n * Determine error type based on status code\n */\nexport function getErrorType(status: number): ErrorType {\n  if (status === 401) return ErrorType.AUTHENTICATION;\n  if (status === 403) return ErrorType.AUTHORIZATION;\n  if (status >= 400 && status < 500) return ErrorType.VALIDATION;\n  if (status >= 500) return ErrorType.SERVER;\n  if (status === 0) return ErrorType.NETWORK;\n  return ErrorType.UNKNOWN;\n}\n\n/**\n * Create standardized API error\n */\nexport function createApiError(\n  status: number,\n  statusText: string,\n  message: string,\n  details?: any\n): ApiError {\n  return {\n    status,\n    statusText,\n    message,\n    details,\n    timestamp: new Date().toISOString(),\n  };\n}\n\n/**\n * Parse error response from API\n */\nexport async function parseErrorResponse(response: Response): Promise<ApiError> {\n  let message = response.statusText || 'An error occurred';\n  let details = null;\n\n  try {\n    const errorData = await response.json();\n    message = errorData.message || errorData.error || message;\n    details = errorData.details || errorData;\n  } catch {\n    // If response is not JSON, use status text\n  }\n\n  return createApiError(response.status, response.statusText, message, details);\n}\n\n/**\n * Global error handler for queries\n */\nexport function createGlobalErrorHandler() {\n  return (error: unknown) => {\n    console.error('[Query Error]', error);\n\n    // Handle different types of errors\n    if (error instanceof Error) {\n      // Network errors, parsing errors, etc.\n      console.error('Error details:', {\n        name: error.name,\n        message: error.message,\n        stack: error.stack,\n      });\n    }\n\n    // Handle API errors\n    if (isApiError(error)) {\n      handleApiError(error);\n    }\n  };\n}\n\n/**\n * Check if error is an API error\n */\nexport function isApiError(error: unknown): error is ApiError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    'status' in error &&\n    'message' in error\n  );\n}\n\n/**\n * Handle specific API error types\n */\nexport function handleApiError(error: ApiError) {\n  const errorType = getErrorType(error.status);\n\n  switch (errorType) {\n    case ErrorType.AUTHENTICATION:\n      handleAuthenticationError(error);\n      break;\n    case ErrorType.AUTHORIZATION:\n      handleAuthorizationError(error);\n      break;\n    case ErrorType.VALIDATION:\n      handleValidationError(error);\n      break;\n    case ErrorType.SERVER:\n      handleServerError(error);\n      break;\n    case ErrorType.NETWORK:\n      handleNetworkError(error);\n      break;\n    default:\n      handleUnknownError(error);\n  }\n}\n\n/**\n * Handle authentication errors (401)\n */\nfunction handleAuthenticationError(error: ApiError) {\n  console.warn('[Auth Error]', error.message);\n  \n  // In development mode, authentication is disabled\n  if (process.env.NODE_ENV === 'development') {\n    console.log('[Dev Mode] Authentication error ignored');\n    return;\n  }\n  \n  // In production, redirect to login or refresh token\n  // This will be implemented when auth system is ready\n}\n\n/**\n * Handle authorization errors (403)\n */\nfunction handleAuthorizationError(error: ApiError) {\n  console.warn('[Authorization Error]', error.message);\n  \n  // Show user-friendly message about insufficient permissions\n  // This will be integrated with notification system\n}\n\n/**\n * Handle validation errors (400-499)\n */\nfunction handleValidationError(error: ApiError) {\n  console.warn('[Validation Error]', error.message);\n  \n  // These are usually handled by individual components\n  // Global handler just logs for debugging\n}\n\n/**\n * Handle server errors (500+)\n */\nfunction handleServerError(error: ApiError) {\n  console.error('[Server Error]', error.message);\n  \n  // Show generic error message to user\n  // Log detailed error for debugging\n}\n\n/**\n * Handle network errors\n */\nfunction handleNetworkError(error: ApiError) {\n  console.error('[Network Error]', error.message);\n  \n  // Show network connectivity message\n  // Suggest retry or check connection\n}\n\n/**\n * Handle unknown errors\n */\nfunction handleUnknownError(error: ApiError) {\n  console.error('[Unknown Error]', error);\n  \n  // Show generic error message\n  // Log for investigation\n}\n\n/**\n * Error boundary for query errors\n */\nexport function setupQueryErrorHandling(queryClient: QueryClient) {\n  // Set up global error handler\n  queryClient.setDefaultOptions({\n    queries: {\n      ...queryClient.getDefaultOptions().queries,\n      throwOnError: false, // Handle errors gracefully\n    },\n    mutations: {\n      ...queryClient.getDefaultOptions().mutations,\n      throwOnError: false, // Handle errors gracefully\n    },\n  });\n\n  // Set up global error handler\n  queryClient.setMutationDefaults(['mutation'], {\n    onError: createGlobalErrorHandler(),\n  });\n}\n\n/**\n * Utility functions for error handling\n */\nexport const errorUtils = {\n  /**\n   * Check if error should trigger retry\n   */\n  shouldRetry: (error: unknown): boolean => {\n    if (isApiError(error)) {\n      const errorType = getErrorType(error.status);\n      // Don't retry client errors (4xx)\n      return errorType !== ErrorType.VALIDATION && \n             errorType !== ErrorType.AUTHENTICATION && \n             errorType !== ErrorType.AUTHORIZATION;\n    }\n    return true; // Retry network and unknown errors\n  },\n\n  /**\n   * Get user-friendly error message\n   */\n  getUserMessage: (error: unknown): string => {\n    if (isApiError(error)) {\n      const errorType = getErrorType(error.status);\n      \n      switch (errorType) {\n        case ErrorType.AUTHENTICATION:\n          return 'Please log in to continue';\n        case ErrorType.AUTHORIZATION:\n          return 'You do not have permission to perform this action';\n        case ErrorType.VALIDATION:\n          return error.message || 'Please check your input and try again';\n        case ErrorType.SERVER:\n          return 'Server error occurred. Please try again later';\n        case ErrorType.NETWORK:\n          return 'Network error. Please check your connection';\n        default:\n          return 'An unexpected error occurred';\n      }\n    }\n    \n    return 'An unexpected error occurred';\n  },\n\n  /**\n   * Check if error is retryable\n   */\n  isRetryable: (error: unknown): boolean => {\n    if (isApiError(error)) {\n      return error.status >= 500 || error.status === 0;\n    }\n    return true;\n  },\n} as const;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;AAiJK;AA/HC,IAAA,AAAK,mCAAA;;;;;;;WAAA;;AAYL,SAAS,aAAa,MAAc;IACzC,IAAI,WAAW,KAAK;IACpB,IAAI,WAAW,KAAK;IACpB,IAAI,UAAU,OAAO,SAAS,KAAK;IACnC,IAAI,UAAU,KAAK;IACnB,IAAI,WAAW,GAAG;IAClB;AACF;AAKO,SAAS,eACd,MAAc,EACd,UAAkB,EAClB,OAAe,EACf,OAAa;IAEb,OAAO;QACL;QACA;QACA;QACA;QACA,WAAW,IAAI,OAAO,WAAW;IACnC;AACF;AAKO,eAAe,mBAAmB,QAAkB;IACzD,IAAI,UAAU,SAAS,UAAU,IAAI;IACrC,IAAI,UAAU;IAEd,IAAI;QACF,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,UAAU,UAAU,OAAO,IAAI,UAAU,KAAK,IAAI;QAClD,UAAU,UAAU,OAAO,IAAI;IACjC,EAAE,OAAM;IACN,2CAA2C;IAC7C;IAEA,OAAO,eAAe,SAAS,MAAM,EAAE,SAAS,UAAU,EAAE,SAAS;AACvE;AAKO,SAAS;IACd,OAAO,CAAC;QACN,QAAQ,KAAK,CAAC,iBAAiB;QAE/B,mCAAmC;QACnC,IAAI,iBAAiB,OAAO;YAC1B,uCAAuC;YACvC,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,MAAM,MAAM,IAAI;gBAChB,SAAS,MAAM,OAAO;gBACtB,OAAO,MAAM,KAAK;YACpB;QACF;QAEA,oBAAoB;QACpB,IAAI,WAAW,QAAQ;YACrB,eAAe;QACjB;IACF;AACF;AAKO,SAAS,WAAW,KAAc;IACvC,OACE,OAAO,UAAU,YACjB,UAAU,QACV,YAAY,SACZ,aAAa;AAEjB;AAKO,SAAS,eAAe,KAAe;IAC5C,MAAM,YAAY,aAAa,MAAM,MAAM;IAE3C,OAAQ;QACN;YACE,0BAA0B;YAC1B;QACF;YACE,yBAAyB;YACzB;QACF;YACE,sBAAsB;YACtB;QACF;YACE,kBAAkB;YAClB;QACF;YACE,mBAAmB;YACnB;QACF;YACE,mBAAmB;IACvB;AACF;AAEA;;CAEC,GACD,SAAS,0BAA0B,KAAe;IAChD,QAAQ,IAAI,CAAC,gBAAgB,MAAM,OAAO;IAE1C,kDAAkD;IAClD,wCAA4C;QAC1C,QAAQ,GAAG,CAAC;QACZ;IACF;AAEA,oDAAoD;AACpD,qDAAqD;AACvD;AAEA;;CAEC,GACD,SAAS,yBAAyB,KAAe;IAC/C,QAAQ,IAAI,CAAC,yBAAyB,MAAM,OAAO;AAEnD,4DAA4D;AAC5D,mDAAmD;AACrD;AAEA;;CAEC,GACD,SAAS,sBAAsB,KAAe;IAC5C,QAAQ,IAAI,CAAC,sBAAsB,MAAM,OAAO;AAEhD,qDAAqD;AACrD,yCAAyC;AAC3C;AAEA;;CAEC,GACD,SAAS,kBAAkB,KAAe;IACxC,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;AAE7C,qCAAqC;AACrC,mCAAmC;AACrC;AAEA;;CAEC,GACD,SAAS,mBAAmB,KAAe;IACzC,QAAQ,KAAK,CAAC,mBAAmB,MAAM,OAAO;AAE9C,oCAAoC;AACpC,oCAAoC;AACtC;AAEA;;CAEC,GACD,SAAS,mBAAmB,KAAe;IACzC,QAAQ,KAAK,CAAC,mBAAmB;AAEjC,6BAA6B;AAC7B,wBAAwB;AAC1B;AAKO,SAAS,wBAAwB,WAAwB;IAC9D,8BAA8B;IAC9B,YAAY,iBAAiB,CAAC;QAC5B,SAAS;YACP,GAAG,YAAY,iBAAiB,GAAG,OAAO;YAC1C,cAAc;QAChB;QACA,WAAW;YACT,GAAG,YAAY,iBAAiB,GAAG,SAAS;YAC5C,cAAc;QAChB;IACF;IAEA,8BAA8B;IAC9B,YAAY,mBAAmB,CAAC;QAAC;KAAW,EAAE;QAC5C,SAAS;IACX;AACF;AAKO,MAAM,aAAa;IACxB;;GAEC,GACD,aAAa,CAAC;QACZ,IAAI,WAAW,QAAQ;YACrB,MAAM,YAAY,aAAa,MAAM,MAAM;YAC3C,kCAAkC;YAClC,OAAO,8BACA,kCACA;QACT;QACA,OAAO,MAAM,mCAAmC;IAClD;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,IAAI,WAAW,QAAQ;YACrB,MAAM,YAAY,aAAa,MAAM,MAAM;YAE3C,OAAQ;gBACN;oBACE,OAAO;gBACT;oBACE,OAAO;gBACT;oBACE,OAAO,MAAM,OAAO,IAAI;gBAC1B;oBACE,OAAO;gBACT;oBACE,OAAO;gBACT;oBACE,OAAO;YACX;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,aAAa,CAAC;QACZ,IAAI,WAAW,QAAQ;YACrB,OAAO,MAAM,MAAM,IAAI,OAAO,MAAM,MAAM,KAAK;QACjD;QACA,OAAO;IACT;AACF"}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/query-utils.ts"], "sourcesContent": ["/**\n * Query Utilities and Helpers\n * Common utilities for working with TanStack Query\n */\n\nimport { QueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';\nimport { QUERY_CONFIG } from './query-client';\nimport { ApiError, isApiError, errorUtils } from './query-error-handler';\n\n/**\n * Base API response interface\n */\nexport interface ApiResponse<T = any> {\n  data: T;\n  message?: string;\n  success: boolean;\n  timestamp: string;\n}\n\n/**\n * Paginated response interface\n */\nexport interface PaginatedResponse<T = any> extends ApiResponse<T[]> {\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n}\n\n/**\n * Query options builder for common patterns\n */\nexport const queryOptionsBuilder = {\n  /**\n   * Build options for real-time data (short cache)\n   */\n  realTime: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({\n    staleTime: QUERY_CONFIG.STALE_TIME.SHORT,\n    gcTime: QUERY_CONFIG.STALE_TIME.MEDIUM,\n    refetchInterval: QUERY_CONFIG.REFETCH_INTERVAL.FAST,\n    ...options,\n  }),\n\n  /**\n   * Build options for static data (long cache)\n   */\n  static: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({\n    staleTime: QUERY_CONFIG.STALE_TIME.VERY_LONG,\n    gcTime: QUERY_CONFIG.STALE_TIME.VERY_LONG,\n    refetchOnWindowFocus: false,\n    refetchOnReconnect: false,\n    ...options,\n  }),\n\n  /**\n   * Build options for user-specific data\n   */\n  userSpecific: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({\n    staleTime: QUERY_CONFIG.STALE_TIME.MEDIUM,\n    gcTime: QUERY_CONFIG.STALE_TIME.LONG,\n    refetchOnWindowFocus: true,\n    ...options,\n  }),\n\n  /**\n   * Build options for background sync data\n   */\n  backgroundSync: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({\n    staleTime: QUERY_CONFIG.STALE_TIME.LONG,\n    gcTime: QUERY_CONFIG.STALE_TIME.VERY_LONG,\n    refetchInterval: QUERY_CONFIG.REFETCH_INTERVAL.SLOW,\n    refetchIntervalInBackground: true,\n    ...options,\n  }),\n};\n\n/**\n * Mutation options builder for common patterns\n */\nexport const mutationOptionsBuilder = {\n  /**\n   * Build options for optimistic updates\n   */\n  optimistic: <T, V>(options?: Partial<UseMutationOptions<T, ApiError, V>>): Partial<UseMutationOptions<T, ApiError, V>> => ({\n    retry: QUERY_CONFIG.RETRY.ONCE,\n    ...options,\n  }),\n\n  /**\n   * Build options for critical operations\n   */\n  critical: <T, V>(options?: Partial<UseMutationOptions<T, ApiError, V>>): Partial<UseMutationOptions<T, ApiError, V>> => ({\n    retry: QUERY_CONFIG.RETRY.DEFAULT,\n    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),\n    ...options,\n  }),\n\n  /**\n   * Build options for background operations\n   */\n  background: <T, V>(options?: Partial<UseMutationOptions<T, ApiError, V>>): Partial<UseMutationOptions<T, ApiError, V>> => ({\n    retry: QUERY_CONFIG.RETRY.TWICE,\n    ...options,\n  }),\n};\n\n/**\n * Cache management utilities\n */\nexport const cacheUtils = {\n  /**\n   * Invalidate queries by pattern\n   */\n  invalidateByPattern: async (queryClient: QueryClient, pattern: string[]) => {\n    await queryClient.invalidateQueries({ queryKey: pattern });\n  },\n\n  /**\n   * Remove queries by pattern\n   */\n  removeByPattern: (queryClient: QueryClient, pattern: string[]) => {\n    queryClient.removeQueries({ queryKey: pattern });\n  },\n\n  /**\n   * Update query data\n   */\n  updateQueryData: <T>(\n    queryClient: QueryClient,\n    queryKey: string[],\n    updater: (oldData: T | undefined) => T\n  ) => {\n    queryClient.setQueryData(queryKey, updater);\n  },\n\n  /**\n   * Optimistically update list data\n   */\n  optimisticListUpdate: <T extends { id: string | number }>(\n    queryClient: QueryClient,\n    queryKey: string[],\n    item: T,\n    operation: 'add' | 'update' | 'remove'\n  ) => {\n    queryClient.setQueryData<T[]>(queryKey, (oldData) => {\n      if (!oldData) return operation === 'add' ? [item] : [];\n\n      switch (operation) {\n        case 'add':\n          return [...oldData, item];\n        case 'update':\n          return oldData.map((existing) =>\n            existing.id === item.id ? { ...existing, ...item } : existing\n          );\n        case 'remove':\n          return oldData.filter((existing) => existing.id !== item.id);\n        default:\n          return oldData;\n      }\n    });\n  },\n\n  /**\n   * Optimistically update paginated data\n   */\n  optimisticPaginatedUpdate: <T extends { id: string | number }>(\n    queryClient: QueryClient,\n    queryKey: string[],\n    item: T,\n    operation: 'add' | 'update' | 'remove'\n  ) => {\n    queryClient.setQueryData<PaginatedResponse<T>>(queryKey, (oldData) => {\n      if (!oldData) return oldData;\n\n      const updatedData = cacheUtils.optimisticListUpdate(\n        queryClient,\n        ['temp'],\n        item,\n        operation\n      );\n\n      return {\n        ...oldData,\n        data: updatedData || oldData.data,\n      };\n    });\n  },\n};\n\n/**\n * Query state utilities\n */\nexport const queryStateUtils = {\n  /**\n   * Check if any queries are loading\n   */\n  isAnyLoading: (queryClient: QueryClient, queryKeys: string[][]): boolean => {\n    return queryKeys.some((key) => {\n      const query = queryClient.getQueryState(key);\n      return query?.fetchStatus === 'fetching';\n    });\n  },\n\n  /**\n   * Check if any queries have errors\n   */\n  hasAnyErrors: (queryClient: QueryClient, queryKeys: string[][]): boolean => {\n    return queryKeys.some((key) => {\n      const query = queryClient.getQueryState(key);\n      return query?.status === 'error';\n    });\n  },\n\n  /**\n   * Get all errors from queries\n   */\n  getAllErrors: (queryClient: QueryClient, queryKeys: string[][]): ApiError[] => {\n    return queryKeys\n      .map((key) => {\n        const query = queryClient.getQueryState(key);\n        return query?.error;\n      })\n      .filter((error): error is ApiError => isApiError(error));\n  },\n\n  /**\n   * Check if data is stale\n   */\n  isStale: (queryClient: QueryClient, queryKey: string[]): boolean => {\n    const query = queryClient.getQueryState(queryKey);\n    return query ? query.isStale : true;\n  },\n};\n\n/**\n * Development utilities\n */\nexport const devUtils = {\n  /**\n   * Log query cache state\n   */\n  logCacheState: (queryClient: QueryClient) => {\n    if (process.env.NODE_ENV === 'development') {\n      const cache = queryClient.getQueryCache();\n      console.log('[Query Cache]', {\n        queries: cache.getAll().length,\n        state: cache.getAll().map((query) => ({\n          key: query.queryKey,\n          status: query.state.status,\n          dataUpdatedAt: query.state.dataUpdatedAt,\n          error: query.state.error,\n        })),\n      });\n    }\n  },\n\n  /**\n   * Clear all cache (development only)\n   */\n  clearAllCache: (queryClient: QueryClient) => {\n    if (process.env.NODE_ENV === 'development') {\n      queryClient.clear();\n      console.log('[Dev] Query cache cleared');\n    }\n  },\n\n  /**\n   * Force refetch all queries (development only)\n   */\n  refetchAll: async (queryClient: QueryClient) => {\n    if (process.env.NODE_ENV === 'development') {\n      await queryClient.refetchQueries();\n      console.log('[Dev] All queries refetched');\n    }\n  },\n};\n\n/**\n * Error handling utilities\n */\nexport const queryErrorUtils = {\n  /**\n   * Handle query error with user feedback\n   */\n  handleQueryError: (error: unknown, context?: string) => {\n    const message = errorUtils.getUserMessage(error);\n    console.error(`[Query Error${context ? ` - ${context}` : ''}]`, error);\n    \n    // This will be integrated with notification system\n    // For now, just log the user-friendly message\n    console.log('[User Message]', message);\n    \n    return message;\n  },\n\n  /**\n   * Handle mutation error with user feedback\n   */\n  handleMutationError: (error: unknown, context?: string) => {\n    const message = errorUtils.getUserMessage(error);\n    console.error(`[Mutation Error${context ? ` - ${context}` : ''}]`, error);\n    \n    // This will be integrated with notification system\n    // For now, just log the user-friendly message\n    console.log('[User Message]', message);\n    \n    return message;\n  },\n};\n\n/**\n * Type guards for API responses\n */\nexport const typeGuards = {\n  /**\n   * Check if response is a valid API response\n   */\n  isApiResponse: <T>(data: unknown): data is ApiResponse<T> => {\n    return (\n      typeof data === 'object' &&\n      data !== null &&\n      'data' in data &&\n      'success' in data &&\n      'timestamp' in data\n    );\n  },\n\n  /**\n   * Check if response is a paginated response\n   */\n  isPaginatedResponse: <T>(data: unknown): data is PaginatedResponse<T> => {\n    return (\n      typeGuards.isApiResponse(data) &&\n      'pagination' in data &&\n      typeof (data as any).pagination === 'object'\n    );\n  },\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAGD;AACA;AA+OQ;;;AAlND,MAAM,sBAAsB;IACjC;;GAEC,GACD,UAAU,CAAI,UAAuE,CAAC;YACpF,WAAW,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,KAAK;YACxC,QAAQ,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,MAAM;YACtC,iBAAiB,gIAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC,IAAI;YACnD,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,QAAQ,CAAI,UAAuE,CAAC;YAClF,WAAW,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,SAAS;YAC5C,QAAQ,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,SAAS;YACzC,sBAAsB;YACtB,oBAAoB;YACpB,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,cAAc,CAAI,UAAuE,CAAC;YACxF,WAAW,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,MAAM;YACzC,QAAQ,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,IAAI;YACpC,sBAAsB;YACtB,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,gBAAgB,CAAI,UAAuE,CAAC;YAC1F,WAAW,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,IAAI;YACvC,QAAQ,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,SAAS;YACzC,iBAAiB,gIAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC,IAAI;YACnD,6BAA6B;YAC7B,GAAG,OAAO;QACZ,CAAC;AACH;AAKO,MAAM,yBAAyB;IACpC;;GAEC,GACD,YAAY,CAAO,UAAuG,CAAC;YACzH,OAAO,gIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI;YAC9B,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,UAAU,CAAO,UAAuG,CAAC;YACvH,OAAO,gIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,OAAO;YACjC,YAAY,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;YACjE,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,YAAY,CAAO,UAAuG,CAAC;YACzH,OAAO,gIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,KAAK;YAC/B,GAAG,OAAO;QACZ,CAAC;AACH;AAKO,MAAM,aAAa;IACxB;;GAEC,GACD,qBAAqB,OAAO,aAA0B;QACpD,MAAM,YAAY,iBAAiB,CAAC;YAAE,UAAU;QAAQ;IAC1D;IAEA;;GAEC,GACD,iBAAiB,CAAC,aAA0B;QAC1C,YAAY,aAAa,CAAC;YAAE,UAAU;QAAQ;IAChD;IAEA;;GAEC,GACD,iBAAiB,CACf,aACA,UACA;QAEA,YAAY,YAAY,CAAC,UAAU;IACrC;IAEA;;GAEC,GACD,sBAAsB,CACpB,aACA,UACA,MACA;QAEA,YAAY,YAAY,CAAM,UAAU,CAAC;YACvC,IAAI,CAAC,SAAS,OAAO,cAAc,QAAQ;gBAAC;aAAK,GAAG,EAAE;YAEtD,OAAQ;gBACN,KAAK;oBACH,OAAO;2BAAI;wBAAS;qBAAK;gBAC3B,KAAK;oBACH,OAAO,QAAQ,GAAG,CAAC,CAAC,WAClB,SAAS,EAAE,KAAK,KAAK,EAAE,GAAG;4BAAE,GAAG,QAAQ;4BAAE,GAAG,IAAI;wBAAC,IAAI;gBAEzD,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAC,WAAa,SAAS,EAAE,KAAK,KAAK,EAAE;gBAC7D;oBACE,OAAO;YACX;QACF;IACF;IAEA;;GAEC,GACD,2BAA2B,CACzB,aACA,UACA,MACA;QAEA,YAAY,YAAY,CAAuB,UAAU,CAAC;YACxD,IAAI,CAAC,SAAS,OAAO;YAErB,MAAM,cAAc,WAAW,oBAAoB,CACjD,aACA;gBAAC;aAAO,EACR,MACA;YAGF,OAAO;gBACL,GAAG,OAAO;gBACV,MAAM,eAAe,QAAQ,IAAI;YACnC;QACF;IACF;AACF;AAKO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,cAAc,CAAC,aAA0B;QACvC,OAAO,UAAU,IAAI,CAAC,CAAC;YACrB,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,OAAO,gBAAgB;QAChC;IACF;IAEA;;GAEC,GACD,cAAc,CAAC,aAA0B;QACvC,OAAO,UAAU,IAAI,CAAC,CAAC;YACrB,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,OAAO,WAAW;QAC3B;IACF;IAEA;;GAEC,GACD,cAAc,CAAC,aAA0B;QACvC,OAAO,UACJ,GAAG,CAAC,CAAC;YACJ,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,OAAO;QAChB,GACC,MAAM,CAAC,CAAC,QAA6B,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD,EAAE;IACrD;IAEA;;GAEC,GACD,SAAS,CAAC,aAA0B;QAClC,MAAM,QAAQ,YAAY,aAAa,CAAC;QACxC,OAAO,QAAQ,MAAM,OAAO,GAAG;IACjC;AACF;AAKO,MAAM,WAAW;IACtB;;GAEC,GACD,eAAe,CAAC;QACd,wCAA4C;YAC1C,MAAM,QAAQ,YAAY,aAAa;YACvC,QAAQ,GAAG,CAAC,iBAAiB;gBAC3B,SAAS,MAAM,MAAM,GAAG,MAAM;gBAC9B,OAAO,MAAM,MAAM,GAAG,GAAG,CAAC,CAAC,QAAU,CAAC;wBACpC,KAAK,MAAM,QAAQ;wBACnB,QAAQ,MAAM,KAAK,CAAC,MAAM;wBAC1B,eAAe,MAAM,KAAK,CAAC,aAAa;wBACxC,OAAO,MAAM,KAAK,CAAC,KAAK;oBAC1B,CAAC;YACH;QACF;IACF;IAEA;;GAEC,GACD,eAAe,CAAC;QACd,wCAA4C;YAC1C,YAAY,KAAK;YACjB,QAAQ,GAAG,CAAC;QACd;IACF;IAEA;;GAEC,GACD,YAAY,OAAO;QACjB,wCAA4C;YAC1C,MAAM,YAAY,cAAc;YAChC,QAAQ,GAAG,CAAC;QACd;IACF;AACF;AAKO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,kBAAkB,CAAC,OAAgB;QACjC,MAAM,UAAU,0IAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QAC1C,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,GAAG,EAAE,SAAS,GAAG,GAAG,CAAC,CAAC,EAAE;QAEhE,mDAAmD;QACnD,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,kBAAkB;QAE9B,OAAO;IACT;IAEA;;GAEC,GACD,qBAAqB,CAAC,OAAgB;QACpC,MAAM,UAAU,0IAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QAC1C,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,UAAU,CAAC,GAAG,EAAE,SAAS,GAAG,GAAG,CAAC,CAAC,EAAE;QAEnE,mDAAmD;QACnD,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,kBAAkB;QAE9B,OAAO;IACT;AACF;AAKO,MAAM,aAAa;IACxB;;GAEC,GACD,eAAe,CAAI;QACjB,OACE,OAAO,SAAS,YAChB,SAAS,QACT,UAAU,QACV,aAAa,QACb,eAAe;IAEnB;IAEA;;GAEC,GACD,qBAAqB,CAAI;QACvB,OACE,WAAW,aAAa,CAAC,SACzB,gBAAgB,QAChB,OAAO,AAAC,KAAa,UAAU,KAAK;IAExC;AACF"}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/base-hooks.ts"], "sourcesContent": ["/**\n * Base API Hooks\n * Foundation hooks for API operations with TanStack Query\n */\n\n'use client';\n\nimport { \n  useQuery, \n  useMutation, \n  useQueryClient,\n  UseQueryOptions,\n  UseMutationOptions \n} from '@tanstack/react-query';\nimport { \n  queryOptionsBuilder, \n  mutationOptionsBuilder,\n  ApiResponse,\n  PaginatedResponse,\n  BaseQueryOptions,\n  BaseMutationOptions\n} from '@/lib/query-utils';\nimport { ApiError, isApiError, errorUtils } from '@/lib/query-error-handler';\n\n/**\n * Base query hook with error handling and type safety\n */\nexport function useBaseQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<TData>,\n  options?: BaseQueryOptions<TData>\n) {\n  return useQuery({\n    queryKey,\n    queryFn,\n    ...options,\n    onError: (error: unknown) => {\n      console.error(`[Query Error] ${queryKey.join(' → ')}:`, error);\n      if (options?.onError) {\n        options.onError(error as ApiError);\n      }\n    },\n  });\n}\n\n/**\n * Base mutation hook with error handling and type safety\n */\nexport function useBaseMutation<TData, TVariables>(\n  mutationFn: (variables: TVariables) => Promise<TData>,\n  options?: BaseMutationOptions<TData, TVariables>\n) {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn,\n    ...options,\n    onError: (error: unknown, variables: TVariables, context: unknown) => {\n      console.error('[Mutation Error]:', error);\n      if (options?.onError) {\n        options.onError(error as ApiError, variables, context);\n      }\n    },\n    onSuccess: (data: TData, variables: TVariables, context: unknown) => {\n      if (options?.onSuccess) {\n        options.onSuccess(data, variables, context);\n      }\n    },\n  });\n}\n\n/**\n * Paginated query hook for list endpoints\n */\nexport function usePaginatedQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<PaginatedResponse<TData>>,\n  options?: BaseQueryOptions<PaginatedResponse<TData>>\n) {\n  return useBaseQuery(queryKey, queryFn, {\n    ...queryOptionsBuilder.userSpecific(),\n    ...options,\n  });\n}\n\n/**\n * Real-time query hook for frequently updated data\n */\nexport function useRealTimeQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<TData>,\n  options?: BaseQueryOptions<TData>\n) {\n  return useBaseQuery(queryKey, queryFn, {\n    ...queryOptionsBuilder.realTime(),\n    ...options,\n  });\n}\n\n/**\n * Static query hook for rarely changing data\n */\nexport function useStaticQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<TData>,\n  options?: BaseQueryOptions<TData>\n) {\n  return useBaseQuery(queryKey, queryFn, {\n    ...queryOptionsBuilder.static(),\n    ...options,\n  });\n}\n\n/**\n * Background sync query hook for data that updates in background\n */\nexport function useBackgroundSyncQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<TData>,\n  options?: BaseQueryOptions<TData>\n) {\n  return useBaseQuery(queryKey, queryFn, {\n    ...queryOptionsBuilder.backgroundSync(),\n    ...options,\n  });\n}\n\n/**\n * Optimistic mutation hook for immediate UI updates\n */\nexport function useOptimisticMutation<TData, TVariables>(\n  mutationFn: (variables: TVariables) => Promise<TData>,\n  options?: BaseMutationOptions<TData, TVariables>\n) {\n  return useBaseMutation(mutationFn, {\n    ...mutationOptionsBuilder.optimistic(),\n    ...options,\n  });\n}\n\n/**\n * Critical mutation hook for important operations with retries\n */\nexport function useCriticalMutation<TData, TVariables>(\n  mutationFn: (variables: TVariables) => Promise<TData>,\n  options?: BaseMutationOptions<TData, TVariables>\n) {\n  return useBaseMutation(mutationFn, {\n    ...mutationOptionsBuilder.critical(),\n    ...options,\n  });\n}\n\n/**\n * Background mutation hook for non-critical operations\n */\nexport function useBackgroundMutation<TData, TVariables>(\n  mutationFn: (variables: TVariables) => Promise<TData>,\n  options?: BaseMutationOptions<TData, TVariables>\n) {\n  return useBaseMutation(mutationFn, {\n    ...mutationOptionsBuilder.background(),\n    ...options,\n  });\n}\n\n/**\n * Hook utilities for common operations\n */\nexport const useApiHookUtils = () => {\n  const queryClient = useQueryClient();\n\n  return {\n    /**\n     * Invalidate queries by pattern\n     */\n    invalidateQueries: (queryKey: readonly unknown[]) => {\n      return queryClient.invalidateQueries({ queryKey });\n    },\n\n    /**\n     * Remove queries from cache\n     */\n    removeQueries: (queryKey: readonly unknown[]) => {\n      return queryClient.removeQueries({ queryKey });\n    },\n\n    /**\n     * Update query data optimistically\n     */\n    updateQueryData: <T>(queryKey: readonly unknown[], updater: (oldData: T | undefined) => T) => {\n      queryClient.setQueryData(queryKey, updater);\n    },\n\n    /**\n     * Get cached query data\n     */\n    getQueryData: <T>(queryKey: readonly unknown[]): T | undefined => {\n      return queryClient.getQueryData(queryKey);\n    },\n\n    /**\n     * Prefetch query data\n     */\n    prefetchQuery: <T>(queryKey: readonly unknown[], queryFn: () => Promise<T>) => {\n      return queryClient.prefetchQuery({ queryKey, queryFn });\n    },\n\n    /**\n     * Check if query is loading\n     */\n    isQueryLoading: (queryKey: readonly unknown[]): boolean => {\n      const query = queryClient.getQueryState(queryKey);\n      return query?.fetchStatus === 'fetching';\n    },\n\n    /**\n     * Get query error\n     */\n    getQueryError: (queryKey: readonly unknown[]): ApiError | null => {\n      const query = queryClient.getQueryState(queryKey);\n      return isApiError(query?.error) ? query.error : null;\n    },\n\n    /**\n     * Handle API error with user feedback\n     */\n    handleApiError: (error: unknown, context?: string): string => {\n      return errorUtils.getUserMessage(error);\n    },\n  };\n};\n\n/**\n * Hook for API status monitoring\n */\nexport const useApiStatus = () => {\n  const queryClient = useQueryClient();\n\n  return {\n    /**\n     * Get overall API status\n     */\n    getApiStatus: () => {\n      const queries = queryClient.getQueryCache().getAll();\n      const totalQueries = queries.length;\n      const loadingQueries = queries.filter(q => q.state.fetchStatus === 'fetching').length;\n      const errorQueries = queries.filter(q => q.state.status === 'error').length;\n      const successQueries = queries.filter(q => q.state.status === 'success').length;\n\n      return {\n        total: totalQueries,\n        loading: loadingQueries,\n        error: errorQueries,\n        success: successQueries,\n        isLoading: loadingQueries > 0,\n        hasErrors: errorQueries > 0,\n        healthScore: totalQueries > 0 ? (successQueries / totalQueries) * 100 : 100,\n      };\n    },\n\n    /**\n     * Get queries by status\n     */\n    getQueriesByStatus: (status: 'loading' | 'error' | 'success' | 'idle') => {\n      const queries = queryClient.getQueryCache().getAll();\n      \n      switch (status) {\n        case 'loading':\n          return queries.filter(q => q.state.fetchStatus === 'fetching');\n        case 'error':\n          return queries.filter(q => q.state.status === 'error');\n        case 'success':\n          return queries.filter(q => q.state.status === 'success');\n        case 'idle':\n          return queries.filter(q => q.state.fetchStatus === 'idle');\n        default:\n          return [];\n      }\n    },\n\n    /**\n     * Clear all errors\n     */\n    clearAllErrors: () => {\n      const errorQueries = queryClient.getQueryCache().getAll()\n        .filter(q => q.state.status === 'error');\n      \n      errorQueries.forEach(query => {\n        queryClient.resetQueries({ queryKey: query.queryKey });\n      });\n    },\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAWD;AAQA;AAfA;AAAA;AAAA;;AAFA;;;;AAsBO,SAAS,aACd,QAA4B,EAC5B,OAA6B,EAC7B,OAAiC;;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd;QACA;QACA,GAAG,OAAO;QACV,OAAO;qCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE;gBACxD,IAAI,SAAS,SAAS;oBACpB,QAAQ,OAAO,CAAC;gBAClB;YACF;;IACF;AACF;GAhBgB;;QAKP,8KAAA,CAAA,WAAQ;;;AAgBV,SAAS,gBACd,UAAqD,EACrD,OAAgD;;IAEhD,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB;QACA,GAAG,OAAO;QACV,OAAO;2CAAE,CAAC,OAAgB,WAAuB;gBAC/C,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,IAAI,SAAS,SAAS;oBACpB,QAAQ,OAAO,CAAC,OAAmB,WAAW;gBAChD;YACF;;QACA,SAAS;2CAAE,CAAC,MAAa,WAAuB;gBAC9C,IAAI,SAAS,WAAW;oBACtB,QAAQ,SAAS,CAAC,MAAM,WAAW;gBACrC;YACF;;IACF;AACF;IArBgB;;QAIM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAoBb,SAAS,kBACd,QAA4B,EAC5B,OAAgD,EAChD,OAAoD;;IAEpD,OAAO,aAAa,UAAU,SAAS;QACrC,GAAG,+HAAA,CAAA,sBAAmB,CAAC,YAAY,EAAE;QACrC,GAAG,OAAO;IACZ;AACF;IATgB;;QAKP;;;AASF,SAAS,iBACd,QAA4B,EAC5B,OAA6B,EAC7B,OAAiC;;IAEjC,OAAO,aAAa,UAAU,SAAS;QACrC,GAAG,+HAAA,CAAA,sBAAmB,CAAC,QAAQ,EAAE;QACjC,GAAG,OAAO;IACZ;AACF;IATgB;;QAKP;;;AASF,SAAS,eACd,QAA4B,EAC5B,OAA6B,EAC7B,OAAiC;;IAEjC,OAAO,aAAa,UAAU,SAAS;QACrC,GAAG,+HAAA,CAAA,sBAAmB,CAAC,MAAM,EAAE;QAC/B,GAAG,OAAO;IACZ;AACF;IATgB;;QAKP;;;AASF,SAAS,uBACd,QAA4B,EAC5B,OAA6B,EAC7B,OAAiC;;IAEjC,OAAO,aAAa,UAAU,SAAS;QACrC,GAAG,+HAAA,CAAA,sBAAmB,CAAC,cAAc,EAAE;QACvC,GAAG,OAAO;IACZ;AACF;IATgB;;QAKP;;;AASF,SAAS,sBACd,UAAqD,EACrD,OAAgD;;IAEhD,OAAO,gBAAgB,YAAY;QACjC,GAAG,+HAAA,CAAA,yBAAsB,CAAC,UAAU,EAAE;QACtC,GAAG,OAAO;IACZ;AACF;IARgB;;QAIP;;;AASF,SAAS,oBACd,UAAqD,EACrD,OAAgD;;IAEhD,OAAO,gBAAgB,YAAY;QACjC,GAAG,+HAAA,CAAA,yBAAsB,CAAC,QAAQ,EAAE;QACpC,GAAG,OAAO;IACZ;AACF;IARgB;;QAIP;;;AASF,SAAS,sBACd,UAAqD,EACrD,OAAgD;;IAEhD,OAAO,gBAAgB,YAAY;QACjC,GAAG,+HAAA,CAAA,yBAAsB,CAAC,UAAU,EAAE;QACtC,GAAG,OAAO;IACZ;AACF;IARgB;;QAIP;;;AASF,MAAM,kBAAkB;;IAC7B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO;QACL;;KAEC,GACD,mBAAmB,CAAC;YAClB,OAAO,YAAY,iBAAiB,CAAC;gBAAE;YAAS;QAClD;QAEA;;KAEC,GACD,eAAe,CAAC;YACd,OAAO,YAAY,aAAa,CAAC;gBAAE;YAAS;QAC9C;QAEA;;KAEC,GACD,iBAAiB,CAAI,UAA8B;YACjD,YAAY,YAAY,CAAC,UAAU;QACrC;QAEA;;KAEC,GACD,cAAc,CAAI;YAChB,OAAO,YAAY,YAAY,CAAC;QAClC;QAEA;;KAEC,GACD,eAAe,CAAI,UAA8B;YAC/C,OAAO,YAAY,aAAa,CAAC;gBAAE;gBAAU;YAAQ;QACvD;QAEA;;KAEC,GACD,gBAAgB,CAAC;YACf,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,OAAO,gBAAgB;QAChC;QAEA;;KAEC,GACD,eAAe,CAAC;YACd,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS,MAAM,KAAK,GAAG;QAClD;QAEA;;KAEC,GACD,gBAAgB,CAAC,OAAgB;YAC/B,OAAO,0IAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QACnC;IACF;AACF;IA9Da;;QACS,yLAAA,CAAA,iBAAc;;;AAkE7B,MAAM,eAAe;;IAC1B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO;QACL;;KAEC,GACD,cAAc;YACZ,MAAM,UAAU,YAAY,aAAa,GAAG,MAAM;YAClD,MAAM,eAAe,QAAQ,MAAM;YACnC,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,WAAW,KAAK,YAAY,MAAM;YACrF,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK,SAAS,MAAM;YAC3E,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK,WAAW,MAAM;YAE/E,OAAO;gBACL,OAAO;gBACP,SAAS;gBACT,OAAO;gBACP,SAAS;gBACT,WAAW,iBAAiB;gBAC5B,WAAW,eAAe;gBAC1B,aAAa,eAAe,IAAI,AAAC,iBAAiB,eAAgB,MAAM;YAC1E;QACF;QAEA;;KAEC,GACD,oBAAoB,CAAC;YACnB,MAAM,UAAU,YAAY,aAAa,GAAG,MAAM;YAElD,OAAQ;gBACN,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,WAAW,KAAK;gBACrD,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK;gBAChD,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK;gBAChD,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,WAAW,KAAK;gBACrD;oBACE,OAAO,EAAE;YACb;QACF;QAEA;;KAEC,GACD,gBAAgB;YACd,MAAM,eAAe,YAAY,aAAa,GAAG,MAAM,GACpD,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK;YAElC,aAAa,OAAO,CAAC,CAAA;gBACnB,YAAY,YAAY,CAAC;oBAAE,UAAU,MAAM,QAAQ;gBAAC;YACtD;QACF;IACF;AACF;KAzDa;;QACS,yLAAA,CAAA,iBAAc"}}, {"offset": {"line": 742, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/auth-hooks.ts"], "sourcesContent": ["/**\n * Authentication API Hooks\n * Hooks for system authentication operations\n */\n\n'use client';\n\nimport { queryKeys } from '@/lib/query-client';\nimport { AuthQueries } from '@/lib/query-types';\nimport { useBaseQuery, useBaseMutation, useApiHookUtils } from './base-hooks';\n\n/**\n * Hook for user login\n */\nexport function useLogin() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<AuthQueries.LoginResponse, AuthQueries.LoginRequest>(\n    async (credentials) => {\n      const response = await fetch('/api/system-auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(credentials),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Login failed: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: (data) => {\n        // Invalidate auth queries on successful login\n        invalidateQueries(queryKeys.auth.all);\n        console.log('✅ Login successful:', data.user.username);\n      },\n      onError: (error) => {\n        console.error('❌ Login failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for user logout\n */\nexport function useLogout() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string }, void>(\n    async () => {\n      const response = await fetch('/api/system-auth/logout', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Logout failed: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Clear all auth-related queries on logout\n        invalidateQueries(queryKeys.auth.all);\n        console.log('✅ Logout successful');\n      },\n      onError: (error) => {\n        console.error('❌ Logout failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for logout from all devices\n */\nexport function useLogoutAll() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string }, void>(\n    async () => {\n      const response = await fetch('/api/system-auth/logout-all', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Logout all failed: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Clear all auth-related queries on logout all\n        invalidateQueries(queryKeys.auth.all);\n        console.log('✅ Logout all successful');\n      },\n      onError: (error) => {\n        console.error('❌ Logout all failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for getting user profile\n */\nexport function useProfile() {\n  return useBaseQuery(\n    queryKeys.auth.profile(),\n    async (): Promise<AuthQueries.ProfileResponse> => {\n      const response = await fetch('/api/system-auth/profile');\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch profile: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: false, // Disable by default, enable when user is authenticated\n      staleTime: 5 * 60 * 1000, // 5 minutes\n      retry: (failureCount, error: any) => {\n        // Don't retry on 401 (unauthorized)\n        if (error?.status === 401) return false;\n        return failureCount < 2;\n      },\n    }\n  );\n}\n\n/**\n * Hook for updating user profile\n */\nexport function useUpdateProfile() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<AuthQueries.ProfileResponse, AuthQueries.UpdateProfileRequest>(\n    async (data) => {\n      const response = await fetch('/api/system-auth/profile', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to update profile: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Invalidate profile query to refetch updated data\n        invalidateQueries(queryKeys.auth.profile());\n        console.log('✅ Profile updated successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Profile update failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for changing password\n */\nexport function useChangePassword() {\n  return useBaseMutation<{ message: string }, AuthQueries.ChangePasswordRequest>(\n    async (data) => {\n      const response = await fetch('/api/system-auth/change-password', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to change password: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        console.log('✅ Password changed successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Password change failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for creating new system user (Admin only)\n */\nexport function useCreateUser() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<AuthQueries.ProfileResponse, AuthQueries.CreateUserRequest>(\n    async (data) => {\n      const response = await fetch('/api/system-auth/users', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to create user: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Invalidate users list to show new user\n        invalidateQueries(queryKeys.auth.users());\n        console.log('✅ User created successfully');\n      },\n      onError: (error) => {\n        console.error('❌ User creation failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for getting system users list (Admin only)\n */\nexport function useSystemUsers() {\n  return useBaseQuery(\n    queryKeys.auth.users(),\n    async (): Promise<AuthQueries.ProfileResponse[]> => {\n      const response = await fetch('/api/system-auth/users');\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch users: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: false, // Enable only for Admin users\n      staleTime: 2 * 60 * 1000, // 2 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting specific system user (Admin only)\n */\nexport function useSystemUser(userId: string) {\n  return useBaseQuery(\n    queryKeys.auth.user(userId),\n    async (): Promise<AuthQueries.ProfileResponse> => {\n      const response = await fetch(`/api/system-auth/users/${userId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch user: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!userId, // Only fetch if userId is provided\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    }\n  );\n}\n\n/**\n * Composite hook for authentication state and actions\n */\nexport function useAuth() {\n  const login = useLogin();\n  const logout = useLogout();\n  const logoutAll = useLogoutAll();\n  const profile = useProfile();\n  const updateProfile = useUpdateProfile();\n  const changePassword = useChangePassword();\n  const createUser = useCreateUser();\n\n  return {\n    // Queries\n    profile,\n    \n    // Mutations\n    login,\n    logout,\n    logoutAll,\n    updateProfile,\n    changePassword,\n    createUser,\n    \n    // Computed state\n    isAuthenticated: !!profile.data,\n    user: profile.data,\n    isLoading: profile.isLoading || login.isPending || logout.isPending,\n    error: profile.error || login.error || logout.error,\n    \n    // Actions\n    loginUser: login.mutate,\n    logoutUser: logout.mutate,\n    logoutAllDevices: logoutAll.mutate,\n    updateUserProfile: updateProfile.mutate,\n    changeUserPassword: changePassword.mutate,\n    createNewUser: createUser.mutate,\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;AAID;AAEA;;AAJA;;;AASO,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;oCACnB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,UAAU,EAAE;YACxD;YAEA,OAAO,SAAS,IAAI;QACtB;mCACA;QACE,SAAS;wCAAE,CAAC;gBACV,8CAA8C;gBAC9C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,GAAG;gBACpC,QAAQ,GAAG,CAAC,uBAAuB,KAAK,IAAI,CAAC,QAAQ;YACvD;;QACA,OAAO;wCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,mBAAmB;YACnC;;IACF;AAEJ;GA9BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AAgCjB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;qCACnB;YACE,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,SAAS,UAAU,EAAE;YACzD;YAEA,OAAO,SAAS,IAAI;QACtB;oCACA;QACE,SAAS;yCAAE;gBACT,2CAA2C;gBAC3C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,GAAG;gBACpC,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;yCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,oBAAoB;YACpC;;IACF;AAEJ;IA7BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AA+BjB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;wCACnB;YACE,MAAM,WAAW,MAAM,MAAM,+BAA+B;gBAC1D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,SAAS,UAAU,EAAE;YAC7D;YAEA,OAAO,SAAS,IAAI;QACtB;uCACA;QACE,SAAS;4CAAE;gBACT,+CAA+C;gBAC/C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,GAAG;gBACpC,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;4CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,wBAAwB;YACxC;;IACF;AAEJ;IA7BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AA+BjB,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,OAAO;mCACtB;YACE,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,OAAO,SAAS,IAAI;QACtB;kCACA;QACE,SAAS;QACT,WAAW,IAAI,KAAK;QACpB,KAAK;uCAAE,CAAC,cAAc;gBACpB,oCAAoC;gBACpC,IAAI,OAAO,WAAW,KAAK,OAAO;gBAClC,OAAO,eAAe;YACxB;;IACF;AAEJ;IAtBgB;;QACP,uIAAA,CAAA,eAAY;;;AA0Bd,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;4CACnB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;YACpE;YAEA,OAAO,SAAS,IAAI;QACtB;2CACA;QACE,SAAS;gDAAE;gBACT,mDAAmD;gBACnD,kBAAkB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,OAAO;gBACxC,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;gDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,4BAA4B;YAC5C;;IACF;AAEJ;IA9BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AAgCjB,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;6CACnB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,oCAAoC;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,SAAS,UAAU,EAAE;YACrE;YAEA,OAAO,SAAS,IAAI;QACtB;4CACA;QACE,SAAS;iDAAE;gBACT,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;iDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,6BAA6B;YAC7C;;IACF;AAEJ;IA1BgB;;QACP,uIAAA,CAAA,kBAAe;;;AA8BjB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;yCACnB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;YACjE;YAEA,OAAO,SAAS,IAAI;QACtB;wCACA;QACE,SAAS;6CAAE;gBACT,yCAAyC;gBACzC,kBAAkB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,KAAK;gBACtC,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;6CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;;IACF;AAEJ;IA9BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AAgCjB,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,KAAK;uCACpB;YACE,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;YACjE;YAEA,OAAO,SAAS,IAAI;QACtB;sCACA;QACE,SAAS;QACT,WAAW,IAAI,KAAK;IACtB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS,cAAc,MAAc;;IAC1C,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI,CAAC;sCACpB;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,QAAQ;YAE/D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;YAChE;YAEA,OAAO,SAAS,IAAI;QACtB;qCACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS;;IACd,MAAM,QAAQ;IACd,MAAM,SAAS;IACf,MAAM,YAAY;IAClB,MAAM,UAAU;IAChB,MAAM,gBAAgB;IACtB,MAAM,iBAAiB;IACvB,MAAM,aAAa;IAEnB,OAAO;QACL,UAAU;QACV;QAEA,YAAY;QACZ;QACA;QACA;QACA;QACA;QACA;QAEA,iBAAiB;QACjB,iBAAiB,CAAC,CAAC,QAAQ,IAAI;QAC/B,MAAM,QAAQ,IAAI;QAClB,WAAW,QAAQ,SAAS,IAAI,MAAM,SAAS,IAAI,OAAO,SAAS;QACnE,OAAO,QAAQ,KAAK,IAAI,MAAM,KAAK,IAAI,OAAO,KAAK;QAEnD,UAAU;QACV,WAAW,MAAM,MAAM;QACvB,YAAY,OAAO,MAAM;QACzB,kBAAkB,UAAU,MAAM;QAClC,mBAAmB,cAAc,MAAM;QACvC,oBAAoB,eAAe,MAAM;QACzC,eAAe,WAAW,MAAM;IAClC;AACF;IAnCgB;;QACA;QACC;QACG;QACF;QACM;QACC;QACJ"}}, {"offset": {"line": 1105, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1111, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/football-hooks.ts"], "sourcesContent": ["/**\n * Football Data API Hooks\n * Hooks for football leagues, teams, and fixtures operations\n */\n\n'use client';\n\nimport { queryKeys } from '@/lib/query-client';\nimport { FootballQueries } from '@/lib/query-types';\nimport { PaginatedResponse } from '@/lib/query-utils';\nimport { \n  useBaseQuery, \n  usePaginatedQuery, \n  useBackgroundSyncQuery,\n  useBaseMutation,\n  useApiHookUtils \n} from './base-hooks';\n\n/**\n * Hook for getting football leagues\n */\nexport function useLeagues(params?: FootballQueries.LeagueQueryParams) {\n  const queryParams = new URLSearchParams();\n  if (params?.page) queryParams.set('page', params.page.toString());\n  if (params?.limit) queryParams.set('limit', params.limit.toString());\n  if (params?.country) queryParams.set('country', params.country);\n  if (params?.isActive !== undefined) queryParams.set('isActive', params.isActive.toString());\n  if (params?.query) queryParams.set('query', params.query);\n\n  return usePaginatedQuery(\n    [...queryKeys.football.leagues(), params],\n    async (): Promise<PaginatedResponse<FootballQueries.League>> => {\n      const response = await fetch(`/api/football/leagues?${queryParams.toString()}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch leagues: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 10 * 60 * 1000, // 10 minutes - leagues don't change often\n    }\n  );\n}\n\n/**\n * Hook for getting specific league\n */\nexport function useLeague(leagueId: string) {\n  return useBaseQuery(\n    queryKeys.football.league(leagueId),\n    async (): Promise<FootballQueries.League> => {\n      const response = await fetch(`/api/football/leagues/${leagueId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch league: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!leagueId,\n      staleTime: 10 * 60 * 1000, // 10 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting football teams\n */\nexport function useTeams(params?: FootballQueries.TeamQueryParams) {\n  const queryParams = new URLSearchParams();\n  if (params?.page) queryParams.set('page', params.page.toString());\n  if (params?.limit) queryParams.set('limit', params.limit.toString());\n  if (params?.leagueId) queryParams.set('leagueId', params.leagueId);\n  if (params?.country) queryParams.set('country', params.country);\n  if (params?.query) queryParams.set('query', params.query);\n\n  return usePaginatedQuery(\n    [...queryKeys.football.teams(), params],\n    async (): Promise<PaginatedResponse<FootballQueries.Team>> => {\n      const response = await fetch(`/api/football/teams?${queryParams.toString()}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch teams: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting specific team\n */\nexport function useTeam(teamId: string) {\n  return useBaseQuery(\n    queryKeys.football.team(teamId),\n    async (): Promise<FootballQueries.Team> => {\n      const response = await fetch(`/api/football/teams/${teamId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch team: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!teamId,\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting football fixtures\n */\nexport function useFixtures(params?: FootballQueries.FixtureQueryParams) {\n  const queryParams = new URLSearchParams();\n  if (params?.page) queryParams.set('page', params.page.toString());\n  if (params?.limit) queryParams.set('limit', params.limit.toString());\n  if (params?.leagueId) queryParams.set('leagueId', params.leagueId);\n  if (params?.teamId) queryParams.set('teamId', params.teamId);\n  if (params?.status) queryParams.set('status', params.status);\n  if (params?.dateFrom) queryParams.set('dateFrom', params.dateFrom);\n  if (params?.dateTo) queryParams.set('dateTo', params.dateTo);\n  if (params?.query) queryParams.set('query', params.query);\n\n  return usePaginatedQuery(\n    [...queryKeys.football.fixtures(), params],\n    async (): Promise<PaginatedResponse<FootballQueries.Fixture>> => {\n      const response = await fetch(`/api/football/fixtures?${queryParams.toString()}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch fixtures: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 1 * 60 * 1000, // 1 minute - fixtures change frequently\n    }\n  );\n}\n\n/**\n * Hook for getting specific fixture\n */\nexport function useFixture(fixtureId: string) {\n  return useBaseQuery(\n    queryKeys.football.fixture(fixtureId),\n    async (): Promise<FootballQueries.Fixture> => {\n      const response = await fetch(`/api/football/fixtures/${fixtureId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch fixture: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!fixtureId,\n      staleTime: 30 * 1000, // 30 seconds - live fixtures need frequent updates\n    }\n  );\n}\n\n/**\n * Hook for getting sync status\n */\nexport function useSyncStatus() {\n  return useBackgroundSyncQuery(\n    queryKeys.football.syncStatus(),\n    async (): Promise<FootballQueries.SyncStatus> => {\n      const response = await fetch('/api/football/fixtures/sync/status');\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch sync status: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 30 * 1000, // 30 seconds\n      refetchInterval: 60 * 1000, // Refetch every minute\n    }\n  );\n}\n\n/**\n * Hook for triggering fixtures sync\n */\nexport function useSyncFixtures() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string; syncId: string }, void>(\n    async () => {\n      const response = await fetch('/api/football/fixtures/sync', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to start sync: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Invalidate sync status and fixtures to show updated data\n        invalidateQueries(queryKeys.football.syncStatus());\n        invalidateQueries(queryKeys.football.fixtures());\n        console.log('✅ Fixtures sync started');\n      },\n      onError: (error) => {\n        console.error('❌ Fixtures sync failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for triggering daily sync\n */\nexport function useDailySync() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string; syncId: string }, void>(\n    async () => {\n      const response = await fetch('/api/football/fixtures/sync/daily', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to start daily sync: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Invalidate sync status and fixtures\n        invalidateQueries(queryKeys.football.syncStatus());\n        invalidateQueries(queryKeys.football.fixtures());\n        console.log('✅ Daily sync started');\n      },\n      onError: (error) => {\n        console.error('❌ Daily sync failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Composite hook for football data operations\n */\nexport function useFootball() {\n  const syncFixtures = useSyncFixtures();\n  const dailySync = useDailySync();\n  const syncStatus = useSyncStatus();\n\n  return {\n    // Sync operations\n    syncFixtures,\n    dailySync,\n    syncStatus,\n    \n    // Sync actions\n    startSync: syncFixtures.mutate,\n    startDailySync: dailySync.mutate,\n    \n    // Sync state\n    isSyncing: syncFixtures.isPending || dailySync.isPending,\n    syncError: syncFixtures.error || dailySync.error,\n    lastSyncStatus: syncStatus.data,\n  };\n}\n\n/**\n * Hook for live fixtures (real-time updates)\n */\nexport function useLiveFixtures() {\n  return useFixtures({\n    status: 'live',\n    limit: 50,\n  });\n}\n\n/**\n * Hook for today's fixtures\n */\nexport function useTodayFixtures() {\n  const today = new Date().toISOString().split('T')[0];\n  \n  return useFixtures({\n    dateFrom: today,\n    dateTo: today,\n    limit: 100,\n  });\n}\n\n/**\n * Hook for upcoming fixtures\n */\nexport function useUpcomingFixtures(days: number = 7) {\n  const today = new Date();\n  const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);\n  \n  return useFixtures({\n    dateFrom: today.toISOString().split('T')[0],\n    dateTo: futureDate.toISOString().split('T')[0],\n    status: 'scheduled',\n    limit: 100,\n  });\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;AAID;AAGA;;AALA;;;AAgBO,SAAS,WAAW,MAA0C;;IACnE,MAAM,cAAc,IAAI;IACxB,IAAI,QAAQ,MAAM,YAAY,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,QAAQ,SAAS,YAAY,GAAG,CAAC,WAAW,OAAO,OAAO;IAC9D,IAAI,QAAQ,aAAa,WAAW,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ,CAAC,QAAQ;IACxF,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK;IAExD,OAAO,CAAA,GAAA,uIAAA,CAAA,oBAAiB,AAAD,EACrB;WAAI,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO;QAAI;KAAO;wCACzC;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,YAAY,QAAQ,IAAI;YAE9E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,OAAO,SAAS,IAAI;QACtB;uCACA;QACE,WAAW,KAAK,KAAK;IACvB;AAEJ;GAvBgB;;QAQP,uIAAA,CAAA,oBAAiB;;;AAoBnB,SAAS,UAAU,QAAgB;;IACxC,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;kCAC1B;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,UAAU;YAEhE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;YAClE;YAEA,OAAO,SAAS,IAAI;QACtB;iCACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,KAAK,KAAK;IACvB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS,SAAS,MAAwC;;IAC/D,MAAM,cAAc,IAAI;IACxB,IAAI,QAAQ,MAAM,YAAY,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,QAAQ,UAAU,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ;IACjE,IAAI,QAAQ,SAAS,YAAY,GAAG,CAAC,WAAW,OAAO,OAAO;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK;IAExD,OAAO,CAAA,GAAA,uIAAA,CAAA,oBAAiB,AAAD,EACrB;WAAI,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,KAAK;QAAI;KAAO;sCACvC;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,YAAY,QAAQ,IAAI;YAE5E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;YACjE;YAEA,OAAO,SAAS,IAAI;QACtB;qCACA;QACE,WAAW,IAAI,KAAK;IACtB;AAEJ;IAvBgB;;QAQP,uIAAA,CAAA,oBAAiB;;;AAoBnB,SAAS,QAAQ,MAAc;;IACpC,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;gCACxB;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,QAAQ;YAE5D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;YAChE;YAEA,OAAO,SAAS,IAAI;QACtB;+BACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS,YAAY,MAA2C;;IACrE,MAAM,cAAc,IAAI;IACxB,IAAI,QAAQ,MAAM,YAAY,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,QAAQ,UAAU,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ;IACjE,IAAI,QAAQ,QAAQ,YAAY,GAAG,CAAC,UAAU,OAAO,MAAM;IAC3D,IAAI,QAAQ,QAAQ,YAAY,GAAG,CAAC,UAAU,OAAO,MAAM;IAC3D,IAAI,QAAQ,UAAU,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ;IACjE,IAAI,QAAQ,QAAQ,YAAY,GAAG,CAAC,UAAU,OAAO,MAAM;IAC3D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK;IAExD,OAAO,CAAA,GAAA,uIAAA,CAAA,oBAAiB,AAAD,EACrB;WAAI,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,QAAQ;QAAI;KAAO;yCAC1C;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,YAAY,QAAQ,IAAI;YAE/E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;YACpE;YAEA,OAAO,SAAS,IAAI;QACtB;wCACA;QACE,WAAW,IAAI,KAAK;IACtB;AAEJ;IA1BgB;;QAWP,uIAAA,CAAA,oBAAiB;;;AAoBnB,SAAS,WAAW,SAAiB;;IAC1C,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;mCAC3B;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,WAAW;YAElE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,OAAO,SAAS,IAAI;QACtB;kCACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,KAAK;IAClB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAC1B,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,UAAU;gDAC7B;YACE,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,SAAS,UAAU,EAAE;YACvE;YAEA,OAAO,SAAS,IAAI;QACtB;+CACA;QACE,WAAW,KAAK;QAChB,iBAAiB,KAAK;IACxB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,yBAAsB;;;AAqBxB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;2CACnB;YACE,MAAM,WAAW,MAAM,MAAM,+BAA+B;gBAC1D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;YAChE;YAEA,OAAO,SAAS,IAAI;QACtB;0CACA;QACE,SAAS;+CAAE;gBACT,2DAA2D;gBAC3D,kBAAkB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,UAAU;gBAC/C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,QAAQ;gBAC7C,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;+CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;;IACF;AAEJ;IA9BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AAgCjB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;wCACnB;YACE,MAAM,WAAW,MAAM,MAAM,qCAAqC;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,SAAS,UAAU,EAAE;YACtE;YAEA,OAAO,SAAS,IAAI;QACtB;uCACA;QACE,SAAS;4CAAE;gBACT,sCAAsC;gBACtC,kBAAkB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,UAAU;gBAC/C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,QAAQ;gBAC7C,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;4CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,wBAAwB;YACxC;;IACF;AAEJ;IA9BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AAgCjB,SAAS;;IACd,MAAM,eAAe;IACrB,MAAM,YAAY;IAClB,MAAM,aAAa;IAEnB,OAAO;QACL,kBAAkB;QAClB;QACA;QACA;QAEA,eAAe;QACf,WAAW,aAAa,MAAM;QAC9B,gBAAgB,UAAU,MAAM;QAEhC,aAAa;QACb,WAAW,aAAa,SAAS,IAAI,UAAU,SAAS;QACxD,WAAW,aAAa,KAAK,IAAI,UAAU,KAAK;QAChD,gBAAgB,WAAW,IAAI;IACjC;AACF;IApBgB;;QACO;QACH;QACC;;;AAsBd,SAAS;;IACd,OAAO,YAAY;QACjB,QAAQ;QACR,OAAO;IACT;AACF;KALgB;;QACP;;;AASF,SAAS;;IACd,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAEpD,OAAO,YAAY;QACjB,UAAU;QACV,QAAQ;QACR,OAAO;IACT;AACF;KARgB;;QAGP;;;AAUF,SAAS,oBAAoB,OAAe,CAAC;;IAClD,MAAM,QAAQ,IAAI;IAClB,MAAM,aAAa,IAAI,KAAK,MAAM,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK;IAEpE,OAAO,YAAY;QACjB,UAAU,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC3C,QAAQ,WAAW,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC9C,QAAQ;QACR,OAAO;IACT;AACF;KAVgB;;QAIP"}}, {"offset": {"line": 1449, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1455, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/types/user.ts"], "sourcesContent": ["/**\n * User Types & Interfaces\n * SystemUser management types for APISportsGame CMS\n */\n\n/**\n * System user roles\n */\nexport type SystemUserRole = 'admin' | 'editor' | 'moderator';\n\n/**\n * User status\n */\nexport type UserStatus = 'active' | 'inactive' | 'suspended';\n\n/**\n * System user interface\n */\nexport interface SystemUser {\n  id: string;\n  username: string;\n  email: string;\n  firstName?: string;\n  lastName?: string;\n  role: SystemUserRole;\n  status: UserStatus;\n  lastLogin?: string;\n  createdAt: string;\n  updatedAt: string;\n  createdBy?: string;\n  avatar?: string;\n  permissions?: string[];\n}\n\n/**\n * Create user request\n */\nexport interface CreateUserRequest {\n  username: string;\n  email: string;\n  password: string;\n  firstName?: string;\n  lastName?: string;\n  role: SystemUserRole;\n  status?: UserStatus;\n}\n\n/**\n * Update user request\n */\nexport interface UpdateUserRequest {\n  email?: string;\n  firstName?: string;\n  lastName?: string;\n  role?: SystemUserRole;\n  status?: UserStatus;\n  avatar?: string;\n}\n\n/**\n * Change password request\n */\nexport interface ChangePasswordRequest {\n  currentPassword: string;\n  newPassword: string;\n  confirmPassword: string;\n}\n\n/**\n * User list query parameters\n */\nexport interface UserListParams {\n  page?: number;\n  limit?: number;\n  search?: string;\n  role?: SystemUserRole;\n  status?: UserStatus;\n  sortBy?: 'username' | 'email' | 'role' | 'status' | 'createdAt' | 'lastLogin';\n  sortOrder?: 'asc' | 'desc';\n}\n\n/**\n * User list response\n */\nexport interface UserListResponse {\n  users: SystemUser[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\n/**\n * User statistics\n */\nexport interface UserStatistics {\n  total: number;\n  active: number;\n  inactive: number;\n  suspended: number;\n  byRole: {\n    admin: number;\n    editor: number;\n    moderator: number;\n  };\n  recentLogins: number;\n  newThisMonth: number;\n}\n\n/**\n * User activity log\n */\nexport interface UserActivity {\n  id: string;\n  userId: string;\n  action: string;\n  description: string;\n  ipAddress?: string;\n  userAgent?: string;\n  createdAt: string;\n}\n\n/**\n * User session\n */\nexport interface UserSession {\n  id: string;\n  userId: string;\n  deviceInfo: string;\n  ipAddress: string;\n  lastActivity: string;\n  createdAt: string;\n  isActive: boolean;\n}\n\n/**\n * Role permissions mapping\n */\nexport const ROLE_PERMISSIONS: Record<SystemUserRole, string[]> = {\n  admin: [\n    'users.create',\n    'users.read',\n    'users.update',\n    'users.delete',\n    'users.manage_roles',\n    'football.create',\n    'football.read',\n    'football.update',\n    'football.delete',\n    'football.sync',\n    'broadcast.create',\n    'broadcast.read',\n    'broadcast.update',\n    'broadcast.delete',\n    'system.settings',\n    'system.logs',\n    'system.health',\n  ],\n  editor: [\n    'users.read',\n    'football.create',\n    'football.read',\n    'football.update',\n    'football.sync',\n    'broadcast.create',\n    'broadcast.read',\n    'broadcast.update',\n    'broadcast.delete',\n  ],\n  moderator: [\n    'users.read',\n    'football.read',\n    'broadcast.read',\n    'broadcast.update',\n  ],\n};\n\n/**\n * Role display names\n */\nexport const ROLE_LABELS: Record<SystemUserRole, string> = {\n  admin: 'Administrator',\n  editor: 'Editor',\n  moderator: 'Moderator',\n};\n\n/**\n * Status display names\n */\nexport const STATUS_LABELS: Record<UserStatus, string> = {\n  active: 'Active',\n  inactive: 'Inactive',\n  suspended: 'Suspended',\n};\n\n/**\n * Role colors for UI\n */\nexport const ROLE_COLORS: Record<SystemUserRole, string> = {\n  admin: '#ff4d4f',\n  editor: '#1890ff',\n  moderator: '#52c41a',\n};\n\n/**\n * Status colors for UI\n */\nexport const STATUS_COLORS: Record<UserStatus, string> = {\n  active: '#52c41a',\n  inactive: '#d9d9d9',\n  suspended: '#ff4d4f',\n};\n\n/**\n * User form validation rules\n */\nexport const USER_VALIDATION = {\n  username: {\n    min: 3,\n    max: 50,\n    pattern: /^[a-zA-Z0-9_-]+$/,\n    message: 'Username must be 3-50 characters and contain only letters, numbers, hyphens, and underscores',\n  },\n  email: {\n    pattern: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n    message: 'Please enter a valid email address',\n  },\n  password: {\n    min: 8,\n    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/,\n    message: 'Password must be at least 8 characters with uppercase, lowercase, number, and special character',\n  },\n  firstName: {\n    max: 50,\n    message: 'First name must not exceed 50 characters',\n  },\n  lastName: {\n    max: 50,\n    message: 'Last name must not exceed 50 characters',\n  },\n};\n\n/**\n * Default user list params\n */\nexport const DEFAULT_USER_PARAMS: UserListParams = {\n  page: 1,\n  limit: 20,\n  sortBy: 'createdAt',\n  sortOrder: 'desc',\n};\n\n/**\n * User helper functions\n */\nexport const userHelpers = {\n  /**\n   * Get user full name\n   */\n  getFullName: (user: SystemUser): string => {\n    if (user.firstName && user.lastName) {\n      return `${user.firstName} ${user.lastName}`;\n    }\n    if (user.firstName) {\n      return user.firstName;\n    }\n    if (user.lastName) {\n      return user.lastName;\n    }\n    return user.username;\n  },\n\n  /**\n   * Get user display name\n   */\n  getDisplayName: (user: SystemUser): string => {\n    const fullName = userHelpers.getFullName(user);\n    return fullName !== user.username ? `${fullName} (${user.username})` : user.username;\n  },\n\n  /**\n   * Check if user has permission\n   */\n  hasPermission: (user: SystemUser, permission: string): boolean => {\n    const rolePermissions = ROLE_PERMISSIONS[user.role] || [];\n    return rolePermissions.includes(permission);\n  },\n\n  /**\n   * Check if user is active\n   */\n  isActive: (user: SystemUser): boolean => {\n    return user.status === 'active';\n  },\n\n  /**\n   * Get user avatar URL or initials\n   */\n  getAvatarDisplay: (user: SystemUser): { type: 'url' | 'initials'; value: string } => {\n    if (user.avatar) {\n      return { type: 'url', value: user.avatar };\n    }\n    \n    const fullName = userHelpers.getFullName(user);\n    const initials = fullName\n      .split(' ')\n      .map(name => name.charAt(0).toUpperCase())\n      .slice(0, 2)\n      .join('');\n    \n    return { type: 'initials', value: initials || user.username.charAt(0).toUpperCase() };\n  },\n\n  /**\n   * Format last login time\n   */\n  formatLastLogin: (lastLogin?: string): string => {\n    if (!lastLogin) return 'Never';\n    \n    const date = new Date(lastLogin);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return 'Yesterday';\n    if (diffDays < 7) return `${diffDays} days ago`;\n    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;\n    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;\n    \n    return `${Math.floor(diffDays / 365)} years ago`;\n  },\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;CAEC;;;;;;;;;;AAmIM,MAAM,mBAAqD;IAChE,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,QAAQ;QACN;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW;QACT;QACA;QACA;QACA;KACD;AACH;AAKO,MAAM,cAA8C;IACzD,OAAO;IACP,QAAQ;IACR,WAAW;AACb;AAKO,MAAM,gBAA4C;IACvD,QAAQ;IACR,UAAU;IACV,WAAW;AACb;AAKO,MAAM,cAA8C;IACzD,OAAO;IACP,QAAQ;IACR,WAAW;AACb;AAKO,MAAM,gBAA4C;IACvD,QAAQ;IACR,UAAU;IACV,WAAW;AACb;AAKO,MAAM,kBAAkB;IAC7B,UAAU;QACR,KAAK;QACL,KAAK;QACL,SAAS;QACT,SAAS;IACX;IACA,OAAO;QACL,SAAS;QACT,SAAS;IACX;IACA,UAAU;QACR,KAAK;QACL,SAAS;QACT,SAAS;IACX;IACA,WAAW;QACT,KAAK;QACL,SAAS;IACX;IACA,UAAU;QACR,KAAK;QACL,SAAS;IACX;AACF;AAKO,MAAM,sBAAsC;IACjD,MAAM;IACN,OAAO;IACP,QAAQ;IACR,WAAW;AACb;AAKO,MAAM,cAAc;IACzB;;GAEC,GACD,aAAa,CAAC;QACZ,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,EAAE;YACnC,OAAO,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;QAC7C;QACA,IAAI,KAAK,SAAS,EAAE;YAClB,OAAO,KAAK,SAAS;QACvB;QACA,IAAI,KAAK,QAAQ,EAAE;YACjB,OAAO,KAAK,QAAQ;QACtB;QACA,OAAO,KAAK,QAAQ;IACtB;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,MAAM,WAAW,YAAY,WAAW,CAAC;QACzC,OAAO,aAAa,KAAK,QAAQ,GAAG,GAAG,SAAS,EAAE,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,QAAQ;IACtF;IAEA;;GAEC,GACD,eAAe,CAAC,MAAkB;QAChC,MAAM,kBAAkB,gBAAgB,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE;QACzD,OAAO,gBAAgB,QAAQ,CAAC;IAClC;IAEA;;GAEC,GACD,UAAU,CAAC;QACT,OAAO,KAAK,MAAM,KAAK;IACzB;IAEA;;GAEC,GACD,kBAAkB,CAAC;QACjB,IAAI,KAAK,MAAM,EAAE;YACf,OAAO;gBAAE,MAAM;gBAAO,OAAO,KAAK,MAAM;YAAC;QAC3C;QAEA,MAAM,WAAW,YAAY,WAAW,CAAC;QACzC,MAAM,WAAW,SACd,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,KAAK,CAAC,GAAG,GACT,IAAI,CAAC;QAER,OAAO;YAAE,MAAM;YAAY,OAAO,YAAY,KAAK,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;QAAG;IACtF;IAEA;;GAEC,GACD,iBAAiB,CAAC;QAChB,IAAI,CAAC,WAAW,OAAO;QAEvB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;QAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;QAEzD,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,WAAW,GAAG,OAAO,GAAG,SAAS,SAAS,CAAC;QAC/C,IAAI,WAAW,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,GAAG,UAAU,CAAC;QACjE,IAAI,WAAW,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,IAAI,WAAW,CAAC;QAEpE,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,KAAK,UAAU,CAAC;IAClD;AACF"}}, {"offset": {"line": 1626, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1632, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/users.ts"], "sourcesContent": ["/**\n * User API Hooks\n * TanStack Query hooks for SystemUser management\n */\n\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { message } from 'antd';\nimport {\n  SystemUser,\n  CreateUserRequest,\n  UpdateUserRequest,\n  ChangePasswordRequest,\n  UserListParams,\n  UserListResponse,\n  UserStatistics,\n  UserActivity,\n  UserSession,\n  DEFAULT_USER_PARAMS,\n} from '@/types/user';\n\n/**\n * API endpoints\n */\nconst API_ENDPOINTS = {\n  users: '/api/system-auth/users',\n  userById: (id: string) => `/api/system-auth/users/${id}`,\n  userStats: '/api/system-auth/users/statistics',\n  userActivity: (id: string) => `/api/system-auth/users/${id}/activity`,\n  userSessions: (id: string) => `/api/system-auth/users/${id}/sessions`,\n  changePassword: (id: string) => `/api/system-auth/users/${id}/change-password`,\n  resetPassword: (id: string) => `/api/system-auth/users/${id}/reset-password`,\n};\n\n/**\n * Query keys\n */\nexport const userQueryKeys = {\n  all: ['users'] as const,\n  lists: () => [...userQueryKeys.all, 'list'] as const,\n  list: (params: UserListParams) => [...userQueryKeys.lists(), params] as const,\n  details: () => [...userQueryKeys.all, 'detail'] as const,\n  detail: (id: string) => [...userQueryKeys.details(), id] as const,\n  statistics: () => [...userQueryKeys.all, 'statistics'] as const,\n  activity: (id: string) => [...userQueryKeys.all, 'activity', id] as const,\n  sessions: (id: string) => [...userQueryKeys.all, 'sessions', id] as const,\n};\n\n/**\n * Mock data for development\n */\nconst mockUsers: SystemUser[] = [\n  {\n    id: '1',\n    username: 'admin',\n    email: '<EMAIL>',\n    firstName: 'System',\n    lastName: 'Administrator',\n    role: 'admin',\n    status: 'active',\n    lastLogin: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago\n    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(), // 30 days ago\n    updatedAt: new Date().toISOString(),\n    createdBy: 'system',\n  },\n  {\n    id: '2',\n    username: 'editor1',\n    email: '<EMAIL>',\n    firstName: 'John',\n    lastName: 'Editor',\n    role: 'editor',\n    status: 'active',\n    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago\n    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15).toISOString(), // 15 days ago\n    updatedAt: new Date().toISOString(),\n    createdBy: '1',\n  },\n  {\n    id: '3',\n    username: 'moderator1',\n    email: '<EMAIL>',\n    firstName: 'Jane',\n    lastName: 'Moderator',\n    role: 'moderator',\n    status: 'active',\n    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago\n    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(), // 7 days ago\n    updatedAt: new Date().toISOString(),\n    createdBy: '1',\n  },\n  {\n    id: '4',\n    username: 'inactive_user',\n    email: '<EMAIL>',\n    firstName: 'Inactive',\n    lastName: 'User',\n    role: 'editor',\n    status: 'inactive',\n    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 60).toISOString(), // 60 days ago\n    updatedAt: new Date().toISOString(),\n    createdBy: '1',\n  },\n];\n\n/**\n * Mock API functions\n */\nconst mockAPI = {\n  getUsers: async (params: UserListParams): Promise<UserListResponse> => {\n    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay\n    \n    let filteredUsers = [...mockUsers];\n    \n    // Apply filters\n    if (params.search) {\n      const search = params.search.toLowerCase();\n      filteredUsers = filteredUsers.filter(user =>\n        user.username.toLowerCase().includes(search) ||\n        user.email.toLowerCase().includes(search) ||\n        user.firstName?.toLowerCase().includes(search) ||\n        user.lastName?.toLowerCase().includes(search)\n      );\n    }\n    \n    if (params.role) {\n      filteredUsers = filteredUsers.filter(user => user.role === params.role);\n    }\n    \n    if (params.status) {\n      filteredUsers = filteredUsers.filter(user => user.status === params.status);\n    }\n    \n    // Apply sorting\n    if (params.sortBy) {\n      filteredUsers.sort((a, b) => {\n        const aValue = a[params.sortBy!] || '';\n        const bValue = b[params.sortBy!] || '';\n        const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n        return params.sortOrder === 'desc' ? -comparison : comparison;\n      });\n    }\n    \n    // Apply pagination\n    const page = params.page || 1;\n    const limit = params.limit || 20;\n    const startIndex = (page - 1) * limit;\n    const endIndex = startIndex + limit;\n    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);\n    \n    return {\n      users: paginatedUsers,\n      total: filteredUsers.length,\n      page,\n      limit,\n      totalPages: Math.ceil(filteredUsers.length / limit),\n    };\n  },\n\n  getUser: async (id: string): Promise<SystemUser> => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    const user = mockUsers.find(u => u.id === id);\n    if (!user) throw new Error('User not found');\n    return user;\n  },\n\n  createUser: async (data: CreateUserRequest): Promise<SystemUser> => {\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    const newUser: SystemUser = {\n      id: String(mockUsers.length + 1),\n      ...data,\n      status: data.status || 'active',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      createdBy: '1', // Current user\n    };\n    mockUsers.push(newUser);\n    return newUser;\n  },\n\n  updateUser: async (id: string, data: UpdateUserRequest): Promise<SystemUser> => {\n    await new Promise(resolve => setTimeout(resolve, 800));\n    const userIndex = mockUsers.findIndex(u => u.id === id);\n    if (userIndex === -1) throw new Error('User not found');\n    \n    mockUsers[userIndex] = {\n      ...mockUsers[userIndex],\n      ...data,\n      updatedAt: new Date().toISOString(),\n    };\n    \n    return mockUsers[userIndex];\n  },\n\n  deleteUser: async (id: string): Promise<void> => {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const userIndex = mockUsers.findIndex(u => u.id === id);\n    if (userIndex === -1) throw new Error('User not found');\n    mockUsers.splice(userIndex, 1);\n  },\n\n  getStatistics: async (): Promise<UserStatistics> => {\n    await new Promise(resolve => setTimeout(resolve, 400));\n    \n    const total = mockUsers.length;\n    const active = mockUsers.filter(u => u.status === 'active').length;\n    const inactive = mockUsers.filter(u => u.status === 'inactive').length;\n    const suspended = mockUsers.filter(u => u.status === 'suspended').length;\n    \n    const byRole = {\n      admin: mockUsers.filter(u => u.role === 'admin').length,\n      editor: mockUsers.filter(u => u.role === 'editor').length,\n      moderator: mockUsers.filter(u => u.role === 'moderator').length,\n    };\n    \n    const recentLogins = mockUsers.filter(u => {\n      if (!u.lastLogin) return false;\n      const lastLogin = new Date(u.lastLogin);\n      const dayAgo = new Date(Date.now() - 1000 * 60 * 60 * 24);\n      return lastLogin > dayAgo;\n    }).length;\n    \n    const monthAgo = new Date(Date.now() - 1000 * 60 * 60 * 24 * 30);\n    const newThisMonth = mockUsers.filter(u => {\n      const created = new Date(u.createdAt);\n      return created > monthAgo;\n    }).length;\n    \n    return {\n      total,\n      active,\n      inactive,\n      suspended,\n      byRole,\n      recentLogins,\n      newThisMonth,\n    };\n  },\n};\n\n/**\n * Get users list\n */\nexport function useUsers(params: UserListParams = DEFAULT_USER_PARAMS) {\n  return useQuery({\n    queryKey: userQueryKeys.list(params),\n    queryFn: () => mockAPI.getUsers(params),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Get user by ID\n */\nexport function useUser(id: string) {\n  return useQuery({\n    queryKey: userQueryKeys.detail(id),\n    queryFn: () => mockAPI.getUser(id),\n    enabled: !!id,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Get user statistics\n */\nexport function useUserStatistics() {\n  return useQuery({\n    queryKey: userQueryKeys.statistics(),\n    queryFn: () => mockAPI.getStatistics(),\n    staleTime: 2 * 60 * 1000, // 2 minutes\n  });\n}\n\n/**\n * Create user mutation\n */\nexport function useCreateUser() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data: CreateUserRequest) => mockAPI.createUser(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.statistics() });\n      message.success('User created successfully');\n    },\n    onError: (error: Error) => {\n      message.error(`Failed to create user: ${error.message}`);\n    },\n  });\n}\n\n/**\n * Update user mutation\n */\nexport function useUpdateUser() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ id, data }: { id: string; data: UpdateUserRequest }) => \n      mockAPI.updateUser(id, data),\n    onSuccess: (updatedUser) => {\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.detail(updatedUser.id) });\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.statistics() });\n      message.success('User updated successfully');\n    },\n    onError: (error: Error) => {\n      message.error(`Failed to update user: ${error.message}`);\n    },\n  });\n}\n\n/**\n * Delete user mutation\n */\nexport function useDeleteUser() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (id: string) => mockAPI.deleteUser(id),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.statistics() });\n      message.success('User deleted successfully');\n    },\n    onError: (error: Error) => {\n      message.error(`Failed to delete user: ${error.message}`);\n    },\n  });\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAID;AAFA;AAAA;AAAA;AACA;;;;;AAcA;;CAEC,GACD,MAAM,gBAAgB;IACpB,OAAO;IACP,UAAU,CAAC,KAAe,CAAC,uBAAuB,EAAE,IAAI;IACxD,WAAW;IACX,cAAc,CAAC,KAAe,CAAC,uBAAuB,EAAE,GAAG,SAAS,CAAC;IACrE,cAAc,CAAC,KAAe,CAAC,uBAAuB,EAAE,GAAG,SAAS,CAAC;IACrE,gBAAgB,CAAC,KAAe,CAAC,uBAAuB,EAAE,GAAG,gBAAgB,CAAC;IAC9E,eAAe,CAAC,KAAe,CAAC,uBAAuB,EAAE,GAAG,eAAe,CAAC;AAC9E;AAKO,MAAM,gBAAgB;IAC3B,KAAK;QAAC;KAAQ;IACd,OAAO,IAAM;eAAI,cAAc,GAAG;YAAE;SAAO;IAC3C,MAAM,CAAC,SAA2B;eAAI,cAAc,KAAK;YAAI;SAAO;IACpE,SAAS,IAAM;eAAI,cAAc,GAAG;YAAE;SAAS;IAC/C,QAAQ,CAAC,KAAe;eAAI,cAAc,OAAO;YAAI;SAAG;IACxD,YAAY,IAAM;eAAI,cAAc,GAAG;YAAE;SAAa;IACtD,UAAU,CAAC,KAAe;eAAI,cAAc,GAAG;YAAE;YAAY;SAAG;IAChE,UAAU,CAAC,KAAe;eAAI,cAAc,GAAG;YAAE;YAAY;SAAG;AAClE;AAEA;;CAEC,GACD,MAAM,YAA0B;IAC9B;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,IAAI,WAAW;QAC5D,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,KAAK,IAAI,WAAW;QACtE,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,GAAG,WAAW;QAChE,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,KAAK,IAAI,WAAW;QACtE,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,IAAI,WAAW;QACjE,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,KAAK,GAAG,WAAW;QACrE,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,KAAK,IAAI,WAAW;QACtE,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;CACD;AAED;;CAEC,GACD,MAAM,UAAU;IACd,UAAU,OAAO;QACf,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,OAAO,yBAAyB;QAEjF,IAAI,gBAAgB;eAAI;SAAU;QAElC,gBAAgB;QAChB,IAAI,OAAO,MAAM,EAAE;YACjB,MAAM,SAAS,OAAO,MAAM,CAAC,WAAW;YACxC,gBAAgB,cAAc,MAAM,CAAC,CAAA,OACnC,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WACrC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAClC,KAAK,SAAS,EAAE,cAAc,SAAS,WACvC,KAAK,QAAQ,EAAE,cAAc,SAAS;QAE1C;QAEA,IAAI,OAAO,IAAI,EAAE;YACf,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,OAAO,IAAI;QACxE;QAEA,IAAI,OAAO,MAAM,EAAE;YACjB,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,OAAO,MAAM;QAC5E;QAEA,gBAAgB;QAChB,IAAI,OAAO,MAAM,EAAE;YACjB,cAAc,IAAI,CAAC,CAAC,GAAG;gBACrB,MAAM,SAAS,CAAC,CAAC,OAAO,MAAM,CAAE,IAAI;gBACpC,MAAM,SAAS,CAAC,CAAC,OAAO,MAAM,CAAE,IAAI;gBACpC,MAAM,aAAa,SAAS,SAAS,CAAC,IAAI,SAAS,SAAS,IAAI;gBAChE,OAAO,OAAO,SAAS,KAAK,SAAS,CAAC,aAAa;YACrD;QACF;QAEA,mBAAmB;QACnB,MAAM,OAAO,OAAO,IAAI,IAAI;QAC5B,MAAM,QAAQ,OAAO,KAAK,IAAI;QAC9B,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;QAChC,MAAM,WAAW,aAAa;QAC9B,MAAM,iBAAiB,cAAc,KAAK,CAAC,YAAY;QAEvD,OAAO;YACL,OAAO;YACP,OAAO,cAAc,MAAM;YAC3B;YACA;YACA,YAAY,KAAK,IAAI,CAAC,cAAc,MAAM,GAAG;QAC/C;IACF;IAEA,SAAS,OAAO;QACd,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAC3B,OAAO;IACT;IAEA,YAAY,OAAO;QACjB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,MAAM,UAAsB;YAC1B,IAAI,OAAO,UAAU,MAAM,GAAG;YAC9B,GAAG,IAAI;YACP,QAAQ,KAAK,MAAM,IAAI;YACvB,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW;QACb;QACA,UAAU,IAAI,CAAC;QACf,OAAO;IACT;IAEA,YAAY,OAAO,IAAY;QAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACpD,IAAI,cAAc,CAAC,GAAG,MAAM,IAAI,MAAM;QAEtC,SAAS,CAAC,UAAU,GAAG;YACrB,GAAG,SAAS,CAAC,UAAU;YACvB,GAAG,IAAI;YACP,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,OAAO,SAAS,CAAC,UAAU;IAC7B;IAEA,YAAY,OAAO;QACjB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACpD,IAAI,cAAc,CAAC,GAAG,MAAM,IAAI,MAAM;QACtC,UAAU,MAAM,CAAC,WAAW;IAC9B;IAEA,eAAe;QACb,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,MAAM,QAAQ,UAAU,MAAM;QAC9B,MAAM,SAAS,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;QAClE,MAAM,WAAW,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;QACtE,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QAExE,MAAM,SAAS;YACb,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;YACvD,QAAQ,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;YACzD,WAAW,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,MAAM;QACjE;QAEA,MAAM,eAAe,UAAU,MAAM,CAAC,CAAA;YACpC,IAAI,CAAC,EAAE,SAAS,EAAE,OAAO;YACzB,MAAM,YAAY,IAAI,KAAK,EAAE,SAAS;YACtC,MAAM,SAAS,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK;YACtD,OAAO,YAAY;QACrB,GAAG,MAAM;QAET,MAAM,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,KAAK;QAC7D,MAAM,eAAe,UAAU,MAAM,CAAC,CAAA;YACpC,MAAM,UAAU,IAAI,KAAK,EAAE,SAAS;YACpC,OAAO,UAAU;QACnB,GAAG,MAAM;QAET,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;AACF;AAKO,SAAS,SAAS,SAAyB,uHAAA,CAAA,sBAAmB;;IACnE,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,cAAc,IAAI,CAAC;QAC7B,OAAO;iCAAE,IAAM,QAAQ,QAAQ,CAAC;;QAChC,WAAW,IAAI,KAAK;IACtB;AACF;GANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,QAAQ,EAAU;;IAChC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,cAAc,MAAM,CAAC;QAC/B,OAAO;gCAAE,IAAM,QAAQ,OAAO,CAAC;;QAC/B,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,cAAc,UAAU;QAClC,OAAO;0CAAE,IAAM,QAAQ,aAAa;;QACpC,WAAW,IAAI,KAAK;IACtB;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC,OAA4B,QAAQ,UAAU,CAAC;;QAC5D,SAAS;yCAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,cAAc,KAAK;gBAAG;gBAChE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,cAAc,UAAU;gBAAG;gBACrE,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;;QACA,OAAO;yCAAE,CAAC;gBACR,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;YACzD;;IACF;AACF;IAdgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAgBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAA2C,GAChE,QAAQ,UAAU,CAAC,IAAI;;QACzB,SAAS;yCAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU,cAAc,KAAK;gBAAG;gBAChE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,cAAc,MAAM,CAAC,YAAY,EAAE;gBAAE;gBAC/E,YAAY,iBAAiB,CAAC;oBAAE,UAAU,cAAc,UAAU;gBAAG;gBACrE,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;;QACA,OAAO;yCAAE,CAAC;gBACR,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;YACzD;;IACF;AACF;IAhBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAkBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC,KAAe,QAAQ,UAAU,CAAC;;QAC/C,SAAS;yCAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,cAAc,KAAK;gBAAG;gBAChE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,cAAc,UAAU;gBAAG;gBACrE,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;;QACA,OAAO;yCAAE,CAAC;gBACR,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;YACzD;;IACF;AACF;IAdgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW"}}, {"offset": {"line": 2010, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2016, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/broadcast-hooks.ts"], "sourcesContent": ["/**\n * Broadcast Links API Hooks\n * Hooks for broadcast links management operations\n */\n\n'use client';\n\nimport { queryKeys } from '@/lib/query-client';\nimport { BroadcastQueries } from '@/lib/query-types';\nimport { PaginatedResponse } from '@/lib/query-utils';\nimport { \n  useBaseQuery, \n  usePaginatedQuery,\n  useBaseMutation,\n  useOptimisticMutation,\n  useApiHookUtils \n} from './base-hooks';\n\n/**\n * Hook for getting broadcast links\n */\nexport function useBroadcastLinks(params?: BroadcastQueries.BroadcastLinkQueryParams) {\n  const queryParams = new URLSearchParams();\n  if (params?.page) queryParams.set('page', params.page.toString());\n  if (params?.limit) queryParams.set('limit', params.limit.toString());\n  if (params?.fixtureId) queryParams.set('fixtureId', params.fixtureId);\n  if (params?.quality) queryParams.set('quality', params.quality);\n  if (params?.language) queryParams.set('language', params.language);\n  if (params?.isActive !== undefined) queryParams.set('isActive', params.isActive.toString());\n  if (params?.query) queryParams.set('query', params.query);\n\n  return usePaginatedQuery(\n    [...queryKeys.broadcast.links(), params],\n    async (): Promise<PaginatedResponse<BroadcastQueries.BroadcastLink>> => {\n      const response = await fetch(`/api/broadcast-links?${queryParams.toString()}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch broadcast links: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 2 * 60 * 1000, // 2 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting specific broadcast link\n */\nexport function useBroadcastLink(linkId: string) {\n  return useBaseQuery(\n    queryKeys.broadcast.link(linkId),\n    async (): Promise<BroadcastQueries.BroadcastLink> => {\n      const response = await fetch(`/api/broadcast-links/${linkId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch broadcast link: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!linkId,\n      staleTime: 2 * 60 * 1000, // 2 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting broadcast links for a specific fixture\n */\nexport function useFixtureBroadcastLinks(fixtureId: string) {\n  return useBaseQuery(\n    queryKeys.broadcast.fixture(fixtureId),\n    async (): Promise<BroadcastQueries.BroadcastLink[]> => {\n      const response = await fetch(`/api/broadcast-links/fixture/${fixtureId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch fixture broadcast links: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!fixtureId,\n      staleTime: 1 * 60 * 1000, // 1 minute - links for live fixtures change frequently\n    }\n  );\n}\n\n/**\n * Hook for creating broadcast link\n */\nexport function useCreateBroadcastLink() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useOptimisticMutation<BroadcastQueries.BroadcastLink, BroadcastQueries.CreateBroadcastLinkRequest>(\n    async (data) => {\n      const response = await fetch('/api/broadcast-links', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to create broadcast link: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: (data) => {\n        // Invalidate broadcast links queries\n        invalidateQueries(queryKeys.broadcast.links());\n        invalidateQueries(queryKeys.broadcast.fixture(data.fixtureId));\n        console.log('✅ Broadcast link created successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Failed to create broadcast link:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for updating broadcast link\n */\nexport function useUpdateBroadcastLink() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useOptimisticMutation<BroadcastQueries.BroadcastLink, { id: string; data: BroadcastQueries.UpdateBroadcastLinkRequest }>(\n    async ({ id, data }) => {\n      const response = await fetch(`/api/broadcast-links/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to update broadcast link: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: (data) => {\n        // Invalidate specific link and related queries\n        invalidateQueries(queryKeys.broadcast.link(data.id));\n        invalidateQueries(queryKeys.broadcast.links());\n        invalidateQueries(queryKeys.broadcast.fixture(data.fixtureId));\n        console.log('✅ Broadcast link updated successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Failed to update broadcast link:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for deleting broadcast link\n */\nexport function useDeleteBroadcastLink() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string }, string>(\n    async (linkId) => {\n      const response = await fetch(`/api/broadcast-links/${linkId}`, {\n        method: 'DELETE',\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to delete broadcast link: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: (_, linkId) => {\n        // Invalidate broadcast links queries\n        invalidateQueries(queryKeys.broadcast.links());\n        invalidateQueries(queryKeys.broadcast.link(linkId));\n        console.log('✅ Broadcast link deleted successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Failed to delete broadcast link:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for toggling broadcast link active status\n */\nexport function useToggleBroadcastLinkStatus() {\n  const { invalidateQueries, updateQueryData } = useApiHookUtils();\n\n  return useOptimisticMutation<BroadcastQueries.BroadcastLink, { id: string; isActive: boolean }>(\n    async ({ id, isActive }) => {\n      const response = await fetch(`/api/broadcast-links/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ isActive }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to toggle broadcast link status: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onMutate: async ({ id, isActive }) => {\n        // Optimistically update the link status\n        const linkQueryKey = queryKeys.broadcast.link(id);\n        const previousLink = updateQueryData<BroadcastQueries.BroadcastLink>(\n          linkQueryKey,\n          (old) => old ? { ...old, isActive } : old\n        );\n\n        return { previousLink, linkQueryKey };\n      },\n      onError: (error, variables, context) => {\n        // Revert optimistic update on error\n        if (context?.previousLink && context?.linkQueryKey) {\n          updateQueryData(context.linkQueryKey, () => context.previousLink);\n        }\n        console.error('❌ Failed to toggle broadcast link status:', error);\n      },\n      onSuccess: (data) => {\n        // Invalidate related queries\n        invalidateQueries(queryKeys.broadcast.links());\n        invalidateQueries(queryKeys.broadcast.fixture(data.fixtureId));\n        console.log('✅ Broadcast link status toggled successfully');\n      },\n    }\n  );\n}\n\n/**\n * Composite hook for broadcast links operations\n */\nexport function useBroadcastLinksManager() {\n  const createLink = useCreateBroadcastLink();\n  const updateLink = useUpdateBroadcastLink();\n  const deleteLink = useDeleteBroadcastLink();\n  const toggleStatus = useToggleBroadcastLinkStatus();\n\n  return {\n    // Mutations\n    createLink,\n    updateLink,\n    deleteLink,\n    toggleStatus,\n    \n    // Actions\n    createBroadcastLink: createLink.mutate,\n    updateBroadcastLink: updateLink.mutate,\n    deleteBroadcastLink: deleteLink.mutate,\n    toggleLinkStatus: toggleStatus.mutate,\n    \n    // State\n    isCreating: createLink.isPending,\n    isUpdating: updateLink.isPending,\n    isDeleting: deleteLink.isPending,\n    isToggling: toggleStatus.isPending,\n    isLoading: createLink.isPending || updateLink.isPending || deleteLink.isPending || toggleStatus.isPending,\n    \n    // Errors\n    createError: createLink.error,\n    updateError: updateLink.error,\n    deleteError: deleteLink.error,\n    toggleError: toggleStatus.error,\n  };\n}\n\n/**\n * Hook for broadcast links by quality\n */\nexport function useBroadcastLinksByQuality(quality: 'HD' | 'SD' | 'Mobile') {\n  return useBroadcastLinks({ quality, isActive: true });\n}\n\n/**\n * Hook for broadcast links by language\n */\nexport function useBroadcastLinksByLanguage(language: string) {\n  return useBroadcastLinks({ language, isActive: true });\n}\n\n/**\n * Hook for active broadcast links\n */\nexport function useActiveBroadcastLinks() {\n  return useBroadcastLinks({ isActive: true });\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAID;AAGA;;AALA;;;AAgBO,SAAS,kBAAkB,MAAkD;;IAClF,MAAM,cAAc,IAAI;IACxB,IAAI,QAAQ,MAAM,YAAY,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,QAAQ,WAAW,YAAY,GAAG,CAAC,aAAa,OAAO,SAAS;IACpE,IAAI,QAAQ,SAAS,YAAY,GAAG,CAAC,WAAW,OAAO,OAAO;IAC9D,IAAI,QAAQ,UAAU,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ;IACjE,IAAI,QAAQ,aAAa,WAAW,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ,CAAC,QAAQ;IACxF,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK;IAExD,OAAO,CAAA,GAAA,uIAAA,CAAA,oBAAiB,AAAD,EACrB;WAAI,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;QAAI;KAAO;+CACxC;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,YAAY,QAAQ,IAAI;YAE7E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;YAC3E;YAEA,OAAO,SAAS,IAAI;QACtB;8CACA;QACE,WAAW,IAAI,KAAK;IACtB;AAEJ;GAzBgB;;QAUP,uIAAA,CAAA,oBAAiB;;;AAoBnB,SAAS,iBAAiB,MAAc;;IAC7C,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC;yCACzB;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,QAAQ;YAE7D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,SAAS,UAAU,EAAE;YAC1E;YAEA,OAAO,SAAS,IAAI;QACtB;wCACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS,yBAAyB,SAAiB;;IACxD,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,OAAO,CAAC;iDAC5B;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,6BAA6B,EAAE,WAAW;YAExE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,yCAAyC,EAAE,SAAS,UAAU,EAAE;YACnF;YAEA,OAAO,SAAS,IAAI;QACtB;gDACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,wBAAqB,AAAD;wDACzB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;YAC3E;YAEA,OAAO,SAAS,IAAI;QACtB;uDACA;QACE,SAAS;4DAAE,CAAC;gBACV,qCAAqC;gBACrC,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;gBAC3C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,SAAS;gBAC5D,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;4DAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,sCAAsC;YACtD;;IACF;AAEJ;IA/BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,wBAAqB;;;AAiCvB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,wBAAqB,AAAD;wDACzB,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE;YACjB,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,IAAI,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;YAC3E;YAEA,OAAO,SAAS,IAAI;QACtB;uDACA;QACE,SAAS;4DAAE,CAAC;gBACV,+CAA+C;gBAC/C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE;gBAClD,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;gBAC3C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,SAAS;gBAC5D,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;4DAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,sCAAsC;YACtD;;IACF;AAEJ;IAhCgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,wBAAqB;;;AAkCvB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;kDACnB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,QAAQ,EAAE;gBAC7D,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;YAC3E;YAEA,OAAO,SAAS,IAAI;QACtB;iDACA;QACE,SAAS;sDAAE,CAAC,GAAG;gBACb,qCAAqC;gBACrC,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;gBAC3C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC3C,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;sDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,sCAAsC;YACtD;;IACF;AAEJ;IA3BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AA6BjB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE7D,OAAO,CAAA,GAAA,uIAAA,CAAA,wBAAqB,AAAD;8DACzB,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE;YACrB,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,IAAI,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,wCAAwC,EAAE,SAAS,UAAU,EAAE;YAClF;YAEA,OAAO,SAAS,IAAI;QACtB;6DACA;QACE,QAAQ;kEAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE;gBAC/B,wCAAwC;gBACxC,MAAM,eAAe,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC9C,MAAM,eAAe,gBACnB;uFACA,CAAC,MAAQ,MAAM;4BAAE,GAAG,GAAG;4BAAE;wBAAS,IAAI;;gBAGxC,OAAO;oBAAE;oBAAc;gBAAa;YACtC;;QACA,OAAO;kEAAE,CAAC,OAAO,WAAW;gBAC1B,oCAAoC;gBACpC,IAAI,SAAS,gBAAgB,SAAS,cAAc;oBAClD,gBAAgB,QAAQ,YAAY;8EAAE,IAAM,QAAQ,YAAY;;gBAClE;gBACA,QAAQ,KAAK,CAAC,6CAA6C;YAC7D;;QACA,SAAS;kEAAE,CAAC;gBACV,6BAA6B;gBAC7B,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;gBAC3C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,SAAS;gBAC5D,QAAQ,GAAG,CAAC;YACd;;IACF;AAEJ;IA7CgB;;QACiC,uIAAA,CAAA,kBAAe;QAEvD,uIAAA,CAAA,wBAAqB;;;AA+CvB,SAAS;;IACd,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,eAAe;IAErB,OAAO;QACL,YAAY;QACZ;QACA;QACA;QACA;QAEA,UAAU;QACV,qBAAqB,WAAW,MAAM;QACtC,qBAAqB,WAAW,MAAM;QACtC,qBAAqB,WAAW,MAAM;QACtC,kBAAkB,aAAa,MAAM;QAErC,QAAQ;QACR,YAAY,WAAW,SAAS;QAChC,YAAY,WAAW,SAAS;QAChC,YAAY,WAAW,SAAS;QAChC,YAAY,aAAa,SAAS;QAClC,WAAW,WAAW,SAAS,IAAI,WAAW,SAAS,IAAI,WAAW,SAAS,IAAI,aAAa,SAAS;QAEzG,SAAS;QACT,aAAa,WAAW,KAAK;QAC7B,aAAa,WAAW,KAAK;QAC7B,aAAa,WAAW,KAAK;QAC7B,aAAa,aAAa,KAAK;IACjC;AACF;IAhCgB;;QACK;QACA;QACA;QACE;;;AAiChB,SAAS,2BAA2B,OAA+B;;IACxE,OAAO,kBAAkB;QAAE;QAAS,UAAU;IAAK;AACrD;IAFgB;;QACP;;;AAMF,SAAS,4BAA4B,QAAgB;;IAC1D,OAAO,kBAAkB;QAAE;QAAU,UAAU;IAAK;AACtD;IAFgB;;QACP;;;AAMF,SAAS;;IACd,OAAO,kBAAkB;QAAE,UAAU;IAAK;AAC5C;KAFgB;;QACP"}}, {"offset": {"line": 2361, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2367, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/health-hooks.ts"], "sourcesContent": ["/**\n * Health Check API Hooks\n * Hooks for API health monitoring operations\n */\n\n'use client';\n\nimport { queryKeys } from '@/lib/query-client';\nimport { HealthQueries } from '@/lib/query-types';\nimport { useBackgroundSyncQuery, useBaseQuery } from './base-hooks';\n\n/**\n * Hook for API health check\n */\nexport function useApiHealth() {\n  return useBackgroundSyncQuery(\n    queryKeys.health.api(),\n    async (): Promise<HealthQueries.HealthResponse> => {\n      const response = await fetch('/api/health');\n\n      if (!response.ok) {\n        throw new Error(`Health check failed: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 30 * 1000, // 30 seconds\n      refetchInterval: 60 * 1000, // Refetch every minute\n      retry: 3,\n      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),\n    }\n  );\n}\n\n/**\n * Hook for database health check\n */\nexport function useDatabaseHealth() {\n  return useBaseQuery(\n    [...queryKeys.health.all, 'database'],\n    async (): Promise<{ status: 'up' | 'down'; responseTime: number }> => {\n      const startTime = performance.now();\n      const response = await fetch('/api/health/database');\n      const endTime = performance.now();\n\n      if (!response.ok) {\n        throw new Error(`Database health check failed: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      return {\n        ...data,\n        responseTime: endTime - startTime,\n      };\n    },\n    {\n      staleTime: 30 * 1000, // 30 seconds\n      retry: 2,\n    }\n  );\n}\n\n/**\n * Hook for external API health check\n */\nexport function useExternalApiHealth() {\n  return useBaseQuery(\n    [...queryKeys.health.all, 'external-api'],\n    async (): Promise<{ status: 'up' | 'down'; responseTime: number }> => {\n      const startTime = performance.now();\n      const response = await fetch('/api/health/external-api');\n      const endTime = performance.now();\n\n      if (!response.ok) {\n        throw new Error(`External API health check failed: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      return {\n        ...data,\n        responseTime: endTime - startTime,\n      };\n    },\n    {\n      staleTime: 60 * 1000, // 1 minute\n      retry: 2,\n    }\n  );\n}\n\n/**\n * Hook for comprehensive system health\n */\nexport function useSystemHealth() {\n  const apiHealth = useApiHealth();\n  const dbHealth = useDatabaseHealth();\n  const externalApiHealth = useExternalApiHealth();\n\n  const isLoading = apiHealth.isLoading || dbHealth.isLoading || externalApiHealth.isLoading;\n  const hasErrors = apiHealth.isError || dbHealth.isError || externalApiHealth.isError;\n\n  // Calculate overall health status\n  const getOverallStatus = (): 'healthy' | 'degraded' | 'unhealthy' => {\n    if (hasErrors) return 'unhealthy';\n    \n    const apiStatus = apiHealth.data?.status;\n    const dbStatus = dbHealth.data?.status;\n    const externalStatus = externalApiHealth.data?.status;\n\n    if (apiStatus === 'healthy' && dbStatus === 'up' && externalStatus === 'up') {\n      return 'healthy';\n    }\n    \n    if (apiStatus === 'unhealthy' || dbStatus === 'down') {\n      return 'unhealthy';\n    }\n    \n    return 'degraded';\n  };\n\n  // Calculate average response time\n  const getAverageResponseTime = (): number => {\n    const times = [\n      dbHealth.data?.responseTime,\n      externalApiHealth.data?.responseTime,\n    ].filter((time): time is number => typeof time === 'number');\n\n    if (times.length === 0) return 0;\n    return times.reduce((sum, time) => sum + time, 0) / times.length;\n  };\n\n  return {\n    // Individual health checks\n    api: apiHealth,\n    database: dbHealth,\n    externalApi: externalApiHealth,\n    \n    // Overall status\n    isLoading,\n    hasErrors,\n    overallStatus: getOverallStatus(),\n    averageResponseTime: getAverageResponseTime(),\n    \n    // Health data\n    healthData: {\n      api: apiHealth.data,\n      database: dbHealth.data,\n      externalApi: externalApiHealth.data,\n    },\n    \n    // Error information\n    errors: {\n      api: apiHealth.error,\n      database: dbHealth.error,\n      externalApi: externalApiHealth.error,\n    },\n    \n    // Refetch functions\n    refetchAll: () => {\n      apiHealth.refetch();\n      dbHealth.refetch();\n      externalApiHealth.refetch();\n    },\n  };\n}\n\n/**\n * Hook for monitoring API performance\n */\nexport function useApiPerformance() {\n  const systemHealth = useSystemHealth();\n\n  const getPerformanceMetrics = () => {\n    const { healthData } = systemHealth;\n    \n    return {\n      uptime: healthData.api?.uptime || 0,\n      responseTime: systemHealth.averageResponseTime,\n      status: systemHealth.overallStatus,\n      services: {\n        database: healthData.database?.status || 'unknown',\n        externalApi: healthData.externalApi?.status || 'unknown',\n      },\n      lastCheck: new Date().toISOString(),\n    };\n  };\n\n  return {\n    ...systemHealth,\n    performanceMetrics: getPerformanceMetrics(),\n    \n    // Performance indicators\n    isPerformanceGood: systemHealth.averageResponseTime < 1000, // Less than 1 second\n    isPerformanceFair: systemHealth.averageResponseTime < 3000, // Less than 3 seconds\n    isPerformancePoor: systemHealth.averageResponseTime >= 3000, // 3+ seconds\n  };\n}\n\n/**\n * Hook for health monitoring dashboard\n */\nexport function useHealthDashboard() {\n  const performance = useApiPerformance();\n  \n  const getDashboardData = () => {\n    const { healthData, overallStatus, averageResponseTime } = performance;\n    \n    return {\n      status: overallStatus,\n      uptime: healthData.api?.uptime || 0,\n      version: healthData.api?.version || 'unknown',\n      responseTime: averageResponseTime,\n      services: [\n        {\n          name: 'Database',\n          status: healthData.database?.status || 'unknown',\n          responseTime: healthData.database?.responseTime || 0,\n        },\n        {\n          name: 'External API',\n          status: healthData.externalApi?.status || 'unknown',\n          responseTime: healthData.externalApi?.responseTime || 0,\n        },\n      ],\n      lastUpdated: new Date().toISOString(),\n    };\n  };\n\n  return {\n    ...performance,\n    dashboardData: getDashboardData(),\n    \n    // Dashboard actions\n    refreshDashboard: performance.refetchAll,\n    \n    // Status indicators\n    statusColor: {\n      healthy: '#10b981', // green\n      degraded: '#f59e0b', // yellow\n      unhealthy: '#ef4444', // red\n    }[performance.overallStatus],\n    \n    statusIcon: {\n      healthy: '✅',\n      degraded: '⚠️',\n      unhealthy: '❌',\n    }[performance.overallStatus],\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAID;AAEA;;AAJA;;;AASO,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAC1B,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,GAAG;+CACpB;YACE,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,SAAS,UAAU,EAAE;YAC/D;YAEA,OAAO,SAAS,IAAI;QACtB;8CACA;QACE,WAAW,KAAK;QAChB,iBAAiB,KAAK;QACtB,OAAO;QACP,UAAU;mDAAE,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;;IACnE;AAEJ;GAnBgB;;QACP,uIAAA,CAAA,yBAAsB;;;AAuBxB,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,GAAG;QAAE;KAAW;0CACrC;YACE,MAAM,YAAY,YAAY,GAAG;YACjC,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,UAAU,YAAY,GAAG;YAE/B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,SAAS,UAAU,EAAE;YACxE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBACL,GAAG,IAAI;gBACP,cAAc,UAAU;YAC1B;QACF;yCACA;QACE,WAAW,KAAK;QAChB,OAAO;IACT;AAEJ;IAvBgB;;QACP,uIAAA,CAAA,eAAY;;;AA2Bd,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,GAAG;QAAE;KAAe;6CACzC;YACE,MAAM,YAAY,YAAY,GAAG;YACjC,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,UAAU,YAAY,GAAG;YAE/B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,SAAS,UAAU,EAAE;YAC5E;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBACL,GAAG,IAAI;gBACP,cAAc,UAAU;YAC1B;QACF;4CACA;QACE,WAAW,KAAK;QAChB,OAAO;IACT;AAEJ;IAvBgB;;QACP,uIAAA,CAAA,eAAY;;;AA2Bd,SAAS;;IACd,MAAM,YAAY;IAClB,MAAM,WAAW;IACjB,MAAM,oBAAoB;IAE1B,MAAM,YAAY,UAAU,SAAS,IAAI,SAAS,SAAS,IAAI,kBAAkB,SAAS;IAC1F,MAAM,YAAY,UAAU,OAAO,IAAI,SAAS,OAAO,IAAI,kBAAkB,OAAO;IAEpF,kCAAkC;IAClC,MAAM,mBAAmB;QACvB,IAAI,WAAW,OAAO;QAEtB,MAAM,YAAY,UAAU,IAAI,EAAE;QAClC,MAAM,WAAW,SAAS,IAAI,EAAE;QAChC,MAAM,iBAAiB,kBAAkB,IAAI,EAAE;QAE/C,IAAI,cAAc,aAAa,aAAa,QAAQ,mBAAmB,MAAM;YAC3E,OAAO;QACT;QAEA,IAAI,cAAc,eAAe,aAAa,QAAQ;YACpD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,kCAAkC;IAClC,MAAM,yBAAyB;QAC7B,MAAM,QAAQ;YACZ,SAAS,IAAI,EAAE;YACf,kBAAkB,IAAI,EAAE;SACzB,CAAC,MAAM,CAAC,CAAC,OAAyB,OAAO,SAAS;QAEnD,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;QAC/B,OAAO,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,MAAM,KAAK,MAAM,MAAM;IAClE;IAEA,OAAO;QACL,2BAA2B;QAC3B,KAAK;QACL,UAAU;QACV,aAAa;QAEb,iBAAiB;QACjB;QACA;QACA,eAAe;QACf,qBAAqB;QAErB,cAAc;QACd,YAAY;YACV,KAAK,UAAU,IAAI;YACnB,UAAU,SAAS,IAAI;YACvB,aAAa,kBAAkB,IAAI;QACrC;QAEA,oBAAoB;QACpB,QAAQ;YACN,KAAK,UAAU,KAAK;YACpB,UAAU,SAAS,KAAK;YACxB,aAAa,kBAAkB,KAAK;QACtC;QAEA,oBAAoB;QACpB,YAAY;YACV,UAAU,OAAO;YACjB,SAAS,OAAO;YAChB,kBAAkB,OAAO;QAC3B;IACF;AACF;IAvEgB;;QACI;QACD;QACS;;;AAyErB,SAAS;;IACd,MAAM,eAAe;IAErB,MAAM,wBAAwB;QAC5B,MAAM,EAAE,UAAU,EAAE,GAAG;QAEvB,OAAO;YACL,QAAQ,WAAW,GAAG,EAAE,UAAU;YAClC,cAAc,aAAa,mBAAmB;YAC9C,QAAQ,aAAa,aAAa;YAClC,UAAU;gBACR,UAAU,WAAW,QAAQ,EAAE,UAAU;gBACzC,aAAa,WAAW,WAAW,EAAE,UAAU;YACjD;YACA,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;IAEA,OAAO;QACL,GAAG,YAAY;QACf,oBAAoB;QAEpB,yBAAyB;QACzB,mBAAmB,aAAa,mBAAmB,GAAG;QACtD,mBAAmB,aAAa,mBAAmB,GAAG;QACtD,mBAAmB,aAAa,mBAAmB,IAAI;IACzD;AACF;IA3BgB;;QACO;;;AA+BhB,SAAS;;IACd,MAAM,eAAc;IAEpB,MAAM,mBAAmB;QACvB,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,mBAAmB,EAAE,GAAG;QAE3D,OAAO;YACL,QAAQ;YACR,QAAQ,WAAW,GAAG,EAAE,UAAU;YAClC,SAAS,WAAW,GAAG,EAAE,WAAW;YACpC,cAAc;YACd,UAAU;gBACR;oBACE,MAAM;oBACN,QAAQ,WAAW,QAAQ,EAAE,UAAU;oBACvC,cAAc,WAAW,QAAQ,EAAE,gBAAgB;gBACrD;gBACA;oBACE,MAAM;oBACN,QAAQ,WAAW,WAAW,EAAE,UAAU;oBAC1C,cAAc,WAAW,WAAW,EAAE,gBAAgB;gBACxD;aACD;YACD,aAAa,IAAI,OAAO,WAAW;QACrC;IACF;IAEA,OAAO;QACL,GAAG,YAAW;QACd,eAAe;QAEf,oBAAoB;QACpB,kBAAkB,aAAY,UAAU;QAExC,oBAAoB;QACpB,aAAa,CAAA;YACX,SAAS;YACT,UAAU;YACV,WAAW;QACb,CAAA,CAAC,CAAC,aAAY,aAAa,CAAC;QAE5B,YAAY,CAAA;YACV,SAAS;YACT,UAAU;YACV,WAAW;QACb,CAAA,CAAC,CAAC,aAAY,aAAa,CAAC;IAC9B;AACF;IA/CgB;;QACM"}}, {"offset": {"line": 2614, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2620, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/index.ts"], "sourcesContent": ["/**\n * API Hooks Index\n * Central export for all API hooks\n */\n\n// Base hooks and utilities\nexport * from './base-hooks';\n\n// Domain-specific hooks\nexport * from './auth-hooks';\nexport * from './football-hooks';\nexport * from './users';\nexport * from './broadcast-hooks';\nexport * from './health-hooks';\n\n// Re-export TanStack Query hooks for convenience\nexport {\n  useQuery,\n  useMutation,\n  useQueryClient,\n  useInfiniteQuery,\n} from '@tanstack/react-query';\n\n/**\n * API hooks library metadata\n */\nexport const API_HOOKS_VERSION = '1.0.0';\nexport const API_HOOKS_NAME = 'APISportsGame API Hooks';\n\n/**\n * Quick setup function for API hooks\n */\nexport function setupApiHooks() {\n  console.log(`${API_HOOKS_NAME} v${API_HOOKS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,2BAA2B;;;;;;;;;;;;;AAqBpB,MAAM,oBAAoB;AAC1B,MAAM,iBAAiB;AAKvB,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,eAAe,EAAE,EAAE,kBAAkB,YAAY,CAAC;AACnE"}}, {"offset": {"line": 2644, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2664, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/api-hooks-demo/page.tsx"], "sourcesContent": ["/**\n * API Hooks Demo Page - Test page for API Hooks functionality\n * Demonstrates usage of various API hooks\n */\n\n'use client';\n\nimport React from 'react';\nimport { \n  useApiHealth, \n  useSystemHealth,\n  useHealthDashboard,\n  useAuth,\n  useApiHookUtils,\n  useApiStatus \n} from '@/hooks/api';\n\nexport default function ApiHooksDemoPage() {\n  const apiHealth = useApiHealth();\n  const systemHealth = useSystemHealth();\n  const healthDashboard = useHealthDashboard();\n  const auth = useAuth();\n  const apiUtils = useApiHookUtils();\n  const apiStatus = useApiStatus();\n\n  const handleTestLogin = () => {\n    auth.loginUser({\n      username: 'admin',\n      password: 'admin123456'\n    });\n  };\n\n  const handleRefreshHealth = () => {\n    healthDashboard.refreshDashboard();\n  };\n\n  const handleClearErrors = () => {\n    apiStatus.clearAllErrors();\n  };\n\n  return (\n    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>\n      <h1>API Hooks Demo</h1>\n      \n      {/* API Status Overview */}\n      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>API Status Overview</h2>\n        <p>Total Queries: <strong>{apiStatus.getApiStatus().total}</strong></p>\n        <p>Loading Queries: <strong>{apiStatus.getApiStatus().loading}</strong></p>\n        <p>Error Queries: <strong>{apiStatus.getApiStatus().error}</strong></p>\n        <p>Success Queries: <strong>{apiStatus.getApiStatus().success}</strong></p>\n        <p>Health Score: <strong>{apiStatus.getApiStatus().healthScore.toFixed(1)}%</strong></p>\n        <p>Is Loading: <strong>{apiStatus.getApiStatus().isLoading ? 'Yes' : 'No'}</strong></p>\n        <p>Has Errors: <strong>{apiStatus.getApiStatus().hasErrors ? 'Yes' : 'No'}</strong></p>\n        \n        <button \n          onClick={handleClearErrors}\n          style={{ marginTop: '10px', padding: '5px 10px' }}\n        >\n          Clear All Errors\n        </button>\n      </div>\n\n      {/* Health Dashboard */}\n      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>Health Dashboard</h2>\n        <p>Overall Status: <strong style={{ color: healthDashboard.statusColor }}>\n          {healthDashboard.statusIcon} {healthDashboard.overallStatus}\n        </strong></p>\n        <p>Average Response Time: <strong>{healthDashboard.averageResponseTime.toFixed(2)}ms</strong></p>\n        <p>Performance: <strong>\n          {healthDashboard.isPerformanceGood ? 'Good' : \n           healthDashboard.isPerformanceFair ? 'Fair' : 'Poor'}\n        </strong></p>\n        \n        <h3>Services Status</h3>\n        {healthDashboard.dashboardData.services.map((service, index) => (\n          <div key={index} style={{ marginLeft: '20px' }}>\n            <p>{service.name}: <strong>{service.status}</strong> ({service.responseTime.toFixed(2)}ms)</p>\n          </div>\n        ))}\n        \n        <button \n          onClick={handleRefreshHealth}\n          disabled={systemHealth.isLoading}\n          style={{ marginTop: '10px', padding: '5px 10px' }}\n        >\n          {systemHealth.isLoading ? 'Refreshing...' : 'Refresh Health'}\n        </button>\n      </div>\n\n      {/* API Health Details */}\n      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>API Health Details</h2>\n        <p>Status: <strong>{apiHealth.data?.status || 'Unknown'}</strong></p>\n        <p>Loading: <strong>{apiHealth.isLoading ? 'Yes' : 'No'}</strong></p>\n        <p>Error: <strong>{apiHealth.error ? String(apiHealth.error) : 'None'}</strong></p>\n        <p>Last Updated: <strong>\n          {apiHealth.dataUpdatedAt ? new Date(apiHealth.dataUpdatedAt).toLocaleTimeString() : 'Never'}\n        </strong></p>\n        \n        {apiHealth.data && (\n          <details style={{ marginTop: '10px' }}>\n            <summary>Health Data</summary>\n            <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>\n              {JSON.stringify(apiHealth.data, null, 2)}\n            </pre>\n          </details>\n        )}\n      </div>\n\n      {/* Authentication Demo */}\n      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>Authentication Demo</h2>\n        <p>Authenticated: <strong>{auth.isAuthenticated ? 'Yes' : 'No'}</strong></p>\n        <p>User: <strong>{auth.user ? `${auth.user.username} (${auth.user.role})` : 'None'}</strong></p>\n        <p>Loading: <strong>{auth.isLoading ? 'Yes' : 'No'}</strong></p>\n        <p>Error: <strong>{auth.error ? String(auth.error) : 'None'}</strong></p>\n        \n        <div style={{ marginTop: '10px' }}>\n          <button \n            onClick={handleTestLogin}\n            disabled={auth.isLoading || auth.isAuthenticated}\n            style={{ marginRight: '10px', padding: '5px 10px' }}\n          >\n            Test Login\n          </button>\n          <button \n            onClick={() => auth.logoutUser()}\n            disabled={auth.isLoading || !auth.isAuthenticated}\n            style={{ padding: '5px 10px' }}\n          >\n            Logout\n          </button>\n        </div>\n      </div>\n\n      {/* System Health Summary */}\n      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>System Health Summary</h2>\n        <p>Overall Status: <strong>{systemHealth.overallStatus}</strong></p>\n        <p>Is Loading: <strong>{systemHealth.isLoading ? 'Yes' : 'No'}</strong></p>\n        <p>Has Errors: <strong>{systemHealth.hasErrors ? 'Yes' : 'No'}</strong></p>\n        <p>Average Response Time: <strong>{systemHealth.averageResponseTime.toFixed(2)}ms</strong></p>\n        \n        <h3>Individual Services</h3>\n        <div style={{ marginLeft: '20px' }}>\n          <p>API: <strong>{systemHealth.api.data?.status || 'Unknown'}</strong></p>\n          <p>Database: <strong>{systemHealth.database.data?.status || 'Unknown'}</strong></p>\n          <p>External API: <strong>{systemHealth.externalApi.data?.status || 'Unknown'}</strong></p>\n        </div>\n      </div>\n\n      {/* Development Info */}\n      {process.env.NODE_ENV === 'development' && (\n        <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n          <h2>Development Info</h2>\n          <p>Environment: <strong>{process.env.NODE_ENV}</strong></p>\n          <p>API Hooks: <strong>Available</strong></p>\n          <p>Query Client: <strong>Connected</strong></p>\n          <p>Real-time Updates: <strong>Active</strong></p>\n        </div>\n      )}\n\n      {/* Navigation */}\n      <div style={{ marginTop: '30px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>Navigation</h2>\n        <p>This demo page shows the API Hooks functionality.</p>\n        <div style={{ display: 'flex', gap: '10px' }}>\n          <a href=\"/\" style={{ color: 'blue', textDecoration: 'underline' }}>\n            ← Back to Home\n          </a>\n          <a href=\"/simple-query-demo\" style={{ color: 'blue', textDecoration: 'underline' }}>\n            Simple Query Demo\n          </a>\n          <a href=\"/store-demo\" style={{ color: 'blue', textDecoration: 'underline' }}>\n            Store Demo\n          </a>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAKD;AAAA;AAAA;AAAA;AAkJO;;;AArJP;;AAYe,SAAS;;IACtB,MAAM,YAAY,CAAA,GAAA,yIAAA,CAAA,eAAY,AAAD;IAC7B,MAAM,eAAe,CAAA,GAAA,yIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,kBAAkB,CAAA,GAAA,yIAAA,CAAA,qBAAkB,AAAD;IACzC,MAAM,OAAO,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD;IACnB,MAAM,WAAW,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAC/B,MAAM,YAAY,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD;IAE7B,MAAM,kBAAkB;QACtB,KAAK,SAAS,CAAC;YACb,UAAU;YACV,UAAU;QACZ;IACF;IAEA,MAAM,sBAAsB;QAC1B,gBAAgB,gBAAgB;IAClC;IAEA,MAAM,oBAAoB;QACxB,UAAU,cAAc;IAC1B;IAEA,qBACE,6LAAC;QAAI,OAAO;YAAE,SAAS;YAAQ,YAAY;QAAoB;;0BAC7D,6LAAC;0BAAG;;;;;;0BAGJ,6LAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCACjG,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;;4BAAE;0CAAe,6LAAC;0CAAQ,UAAU,YAAY,GAAG,KAAK;;;;;;;;;;;;kCACzD,6LAAC;;4BAAE;0CAAiB,6LAAC;0CAAQ,UAAU,YAAY,GAAG,OAAO;;;;;;;;;;;;kCAC7D,6LAAC;;4BAAE;0CAAe,6LAAC;0CAAQ,UAAU,YAAY,GAAG,KAAK;;;;;;;;;;;;kCACzD,6LAAC;;4BAAE;0CAAiB,6LAAC;0CAAQ,UAAU,YAAY,GAAG,OAAO;;;;;;;;;;;;kCAC7D,6LAAC;;4BAAE;0CAAc,6LAAC;;oCAAQ,UAAU,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC;oCAAG;;;;;;;;;;;;;kCAC1E,6LAAC;;4BAAE;0CAAY,6LAAC;0CAAQ,UAAU,YAAY,GAAG,SAAS,GAAG,QAAQ;;;;;;;;;;;;kCACrE,6LAAC;;4BAAE;0CAAY,6LAAC;0CAAQ,UAAU,YAAY,GAAG,SAAS,GAAG,QAAQ;;;;;;;;;;;;kCAErE,6LAAC;wBACC,SAAS;wBACT,OAAO;4BAAE,WAAW;4BAAQ,SAAS;wBAAW;kCACjD;;;;;;;;;;;;0BAMH,6LAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCACjG,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;;4BAAE;0CAAgB,6LAAC;gCAAO,OAAO;oCAAE,OAAO,gBAAgB,WAAW;gCAAC;;oCACpE,gBAAgB,UAAU;oCAAC;oCAAE,gBAAgB,aAAa;;;;;;;;;;;;;kCAE7D,6LAAC;;4BAAE;0CAAuB,6LAAC;;oCAAQ,gBAAgB,mBAAmB,CAAC,OAAO,CAAC;oCAAG;;;;;;;;;;;;;kCAClF,6LAAC;;4BAAE;0CAAa,6LAAC;0CACd,gBAAgB,iBAAiB,GAAG,SACpC,gBAAgB,iBAAiB,GAAG,SAAS;;;;;;;;;;;;kCAGhD,6LAAC;kCAAG;;;;;;oBACH,gBAAgB,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACpD,6LAAC;4BAAgB,OAAO;gCAAE,YAAY;4BAAO;sCAC3C,cAAA,6LAAC;;oCAAG,QAAQ,IAAI;oCAAC;kDAAE,6LAAC;kDAAQ,QAAQ,MAAM;;;;;;oCAAU;oCAAG,QAAQ,YAAY,CAAC,OAAO,CAAC;oCAAG;;;;;;;2BAD/E;;;;;kCAKZ,6LAAC;wBACC,SAAS;wBACT,UAAU,aAAa,SAAS;wBAChC,OAAO;4BAAE,WAAW;4BAAQ,SAAS;wBAAW;kCAE/C,aAAa,SAAS,GAAG,kBAAkB;;;;;;;;;;;;0BAKhD,6LAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCACjG,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;;4BAAE;0CAAQ,6LAAC;0CAAQ,UAAU,IAAI,EAAE,UAAU;;;;;;;;;;;;kCAC9C,6LAAC;;4BAAE;0CAAS,6LAAC;0CAAQ,UAAU,SAAS,GAAG,QAAQ;;;;;;;;;;;;kCACnD,6LAAC;;4BAAE;0CAAO,6LAAC;0CAAQ,UAAU,KAAK,GAAG,OAAO,UAAU,KAAK,IAAI;;;;;;;;;;;;kCAC/D,6LAAC;;4BAAE;0CAAc,6LAAC;0CACf,UAAU,aAAa,GAAG,IAAI,KAAK,UAAU,aAAa,EAAE,kBAAkB,KAAK;;;;;;;;;;;;oBAGrF,UAAU,IAAI,kBACb,6LAAC;wBAAQ,OAAO;4BAAE,WAAW;wBAAO;;0CAClC,6LAAC;0CAAQ;;;;;;0CACT,6LAAC;gCAAI,OAAO;oCAAE,YAAY;oCAAW,SAAS;oCAAQ,UAAU;gCAAO;0CACpE,KAAK,SAAS,CAAC,UAAU,IAAI,EAAE,MAAM;;;;;;;;;;;;;;;;;;0BAO9C,6LAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCACjG,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;;4BAAE;0CAAe,6LAAC;0CAAQ,KAAK,eAAe,GAAG,QAAQ;;;;;;;;;;;;kCAC1D,6LAAC;;4BAAE;0CAAM,6LAAC;0CAAQ,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG;;;;;;;;;;;;kCAC5E,6LAAC;;4BAAE;0CAAS,6LAAC;0CAAQ,KAAK,SAAS,GAAG,QAAQ;;;;;;;;;;;;kCAC9C,6LAAC;;4BAAE;0CAAO,6LAAC;0CAAQ,KAAK,KAAK,GAAG,OAAO,KAAK,KAAK,IAAI;;;;;;;;;;;;kCAErD,6LAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAO;;0CAC9B,6LAAC;gCACC,SAAS;gCACT,UAAU,KAAK,SAAS,IAAI,KAAK,eAAe;gCAChD,OAAO;oCAAE,aAAa;oCAAQ,SAAS;gCAAW;0CACnD;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,KAAK,UAAU;gCAC9B,UAAU,KAAK,SAAS,IAAI,CAAC,KAAK,eAAe;gCACjD,OAAO;oCAAE,SAAS;gCAAW;0CAC9B;;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCACjG,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;;4BAAE;0CAAgB,6LAAC;0CAAQ,aAAa,aAAa;;;;;;;;;;;;kCACtD,6LAAC;;4BAAE;0CAAY,6LAAC;0CAAQ,aAAa,SAAS,GAAG,QAAQ;;;;;;;;;;;;kCACzD,6LAAC;;4BAAE;0CAAY,6LAAC;0CAAQ,aAAa,SAAS,GAAG,QAAQ;;;;;;;;;;;;kCACzD,6LAAC;;4BAAE;0CAAuB,6LAAC;;oCAAQ,aAAa,mBAAmB,CAAC,OAAO,CAAC;oCAAG;;;;;;;;;;;;;kCAE/E,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;wBAAI,OAAO;4BAAE,YAAY;wBAAO;;0CAC/B,6LAAC;;oCAAE;kDAAK,6LAAC;kDAAQ,aAAa,GAAG,CAAC,IAAI,EAAE,UAAU;;;;;;;;;;;;0CAClD,6LAAC;;oCAAE;kDAAU,6LAAC;kDAAQ,aAAa,QAAQ,CAAC,IAAI,EAAE,UAAU;;;;;;;;;;;;0CAC5D,6LAAC;;oCAAE;kDAAc,6LAAC;kDAAQ,aAAa,WAAW,CAAC,IAAI,EAAE,UAAU;;;;;;;;;;;;;;;;;;;;;;;;YAKtE,oDAAyB,+BACxB,6LAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCACjG,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;;4BAAE;0CAAa,6LAAC;;;;;;;;;;;;;kCACjB,6LAAC;;4BAAE;0CAAW,6LAAC;0CAAO;;;;;;;;;;;;kCACtB,6LAAC;;4BAAE;0CAAc,6LAAC;0CAAO;;;;;;;;;;;;kCACzB,6LAAC;;4BAAE;0CAAmB,6LAAC;0CAAO;;;;;;;;;;;;;;;;;;0BAKlC,6LAAC;gBAAI,OAAO;oBAAE,WAAW;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCAC9F,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;kCAAE;;;;;;kCACH,6LAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,KAAK;wBAAO;;0CACzC,6LAAC;gCAAE,MAAK;gCAAI,OAAO;oCAAE,OAAO;oCAAQ,gBAAgB;gCAAY;0CAAG;;;;;;0CAGnE,6LAAC;gCAAE,MAAK;gCAAqB,OAAO;oCAAE,OAAO;oCAAQ,gBAAgB;gCAAY;0CAAG;;;;;;0CAGpF,6LAAC;gCAAE,MAAK;gCAAc,OAAO;oCAAE,OAAO;oCAAQ,gBAAgB;gCAAY;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAOvF;GArKwB;;QACJ,yIAAA,CAAA,eAAY;QACT,yIAAA,CAAA,kBAAe;QACZ,yIAAA,CAAA,qBAAkB;QAC7B,uIAAA,CAAA,UAAO;QACH,uIAAA,CAAA,kBAAe;QACd,uIAAA,CAAA,eAAY;;;KANR"}}, {"offset": {"line": 3563, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}