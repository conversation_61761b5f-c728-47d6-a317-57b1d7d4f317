{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/ui/button.tsx"], "sourcesContent": ["/**\n * Enhanced Button Component\n * Extended Ant Design Button with theme integration and additional variants\n */\n\n'use client';\n\nimport React from 'react';\nimport { Button as AntButton, ButtonProps as AntButtonProps } from 'antd';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Extended button props\n */\nexport interface ButtonProps extends AntButtonProps {\n  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost';\n  fullWidth?: boolean;\n  compact?: boolean;\n}\n\n/**\n * Enhanced Button component with theme integration\n */\nexport function Button({ \n  variant = 'primary',\n  fullWidth = false,\n  compact = false,\n  className,\n  style,\n  children,\n  ...props \n}: ButtonProps) {\n  const themeStyles = useThemeStyles();\n\n  // Get variant-specific styles\n  const getVariantStyle = () => {\n    const baseStyle = {\n      transition: 'all 0.2s ease',\n      ...(fullWidth && { width: '100%' }),\n      ...(compact && { \n        height: '32px', \n        padding: '0 12px',\n        fontSize: '12px'\n      }),\n    };\n\n    switch (variant) {\n      case 'secondary':\n        return {\n          ...baseStyle,\n          backgroundColor: 'transparent',\n          borderColor: themeStyles.getBorderColor('primary'),\n          color: themeStyles.getTextColor('primary'),\n        };\n      case 'success':\n        return {\n          ...baseStyle,\n          backgroundColor: themeStyles.getColor('success'),\n          borderColor: themeStyles.getColor('success'),\n          color: 'white',\n        };\n      case 'warning':\n        return {\n          ...baseStyle,\n          backgroundColor: themeStyles.getColor('warning'),\n          borderColor: themeStyles.getColor('warning'),\n          color: 'white',\n        };\n      case 'error':\n        return {\n          ...baseStyle,\n          backgroundColor: themeStyles.getColor('error'),\n          borderColor: themeStyles.getColor('error'),\n          color: 'white',\n        };\n      case 'ghost':\n        return {\n          ...baseStyle,\n          backgroundColor: 'transparent',\n          borderColor: 'transparent',\n          color: themeStyles.getTextColor('secondary'),\n        };\n      default:\n        return baseStyle;\n    }\n  };\n\n  // Map variant to Ant Design type\n  const getAntType = (): AntButtonProps['type'] => {\n    switch (variant) {\n      case 'primary':\n        return 'primary';\n      case 'secondary':\n        return 'default';\n      case 'ghost':\n        return 'text';\n      default:\n        return 'primary';\n    }\n  };\n\n  return (\n    <AntButton\n      type={getAntType()}\n      className={className}\n      style={{\n        ...getVariantStyle(),\n        ...style,\n      }}\n      {...props}\n    >\n      {children}\n    </AntButton>\n  );\n}\n\n/**\n * Button group component\n */\nexport interface ButtonGroupProps {\n  children: React.ReactNode;\n  direction?: 'horizontal' | 'vertical';\n  spacing?: number;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function ButtonGroup({ \n  children, \n  direction = 'horizontal',\n  spacing = 8,\n  className,\n  style \n}: ButtonGroupProps) {\n  const groupStyle: React.CSSProperties = {\n    display: 'flex',\n    flexDirection: direction === 'vertical' ? 'column' : 'row',\n    gap: `${spacing}px`,\n    ...style,\n  };\n\n  return (\n    <div className={className} style={groupStyle}>\n      {children}\n    </div>\n  );\n}\n\n/**\n * Icon button component\n */\nexport interface IconButtonProps extends Omit<ButtonProps, 'children'> {\n  icon: React.ReactNode;\n  tooltip?: string;\n  size?: 'small' | 'medium' | 'large';\n}\n\nexport function IconButton({ \n  icon, \n  tooltip, \n  size = 'medium',\n  ...props \n}: IconButtonProps) {\n  const sizeMap = {\n    small: { width: '32px', height: '32px', padding: '0' },\n    medium: { width: '40px', height: '40px', padding: '0' },\n    large: { width: '48px', height: '48px', padding: '0' },\n  };\n\n  const buttonElement = (\n    <Button\n      style={{\n        ...sizeMap[size],\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n      }}\n      {...props}\n    >\n      {icon}\n    </Button>\n  );\n\n  if (tooltip) {\n    const { Tooltip } = require('antd');\n    return <Tooltip title={tooltip}>{buttonElement}</Tooltip>;\n  }\n\n  return buttonElement;\n}\n\n/**\n * Loading button component\n */\nexport interface LoadingButtonProps extends ButtonProps {\n  isLoading?: boolean;\n  loadingText?: string;\n}\n\nexport function LoadingButton({ \n  isLoading = false,\n  loadingText = 'Loading...',\n  children,\n  disabled,\n  ...props \n}: LoadingButtonProps) {\n  return (\n    <Button\n      loading={isLoading}\n      disabled={disabled || isLoading}\n      {...props}\n    >\n      {isLoading ? loadingText : children}\n    </Button>\n  );\n}\n\n/**\n * Confirm button component\n */\nexport interface ConfirmButtonProps extends ButtonProps {\n  onConfirm: () => void;\n  confirmTitle?: string;\n  confirmDescription?: string;\n  okText?: string;\n  cancelText?: string;\n}\n\nexport function ConfirmButton({\n  onConfirm,\n  confirmTitle = 'Are you sure?',\n  confirmDescription = 'This action cannot be undone.',\n  okText = 'Yes',\n  cancelText = 'No',\n  children,\n  ...props\n}: ConfirmButtonProps) {\n  const { Popconfirm } = require('antd');\n\n  return (\n    <Popconfirm\n      title={confirmTitle}\n      description={confirmDescription}\n      onConfirm={onConfirm}\n      okText={okText}\n      cancelText={cancelText}\n    >\n      <Button {...props}>\n        {children}\n      </Button>\n    </Popconfirm>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAMD;AAAA;AADA;;;AAHA;;;AAkBO,SAAS,OAAO,EACrB,UAAU,SAAS,EACnB,YAAY,KAAK,EACjB,UAAU,KAAK,EACf,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OACS;;IACZ,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,8BAA8B;IAC9B,MAAM,kBAAkB;QACtB,MAAM,YAAY;YAChB,YAAY;YACZ,GAAI,aAAa;gBAAE,OAAO;YAAO,CAAC;YAClC,GAAI,WAAW;gBACb,QAAQ;gBACR,SAAS;gBACT,UAAU;YACZ,CAAC;QACH;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,iBAAiB;oBACjB,aAAa,YAAY,cAAc,CAAC;oBACxC,OAAO,YAAY,YAAY,CAAC;gBAClC;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,iBAAiB,YAAY,QAAQ,CAAC;oBACtC,aAAa,YAAY,QAAQ,CAAC;oBAClC,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,iBAAiB,YAAY,QAAQ,CAAC;oBACtC,aAAa,YAAY,QAAQ,CAAC;oBAClC,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,iBAAiB,YAAY,QAAQ,CAAC;oBACtC,aAAa,YAAY,QAAQ,CAAC;oBAClC,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,iBAAiB;oBACjB,aAAa;oBACb,OAAO,YAAY,YAAY,CAAC;gBAClC;YACF;gBACE,OAAO;QACX;IACF;IAEA,iCAAiC;IACjC,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,qMAAA,CAAA,SAAS;QACR,MAAM;QACN,WAAW;QACX,OAAO;YACL,GAAG,iBAAiB;YACpB,GAAG,KAAK;QACV;QACC,GAAG,KAAK;kBAER;;;;;;AAGP;GA3FgB;;QASM,wHAAA,CAAA,iBAAc;;;KATpB;AAwGT,SAAS,YAAY,EAC1B,QAAQ,EACR,YAAY,YAAY,EACxB,UAAU,CAAC,EACX,SAAS,EACT,KAAK,EACY;IACjB,MAAM,aAAkC;QACtC,SAAS;QACT,eAAe,cAAc,aAAa,WAAW;QACrD,KAAK,GAAG,QAAQ,EAAE,CAAC;QACnB,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;kBAC/B;;;;;;AAGP;MAnBgB;AA8BT,SAAS,WAAW,EACzB,IAAI,EACJ,OAAO,EACP,OAAO,QAAQ,EACf,GAAG,OACa;IAChB,MAAM,UAAU;QACd,OAAO;YAAE,OAAO;YAAQ,QAAQ;YAAQ,SAAS;QAAI;QACrD,QAAQ;YAAE,OAAO;YAAQ,QAAQ;YAAQ,SAAS;QAAI;QACtD,OAAO;YAAE,OAAO;YAAQ,QAAQ;YAAQ,SAAS;QAAI;IACvD;IAEA,MAAM,8BACJ,6LAAC;QACC,OAAO;YACL,GAAG,OAAO,CAAC,KAAK;YAChB,SAAS;YACT,YAAY;YACZ,gBAAgB;QAClB;QACC,GAAG,KAAK;kBAER;;;;;;IAIL,IAAI,SAAS;QACX,MAAM,EAAE,OAAO,EAAE;QACjB,qBAAO,6LAAC;YAAQ,OAAO;sBAAU;;;;;;IACnC;IAEA,OAAO;AACT;MAhCgB;AA0CT,SAAS,cAAc,EAC5B,YAAY,KAAK,EACjB,cAAc,YAAY,EAC1B,QAAQ,EACR,QAAQ,EACR,GAAG,OACgB;IACnB,qBACE,6LAAC;QACC,SAAS;QACT,UAAU,YAAY;QACrB,GAAG,KAAK;kBAER,YAAY,cAAc;;;;;;AAGjC;MAhBgB;AA6BT,SAAS,cAAc,EAC5B,SAAS,EACT,eAAe,eAAe,EAC9B,qBAAqB,+BAA+B,EACpD,SAAS,KAAK,EACd,aAAa,IAAI,EACjB,QAAQ,EACR,GAAG,OACgB;IACnB,MAAM,EAAE,UAAU,EAAE;IAEpB,qBACE,6LAAC;QACC,OAAO;QACP,aAAa;QACb,WAAW;QACX,QAAQ;QACR,YAAY;kBAEZ,cAAA,6LAAC;YAAQ,GAAG,KAAK;sBACd;;;;;;;;;;;AAIT;MAxBgB"}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/ui/input.tsx"], "sourcesContent": ["/**\n * Enhanced Input Components\n * Extended Ant Design Input with theme integration and additional features\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { \n  Input as AntInput, \n  InputProps as AntInputProps,\n  Select as AntSelect,\n  SelectProps as AntSelectProps,\n  DatePicker as AntDatePicker,\n  DatePickerProps as AntDatePickerProps\n} from 'antd';\nimport { SearchOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\n\nconst { TextArea, Password } = AntInput;\nconst { Option } = AntSelect;\n\n/**\n * Enhanced input props\n */\nexport interface InputProps extends AntInputProps {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  fullWidth?: boolean;\n  compact?: boolean;\n}\n\n/**\n * Enhanced Input component\n */\nexport function Input({ \n  label,\n  error,\n  helperText,\n  fullWidth = false,\n  compact = false,\n  className,\n  style,\n  ...props \n}: InputProps) {\n  const themeStyles = useThemeStyles();\n\n  const inputStyle: React.CSSProperties = {\n    ...(fullWidth && { width: '100%' }),\n    ...(compact && { height: '32px' }),\n    ...style,\n  };\n\n  const containerStyle: React.CSSProperties = {\n    width: fullWidth ? '100%' : 'auto',\n  };\n\n  return (\n    <div style={containerStyle}>\n      {label && (\n        <label style={{ \n          display: 'block', \n          marginBottom: '4px',\n          color: themeStyles.getTextColor('primary'),\n          fontSize: '14px',\n          fontWeight: 500,\n        }}>\n          {label}\n        </label>\n      )}\n      <AntInput\n        className={className}\n        style={inputStyle}\n        status={error ? 'error' : undefined}\n        {...props}\n      />\n      {(error || helperText) && (\n        <div style={{ \n          marginTop: '4px',\n          fontSize: '12px',\n          color: error ? themeStyles.getColor('error') : themeStyles.getTextColor('secondary'),\n        }}>\n          {error || helperText}\n        </div>\n      )}\n    </div>\n  );\n}\n\n/**\n * Search input component\n */\nexport interface SearchInputProps extends Omit<InputProps, 'prefix'> {\n  onSearch?: (value: string) => void;\n  searchButton?: boolean;\n}\n\nexport function SearchInput({ \n  onSearch,\n  searchButton = false,\n  ...props \n}: SearchInputProps) {\n  if (searchButton) {\n    return (\n      <Input\n        {...props}\n        suffix={\n          <SearchOutlined \n            style={{ cursor: 'pointer' }}\n            onClick={() => onSearch?.(props.value as string || '')}\n          />\n        }\n      />\n    );\n  }\n\n  return (\n    <AntInput.Search\n      onSearch={onSearch}\n      {...props}\n    />\n  );\n}\n\n/**\n * Password input component\n */\nexport interface PasswordInputProps extends InputProps {\n  showToggle?: boolean;\n  strength?: boolean;\n}\n\nexport function PasswordInput({ \n  showToggle = true,\n  strength = false,\n  ...props \n}: PasswordInputProps) {\n  const [visible, setVisible] = useState(false);\n\n  const getPasswordStrength = (password: string): { level: number; text: string; color: string } => {\n    if (!password) return { level: 0, text: '', color: '' };\n    \n    let score = 0;\n    if (password.length >= 8) score++;\n    if (/[a-z]/.test(password)) score++;\n    if (/[A-Z]/.test(password)) score++;\n    if (/[0-9]/.test(password)) score++;\n    if (/[^A-Za-z0-9]/.test(password)) score++;\n\n    const levels = [\n      { level: 0, text: '', color: '' },\n      { level: 1, text: 'Very Weak', color: '#ff4d4f' },\n      { level: 2, text: 'Weak', color: '#ff7a45' },\n      { level: 3, text: 'Fair', color: '#ffa940' },\n      { level: 4, text: 'Good', color: '#52c41a' },\n      { level: 5, text: 'Strong', color: '#389e0d' },\n    ];\n\n    return levels[score];\n  };\n\n  const passwordStrength = strength ? getPasswordStrength(props.value as string || '') : null;\n\n  return (\n    <div style={{ width: props.fullWidth ? '100%' : 'auto' }}>\n      {showToggle ? (\n        <Input\n          type={visible ? 'text' : 'password'}\n          suffix={\n            visible ? (\n              <EyeInvisibleOutlined onClick={() => setVisible(false)} />\n            ) : (\n              <EyeOutlined onClick={() => setVisible(true)} />\n            )\n          }\n          {...props}\n        />\n      ) : (\n        <Password {...props} />\n      )}\n      {strength && passwordStrength && passwordStrength.level > 0 && (\n        <div style={{ marginTop: '4px' }}>\n          <div style={{ \n            height: '4px', \n            backgroundColor: '#f0f0f0', \n            borderRadius: '2px',\n            overflow: 'hidden',\n          }}>\n            <div style={{\n              height: '100%',\n              width: `${(passwordStrength.level / 5) * 100}%`,\n              backgroundColor: passwordStrength.color,\n              transition: 'all 0.3s ease',\n            }} />\n          </div>\n          <div style={{ \n            marginTop: '2px',\n            fontSize: '12px',\n            color: passwordStrength.color,\n          }}>\n            {passwordStrength.text}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\n/**\n * Textarea component\n */\nexport interface TextareaProps extends InputProps {\n  rows?: number;\n  autoSize?: boolean | { minRows?: number; maxRows?: number };\n  showCount?: boolean;\n  maxLength?: number;\n}\n\nexport function Textarea({ \n  rows = 4,\n  autoSize = false,\n  showCount = false,\n  maxLength,\n  ...props \n}: TextareaProps) {\n  return (\n    <Input\n      as={TextArea}\n      rows={rows}\n      autoSize={autoSize}\n      showCount={showCount}\n      maxLength={maxLength}\n      {...props}\n    />\n  );\n}\n\n/**\n * Select component\n */\nexport interface SelectProps extends AntSelectProps {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  fullWidth?: boolean;\n  compact?: boolean;\n  options?: Array<{ label: string; value: any; disabled?: boolean }>;\n}\n\nexport function Select({ \n  label,\n  error,\n  helperText,\n  fullWidth = false,\n  compact = false,\n  options = [],\n  children,\n  style,\n  ...props \n}: SelectProps) {\n  const themeStyles = useThemeStyles();\n\n  const selectStyle: React.CSSProperties = {\n    ...(fullWidth && { width: '100%' }),\n    ...(compact && { height: '32px' }),\n    ...style,\n  };\n\n  return (\n    <div style={{ width: fullWidth ? '100%' : 'auto' }}>\n      {label && (\n        <label style={{ \n          display: 'block', \n          marginBottom: '4px',\n          color: themeStyles.getTextColor('primary'),\n          fontSize: '14px',\n          fontWeight: 500,\n        }}>\n          {label}\n        </label>\n      )}\n      <AntSelect\n        style={selectStyle}\n        status={error ? 'error' : undefined}\n        {...props}\n      >\n        {options.length > 0 \n          ? options.map(option => (\n              <Option \n                key={option.value} \n                value={option.value}\n                disabled={option.disabled}\n              >\n                {option.label}\n              </Option>\n            ))\n          : children\n        }\n      </AntSelect>\n      {(error || helperText) && (\n        <div style={{ \n          marginTop: '4px',\n          fontSize: '12px',\n          color: error ? themeStyles.getColor('error') : themeStyles.getTextColor('secondary'),\n        }}>\n          {error || helperText}\n        </div>\n      )}\n    </div>\n  );\n}\n\n/**\n * Date picker component\n */\nexport interface DatePickerProps extends AntDatePickerProps {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  fullWidth?: boolean;\n  compact?: boolean;\n}\n\nexport function DatePicker({ \n  label,\n  error,\n  helperText,\n  fullWidth = false,\n  compact = false,\n  style,\n  ...props \n}: DatePickerProps) {\n  const themeStyles = useThemeStyles();\n\n  const pickerStyle: React.CSSProperties = {\n    ...(fullWidth && { width: '100%' }),\n    ...(compact && { height: '32px' }),\n    ...style,\n  };\n\n  return (\n    <div style={{ width: fullWidth ? '100%' : 'auto' }}>\n      {label && (\n        <label style={{ \n          display: 'block', \n          marginBottom: '4px',\n          color: themeStyles.getTextColor('primary'),\n          fontSize: '14px',\n          fontWeight: 500,\n        }}>\n          {label}\n        </label>\n      )}\n      <AntDatePicker\n        style={pickerStyle}\n        status={error ? 'error' : undefined}\n        {...props}\n      />\n      {(error || helperText) && (\n        <div style={{ \n          marginTop: '4px',\n          fontSize: '12px',\n          color: error ? themeStyles.getColor('error') : themeStyles.getTextColor('secondary'),\n        }}>\n          {error || helperText}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAID;AAUA;AAAA;AATA;AAAA;AAQA;AAAA;AAAA;AARA;;;AAHA;;;;;AAcA,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,mLAAA,CAAA,QAAQ;AACvC,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAS;AAgBrB,SAAS,MAAM,EACpB,KAAK,EACL,KAAK,EACL,UAAU,EACV,YAAY,KAAK,EACjB,UAAU,KAAK,EACf,SAAS,EACT,KAAK,EACL,GAAG,OACQ;;IACX,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,aAAkC;QACtC,GAAI,aAAa;YAAE,OAAO;QAAO,CAAC;QAClC,GAAI,WAAW;YAAE,QAAQ;QAAO,CAAC;QACjC,GAAG,KAAK;IACV;IAEA,MAAM,iBAAsC;QAC1C,OAAO,YAAY,SAAS;IAC9B;IAEA,qBACE,6LAAC;QAAI,OAAO;;YACT,uBACC,6LAAC;gBAAM,OAAO;oBACZ,SAAS;oBACT,cAAc;oBACd,OAAO,YAAY,YAAY,CAAC;oBAChC,UAAU;oBACV,YAAY;gBACd;0BACG;;;;;;0BAGL,6LAAC,mLAAA,CAAA,QAAQ;gBACP,WAAW;gBACX,OAAO;gBACP,QAAQ,QAAQ,UAAU;gBACzB,GAAG,KAAK;;;;;;YAEV,CAAC,SAAS,UAAU,mBACnB,6LAAC;gBAAI,OAAO;oBACV,WAAW;oBACX,UAAU;oBACV,OAAO,QAAQ,YAAY,QAAQ,CAAC,WAAW,YAAY,YAAY,CAAC;gBAC1E;0BACG,SAAS;;;;;;;;;;;;AAKpB;GApDgB;;QAUM,wHAAA,CAAA,iBAAc;;;KAVpB;AA8DT,SAAS,YAAY,EAC1B,QAAQ,EACR,eAAe,KAAK,EACpB,GAAG,OACc;IACjB,IAAI,cAAc;QAChB,qBACE,6LAAC;YACE,GAAG,KAAK;YACT,sBACE,6LAAC,yNAAA,CAAA,iBAAc;gBACb,OAAO;oBAAE,QAAQ;gBAAU;gBAC3B,SAAS,IAAM,WAAW,MAAM,KAAK,IAAc;;;;;;;;;;;IAK7D;IAEA,qBACE,6LAAC,mLAAA,CAAA,QAAQ,CAAC,MAAM;QACd,UAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAzBgB;AAmCT,SAAS,cAAc,EAC5B,aAAa,IAAI,EACjB,WAAW,KAAK,EAChB,GAAG,OACgB;;IACnB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,UAAU,OAAO;YAAE,OAAO;YAAG,MAAM;YAAI,OAAO;QAAG;QAEtD,IAAI,QAAQ;QACZ,IAAI,SAAS,MAAM,IAAI,GAAG;QAC1B,IAAI,QAAQ,IAAI,CAAC,WAAW;QAC5B,IAAI,QAAQ,IAAI,CAAC,WAAW;QAC5B,IAAI,QAAQ,IAAI,CAAC,WAAW;QAC5B,IAAI,eAAe,IAAI,CAAC,WAAW;QAEnC,MAAM,SAAS;YACb;gBAAE,OAAO;gBAAG,MAAM;gBAAI,OAAO;YAAG;YAChC;gBAAE,OAAO;gBAAG,MAAM;gBAAa,OAAO;YAAU;YAChD;gBAAE,OAAO;gBAAG,MAAM;gBAAQ,OAAO;YAAU;YAC3C;gBAAE,OAAO;gBAAG,MAAM;gBAAQ,OAAO;YAAU;YAC3C;gBAAE,OAAO;gBAAG,MAAM;gBAAQ,OAAO;YAAU;YAC3C;gBAAE,OAAO;gBAAG,MAAM;gBAAU,OAAO;YAAU;SAC9C;QAED,OAAO,MAAM,CAAC,MAAM;IACtB;IAEA,MAAM,mBAAmB,WAAW,oBAAoB,MAAM,KAAK,IAAc,MAAM;IAEvF,qBACE,6LAAC;QAAI,OAAO;YAAE,OAAO,MAAM,SAAS,GAAG,SAAS;QAAO;;YACpD,2BACC,6LAAC;gBACC,MAAM,UAAU,SAAS;gBACzB,QACE,wBACE,6LAAC,qOAAA,CAAA,uBAAoB;oBAAC,SAAS,IAAM,WAAW;;;;;2CAEhD,6LAAC,mNAAA,CAAA,cAAW;oBAAC,SAAS,IAAM,WAAW;;;;;;gBAG1C,GAAG,KAAK;;;;;qCAGX,6LAAC;gBAAU,GAAG,KAAK;;;;;;YAEpB,YAAY,oBAAoB,iBAAiB,KAAK,GAAG,mBACxD,6LAAC;gBAAI,OAAO;oBAAE,WAAW;gBAAM;;kCAC7B,6LAAC;wBAAI,OAAO;4BACV,QAAQ;4BACR,iBAAiB;4BACjB,cAAc;4BACd,UAAU;wBACZ;kCACE,cAAA,6LAAC;4BAAI,OAAO;gCACV,QAAQ;gCACR,OAAO,GAAG,AAAC,iBAAiB,KAAK,GAAG,IAAK,IAAI,CAAC,CAAC;gCAC/C,iBAAiB,iBAAiB,KAAK;gCACvC,YAAY;4BACd;;;;;;;;;;;kCAEF,6LAAC;wBAAI,OAAO;4BACV,WAAW;4BACX,UAAU;4BACV,OAAO,iBAAiB,KAAK;wBAC/B;kCACG,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;AAMlC;IA1EgB;MAAA;AAsFT,SAAS,SAAS,EACvB,OAAO,CAAC,EACR,WAAW,KAAK,EAChB,YAAY,KAAK,EACjB,SAAS,EACT,GAAG,OACW;IACd,qBACE,6LAAC;QACC,IAAI;QACJ,MAAM;QACN,UAAU;QACV,WAAW;QACX,WAAW;QACV,GAAG,KAAK;;;;;;AAGf;MAjBgB;AA+BT,SAAS,OAAO,EACrB,KAAK,EACL,KAAK,EACL,UAAU,EACV,YAAY,KAAK,EACjB,UAAU,KAAK,EACf,UAAU,EAAE,EACZ,QAAQ,EACR,KAAK,EACL,GAAG,OACS;;IACZ,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,cAAmC;QACvC,GAAI,aAAa;YAAE,OAAO;QAAO,CAAC;QAClC,GAAI,WAAW;YAAE,QAAQ;QAAO,CAAC;QACjC,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QAAI,OAAO;YAAE,OAAO,YAAY,SAAS;QAAO;;YAC9C,uBACC,6LAAC;gBAAM,OAAO;oBACZ,SAAS;oBACT,cAAc;oBACd,OAAO,YAAY,YAAY,CAAC;oBAChC,UAAU;oBACV,YAAY;gBACd;0BACG;;;;;;0BAGL,6LAAC,qLAAA,CAAA,SAAS;gBACR,OAAO;gBACP,QAAQ,QAAQ,UAAU;gBACzB,GAAG,KAAK;0BAER,QAAQ,MAAM,GAAG,IACd,QAAQ,GAAG,CAAC,CAAA,uBACV,6LAAC;wBAEC,OAAO,OAAO,KAAK;wBACnB,UAAU,OAAO,QAAQ;kCAExB,OAAO,KAAK;uBAJR,OAAO,KAAK;;;;gCAOrB;;;;;;YAGL,CAAC,SAAS,UAAU,mBACnB,6LAAC;gBAAI,OAAO;oBACV,WAAW;oBACX,UAAU;oBACV,OAAO,QAAQ,YAAY,QAAQ,CAAC,WAAW,YAAY,YAAY,CAAC;gBAC1E;0BACG,SAAS;;;;;;;;;;;;AAKpB;IA7DgB;;QAWM,wHAAA,CAAA,iBAAc;;;MAXpB;AA0ET,SAAS,WAAW,EACzB,KAAK,EACL,KAAK,EACL,UAAU,EACV,YAAY,KAAK,EACjB,UAAU,KAAK,EACf,KAAK,EACL,GAAG,OACa;;IAChB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,cAAmC;QACvC,GAAI,aAAa;YAAE,OAAO;QAAO,CAAC;QAClC,GAAI,WAAW;YAAE,QAAQ;QAAO,CAAC;QACjC,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QAAI,OAAO;YAAE,OAAO,YAAY,SAAS;QAAO;;YAC9C,uBACC,6LAAC;gBAAM,OAAO;oBACZ,SAAS;oBACT,cAAc;oBACd,OAAO,YAAY,YAAY,CAAC;oBAChC,UAAU;oBACV,YAAY;gBACd;0BACG;;;;;;0BAGL,6LAAC,iMAAA,CAAA,aAAa;gBACZ,OAAO;gBACP,QAAQ,QAAQ,UAAU;gBACzB,GAAG,KAAK;;;;;;YAEV,CAAC,SAAS,UAAU,mBACnB,6LAAC;gBAAI,OAAO;oBACV,WAAW;oBACX,UAAU;oBACV,OAAO,QAAQ,YAAY,QAAQ,CAAC,WAAW,YAAY,YAAY,CAAC;gBAC1E;0BACG,SAAS;;;;;;;;;;;;AAKpB;IA9CgB;;QASM,wHAAA,CAAA,iBAAc;;;MATpB"}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/ui/card.tsx"], "sourcesContent": ["/**\n * Enhanced Card Component\n * Extended Ant Design Card with theme integration and additional variants\n */\n\n'use client';\n\nimport React from 'react';\nimport { Card as AntCard, CardProps as AntCardProps, Skeleton, Empty } from 'antd';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Enhanced card props\n */\nexport interface CardProps extends AntCardProps {\n  variant?: 'default' | 'outlined' | 'elevated' | 'flat';\n  padding?: 'none' | 'small' | 'medium' | 'large';\n  loading?: boolean;\n  empty?: boolean;\n  emptyText?: string;\n  emptyDescription?: string;\n  hover?: boolean;\n  clickable?: boolean;\n  onClick?: () => void;\n}\n\n/**\n * Enhanced Card component\n */\nexport function Card({ \n  variant = 'default',\n  padding = 'medium',\n  loading = false,\n  empty = false,\n  emptyText = 'No data',\n  emptyDescription,\n  hover = false,\n  clickable = false,\n  onClick,\n  className,\n  style,\n  children,\n  ...props \n}: CardProps) {\n  const themeStyles = useThemeStyles();\n\n  // Get variant-specific styles\n  const getVariantStyle = (): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      transition: 'all 0.2s ease',\n      ...(clickable && { cursor: 'pointer' }),\n      ...(hover && {\n        ':hover': {\n          transform: 'translateY(-2px)',\n          boxShadow: themeStyles.colors.background.elevated,\n        }\n      }),\n    };\n\n    switch (variant) {\n      case 'outlined':\n        return {\n          ...baseStyle,\n          border: `1px solid ${themeStyles.getBorderColor('primary')}`,\n          boxShadow: 'none',\n        };\n      case 'elevated':\n        return {\n          ...baseStyle,\n          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n          border: 'none',\n        };\n      case 'flat':\n        return {\n          ...baseStyle,\n          boxShadow: 'none',\n          border: 'none',\n          backgroundColor: 'transparent',\n        };\n      default:\n        return baseStyle;\n    }\n  };\n\n  // Get padding styles\n  const getPaddingStyle = (): React.CSSProperties => {\n    const paddingMap = {\n      none: { padding: 0 },\n      small: { padding: '12px' },\n      medium: { padding: '16px' },\n      large: { padding: '24px' },\n    };\n    return paddingMap[padding];\n  };\n\n  const cardStyle: React.CSSProperties = {\n    ...getVariantStyle(),\n    ...style,\n  };\n\n  const bodyStyle: React.CSSProperties = {\n    ...getPaddingStyle(),\n    ...props.bodyStyle,\n  };\n\n  // Handle loading state\n  if (loading) {\n    return (\n      <AntCard\n        className={className}\n        style={cardStyle}\n        bodyStyle={bodyStyle}\n        {...props}\n      >\n        <Skeleton active />\n      </AntCard>\n    );\n  }\n\n  // Handle empty state\n  if (empty) {\n    return (\n      <AntCard\n        className={className}\n        style={cardStyle}\n        bodyStyle={bodyStyle}\n        {...props}\n      >\n        <Empty \n          description={emptyText}\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n        >\n          {emptyDescription && (\n            <div style={{ \n              marginTop: '8px',\n              color: themeStyles.getTextColor('secondary'),\n              fontSize: '12px',\n            }}>\n              {emptyDescription}\n            </div>\n          )}\n        </Empty>\n      </AntCard>\n    );\n  }\n\n  return (\n    <AntCard\n      className={className}\n      style={cardStyle}\n      bodyStyle={bodyStyle}\n      onClick={clickable ? onClick : undefined}\n      {...props}\n    >\n      {children}\n    </AntCard>\n  );\n}\n\n/**\n * Stat card component for displaying statistics\n */\nexport interface StatCardProps {\n  title: string;\n  value: string | number;\n  subtitle?: string;\n  icon?: React.ReactNode;\n  trend?: {\n    value: number;\n    isPositive: boolean;\n  };\n  loading?: boolean;\n  onClick?: () => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function StatCard({\n  title,\n  value,\n  subtitle,\n  icon,\n  trend,\n  loading = false,\n  onClick,\n  className,\n  style,\n}: StatCardProps) {\n  const themeStyles = useThemeStyles();\n\n  if (loading) {\n    return (\n      <Card className={className} style={style} loading />\n    );\n  }\n\n  return (\n    <Card \n      className={className} \n      style={style}\n      clickable={!!onClick}\n      onClick={onClick}\n      hover={!!onClick}\n    >\n      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <div style={{ flex: 1 }}>\n          <div style={{ \n            fontSize: '14px',\n            color: themeStyles.getTextColor('secondary'),\n            marginBottom: '4px',\n          }}>\n            {title}\n          </div>\n          <div style={{ \n            fontSize: '24px',\n            fontWeight: 'bold',\n            color: themeStyles.getTextColor('primary'),\n            marginBottom: subtitle || trend ? '4px' : 0,\n          }}>\n            {value}\n          </div>\n          {subtitle && (\n            <div style={{ \n              fontSize: '12px',\n              color: themeStyles.getTextColor('tertiary'),\n            }}>\n              {subtitle}\n            </div>\n          )}\n          {trend && (\n            <div style={{ \n              fontSize: '12px',\n              color: trend.isPositive ? themeStyles.getColor('success') : themeStyles.getColor('error'),\n              marginTop: '4px',\n            }}>\n              {trend.isPositive ? '↗' : '↘'} {Math.abs(trend.value)}%\n            </div>\n          )}\n        </div>\n        {icon && (\n          <div style={{ \n            fontSize: '32px',\n            color: themeStyles.getColor('primary'),\n            opacity: 0.7,\n          }}>\n            {icon}\n          </div>\n        )}\n      </div>\n    </Card>\n  );\n}\n\n/**\n * Info card component for displaying information with actions\n */\nexport interface InfoCardProps {\n  title: string;\n  description?: string;\n  icon?: React.ReactNode;\n  actions?: React.ReactNode[];\n  extra?: React.ReactNode;\n  children?: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function InfoCard({\n  title,\n  description,\n  icon,\n  actions,\n  extra,\n  children,\n  className,\n  style,\n}: InfoCardProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <Card \n      className={className} \n      style={style}\n      title={\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          {icon && (\n            <div style={{ \n              fontSize: '20px',\n              color: themeStyles.getColor('primary'),\n            }}>\n              {icon}\n            </div>\n          )}\n          <div>\n            <div style={{ \n              fontSize: '16px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n            }}>\n              {title}\n            </div>\n            {description && (\n              <div style={{ \n                fontSize: '12px',\n                color: themeStyles.getTextColor('secondary'),\n                marginTop: '2px',\n              }}>\n                {description}\n              </div>\n            )}\n          </div>\n        </div>\n      }\n      extra={extra}\n      actions={actions}\n    >\n      {children}\n    </Card>\n  );\n}\n\n/**\n * Grid card component for displaying items in a grid\n */\nexport interface GridCardProps {\n  title?: string;\n  items: Array<{\n    id: string | number;\n    title: string;\n    description?: string;\n    icon?: React.ReactNode;\n    onClick?: () => void;\n  }>;\n  columns?: number;\n  loading?: boolean;\n  empty?: boolean;\n  emptyText?: string;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function GridCard({\n  title,\n  items,\n  columns = 3,\n  loading = false,\n  empty = false,\n  emptyText = 'No items',\n  className,\n  style,\n}: GridCardProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <Card \n      title={title}\n      className={className} \n      style={style}\n      loading={loading}\n      empty={empty || items.length === 0}\n      emptyText={emptyText}\n    >\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: `repeat(${columns}, 1fr)`,\n        gap: '16px',\n      }}>\n        {items.map((item) => (\n          <div\n            key={item.id}\n            style={{\n              padding: '12px',\n              border: `1px solid ${themeStyles.getBorderColor('primary')}`,\n              borderRadius: '6px',\n              cursor: item.onClick ? 'pointer' : 'default',\n              transition: 'all 0.2s ease',\n              ':hover': item.onClick ? {\n                borderColor: themeStyles.getColor('primary'),\n                backgroundColor: themeStyles.getBackgroundColor('elevated'),\n              } : {},\n            }}\n            onClick={item.onClick}\n          >\n            {item.icon && (\n              <div style={{ \n                fontSize: '24px',\n                color: themeStyles.getColor('primary'),\n                marginBottom: '8px',\n              }}>\n                {item.icon}\n              </div>\n            )}\n            <div style={{ \n              fontSize: '14px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n              marginBottom: '4px',\n            }}>\n              {item.title}\n            </div>\n            {item.description && (\n              <div style={{ \n                fontSize: '12px',\n                color: themeStyles.getTextColor('secondary'),\n              }}>\n                {item.description}\n              </div>\n            )}\n          </div>\n        ))}\n      </div>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAMD;AAAA;AADA;AAAA;AAAA;;;AAHA;;;AAwBO,SAAS,KAAK,EACnB,UAAU,SAAS,EACnB,UAAU,QAAQ,EAClB,UAAU,KAAK,EACf,QAAQ,KAAK,EACb,YAAY,SAAS,EACrB,gBAAgB,EAChB,QAAQ,KAAK,EACb,YAAY,KAAK,EACjB,OAAO,EACP,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OACO;;IACV,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,8BAA8B;IAC9B,MAAM,kBAAkB;QACtB,MAAM,YAAiC;YACrC,YAAY;YACZ,GAAI,aAAa;gBAAE,QAAQ;YAAU,CAAC;YACtC,GAAI,SAAS;gBACX,UAAU;oBACR,WAAW;oBACX,WAAW,YAAY,MAAM,CAAC,UAAU,CAAC,QAAQ;gBACnD;YACF,CAAC;QACH;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,QAAQ,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;oBAC5D,WAAW;gBACb;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,WAAW;oBACX,QAAQ;gBACV;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,WAAW;oBACX,QAAQ;oBACR,iBAAiB;gBACnB;YACF;gBACE,OAAO;QACX;IACF;IAEA,qBAAqB;IACrB,MAAM,kBAAkB;QACtB,MAAM,aAAa;YACjB,MAAM;gBAAE,SAAS;YAAE;YACnB,OAAO;gBAAE,SAAS;YAAO;YACzB,QAAQ;gBAAE,SAAS;YAAO;YAC1B,OAAO;gBAAE,SAAS;YAAO;QAC3B;QACA,OAAO,UAAU,CAAC,QAAQ;IAC5B;IAEA,MAAM,YAAiC;QACrC,GAAG,iBAAiB;QACpB,GAAG,KAAK;IACV;IAEA,MAAM,YAAiC;QACrC,GAAG,iBAAiB;QACpB,GAAG,MAAM,SAAS;IACpB;IAEA,uBAAuB;IACvB,IAAI,SAAS;QACX,qBACE,6LAAC,iLAAA,CAAA,OAAO;YACN,WAAW;YACX,OAAO;YACP,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,6LAAC,yLAAA,CAAA,WAAQ;gBAAC,MAAM;;;;;;;;;;;IAGtB;IAEA,qBAAqB;IACrB,IAAI,OAAO;QACT,qBACE,6LAAC,iLAAA,CAAA,OAAO;YACN,WAAW;YACX,OAAO;YACP,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,6LAAC,mLAAA,CAAA,QAAK;gBACJ,aAAa;gBACb,OAAO,mLAAA,CAAA,QAAK,CAAC,sBAAsB;0BAElC,kCACC,6LAAC;oBAAI,OAAO;wBACV,WAAW;wBACX,OAAO,YAAY,YAAY,CAAC;wBAChC,UAAU;oBACZ;8BACG;;;;;;;;;;;;;;;;IAMb;IAEA,qBACE,6LAAC,iLAAA,CAAA,OAAO;QACN,WAAW;QACX,OAAO;QACP,WAAW;QACX,SAAS,YAAY,UAAU;QAC9B,GAAG,KAAK;kBAER;;;;;;AAGP;GAhIgB;;QAeM,wHAAA,CAAA,iBAAc;;;KAfpB;AAoJT,SAAS,SAAS,EACvB,KAAK,EACL,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,UAAU,KAAK,EACf,OAAO,EACP,SAAS,EACT,KAAK,EACS;;IACd,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAK,WAAW;YAAW,OAAO;YAAO,OAAO;;;;;;IAErD;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;QACP,WAAW,CAAC,CAAC;QACb,SAAS;QACT,OAAO,CAAC,CAAC;kBAET,cAAA,6LAAC;YAAI,OAAO;gBAAE,SAAS;gBAAQ,YAAY;gBAAU,gBAAgB;YAAgB;;8BACnF,6LAAC;oBAAI,OAAO;wBAAE,MAAM;oBAAE;;sCACpB,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;gCAChC,cAAc;4BAChB;sCACG;;;;;;sCAEH,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,YAAY;gCACZ,OAAO,YAAY,YAAY,CAAC;gCAChC,cAAc,YAAY,QAAQ,QAAQ;4BAC5C;sCACG;;;;;;wBAEF,0BACC,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;4BAClC;sCACG;;;;;;wBAGJ,uBACC,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,MAAM,UAAU,GAAG,YAAY,QAAQ,CAAC,aAAa,YAAY,QAAQ,CAAC;gCACjF,WAAW;4BACb;;gCACG,MAAM,UAAU,GAAG,MAAM;gCAAI;gCAAE,KAAK,GAAG,CAAC,MAAM,KAAK;gCAAE;;;;;;;;;;;;;gBAI3D,sBACC,6LAAC;oBAAI,OAAO;wBACV,UAAU;wBACV,OAAO,YAAY,QAAQ,CAAC;wBAC5B,SAAS;oBACX;8BACG;;;;;;;;;;;;;;;;;AAMb;IA1EgB;;QAWM,wHAAA,CAAA,iBAAc;;;MAXpB;AA0FT,SAAS,SAAS,EACvB,KAAK,EACL,WAAW,EACX,IAAI,EACJ,OAAO,EACP,KAAK,EACL,QAAQ,EACR,SAAS,EACT,KAAK,EACS;;IACd,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;QACP,qBACE,6LAAC;YAAI,OAAO;gBAAE,SAAS;gBAAQ,YAAY;gBAAU,KAAK;YAAO;;gBAC9D,sBACC,6LAAC;oBAAI,OAAO;wBACV,UAAU;wBACV,OAAO,YAAY,QAAQ,CAAC;oBAC9B;8BACG;;;;;;8BAGL,6LAAC;;sCACC,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,YAAY;gCACZ,OAAO,YAAY,YAAY,CAAC;4BAClC;sCACG;;;;;;wBAEF,6BACC,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;gCAChC,WAAW;4BACb;sCACG;;;;;;;;;;;;;;;;;;QAMX,OAAO;QACP,SAAS;kBAER;;;;;;AAGP;IApDgB;;QAUM,wHAAA,CAAA,iBAAc;;;MAVpB;AA0ET,SAAS,SAAS,EACvB,KAAK,EACL,KAAK,EACL,UAAU,CAAC,EACX,UAAU,KAAK,EACf,QAAQ,KAAK,EACb,YAAY,UAAU,EACtB,SAAS,EACT,KAAK,EACS;;IACd,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,6LAAC;QACC,OAAO;QACP,WAAW;QACX,OAAO;QACP,SAAS;QACT,OAAO,SAAS,MAAM,MAAM,KAAK;QACjC,WAAW;kBAEX,cAAA,6LAAC;YAAI,OAAO;gBACV,SAAS;gBACT,qBAAqB,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC;gBAC9C,KAAK;YACP;sBACG,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;oBAEC,OAAO;wBACL,SAAS;wBACT,QAAQ,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;wBAC5D,cAAc;wBACd,QAAQ,KAAK,OAAO,GAAG,YAAY;wBACnC,YAAY;wBACZ,UAAU,KAAK,OAAO,GAAG;4BACvB,aAAa,YAAY,QAAQ,CAAC;4BAClC,iBAAiB,YAAY,kBAAkB,CAAC;wBAClD,IAAI,CAAC;oBACP;oBACA,SAAS,KAAK,OAAO;;wBAEpB,KAAK,IAAI,kBACR,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,YAAY,QAAQ,CAAC;gCAC5B,cAAc;4BAChB;sCACG,KAAK,IAAI;;;;;;sCAGd,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,YAAY;gCACZ,OAAO,YAAY,YAAY,CAAC;gCAChC,cAAc;4BAChB;sCACG,KAAK,KAAK;;;;;;wBAEZ,KAAK,WAAW,kBACf,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;4BAClC;sCACG,KAAK,WAAW;;;;;;;mBApChB,KAAK,EAAE;;;;;;;;;;;;;;;AA4CxB;IAxEgB;;QAUM,wHAAA,CAAA,iBAAc;;;MAVpB"}}, {"offset": {"line": 1120, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1126, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/ui/index.ts"], "sourcesContent": ["/**\n * UI Components Index\n * Export all common UI components\n */\n\n// Button components\nexport * from './button';\n\n// Input components\nexport * from './input';\n\n// Card components\nexport * from './card';\n\n// Re-export commonly used Ant Design components for convenience\nexport {\n  Typography,\n  Space,\n  Divider,\n  Tag,\n  Badge,\n  Avatar,\n  Tooltip,\n  Popover,\n  Dropdown,\n  Menu,\n  Breadcrumb,\n  Steps,\n  Progress,\n  Spin,\n  Alert,\n  Message,\n  Notification,\n  Modal,\n  Drawer,\n  Popconfirm,\n} from 'antd';\n\n/**\n * UI components metadata\n */\nexport const UI_COMPONENTS_VERSION = '1.0.0';\nexport const UI_COMPONENTS_NAME = 'APISportsGame UI Components';\n\n/**\n * Setup function for UI components\n */\nexport function setupUIComponents() {\n  console.log(`${UI_COMPONENTS_NAME} v${UI_COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,oBAAoB;;;;;;;;;;AAoCb,MAAM,wBAAwB;AAC9B,MAAM,qBAAqB;AAK3B,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,mBAAmB,EAAE,EAAE,sBAAsB,YAAY,CAAC;AAC3E"}}, {"offset": {"line": 1147, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1164, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/query-error-handler.ts"], "sourcesContent": ["/**\n * Query Error Handler\n * Centralized error handling for TanStack Query\n */\n\nimport { QueryClient } from '@tanstack/react-query';\n\n/**\n * API Error interface\n */\nexport interface ApiError {\n  status: number;\n  statusText: string;\n  message: string;\n  details?: any;\n  timestamp: string;\n}\n\n/**\n * Error types for different scenarios\n */\nexport enum ErrorType {\n  NETWORK = 'NETWORK',\n  AUTHENTICATION = 'AUTHENTICATION',\n  AUTHORIZATION = 'AUTHORIZATION',\n  VALIDATION = 'VALIDATION',\n  SERVER = 'SERVER',\n  UNKNOWN = 'UNKNOWN',\n}\n\n/**\n * Determine error type based on status code\n */\nexport function getErrorType(status: number): ErrorType {\n  if (status === 401) return ErrorType.AUTHENTICATION;\n  if (status === 403) return ErrorType.AUTHORIZATION;\n  if (status >= 400 && status < 500) return ErrorType.VALIDATION;\n  if (status >= 500) return ErrorType.SERVER;\n  if (status === 0) return ErrorType.NETWORK;\n  return ErrorType.UNKNOWN;\n}\n\n/**\n * Create standardized API error\n */\nexport function createApiError(\n  status: number,\n  statusText: string,\n  message: string,\n  details?: any\n): ApiError {\n  return {\n    status,\n    statusText,\n    message,\n    details,\n    timestamp: new Date().toISOString(),\n  };\n}\n\n/**\n * Parse error response from API\n */\nexport async function parseErrorResponse(response: Response): Promise<ApiError> {\n  let message = response.statusText || 'An error occurred';\n  let details = null;\n\n  try {\n    const errorData = await response.json();\n    message = errorData.message || errorData.error || message;\n    details = errorData.details || errorData;\n  } catch {\n    // If response is not JSON, use status text\n  }\n\n  return createApiError(response.status, response.statusText, message, details);\n}\n\n/**\n * Global error handler for queries\n */\nexport function createGlobalErrorHandler() {\n  return (error: unknown) => {\n    console.error('[Query Error]', error);\n\n    // Handle different types of errors\n    if (error instanceof Error) {\n      // Network errors, parsing errors, etc.\n      console.error('Error details:', {\n        name: error.name,\n        message: error.message,\n        stack: error.stack,\n      });\n    }\n\n    // Handle API errors\n    if (isApiError(error)) {\n      handleApiError(error);\n    }\n  };\n}\n\n/**\n * Check if error is an API error\n */\nexport function isApiError(error: unknown): error is ApiError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    'status' in error &&\n    'message' in error\n  );\n}\n\n/**\n * Handle specific API error types\n */\nexport function handleApiError(error: ApiError) {\n  const errorType = getErrorType(error.status);\n\n  switch (errorType) {\n    case ErrorType.AUTHENTICATION:\n      handleAuthenticationError(error);\n      break;\n    case ErrorType.AUTHORIZATION:\n      handleAuthorizationError(error);\n      break;\n    case ErrorType.VALIDATION:\n      handleValidationError(error);\n      break;\n    case ErrorType.SERVER:\n      handleServerError(error);\n      break;\n    case ErrorType.NETWORK:\n      handleNetworkError(error);\n      break;\n    default:\n      handleUnknownError(error);\n  }\n}\n\n/**\n * Handle authentication errors (401)\n */\nfunction handleAuthenticationError(error: ApiError) {\n  console.warn('[Auth Error]', error.message);\n  \n  // In development mode, authentication is disabled\n  if (process.env.NODE_ENV === 'development') {\n    console.log('[Dev Mode] Authentication error ignored');\n    return;\n  }\n  \n  // In production, redirect to login or refresh token\n  // This will be implemented when auth system is ready\n}\n\n/**\n * Handle authorization errors (403)\n */\nfunction handleAuthorizationError(error: ApiError) {\n  console.warn('[Authorization Error]', error.message);\n  \n  // Show user-friendly message about insufficient permissions\n  // This will be integrated with notification system\n}\n\n/**\n * Handle validation errors (400-499)\n */\nfunction handleValidationError(error: ApiError) {\n  console.warn('[Validation Error]', error.message);\n  \n  // These are usually handled by individual components\n  // Global handler just logs for debugging\n}\n\n/**\n * Handle server errors (500+)\n */\nfunction handleServerError(error: ApiError) {\n  console.error('[Server Error]', error.message);\n  \n  // Show generic error message to user\n  // Log detailed error for debugging\n}\n\n/**\n * Handle network errors\n */\nfunction handleNetworkError(error: ApiError) {\n  console.error('[Network Error]', error.message);\n  \n  // Show network connectivity message\n  // Suggest retry or check connection\n}\n\n/**\n * Handle unknown errors\n */\nfunction handleUnknownError(error: ApiError) {\n  console.error('[Unknown Error]', error);\n  \n  // Show generic error message\n  // Log for investigation\n}\n\n/**\n * Error boundary for query errors\n */\nexport function setupQueryErrorHandling(queryClient: QueryClient) {\n  // Set up global error handler\n  queryClient.setDefaultOptions({\n    queries: {\n      ...queryClient.getDefaultOptions().queries,\n      throwOnError: false, // Handle errors gracefully\n    },\n    mutations: {\n      ...queryClient.getDefaultOptions().mutations,\n      throwOnError: false, // Handle errors gracefully\n    },\n  });\n\n  // Set up global error handler\n  queryClient.setMutationDefaults(['mutation'], {\n    onError: createGlobalErrorHandler(),\n  });\n}\n\n/**\n * Utility functions for error handling\n */\nexport const errorUtils = {\n  /**\n   * Check if error should trigger retry\n   */\n  shouldRetry: (error: unknown): boolean => {\n    if (isApiError(error)) {\n      const errorType = getErrorType(error.status);\n      // Don't retry client errors (4xx)\n      return errorType !== ErrorType.VALIDATION && \n             errorType !== ErrorType.AUTHENTICATION && \n             errorType !== ErrorType.AUTHORIZATION;\n    }\n    return true; // Retry network and unknown errors\n  },\n\n  /**\n   * Get user-friendly error message\n   */\n  getUserMessage: (error: unknown): string => {\n    if (isApiError(error)) {\n      const errorType = getErrorType(error.status);\n      \n      switch (errorType) {\n        case ErrorType.AUTHENTICATION:\n          return 'Please log in to continue';\n        case ErrorType.AUTHORIZATION:\n          return 'You do not have permission to perform this action';\n        case ErrorType.VALIDATION:\n          return error.message || 'Please check your input and try again';\n        case ErrorType.SERVER:\n          return 'Server error occurred. Please try again later';\n        case ErrorType.NETWORK:\n          return 'Network error. Please check your connection';\n        default:\n          return 'An unexpected error occurred';\n      }\n    }\n    \n    return 'An unexpected error occurred';\n  },\n\n  /**\n   * Check if error is retryable\n   */\n  isRetryable: (error: unknown): boolean => {\n    if (isApiError(error)) {\n      return error.status >= 500 || error.status === 0;\n    }\n    return true;\n  },\n} as const;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;AAiJK;AA/HC,IAAA,AAAK,mCAAA;;;;;;;WAAA;;AAYL,SAAS,aAAa,MAAc;IACzC,IAAI,WAAW,KAAK;IACpB,IAAI,WAAW,KAAK;IACpB,IAAI,UAAU,OAAO,SAAS,KAAK;IACnC,IAAI,UAAU,KAAK;IACnB,IAAI,WAAW,GAAG;IAClB;AACF;AAKO,SAAS,eACd,MAAc,EACd,UAAkB,EAClB,OAAe,EACf,OAAa;IAEb,OAAO;QACL;QACA;QACA;QACA;QACA,WAAW,IAAI,OAAO,WAAW;IACnC;AACF;AAKO,eAAe,mBAAmB,QAAkB;IACzD,IAAI,UAAU,SAAS,UAAU,IAAI;IACrC,IAAI,UAAU;IAEd,IAAI;QACF,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,UAAU,UAAU,OAAO,IAAI,UAAU,KAAK,IAAI;QAClD,UAAU,UAAU,OAAO,IAAI;IACjC,EAAE,OAAM;IACN,2CAA2C;IAC7C;IAEA,OAAO,eAAe,SAAS,MAAM,EAAE,SAAS,UAAU,EAAE,SAAS;AACvE;AAKO,SAAS;IACd,OAAO,CAAC;QACN,QAAQ,KAAK,CAAC,iBAAiB;QAE/B,mCAAmC;QACnC,IAAI,iBAAiB,OAAO;YAC1B,uCAAuC;YACvC,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,MAAM,MAAM,IAAI;gBAChB,SAAS,MAAM,OAAO;gBACtB,OAAO,MAAM,KAAK;YACpB;QACF;QAEA,oBAAoB;QACpB,IAAI,WAAW,QAAQ;YACrB,eAAe;QACjB;IACF;AACF;AAKO,SAAS,WAAW,KAAc;IACvC,OACE,OAAO,UAAU,YACjB,UAAU,QACV,YAAY,SACZ,aAAa;AAEjB;AAKO,SAAS,eAAe,KAAe;IAC5C,MAAM,YAAY,aAAa,MAAM,MAAM;IAE3C,OAAQ;QACN;YACE,0BAA0B;YAC1B;QACF;YACE,yBAAyB;YACzB;QACF;YACE,sBAAsB;YACtB;QACF;YACE,kBAAkB;YAClB;QACF;YACE,mBAAmB;YACnB;QACF;YACE,mBAAmB;IACvB;AACF;AAEA;;CAEC,GACD,SAAS,0BAA0B,KAAe;IAChD,QAAQ,IAAI,CAAC,gBAAgB,MAAM,OAAO;IAE1C,kDAAkD;IAClD,wCAA4C;QAC1C,QAAQ,GAAG,CAAC;QACZ;IACF;AAEA,oDAAoD;AACpD,qDAAqD;AACvD;AAEA;;CAEC,GACD,SAAS,yBAAyB,KAAe;IAC/C,QAAQ,IAAI,CAAC,yBAAyB,MAAM,OAAO;AAEnD,4DAA4D;AAC5D,mDAAmD;AACrD;AAEA;;CAEC,GACD,SAAS,sBAAsB,KAAe;IAC5C,QAAQ,IAAI,CAAC,sBAAsB,MAAM,OAAO;AAEhD,qDAAqD;AACrD,yCAAyC;AAC3C;AAEA;;CAEC,GACD,SAAS,kBAAkB,KAAe;IACxC,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;AAE7C,qCAAqC;AACrC,mCAAmC;AACrC;AAEA;;CAEC,GACD,SAAS,mBAAmB,KAAe;IACzC,QAAQ,KAAK,CAAC,mBAAmB,MAAM,OAAO;AAE9C,oCAAoC;AACpC,oCAAoC;AACtC;AAEA;;CAEC,GACD,SAAS,mBAAmB,KAAe;IACzC,QAAQ,KAAK,CAAC,mBAAmB;AAEjC,6BAA6B;AAC7B,wBAAwB;AAC1B;AAKO,SAAS,wBAAwB,WAAwB;IAC9D,8BAA8B;IAC9B,YAAY,iBAAiB,CAAC;QAC5B,SAAS;YACP,GAAG,YAAY,iBAAiB,GAAG,OAAO;YAC1C,cAAc;QAChB;QACA,WAAW;YACT,GAAG,YAAY,iBAAiB,GAAG,SAAS;YAC5C,cAAc;QAChB;IACF;IAEA,8BAA8B;IAC9B,YAAY,mBAAmB,CAAC;QAAC;KAAW,EAAE;QAC5C,SAAS;IACX;AACF;AAKO,MAAM,aAAa;IACxB;;GAEC,GACD,aAAa,CAAC;QACZ,IAAI,WAAW,QAAQ;YACrB,MAAM,YAAY,aAAa,MAAM,MAAM;YAC3C,kCAAkC;YAClC,OAAO,8BACA,kCACA;QACT;QACA,OAAO,MAAM,mCAAmC;IAClD;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,IAAI,WAAW,QAAQ;YACrB,MAAM,YAAY,aAAa,MAAM,MAAM;YAE3C,OAAQ;gBACN;oBACE,OAAO;gBACT;oBACE,OAAO;gBACT;oBACE,OAAO,MAAM,OAAO,IAAI;gBAC1B;oBACE,OAAO;gBACT;oBACE,OAAO;gBACT;oBACE,OAAO;YACX;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,aAAa,CAAC;QACZ,IAAI,WAAW,QAAQ;YACrB,OAAO,MAAM,MAAM,IAAI,OAAO,MAAM,MAAM,KAAK;QACjD;QACA,OAAO;IACT;AACF"}}, {"offset": {"line": 1371, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1377, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/query-utils.ts"], "sourcesContent": ["/**\n * Query Utilities and Helpers\n * Common utilities for working with TanStack Query\n */\n\nimport { QueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';\nimport { QUERY_CONFIG } from './query-client';\nimport { ApiError, isApiError, errorUtils } from './query-error-handler';\n\n/**\n * Base API response interface\n */\nexport interface ApiResponse<T = any> {\n  data: T;\n  message?: string;\n  success: boolean;\n  timestamp: string;\n}\n\n/**\n * Paginated response interface\n */\nexport interface PaginatedResponse<T = any> extends ApiResponse<T[]> {\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n}\n\n/**\n * Query options builder for common patterns\n */\nexport const queryOptionsBuilder = {\n  /**\n   * Build options for real-time data (short cache)\n   */\n  realTime: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({\n    staleTime: QUERY_CONFIG.STALE_TIME.SHORT,\n    gcTime: QUERY_CONFIG.STALE_TIME.MEDIUM,\n    refetchInterval: QUERY_CONFIG.REFETCH_INTERVAL.FAST,\n    ...options,\n  }),\n\n  /**\n   * Build options for static data (long cache)\n   */\n  static: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({\n    staleTime: QUERY_CONFIG.STALE_TIME.VERY_LONG,\n    gcTime: QUERY_CONFIG.STALE_TIME.VERY_LONG,\n    refetchOnWindowFocus: false,\n    refetchOnReconnect: false,\n    ...options,\n  }),\n\n  /**\n   * Build options for user-specific data\n   */\n  userSpecific: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({\n    staleTime: QUERY_CONFIG.STALE_TIME.MEDIUM,\n    gcTime: QUERY_CONFIG.STALE_TIME.LONG,\n    refetchOnWindowFocus: true,\n    ...options,\n  }),\n\n  /**\n   * Build options for background sync data\n   */\n  backgroundSync: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({\n    staleTime: QUERY_CONFIG.STALE_TIME.LONG,\n    gcTime: QUERY_CONFIG.STALE_TIME.VERY_LONG,\n    refetchInterval: QUERY_CONFIG.REFETCH_INTERVAL.SLOW,\n    refetchIntervalInBackground: true,\n    ...options,\n  }),\n};\n\n/**\n * Mutation options builder for common patterns\n */\nexport const mutationOptionsBuilder = {\n  /**\n   * Build options for optimistic updates\n   */\n  optimistic: <T, V>(options?: Partial<UseMutationOptions<T, ApiError, V>>): Partial<UseMutationOptions<T, ApiError, V>> => ({\n    retry: QUERY_CONFIG.RETRY.ONCE,\n    ...options,\n  }),\n\n  /**\n   * Build options for critical operations\n   */\n  critical: <T, V>(options?: Partial<UseMutationOptions<T, ApiError, V>>): Partial<UseMutationOptions<T, ApiError, V>> => ({\n    retry: QUERY_CONFIG.RETRY.DEFAULT,\n    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),\n    ...options,\n  }),\n\n  /**\n   * Build options for background operations\n   */\n  background: <T, V>(options?: Partial<UseMutationOptions<T, ApiError, V>>): Partial<UseMutationOptions<T, ApiError, V>> => ({\n    retry: QUERY_CONFIG.RETRY.TWICE,\n    ...options,\n  }),\n};\n\n/**\n * Cache management utilities\n */\nexport const cacheUtils = {\n  /**\n   * Invalidate queries by pattern\n   */\n  invalidateByPattern: async (queryClient: QueryClient, pattern: string[]) => {\n    await queryClient.invalidateQueries({ queryKey: pattern });\n  },\n\n  /**\n   * Remove queries by pattern\n   */\n  removeByPattern: (queryClient: QueryClient, pattern: string[]) => {\n    queryClient.removeQueries({ queryKey: pattern });\n  },\n\n  /**\n   * Update query data\n   */\n  updateQueryData: <T>(\n    queryClient: QueryClient,\n    queryKey: string[],\n    updater: (oldData: T | undefined) => T\n  ) => {\n    queryClient.setQueryData(queryKey, updater);\n  },\n\n  /**\n   * Optimistically update list data\n   */\n  optimisticListUpdate: <T extends { id: string | number }>(\n    queryClient: QueryClient,\n    queryKey: string[],\n    item: T,\n    operation: 'add' | 'update' | 'remove'\n  ) => {\n    queryClient.setQueryData<T[]>(queryKey, (oldData) => {\n      if (!oldData) return operation === 'add' ? [item] : [];\n\n      switch (operation) {\n        case 'add':\n          return [...oldData, item];\n        case 'update':\n          return oldData.map((existing) =>\n            existing.id === item.id ? { ...existing, ...item } : existing\n          );\n        case 'remove':\n          return oldData.filter((existing) => existing.id !== item.id);\n        default:\n          return oldData;\n      }\n    });\n  },\n\n  /**\n   * Optimistically update paginated data\n   */\n  optimisticPaginatedUpdate: <T extends { id: string | number }>(\n    queryClient: QueryClient,\n    queryKey: string[],\n    item: T,\n    operation: 'add' | 'update' | 'remove'\n  ) => {\n    queryClient.setQueryData<PaginatedResponse<T>>(queryKey, (oldData) => {\n      if (!oldData) return oldData;\n\n      const updatedData = cacheUtils.optimisticListUpdate(\n        queryClient,\n        ['temp'],\n        item,\n        operation\n      );\n\n      return {\n        ...oldData,\n        data: updatedData || oldData.data,\n      };\n    });\n  },\n};\n\n/**\n * Query state utilities\n */\nexport const queryStateUtils = {\n  /**\n   * Check if any queries are loading\n   */\n  isAnyLoading: (queryClient: QueryClient, queryKeys: string[][]): boolean => {\n    return queryKeys.some((key) => {\n      const query = queryClient.getQueryState(key);\n      return query?.fetchStatus === 'fetching';\n    });\n  },\n\n  /**\n   * Check if any queries have errors\n   */\n  hasAnyErrors: (queryClient: QueryClient, queryKeys: string[][]): boolean => {\n    return queryKeys.some((key) => {\n      const query = queryClient.getQueryState(key);\n      return query?.status === 'error';\n    });\n  },\n\n  /**\n   * Get all errors from queries\n   */\n  getAllErrors: (queryClient: QueryClient, queryKeys: string[][]): ApiError[] => {\n    return queryKeys\n      .map((key) => {\n        const query = queryClient.getQueryState(key);\n        return query?.error;\n      })\n      .filter((error): error is ApiError => isApiError(error));\n  },\n\n  /**\n   * Check if data is stale\n   */\n  isStale: (queryClient: QueryClient, queryKey: string[]): boolean => {\n    const query = queryClient.getQueryState(queryKey);\n    return query ? query.isStale : true;\n  },\n};\n\n/**\n * Development utilities\n */\nexport const devUtils = {\n  /**\n   * Log query cache state\n   */\n  logCacheState: (queryClient: QueryClient) => {\n    if (process.env.NODE_ENV === 'development') {\n      const cache = queryClient.getQueryCache();\n      console.log('[Query Cache]', {\n        queries: cache.getAll().length,\n        state: cache.getAll().map((query) => ({\n          key: query.queryKey,\n          status: query.state.status,\n          dataUpdatedAt: query.state.dataUpdatedAt,\n          error: query.state.error,\n        })),\n      });\n    }\n  },\n\n  /**\n   * Clear all cache (development only)\n   */\n  clearAllCache: (queryClient: QueryClient) => {\n    if (process.env.NODE_ENV === 'development') {\n      queryClient.clear();\n      console.log('[Dev] Query cache cleared');\n    }\n  },\n\n  /**\n   * Force refetch all queries (development only)\n   */\n  refetchAll: async (queryClient: QueryClient) => {\n    if (process.env.NODE_ENV === 'development') {\n      await queryClient.refetchQueries();\n      console.log('[Dev] All queries refetched');\n    }\n  },\n};\n\n/**\n * Error handling utilities\n */\nexport const queryErrorUtils = {\n  /**\n   * Handle query error with user feedback\n   */\n  handleQueryError: (error: unknown, context?: string) => {\n    const message = errorUtils.getUserMessage(error);\n    console.error(`[Query Error${context ? ` - ${context}` : ''}]`, error);\n    \n    // This will be integrated with notification system\n    // For now, just log the user-friendly message\n    console.log('[User Message]', message);\n    \n    return message;\n  },\n\n  /**\n   * Handle mutation error with user feedback\n   */\n  handleMutationError: (error: unknown, context?: string) => {\n    const message = errorUtils.getUserMessage(error);\n    console.error(`[Mutation Error${context ? ` - ${context}` : ''}]`, error);\n    \n    // This will be integrated with notification system\n    // For now, just log the user-friendly message\n    console.log('[User Message]', message);\n    \n    return message;\n  },\n};\n\n/**\n * Type guards for API responses\n */\nexport const typeGuards = {\n  /**\n   * Check if response is a valid API response\n   */\n  isApiResponse: <T>(data: unknown): data is ApiResponse<T> => {\n    return (\n      typeof data === 'object' &&\n      data !== null &&\n      'data' in data &&\n      'success' in data &&\n      'timestamp' in data\n    );\n  },\n\n  /**\n   * Check if response is a paginated response\n   */\n  isPaginatedResponse: <T>(data: unknown): data is PaginatedResponse<T> => {\n    return (\n      typeGuards.isApiResponse(data) &&\n      'pagination' in data &&\n      typeof (data as any).pagination === 'object'\n    );\n  },\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAGD;AACA;AA+OQ;;;AAlND,MAAM,sBAAsB;IACjC;;GAEC,GACD,UAAU,CAAI,UAAuE,CAAC;YACpF,WAAW,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,KAAK;YACxC,QAAQ,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,MAAM;YACtC,iBAAiB,gIAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC,IAAI;YACnD,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,QAAQ,CAAI,UAAuE,CAAC;YAClF,WAAW,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,SAAS;YAC5C,QAAQ,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,SAAS;YACzC,sBAAsB;YACtB,oBAAoB;YACpB,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,cAAc,CAAI,UAAuE,CAAC;YACxF,WAAW,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,MAAM;YACzC,QAAQ,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,IAAI;YACpC,sBAAsB;YACtB,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,gBAAgB,CAAI,UAAuE,CAAC;YAC1F,WAAW,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,IAAI;YACvC,QAAQ,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,SAAS;YACzC,iBAAiB,gIAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC,IAAI;YACnD,6BAA6B;YAC7B,GAAG,OAAO;QACZ,CAAC;AACH;AAKO,MAAM,yBAAyB;IACpC;;GAEC,GACD,YAAY,CAAO,UAAuG,CAAC;YACzH,OAAO,gIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI;YAC9B,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,UAAU,CAAO,UAAuG,CAAC;YACvH,OAAO,gIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,OAAO;YACjC,YAAY,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;YACjE,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,YAAY,CAAO,UAAuG,CAAC;YACzH,OAAO,gIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,KAAK;YAC/B,GAAG,OAAO;QACZ,CAAC;AACH;AAKO,MAAM,aAAa;IACxB;;GAEC,GACD,qBAAqB,OAAO,aAA0B;QACpD,MAAM,YAAY,iBAAiB,CAAC;YAAE,UAAU;QAAQ;IAC1D;IAEA;;GAEC,GACD,iBAAiB,CAAC,aAA0B;QAC1C,YAAY,aAAa,CAAC;YAAE,UAAU;QAAQ;IAChD;IAEA;;GAEC,GACD,iBAAiB,CACf,aACA,UACA;QAEA,YAAY,YAAY,CAAC,UAAU;IACrC;IAEA;;GAEC,GACD,sBAAsB,CACpB,aACA,UACA,MACA;QAEA,YAAY,YAAY,CAAM,UAAU,CAAC;YACvC,IAAI,CAAC,SAAS,OAAO,cAAc,QAAQ;gBAAC;aAAK,GAAG,EAAE;YAEtD,OAAQ;gBACN,KAAK;oBACH,OAAO;2BAAI;wBAAS;qBAAK;gBAC3B,KAAK;oBACH,OAAO,QAAQ,GAAG,CAAC,CAAC,WAClB,SAAS,EAAE,KAAK,KAAK,EAAE,GAAG;4BAAE,GAAG,QAAQ;4BAAE,GAAG,IAAI;wBAAC,IAAI;gBAEzD,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAC,WAAa,SAAS,EAAE,KAAK,KAAK,EAAE;gBAC7D;oBACE,OAAO;YACX;QACF;IACF;IAEA;;GAEC,GACD,2BAA2B,CACzB,aACA,UACA,MACA;QAEA,YAAY,YAAY,CAAuB,UAAU,CAAC;YACxD,IAAI,CAAC,SAAS,OAAO;YAErB,MAAM,cAAc,WAAW,oBAAoB,CACjD,aACA;gBAAC;aAAO,EACR,MACA;YAGF,OAAO;gBACL,GAAG,OAAO;gBACV,MAAM,eAAe,QAAQ,IAAI;YACnC;QACF;IACF;AACF;AAKO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,cAAc,CAAC,aAA0B;QACvC,OAAO,UAAU,IAAI,CAAC,CAAC;YACrB,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,OAAO,gBAAgB;QAChC;IACF;IAEA;;GAEC,GACD,cAAc,CAAC,aAA0B;QACvC,OAAO,UAAU,IAAI,CAAC,CAAC;YACrB,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,OAAO,WAAW;QAC3B;IACF;IAEA;;GAEC,GACD,cAAc,CAAC,aAA0B;QACvC,OAAO,UACJ,GAAG,CAAC,CAAC;YACJ,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,OAAO;QAChB,GACC,MAAM,CAAC,CAAC,QAA6B,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD,EAAE;IACrD;IAEA;;GAEC,GACD,SAAS,CAAC,aAA0B;QAClC,MAAM,QAAQ,YAAY,aAAa,CAAC;QACxC,OAAO,QAAQ,MAAM,OAAO,GAAG;IACjC;AACF;AAKO,MAAM,WAAW;IACtB;;GAEC,GACD,eAAe,CAAC;QACd,wCAA4C;YAC1C,MAAM,QAAQ,YAAY,aAAa;YACvC,QAAQ,GAAG,CAAC,iBAAiB;gBAC3B,SAAS,MAAM,MAAM,GAAG,MAAM;gBAC9B,OAAO,MAAM,MAAM,GAAG,GAAG,CAAC,CAAC,QAAU,CAAC;wBACpC,KAAK,MAAM,QAAQ;wBACnB,QAAQ,MAAM,KAAK,CAAC,MAAM;wBAC1B,eAAe,MAAM,KAAK,CAAC,aAAa;wBACxC,OAAO,MAAM,KAAK,CAAC,KAAK;oBAC1B,CAAC;YACH;QACF;IACF;IAEA;;GAEC,GACD,eAAe,CAAC;QACd,wCAA4C;YAC1C,YAAY,KAAK;YACjB,QAAQ,GAAG,CAAC;QACd;IACF;IAEA;;GAEC,GACD,YAAY,OAAO;QACjB,wCAA4C;YAC1C,MAAM,YAAY,cAAc;YAChC,QAAQ,GAAG,CAAC;QACd;IACF;AACF;AAKO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,kBAAkB,CAAC,OAAgB;QACjC,MAAM,UAAU,0IAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QAC1C,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,GAAG,EAAE,SAAS,GAAG,GAAG,CAAC,CAAC,EAAE;QAEhE,mDAAmD;QACnD,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,kBAAkB;QAE9B,OAAO;IACT;IAEA;;GAEC,GACD,qBAAqB,CAAC,OAAgB;QACpC,MAAM,UAAU,0IAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QAC1C,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,UAAU,CAAC,GAAG,EAAE,SAAS,GAAG,GAAG,CAAC,CAAC,EAAE;QAEnE,mDAAmD;QACnD,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,kBAAkB;QAE9B,OAAO;IACT;AACF;AAKO,MAAM,aAAa;IACxB;;GAEC,GACD,eAAe,CAAI;QACjB,OACE,OAAO,SAAS,YAChB,SAAS,QACT,UAAU,QACV,aAAa,QACb,eAAe;IAEnB;IAEA;;GAEC,GACD,qBAAqB,CAAI;QACvB,OACE,WAAW,aAAa,CAAC,SACzB,gBAAgB,QAChB,OAAO,AAAC,KAAa,UAAU,KAAK;IAExC;AACF"}}, {"offset": {"line": 1614, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1620, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/base-hooks.ts"], "sourcesContent": ["/**\n * Base API Hooks\n * Foundation hooks for API operations with TanStack Query\n */\n\n'use client';\n\nimport { \n  useQuery, \n  useMutation, \n  useQueryClient,\n  UseQueryOptions,\n  UseMutationOptions \n} from '@tanstack/react-query';\nimport { \n  queryOptionsBuilder, \n  mutationOptionsBuilder,\n  ApiResponse,\n  PaginatedResponse,\n  BaseQueryOptions,\n  BaseMutationOptions\n} from '@/lib/query-utils';\nimport { ApiError, isApiError, errorUtils } from '@/lib/query-error-handler';\n\n/**\n * Base query hook with error handling and type safety\n */\nexport function useBaseQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<TData>,\n  options?: BaseQueryOptions<TData>\n) {\n  return useQuery({\n    queryKey,\n    queryFn,\n    ...options,\n    onError: (error: unknown) => {\n      console.error(`[Query Error] ${queryKey.join(' → ')}:`, error);\n      if (options?.onError) {\n        options.onError(error as ApiError);\n      }\n    },\n  });\n}\n\n/**\n * Base mutation hook with error handling and type safety\n */\nexport function useBaseMutation<TData, TVariables>(\n  mutationFn: (variables: TVariables) => Promise<TData>,\n  options?: BaseMutationOptions<TData, TVariables>\n) {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn,\n    ...options,\n    onError: (error: unknown, variables: TVariables, context: unknown) => {\n      console.error('[Mutation Error]:', error);\n      if (options?.onError) {\n        options.onError(error as ApiError, variables, context);\n      }\n    },\n    onSuccess: (data: TData, variables: TVariables, context: unknown) => {\n      if (options?.onSuccess) {\n        options.onSuccess(data, variables, context);\n      }\n    },\n  });\n}\n\n/**\n * Paginated query hook for list endpoints\n */\nexport function usePaginatedQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<PaginatedResponse<TData>>,\n  options?: BaseQueryOptions<PaginatedResponse<TData>>\n) {\n  return useBaseQuery(queryKey, queryFn, {\n    ...queryOptionsBuilder.userSpecific(),\n    ...options,\n  });\n}\n\n/**\n * Real-time query hook for frequently updated data\n */\nexport function useRealTimeQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<TData>,\n  options?: BaseQueryOptions<TData>\n) {\n  return useBaseQuery(queryKey, queryFn, {\n    ...queryOptionsBuilder.realTime(),\n    ...options,\n  });\n}\n\n/**\n * Static query hook for rarely changing data\n */\nexport function useStaticQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<TData>,\n  options?: BaseQueryOptions<TData>\n) {\n  return useBaseQuery(queryKey, queryFn, {\n    ...queryOptionsBuilder.static(),\n    ...options,\n  });\n}\n\n/**\n * Background sync query hook for data that updates in background\n */\nexport function useBackgroundSyncQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<TData>,\n  options?: BaseQueryOptions<TData>\n) {\n  return useBaseQuery(queryKey, queryFn, {\n    ...queryOptionsBuilder.backgroundSync(),\n    ...options,\n  });\n}\n\n/**\n * Optimistic mutation hook for immediate UI updates\n */\nexport function useOptimisticMutation<TData, TVariables>(\n  mutationFn: (variables: TVariables) => Promise<TData>,\n  options?: BaseMutationOptions<TData, TVariables>\n) {\n  return useBaseMutation(mutationFn, {\n    ...mutationOptionsBuilder.optimistic(),\n    ...options,\n  });\n}\n\n/**\n * Critical mutation hook for important operations with retries\n */\nexport function useCriticalMutation<TData, TVariables>(\n  mutationFn: (variables: TVariables) => Promise<TData>,\n  options?: BaseMutationOptions<TData, TVariables>\n) {\n  return useBaseMutation(mutationFn, {\n    ...mutationOptionsBuilder.critical(),\n    ...options,\n  });\n}\n\n/**\n * Background mutation hook for non-critical operations\n */\nexport function useBackgroundMutation<TData, TVariables>(\n  mutationFn: (variables: TVariables) => Promise<TData>,\n  options?: BaseMutationOptions<TData, TVariables>\n) {\n  return useBaseMutation(mutationFn, {\n    ...mutationOptionsBuilder.background(),\n    ...options,\n  });\n}\n\n/**\n * Hook utilities for common operations\n */\nexport const useApiHookUtils = () => {\n  const queryClient = useQueryClient();\n\n  return {\n    /**\n     * Invalidate queries by pattern\n     */\n    invalidateQueries: (queryKey: readonly unknown[]) => {\n      return queryClient.invalidateQueries({ queryKey });\n    },\n\n    /**\n     * Remove queries from cache\n     */\n    removeQueries: (queryKey: readonly unknown[]) => {\n      return queryClient.removeQueries({ queryKey });\n    },\n\n    /**\n     * Update query data optimistically\n     */\n    updateQueryData: <T>(queryKey: readonly unknown[], updater: (oldData: T | undefined) => T) => {\n      queryClient.setQueryData(queryKey, updater);\n    },\n\n    /**\n     * Get cached query data\n     */\n    getQueryData: <T>(queryKey: readonly unknown[]): T | undefined => {\n      return queryClient.getQueryData(queryKey);\n    },\n\n    /**\n     * Prefetch query data\n     */\n    prefetchQuery: <T>(queryKey: readonly unknown[], queryFn: () => Promise<T>) => {\n      return queryClient.prefetchQuery({ queryKey, queryFn });\n    },\n\n    /**\n     * Check if query is loading\n     */\n    isQueryLoading: (queryKey: readonly unknown[]): boolean => {\n      const query = queryClient.getQueryState(queryKey);\n      return query?.fetchStatus === 'fetching';\n    },\n\n    /**\n     * Get query error\n     */\n    getQueryError: (queryKey: readonly unknown[]): ApiError | null => {\n      const query = queryClient.getQueryState(queryKey);\n      return isApiError(query?.error) ? query.error : null;\n    },\n\n    /**\n     * Handle API error with user feedback\n     */\n    handleApiError: (error: unknown, context?: string): string => {\n      return errorUtils.getUserMessage(error);\n    },\n  };\n};\n\n/**\n * Hook for API status monitoring\n */\nexport const useApiStatus = () => {\n  const queryClient = useQueryClient();\n\n  return {\n    /**\n     * Get overall API status\n     */\n    getApiStatus: () => {\n      const queries = queryClient.getQueryCache().getAll();\n      const totalQueries = queries.length;\n      const loadingQueries = queries.filter(q => q.state.fetchStatus === 'fetching').length;\n      const errorQueries = queries.filter(q => q.state.status === 'error').length;\n      const successQueries = queries.filter(q => q.state.status === 'success').length;\n\n      return {\n        total: totalQueries,\n        loading: loadingQueries,\n        error: errorQueries,\n        success: successQueries,\n        isLoading: loadingQueries > 0,\n        hasErrors: errorQueries > 0,\n        healthScore: totalQueries > 0 ? (successQueries / totalQueries) * 100 : 100,\n      };\n    },\n\n    /**\n     * Get queries by status\n     */\n    getQueriesByStatus: (status: 'loading' | 'error' | 'success' | 'idle') => {\n      const queries = queryClient.getQueryCache().getAll();\n      \n      switch (status) {\n        case 'loading':\n          return queries.filter(q => q.state.fetchStatus === 'fetching');\n        case 'error':\n          return queries.filter(q => q.state.status === 'error');\n        case 'success':\n          return queries.filter(q => q.state.status === 'success');\n        case 'idle':\n          return queries.filter(q => q.state.fetchStatus === 'idle');\n        default:\n          return [];\n      }\n    },\n\n    /**\n     * Clear all errors\n     */\n    clearAllErrors: () => {\n      const errorQueries = queryClient.getQueryCache().getAll()\n        .filter(q => q.state.status === 'error');\n      \n      errorQueries.forEach(query => {\n        queryClient.resetQueries({ queryKey: query.queryKey });\n      });\n    },\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAWD;AAQA;AAfA;AAAA;AAAA;;AAFA;;;;AAsBO,SAAS,aACd,QAA4B,EAC5B,OAA6B,EAC7B,OAAiC;;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd;QACA;QACA,GAAG,OAAO;QACV,OAAO;qCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE;gBACxD,IAAI,SAAS,SAAS;oBACpB,QAAQ,OAAO,CAAC;gBAClB;YACF;;IACF;AACF;GAhBgB;;QAKP,8KAAA,CAAA,WAAQ;;;AAgBV,SAAS,gBACd,UAAqD,EACrD,OAAgD;;IAEhD,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB;QACA,GAAG,OAAO;QACV,OAAO;2CAAE,CAAC,OAAgB,WAAuB;gBAC/C,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,IAAI,SAAS,SAAS;oBACpB,QAAQ,OAAO,CAAC,OAAmB,WAAW;gBAChD;YACF;;QACA,SAAS;2CAAE,CAAC,MAAa,WAAuB;gBAC9C,IAAI,SAAS,WAAW;oBACtB,QAAQ,SAAS,CAAC,MAAM,WAAW;gBACrC;YACF;;IACF;AACF;IArBgB;;QAIM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAoBb,SAAS,kBACd,QAA4B,EAC5B,OAAgD,EAChD,OAAoD;;IAEpD,OAAO,aAAa,UAAU,SAAS;QACrC,GAAG,+HAAA,CAAA,sBAAmB,CAAC,YAAY,EAAE;QACrC,GAAG,OAAO;IACZ;AACF;IATgB;;QAKP;;;AASF,SAAS,iBACd,QAA4B,EAC5B,OAA6B,EAC7B,OAAiC;;IAEjC,OAAO,aAAa,UAAU,SAAS;QACrC,GAAG,+HAAA,CAAA,sBAAmB,CAAC,QAAQ,EAAE;QACjC,GAAG,OAAO;IACZ;AACF;IATgB;;QAKP;;;AASF,SAAS,eACd,QAA4B,EAC5B,OAA6B,EAC7B,OAAiC;;IAEjC,OAAO,aAAa,UAAU,SAAS;QACrC,GAAG,+HAAA,CAAA,sBAAmB,CAAC,MAAM,EAAE;QAC/B,GAAG,OAAO;IACZ;AACF;IATgB;;QAKP;;;AASF,SAAS,uBACd,QAA4B,EAC5B,OAA6B,EAC7B,OAAiC;;IAEjC,OAAO,aAAa,UAAU,SAAS;QACrC,GAAG,+HAAA,CAAA,sBAAmB,CAAC,cAAc,EAAE;QACvC,GAAG,OAAO;IACZ;AACF;IATgB;;QAKP;;;AASF,SAAS,sBACd,UAAqD,EACrD,OAAgD;;IAEhD,OAAO,gBAAgB,YAAY;QACjC,GAAG,+HAAA,CAAA,yBAAsB,CAAC,UAAU,EAAE;QACtC,GAAG,OAAO;IACZ;AACF;IARgB;;QAIP;;;AASF,SAAS,oBACd,UAAqD,EACrD,OAAgD;;IAEhD,OAAO,gBAAgB,YAAY;QACjC,GAAG,+HAAA,CAAA,yBAAsB,CAAC,QAAQ,EAAE;QACpC,GAAG,OAAO;IACZ;AACF;IARgB;;QAIP;;;AASF,SAAS,sBACd,UAAqD,EACrD,OAAgD;;IAEhD,OAAO,gBAAgB,YAAY;QACjC,GAAG,+HAAA,CAAA,yBAAsB,CAAC,UAAU,EAAE;QACtC,GAAG,OAAO;IACZ;AACF;IARgB;;QAIP;;;AASF,MAAM,kBAAkB;;IAC7B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO;QACL;;KAEC,GACD,mBAAmB,CAAC;YAClB,OAAO,YAAY,iBAAiB,CAAC;gBAAE;YAAS;QAClD;QAEA;;KAEC,GACD,eAAe,CAAC;YACd,OAAO,YAAY,aAAa,CAAC;gBAAE;YAAS;QAC9C;QAEA;;KAEC,GACD,iBAAiB,CAAI,UAA8B;YACjD,YAAY,YAAY,CAAC,UAAU;QACrC;QAEA;;KAEC,GACD,cAAc,CAAI;YAChB,OAAO,YAAY,YAAY,CAAC;QAClC;QAEA;;KAEC,GACD,eAAe,CAAI,UAA8B;YAC/C,OAAO,YAAY,aAAa,CAAC;gBAAE;gBAAU;YAAQ;QACvD;QAEA;;KAEC,GACD,gBAAgB,CAAC;YACf,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,OAAO,gBAAgB;QAChC;QAEA;;KAEC,GACD,eAAe,CAAC;YACd,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS,MAAM,KAAK,GAAG;QAClD;QAEA;;KAEC,GACD,gBAAgB,CAAC,OAAgB;YAC/B,OAAO,0IAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QACnC;IACF;AACF;IA9Da;;QACS,yLAAA,CAAA,iBAAc;;;AAkE7B,MAAM,eAAe;;IAC1B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO;QACL;;KAEC,GACD,cAAc;YACZ,MAAM,UAAU,YAAY,aAAa,GAAG,MAAM;YAClD,MAAM,eAAe,QAAQ,MAAM;YACnC,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,WAAW,KAAK,YAAY,MAAM;YACrF,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK,SAAS,MAAM;YAC3E,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK,WAAW,MAAM;YAE/E,OAAO;gBACL,OAAO;gBACP,SAAS;gBACT,OAAO;gBACP,SAAS;gBACT,WAAW,iBAAiB;gBAC5B,WAAW,eAAe;gBAC1B,aAAa,eAAe,IAAI,AAAC,iBAAiB,eAAgB,MAAM;YAC1E;QACF;QAEA;;KAEC,GACD,oBAAoB,CAAC;YACnB,MAAM,UAAU,YAAY,aAAa,GAAG,MAAM;YAElD,OAAQ;gBACN,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,WAAW,KAAK;gBACrD,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK;gBAChD,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK;gBAChD,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,WAAW,KAAK;gBACrD;oBACE,OAAO,EAAE;YACb;QACF;QAEA;;KAEC,GACD,gBAAgB;YACd,MAAM,eAAe,YAAY,aAAa,GAAG,MAAM,GACpD,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK;YAElC,aAAa,OAAO,CAAC,CAAA;gBACnB,YAAY,YAAY,CAAC;oBAAE,UAAU,MAAM,QAAQ;gBAAC;YACtD;QACF;IACF;AACF;KAzDa;;QACS,yLAAA,CAAA,iBAAc"}}, {"offset": {"line": 1899, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1905, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/auth-hooks.ts"], "sourcesContent": ["/**\n * Authentication API Hooks\n * Hooks for system authentication operations\n */\n\n'use client';\n\nimport { queryKeys } from '@/lib/query-client';\nimport { AuthQueries } from '@/lib/query-types';\nimport { useBaseQuery, useBaseMutation, useApiHookUtils } from './base-hooks';\n\n/**\n * Hook for user login\n */\nexport function useLogin() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<AuthQueries.LoginResponse, AuthQueries.LoginRequest>(\n    async (credentials) => {\n      const response = await fetch('/api/system-auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(credentials),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Login failed: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: (data) => {\n        // Invalidate auth queries on successful login\n        invalidateQueries(queryKeys.auth.all);\n        console.log('✅ Login successful:', data.user.username);\n      },\n      onError: (error) => {\n        console.error('❌ Login failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for user logout\n */\nexport function useLogout() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string }, void>(\n    async () => {\n      const response = await fetch('/api/system-auth/logout', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Logout failed: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Clear all auth-related queries on logout\n        invalidateQueries(queryKeys.auth.all);\n        console.log('✅ Logout successful');\n      },\n      onError: (error) => {\n        console.error('❌ Logout failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for logout from all devices\n */\nexport function useLogoutAll() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string }, void>(\n    async () => {\n      const response = await fetch('/api/system-auth/logout-all', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Logout all failed: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Clear all auth-related queries on logout all\n        invalidateQueries(queryKeys.auth.all);\n        console.log('✅ Logout all successful');\n      },\n      onError: (error) => {\n        console.error('❌ Logout all failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for getting user profile\n */\nexport function useProfile() {\n  return useBaseQuery(\n    queryKeys.auth.profile(),\n    async (): Promise<AuthQueries.ProfileResponse> => {\n      const response = await fetch('/api/system-auth/profile');\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch profile: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: false, // Disable by default, enable when user is authenticated\n      staleTime: 5 * 60 * 1000, // 5 minutes\n      retry: (failureCount, error: any) => {\n        // Don't retry on 401 (unauthorized)\n        if (error?.status === 401) return false;\n        return failureCount < 2;\n      },\n    }\n  );\n}\n\n/**\n * Hook for updating user profile\n */\nexport function useUpdateProfile() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<AuthQueries.ProfileResponse, AuthQueries.UpdateProfileRequest>(\n    async (data) => {\n      const response = await fetch('/api/system-auth/profile', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to update profile: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Invalidate profile query to refetch updated data\n        invalidateQueries(queryKeys.auth.profile());\n        console.log('✅ Profile updated successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Profile update failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for changing password\n */\nexport function useChangePassword() {\n  return useBaseMutation<{ message: string }, AuthQueries.ChangePasswordRequest>(\n    async (data) => {\n      const response = await fetch('/api/system-auth/change-password', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to change password: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        console.log('✅ Password changed successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Password change failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for creating new system user (Admin only)\n */\nexport function useCreateUser() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<AuthQueries.ProfileResponse, AuthQueries.CreateUserRequest>(\n    async (data) => {\n      const response = await fetch('/api/system-auth/users', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to create user: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Invalidate users list to show new user\n        invalidateQueries(queryKeys.auth.users());\n        console.log('✅ User created successfully');\n      },\n      onError: (error) => {\n        console.error('❌ User creation failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for getting system users list (Admin only)\n */\nexport function useSystemUsers() {\n  return useBaseQuery(\n    queryKeys.auth.users(),\n    async (): Promise<AuthQueries.ProfileResponse[]> => {\n      const response = await fetch('/api/system-auth/users');\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch users: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: false, // Enable only for Admin users\n      staleTime: 2 * 60 * 1000, // 2 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting specific system user (Admin only)\n */\nexport function useSystemUser(userId: string) {\n  return useBaseQuery(\n    queryKeys.auth.user(userId),\n    async (): Promise<AuthQueries.ProfileResponse> => {\n      const response = await fetch(`/api/system-auth/users/${userId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch user: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!userId, // Only fetch if userId is provided\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    }\n  );\n}\n\n/**\n * Composite hook for authentication state and actions\n */\nexport function useAuth() {\n  const login = useLogin();\n  const logout = useLogout();\n  const logoutAll = useLogoutAll();\n  const profile = useProfile();\n  const updateProfile = useUpdateProfile();\n  const changePassword = useChangePassword();\n  const createUser = useCreateUser();\n\n  return {\n    // Queries\n    profile,\n    \n    // Mutations\n    login,\n    logout,\n    logoutAll,\n    updateProfile,\n    changePassword,\n    createUser,\n    \n    // Computed state\n    isAuthenticated: !!profile.data,\n    user: profile.data,\n    isLoading: profile.isLoading || login.isPending || logout.isPending,\n    error: profile.error || login.error || logout.error,\n    \n    // Actions\n    loginUser: login.mutate,\n    logoutUser: logout.mutate,\n    logoutAllDevices: logoutAll.mutate,\n    updateUserProfile: updateProfile.mutate,\n    changeUserPassword: changePassword.mutate,\n    createNewUser: createUser.mutate,\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;AAID;AAEA;;AAJA;;;AASO,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;oCACnB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,UAAU,EAAE;YACxD;YAEA,OAAO,SAAS,IAAI;QACtB;mCACA;QACE,SAAS;wCAAE,CAAC;gBACV,8CAA8C;gBAC9C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,GAAG;gBACpC,QAAQ,GAAG,CAAC,uBAAuB,KAAK,IAAI,CAAC,QAAQ;YACvD;;QACA,OAAO;wCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,mBAAmB;YACnC;;IACF;AAEJ;GA9BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AAgCjB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;qCACnB;YACE,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,SAAS,UAAU,EAAE;YACzD;YAEA,OAAO,SAAS,IAAI;QACtB;oCACA;QACE,SAAS;yCAAE;gBACT,2CAA2C;gBAC3C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,GAAG;gBACpC,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;yCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,oBAAoB;YACpC;;IACF;AAEJ;IA7BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AA+BjB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;wCACnB;YACE,MAAM,WAAW,MAAM,MAAM,+BAA+B;gBAC1D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,SAAS,UAAU,EAAE;YAC7D;YAEA,OAAO,SAAS,IAAI;QACtB;uCACA;QACE,SAAS;4CAAE;gBACT,+CAA+C;gBAC/C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,GAAG;gBACpC,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;4CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,wBAAwB;YACxC;;IACF;AAEJ;IA7BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AA+BjB,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,OAAO;mCACtB;YACE,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,OAAO,SAAS,IAAI;QACtB;kCACA;QACE,SAAS;QACT,WAAW,IAAI,KAAK;QACpB,KAAK;uCAAE,CAAC,cAAc;gBACpB,oCAAoC;gBACpC,IAAI,OAAO,WAAW,KAAK,OAAO;gBAClC,OAAO,eAAe;YACxB;;IACF;AAEJ;IAtBgB;;QACP,uIAAA,CAAA,eAAY;;;AA0Bd,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;4CACnB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;YACpE;YAEA,OAAO,SAAS,IAAI;QACtB;2CACA;QACE,SAAS;gDAAE;gBACT,mDAAmD;gBACnD,kBAAkB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,OAAO;gBACxC,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;gDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,4BAA4B;YAC5C;;IACF;AAEJ;IA9BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AAgCjB,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;6CACnB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,oCAAoC;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,SAAS,UAAU,EAAE;YACrE;YAEA,OAAO,SAAS,IAAI;QACtB;4CACA;QACE,SAAS;iDAAE;gBACT,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;iDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,6BAA6B;YAC7C;;IACF;AAEJ;IA1BgB;;QACP,uIAAA,CAAA,kBAAe;;;AA8BjB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;yCACnB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;YACjE;YAEA,OAAO,SAAS,IAAI;QACtB;wCACA;QACE,SAAS;6CAAE;gBACT,yCAAyC;gBACzC,kBAAkB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,KAAK;gBACtC,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;6CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;;IACF;AAEJ;IA9BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AAgCjB,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,KAAK;uCACpB;YACE,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;YACjE;YAEA,OAAO,SAAS,IAAI;QACtB;sCACA;QACE,SAAS;QACT,WAAW,IAAI,KAAK;IACtB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS,cAAc,MAAc;;IAC1C,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI,CAAC;sCACpB;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,QAAQ;YAE/D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;YAChE;YAEA,OAAO,SAAS,IAAI;QACtB;qCACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS;;IACd,MAAM,QAAQ;IACd,MAAM,SAAS;IACf,MAAM,YAAY;IAClB,MAAM,UAAU;IAChB,MAAM,gBAAgB;IACtB,MAAM,iBAAiB;IACvB,MAAM,aAAa;IAEnB,OAAO;QACL,UAAU;QACV;QAEA,YAAY;QACZ;QACA;QACA;QACA;QACA;QACA;QAEA,iBAAiB;QACjB,iBAAiB,CAAC,CAAC,QAAQ,IAAI;QAC/B,MAAM,QAAQ,IAAI;QAClB,WAAW,QAAQ,SAAS,IAAI,MAAM,SAAS,IAAI,OAAO,SAAS;QACnE,OAAO,QAAQ,KAAK,IAAI,MAAM,KAAK,IAAI,OAAO,KAAK;QAEnD,UAAU;QACV,WAAW,MAAM,MAAM;QACvB,YAAY,OAAO,MAAM;QACzB,kBAAkB,UAAU,MAAM;QAClC,mBAAmB,cAAc,MAAM;QACvC,oBAAoB,eAAe,MAAM;QACzC,eAAe,WAAW,MAAM;IAClC;AACF;IAnCgB;;QACA;QACC;QACG;QACF;QACM;QACC;QACJ"}}, {"offset": {"line": 2262, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2268, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/football-hooks.ts"], "sourcesContent": ["/**\n * Football Data API Hooks\n * Hooks for football leagues, teams, and fixtures operations\n */\n\n'use client';\n\nimport { queryKeys } from '@/lib/query-client';\nimport { FootballQueries } from '@/lib/query-types';\nimport { PaginatedResponse } from '@/lib/query-utils';\nimport { \n  useBaseQuery, \n  usePaginatedQuery, \n  useBackgroundSyncQuery,\n  useBaseMutation,\n  useApiHookUtils \n} from './base-hooks';\n\n/**\n * Hook for getting football leagues\n */\nexport function useLeagues(params?: FootballQueries.LeagueQueryParams) {\n  const queryParams = new URLSearchParams();\n  if (params?.page) queryParams.set('page', params.page.toString());\n  if (params?.limit) queryParams.set('limit', params.limit.toString());\n  if (params?.country) queryParams.set('country', params.country);\n  if (params?.isActive !== undefined) queryParams.set('isActive', params.isActive.toString());\n  if (params?.query) queryParams.set('query', params.query);\n\n  return usePaginatedQuery(\n    [...queryKeys.football.leagues(), params],\n    async (): Promise<PaginatedResponse<FootballQueries.League>> => {\n      const response = await fetch(`/api/football/leagues?${queryParams.toString()}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch leagues: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 10 * 60 * 1000, // 10 minutes - leagues don't change often\n    }\n  );\n}\n\n/**\n * Hook for getting specific league\n */\nexport function useLeague(leagueId: string) {\n  return useBaseQuery(\n    queryKeys.football.league(leagueId),\n    async (): Promise<FootballQueries.League> => {\n      const response = await fetch(`/api/football/leagues/${leagueId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch league: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!leagueId,\n      staleTime: 10 * 60 * 1000, // 10 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting football teams\n */\nexport function useTeams(params?: FootballQueries.TeamQueryParams) {\n  const queryParams = new URLSearchParams();\n  if (params?.page) queryParams.set('page', params.page.toString());\n  if (params?.limit) queryParams.set('limit', params.limit.toString());\n  if (params?.leagueId) queryParams.set('leagueId', params.leagueId);\n  if (params?.country) queryParams.set('country', params.country);\n  if (params?.query) queryParams.set('query', params.query);\n\n  return usePaginatedQuery(\n    [...queryKeys.football.teams(), params],\n    async (): Promise<PaginatedResponse<FootballQueries.Team>> => {\n      const response = await fetch(`/api/football/teams?${queryParams.toString()}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch teams: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting specific team\n */\nexport function useTeam(teamId: string) {\n  return useBaseQuery(\n    queryKeys.football.team(teamId),\n    async (): Promise<FootballQueries.Team> => {\n      const response = await fetch(`/api/football/teams/${teamId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch team: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!teamId,\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting football fixtures\n */\nexport function useFixtures(params?: FootballQueries.FixtureQueryParams) {\n  const queryParams = new URLSearchParams();\n  if (params?.page) queryParams.set('page', params.page.toString());\n  if (params?.limit) queryParams.set('limit', params.limit.toString());\n  if (params?.leagueId) queryParams.set('leagueId', params.leagueId);\n  if (params?.teamId) queryParams.set('teamId', params.teamId);\n  if (params?.status) queryParams.set('status', params.status);\n  if (params?.dateFrom) queryParams.set('dateFrom', params.dateFrom);\n  if (params?.dateTo) queryParams.set('dateTo', params.dateTo);\n  if (params?.query) queryParams.set('query', params.query);\n\n  return usePaginatedQuery(\n    [...queryKeys.football.fixtures(), params],\n    async (): Promise<PaginatedResponse<FootballQueries.Fixture>> => {\n      const response = await fetch(`/api/football/fixtures?${queryParams.toString()}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch fixtures: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 1 * 60 * 1000, // 1 minute - fixtures change frequently\n    }\n  );\n}\n\n/**\n * Hook for getting specific fixture\n */\nexport function useFixture(fixtureId: string) {\n  return useBaseQuery(\n    queryKeys.football.fixture(fixtureId),\n    async (): Promise<FootballQueries.Fixture> => {\n      const response = await fetch(`/api/football/fixtures/${fixtureId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch fixture: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!fixtureId,\n      staleTime: 30 * 1000, // 30 seconds - live fixtures need frequent updates\n    }\n  );\n}\n\n/**\n * Hook for getting sync status\n */\nexport function useSyncStatus() {\n  return useBackgroundSyncQuery(\n    queryKeys.football.syncStatus(),\n    async (): Promise<FootballQueries.SyncStatus> => {\n      const response = await fetch('/api/football/fixtures/sync/status');\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch sync status: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 30 * 1000, // 30 seconds\n      refetchInterval: 60 * 1000, // Refetch every minute\n    }\n  );\n}\n\n/**\n * Hook for triggering fixtures sync\n */\nexport function useSyncFixtures() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string; syncId: string }, void>(\n    async () => {\n      const response = await fetch('/api/football/fixtures/sync', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to start sync: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Invalidate sync status and fixtures to show updated data\n        invalidateQueries(queryKeys.football.syncStatus());\n        invalidateQueries(queryKeys.football.fixtures());\n        console.log('✅ Fixtures sync started');\n      },\n      onError: (error) => {\n        console.error('❌ Fixtures sync failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for triggering daily sync\n */\nexport function useDailySync() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string; syncId: string }, void>(\n    async () => {\n      const response = await fetch('/api/football/fixtures/sync/daily', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to start daily sync: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Invalidate sync status and fixtures\n        invalidateQueries(queryKeys.football.syncStatus());\n        invalidateQueries(queryKeys.football.fixtures());\n        console.log('✅ Daily sync started');\n      },\n      onError: (error) => {\n        console.error('❌ Daily sync failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Composite hook for football data operations\n */\nexport function useFootball() {\n  const syncFixtures = useSyncFixtures();\n  const dailySync = useDailySync();\n  const syncStatus = useSyncStatus();\n\n  return {\n    // Sync operations\n    syncFixtures,\n    dailySync,\n    syncStatus,\n    \n    // Sync actions\n    startSync: syncFixtures.mutate,\n    startDailySync: dailySync.mutate,\n    \n    // Sync state\n    isSyncing: syncFixtures.isPending || dailySync.isPending,\n    syncError: syncFixtures.error || dailySync.error,\n    lastSyncStatus: syncStatus.data,\n  };\n}\n\n/**\n * Hook for live fixtures (real-time updates)\n */\nexport function useLiveFixtures() {\n  return useFixtures({\n    status: 'live',\n    limit: 50,\n  });\n}\n\n/**\n * Hook for today's fixtures\n */\nexport function useTodayFixtures() {\n  const today = new Date().toISOString().split('T')[0];\n  \n  return useFixtures({\n    dateFrom: today,\n    dateTo: today,\n    limit: 100,\n  });\n}\n\n/**\n * Hook for upcoming fixtures\n */\nexport function useUpcomingFixtures(days: number = 7) {\n  const today = new Date();\n  const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);\n  \n  return useFixtures({\n    dateFrom: today.toISOString().split('T')[0],\n    dateTo: futureDate.toISOString().split('T')[0],\n    status: 'scheduled',\n    limit: 100,\n  });\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;AAID;AAGA;;AALA;;;AAgBO,SAAS,WAAW,MAA0C;;IACnE,MAAM,cAAc,IAAI;IACxB,IAAI,QAAQ,MAAM,YAAY,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,QAAQ,SAAS,YAAY,GAAG,CAAC,WAAW,OAAO,OAAO;IAC9D,IAAI,QAAQ,aAAa,WAAW,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ,CAAC,QAAQ;IACxF,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK;IAExD,OAAO,CAAA,GAAA,uIAAA,CAAA,oBAAiB,AAAD,EACrB;WAAI,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO;QAAI;KAAO;wCACzC;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,YAAY,QAAQ,IAAI;YAE9E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,OAAO,SAAS,IAAI;QACtB;uCACA;QACE,WAAW,KAAK,KAAK;IACvB;AAEJ;GAvBgB;;QAQP,uIAAA,CAAA,oBAAiB;;;AAoBnB,SAAS,UAAU,QAAgB;;IACxC,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;kCAC1B;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,UAAU;YAEhE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;YAClE;YAEA,OAAO,SAAS,IAAI;QACtB;iCACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,KAAK,KAAK;IACvB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS,SAAS,MAAwC;;IAC/D,MAAM,cAAc,IAAI;IACxB,IAAI,QAAQ,MAAM,YAAY,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,QAAQ,UAAU,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ;IACjE,IAAI,QAAQ,SAAS,YAAY,GAAG,CAAC,WAAW,OAAO,OAAO;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK;IAExD,OAAO,CAAA,GAAA,uIAAA,CAAA,oBAAiB,AAAD,EACrB;WAAI,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,KAAK;QAAI;KAAO;sCACvC;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,YAAY,QAAQ,IAAI;YAE5E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;YACjE;YAEA,OAAO,SAAS,IAAI;QACtB;qCACA;QACE,WAAW,IAAI,KAAK;IACtB;AAEJ;IAvBgB;;QAQP,uIAAA,CAAA,oBAAiB;;;AAoBnB,SAAS,QAAQ,MAAc;;IACpC,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;gCACxB;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,QAAQ;YAE5D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;YAChE;YAEA,OAAO,SAAS,IAAI;QACtB;+BACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS,YAAY,MAA2C;;IACrE,MAAM,cAAc,IAAI;IACxB,IAAI,QAAQ,MAAM,YAAY,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,QAAQ,UAAU,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ;IACjE,IAAI,QAAQ,QAAQ,YAAY,GAAG,CAAC,UAAU,OAAO,MAAM;IAC3D,IAAI,QAAQ,QAAQ,YAAY,GAAG,CAAC,UAAU,OAAO,MAAM;IAC3D,IAAI,QAAQ,UAAU,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ;IACjE,IAAI,QAAQ,QAAQ,YAAY,GAAG,CAAC,UAAU,OAAO,MAAM;IAC3D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK;IAExD,OAAO,CAAA,GAAA,uIAAA,CAAA,oBAAiB,AAAD,EACrB;WAAI,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,QAAQ;QAAI;KAAO;yCAC1C;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,YAAY,QAAQ,IAAI;YAE/E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;YACpE;YAEA,OAAO,SAAS,IAAI;QACtB;wCACA;QACE,WAAW,IAAI,KAAK;IACtB;AAEJ;IA1BgB;;QAWP,uIAAA,CAAA,oBAAiB;;;AAoBnB,SAAS,WAAW,SAAiB;;IAC1C,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;mCAC3B;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,WAAW;YAElE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,OAAO,SAAS,IAAI;QACtB;kCACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,KAAK;IAClB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAC1B,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,UAAU;gDAC7B;YACE,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,SAAS,UAAU,EAAE;YACvE;YAEA,OAAO,SAAS,IAAI;QACtB;+CACA;QACE,WAAW,KAAK;QAChB,iBAAiB,KAAK;IACxB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,yBAAsB;;;AAqBxB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;2CACnB;YACE,MAAM,WAAW,MAAM,MAAM,+BAA+B;gBAC1D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;YAChE;YAEA,OAAO,SAAS,IAAI;QACtB;0CACA;QACE,SAAS;+CAAE;gBACT,2DAA2D;gBAC3D,kBAAkB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,UAAU;gBAC/C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,QAAQ;gBAC7C,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;+CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;;IACF;AAEJ;IA9BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AAgCjB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;wCACnB;YACE,MAAM,WAAW,MAAM,MAAM,qCAAqC;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,SAAS,UAAU,EAAE;YACtE;YAEA,OAAO,SAAS,IAAI;QACtB;uCACA;QACE,SAAS;4CAAE;gBACT,sCAAsC;gBACtC,kBAAkB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,UAAU;gBAC/C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,QAAQ;gBAC7C,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;4CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,wBAAwB;YACxC;;IACF;AAEJ;IA9BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AAgCjB,SAAS;;IACd,MAAM,eAAe;IACrB,MAAM,YAAY;IAClB,MAAM,aAAa;IAEnB,OAAO;QACL,kBAAkB;QAClB;QACA;QACA;QAEA,eAAe;QACf,WAAW,aAAa,MAAM;QAC9B,gBAAgB,UAAU,MAAM;QAEhC,aAAa;QACb,WAAW,aAAa,SAAS,IAAI,UAAU,SAAS;QACxD,WAAW,aAAa,KAAK,IAAI,UAAU,KAAK;QAChD,gBAAgB,WAAW,IAAI;IACjC;AACF;IApBgB;;QACO;QACH;QACC;;;AAsBd,SAAS;;IACd,OAAO,YAAY;QACjB,QAAQ;QACR,OAAO;IACT;AACF;KALgB;;QACP;;;AASF,SAAS;;IACd,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAEpD,OAAO,YAAY;QACjB,UAAU;QACV,QAAQ;QACR,OAAO;IACT;AACF;KARgB;;QAGP;;;AAUF,SAAS,oBAAoB,OAAe,CAAC;;IAClD,MAAM,QAAQ,IAAI;IAClB,MAAM,aAAa,IAAI,KAAK,MAAM,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK;IAEpE,OAAO,YAAY;QACjB,UAAU,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC3C,QAAQ,WAAW,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC9C,QAAQ;QACR,OAAO;IACT;AACF;KAVgB;;QAIP"}}, {"offset": {"line": 2606, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2612, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/broadcast-hooks.ts"], "sourcesContent": ["/**\n * Broadcast Links API Hooks\n * Hooks for broadcast links management operations\n */\n\n'use client';\n\nimport { queryKeys } from '@/lib/query-client';\nimport { BroadcastQueries } from '@/lib/query-types';\nimport { PaginatedResponse } from '@/lib/query-utils';\nimport { \n  useBaseQuery, \n  usePaginatedQuery,\n  useBaseMutation,\n  useOptimisticMutation,\n  useApiHookUtils \n} from './base-hooks';\n\n/**\n * Hook for getting broadcast links\n */\nexport function useBroadcastLinks(params?: BroadcastQueries.BroadcastLinkQueryParams) {\n  const queryParams = new URLSearchParams();\n  if (params?.page) queryParams.set('page', params.page.toString());\n  if (params?.limit) queryParams.set('limit', params.limit.toString());\n  if (params?.fixtureId) queryParams.set('fixtureId', params.fixtureId);\n  if (params?.quality) queryParams.set('quality', params.quality);\n  if (params?.language) queryParams.set('language', params.language);\n  if (params?.isActive !== undefined) queryParams.set('isActive', params.isActive.toString());\n  if (params?.query) queryParams.set('query', params.query);\n\n  return usePaginatedQuery(\n    [...queryKeys.broadcast.links(), params],\n    async (): Promise<PaginatedResponse<BroadcastQueries.BroadcastLink>> => {\n      const response = await fetch(`/api/broadcast-links?${queryParams.toString()}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch broadcast links: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 2 * 60 * 1000, // 2 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting specific broadcast link\n */\nexport function useBroadcastLink(linkId: string) {\n  return useBaseQuery(\n    queryKeys.broadcast.link(linkId),\n    async (): Promise<BroadcastQueries.BroadcastLink> => {\n      const response = await fetch(`/api/broadcast-links/${linkId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch broadcast link: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!linkId,\n      staleTime: 2 * 60 * 1000, // 2 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting broadcast links for a specific fixture\n */\nexport function useFixtureBroadcastLinks(fixtureId: string) {\n  return useBaseQuery(\n    queryKeys.broadcast.fixture(fixtureId),\n    async (): Promise<BroadcastQueries.BroadcastLink[]> => {\n      const response = await fetch(`/api/broadcast-links/fixture/${fixtureId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch fixture broadcast links: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!fixtureId,\n      staleTime: 1 * 60 * 1000, // 1 minute - links for live fixtures change frequently\n    }\n  );\n}\n\n/**\n * Hook for creating broadcast link\n */\nexport function useCreateBroadcastLink() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useOptimisticMutation<BroadcastQueries.BroadcastLink, BroadcastQueries.CreateBroadcastLinkRequest>(\n    async (data) => {\n      const response = await fetch('/api/broadcast-links', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to create broadcast link: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: (data) => {\n        // Invalidate broadcast links queries\n        invalidateQueries(queryKeys.broadcast.links());\n        invalidateQueries(queryKeys.broadcast.fixture(data.fixtureId));\n        console.log('✅ Broadcast link created successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Failed to create broadcast link:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for updating broadcast link\n */\nexport function useUpdateBroadcastLink() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useOptimisticMutation<BroadcastQueries.BroadcastLink, { id: string; data: BroadcastQueries.UpdateBroadcastLinkRequest }>(\n    async ({ id, data }) => {\n      const response = await fetch(`/api/broadcast-links/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to update broadcast link: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: (data) => {\n        // Invalidate specific link and related queries\n        invalidateQueries(queryKeys.broadcast.link(data.id));\n        invalidateQueries(queryKeys.broadcast.links());\n        invalidateQueries(queryKeys.broadcast.fixture(data.fixtureId));\n        console.log('✅ Broadcast link updated successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Failed to update broadcast link:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for deleting broadcast link\n */\nexport function useDeleteBroadcastLink() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string }, string>(\n    async (linkId) => {\n      const response = await fetch(`/api/broadcast-links/${linkId}`, {\n        method: 'DELETE',\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to delete broadcast link: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: (_, linkId) => {\n        // Invalidate broadcast links queries\n        invalidateQueries(queryKeys.broadcast.links());\n        invalidateQueries(queryKeys.broadcast.link(linkId));\n        console.log('✅ Broadcast link deleted successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Failed to delete broadcast link:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for toggling broadcast link active status\n */\nexport function useToggleBroadcastLinkStatus() {\n  const { invalidateQueries, updateQueryData } = useApiHookUtils();\n\n  return useOptimisticMutation<BroadcastQueries.BroadcastLink, { id: string; isActive: boolean }>(\n    async ({ id, isActive }) => {\n      const response = await fetch(`/api/broadcast-links/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ isActive }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to toggle broadcast link status: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onMutate: async ({ id, isActive }) => {\n        // Optimistically update the link status\n        const linkQueryKey = queryKeys.broadcast.link(id);\n        const previousLink = updateQueryData<BroadcastQueries.BroadcastLink>(\n          linkQueryKey,\n          (old) => old ? { ...old, isActive } : old\n        );\n\n        return { previousLink, linkQueryKey };\n      },\n      onError: (error, variables, context) => {\n        // Revert optimistic update on error\n        if (context?.previousLink && context?.linkQueryKey) {\n          updateQueryData(context.linkQueryKey, () => context.previousLink);\n        }\n        console.error('❌ Failed to toggle broadcast link status:', error);\n      },\n      onSuccess: (data) => {\n        // Invalidate related queries\n        invalidateQueries(queryKeys.broadcast.links());\n        invalidateQueries(queryKeys.broadcast.fixture(data.fixtureId));\n        console.log('✅ Broadcast link status toggled successfully');\n      },\n    }\n  );\n}\n\n/**\n * Composite hook for broadcast links operations\n */\nexport function useBroadcastLinksManager() {\n  const createLink = useCreateBroadcastLink();\n  const updateLink = useUpdateBroadcastLink();\n  const deleteLink = useDeleteBroadcastLink();\n  const toggleStatus = useToggleBroadcastLinkStatus();\n\n  return {\n    // Mutations\n    createLink,\n    updateLink,\n    deleteLink,\n    toggleStatus,\n    \n    // Actions\n    createBroadcastLink: createLink.mutate,\n    updateBroadcastLink: updateLink.mutate,\n    deleteBroadcastLink: deleteLink.mutate,\n    toggleLinkStatus: toggleStatus.mutate,\n    \n    // State\n    isCreating: createLink.isPending,\n    isUpdating: updateLink.isPending,\n    isDeleting: deleteLink.isPending,\n    isToggling: toggleStatus.isPending,\n    isLoading: createLink.isPending || updateLink.isPending || deleteLink.isPending || toggleStatus.isPending,\n    \n    // Errors\n    createError: createLink.error,\n    updateError: updateLink.error,\n    deleteError: deleteLink.error,\n    toggleError: toggleStatus.error,\n  };\n}\n\n/**\n * Hook for broadcast links by quality\n */\nexport function useBroadcastLinksByQuality(quality: 'HD' | 'SD' | 'Mobile') {\n  return useBroadcastLinks({ quality, isActive: true });\n}\n\n/**\n * Hook for broadcast links by language\n */\nexport function useBroadcastLinksByLanguage(language: string) {\n  return useBroadcastLinks({ language, isActive: true });\n}\n\n/**\n * Hook for active broadcast links\n */\nexport function useActiveBroadcastLinks() {\n  return useBroadcastLinks({ isActive: true });\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAID;AAGA;;AALA;;;AAgBO,SAAS,kBAAkB,MAAkD;;IAClF,MAAM,cAAc,IAAI;IACxB,IAAI,QAAQ,MAAM,YAAY,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,QAAQ,WAAW,YAAY,GAAG,CAAC,aAAa,OAAO,SAAS;IACpE,IAAI,QAAQ,SAAS,YAAY,GAAG,CAAC,WAAW,OAAO,OAAO;IAC9D,IAAI,QAAQ,UAAU,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ;IACjE,IAAI,QAAQ,aAAa,WAAW,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ,CAAC,QAAQ;IACxF,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK;IAExD,OAAO,CAAA,GAAA,uIAAA,CAAA,oBAAiB,AAAD,EACrB;WAAI,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;QAAI;KAAO;+CACxC;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,YAAY,QAAQ,IAAI;YAE7E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;YAC3E;YAEA,OAAO,SAAS,IAAI;QACtB;8CACA;QACE,WAAW,IAAI,KAAK;IACtB;AAEJ;GAzBgB;;QAUP,uIAAA,CAAA,oBAAiB;;;AAoBnB,SAAS,iBAAiB,MAAc;;IAC7C,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC;yCACzB;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,QAAQ;YAE7D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,SAAS,UAAU,EAAE;YAC1E;YAEA,OAAO,SAAS,IAAI;QACtB;wCACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS,yBAAyB,SAAiB;;IACxD,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,OAAO,CAAC;iDAC5B;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,6BAA6B,EAAE,WAAW;YAExE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,yCAAyC,EAAE,SAAS,UAAU,EAAE;YACnF;YAEA,OAAO,SAAS,IAAI;QACtB;gDACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,wBAAqB,AAAD;wDACzB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;YAC3E;YAEA,OAAO,SAAS,IAAI;QACtB;uDACA;QACE,SAAS;4DAAE,CAAC;gBACV,qCAAqC;gBACrC,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;gBAC3C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,SAAS;gBAC5D,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;4DAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,sCAAsC;YACtD;;IACF;AAEJ;IA/BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,wBAAqB;;;AAiCvB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,wBAAqB,AAAD;wDACzB,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE;YACjB,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,IAAI,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;YAC3E;YAEA,OAAO,SAAS,IAAI;QACtB;uDACA;QACE,SAAS;4DAAE,CAAC;gBACV,+CAA+C;gBAC/C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE;gBAClD,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;gBAC3C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,SAAS;gBAC5D,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;4DAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,sCAAsC;YACtD;;IACF;AAEJ;IAhCgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,wBAAqB;;;AAkCvB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;kDACnB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,QAAQ,EAAE;gBAC7D,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;YAC3E;YAEA,OAAO,SAAS,IAAI;QACtB;iDACA;QACE,SAAS;sDAAE,CAAC,GAAG;gBACb,qCAAqC;gBACrC,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;gBAC3C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC3C,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;sDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,sCAAsC;YACtD;;IACF;AAEJ;IA3BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AA6BjB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE7D,OAAO,CAAA,GAAA,uIAAA,CAAA,wBAAqB,AAAD;8DACzB,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE;YACrB,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,IAAI,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,wCAAwC,EAAE,SAAS,UAAU,EAAE;YAClF;YAEA,OAAO,SAAS,IAAI;QACtB;6DACA;QACE,QAAQ;kEAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE;gBAC/B,wCAAwC;gBACxC,MAAM,eAAe,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC9C,MAAM,eAAe,gBACnB;uFACA,CAAC,MAAQ,MAAM;4BAAE,GAAG,GAAG;4BAAE;wBAAS,IAAI;;gBAGxC,OAAO;oBAAE;oBAAc;gBAAa;YACtC;;QACA,OAAO;kEAAE,CAAC,OAAO,WAAW;gBAC1B,oCAAoC;gBACpC,IAAI,SAAS,gBAAgB,SAAS,cAAc;oBAClD,gBAAgB,QAAQ,YAAY;8EAAE,IAAM,QAAQ,YAAY;;gBAClE;gBACA,QAAQ,KAAK,CAAC,6CAA6C;YAC7D;;QACA,SAAS;kEAAE,CAAC;gBACV,6BAA6B;gBAC7B,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;gBAC3C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,SAAS;gBAC5D,QAAQ,GAAG,CAAC;YACd;;IACF;AAEJ;IA7CgB;;QACiC,uIAAA,CAAA,kBAAe;QAEvD,uIAAA,CAAA,wBAAqB;;;AA+CvB,SAAS;;IACd,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,eAAe;IAErB,OAAO;QACL,YAAY;QACZ;QACA;QACA;QACA;QAEA,UAAU;QACV,qBAAqB,WAAW,MAAM;QACtC,qBAAqB,WAAW,MAAM;QACtC,qBAAqB,WAAW,MAAM;QACtC,kBAAkB,aAAa,MAAM;QAErC,QAAQ;QACR,YAAY,WAAW,SAAS;QAChC,YAAY,WAAW,SAAS;QAChC,YAAY,WAAW,SAAS;QAChC,YAAY,aAAa,SAAS;QAClC,WAAW,WAAW,SAAS,IAAI,WAAW,SAAS,IAAI,WAAW,SAAS,IAAI,aAAa,SAAS;QAEzG,SAAS;QACT,aAAa,WAAW,KAAK;QAC7B,aAAa,WAAW,KAAK;QAC7B,aAAa,WAAW,KAAK;QAC7B,aAAa,aAAa,KAAK;IACjC;AACF;IAhCgB;;QACK;QACA;QACA;QACE;;;AAiChB,SAAS,2BAA2B,OAA+B;;IACxE,OAAO,kBAAkB;QAAE;QAAS,UAAU;IAAK;AACrD;IAFgB;;QACP;;;AAMF,SAAS,4BAA4B,QAAgB;;IAC1D,OAAO,kBAAkB;QAAE;QAAU,UAAU;IAAK;AACtD;IAFgB;;QACP;;;AAMF,SAAS;;IACd,OAAO,kBAAkB;QAAE,UAAU;IAAK;AAC5C;KAFgB;;QACP"}}, {"offset": {"line": 2957, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2963, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/health-hooks.ts"], "sourcesContent": ["/**\n * Health Check API Hooks\n * Hooks for API health monitoring operations\n */\n\n'use client';\n\nimport { queryKeys } from '@/lib/query-client';\nimport { HealthQueries } from '@/lib/query-types';\nimport { useBackgroundSyncQuery, useBaseQuery } from './base-hooks';\n\n/**\n * Hook for API health check\n */\nexport function useApiHealth() {\n  return useBackgroundSyncQuery(\n    queryKeys.health.api(),\n    async (): Promise<HealthQueries.HealthResponse> => {\n      const response = await fetch('/api/health');\n\n      if (!response.ok) {\n        throw new Error(`Health check failed: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 30 * 1000, // 30 seconds\n      refetchInterval: 60 * 1000, // Refetch every minute\n      retry: 3,\n      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),\n    }\n  );\n}\n\n/**\n * Hook for database health check\n */\nexport function useDatabaseHealth() {\n  return useBaseQuery(\n    [...queryKeys.health.all, 'database'],\n    async (): Promise<{ status: 'up' | 'down'; responseTime: number }> => {\n      const startTime = performance.now();\n      const response = await fetch('/api/health/database');\n      const endTime = performance.now();\n\n      if (!response.ok) {\n        throw new Error(`Database health check failed: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      return {\n        ...data,\n        responseTime: endTime - startTime,\n      };\n    },\n    {\n      staleTime: 30 * 1000, // 30 seconds\n      retry: 2,\n    }\n  );\n}\n\n/**\n * Hook for external API health check\n */\nexport function useExternalApiHealth() {\n  return useBaseQuery(\n    [...queryKeys.health.all, 'external-api'],\n    async (): Promise<{ status: 'up' | 'down'; responseTime: number }> => {\n      const startTime = performance.now();\n      const response = await fetch('/api/health/external-api');\n      const endTime = performance.now();\n\n      if (!response.ok) {\n        throw new Error(`External API health check failed: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      return {\n        ...data,\n        responseTime: endTime - startTime,\n      };\n    },\n    {\n      staleTime: 60 * 1000, // 1 minute\n      retry: 2,\n    }\n  );\n}\n\n/**\n * Hook for comprehensive system health\n */\nexport function useSystemHealth() {\n  const apiHealth = useApiHealth();\n  const dbHealth = useDatabaseHealth();\n  const externalApiHealth = useExternalApiHealth();\n\n  const isLoading = apiHealth.isLoading || dbHealth.isLoading || externalApiHealth.isLoading;\n  const hasErrors = apiHealth.isError || dbHealth.isError || externalApiHealth.isError;\n\n  // Calculate overall health status\n  const getOverallStatus = (): 'healthy' | 'degraded' | 'unhealthy' => {\n    if (hasErrors) return 'unhealthy';\n    \n    const apiStatus = apiHealth.data?.status;\n    const dbStatus = dbHealth.data?.status;\n    const externalStatus = externalApiHealth.data?.status;\n\n    if (apiStatus === 'healthy' && dbStatus === 'up' && externalStatus === 'up') {\n      return 'healthy';\n    }\n    \n    if (apiStatus === 'unhealthy' || dbStatus === 'down') {\n      return 'unhealthy';\n    }\n    \n    return 'degraded';\n  };\n\n  // Calculate average response time\n  const getAverageResponseTime = (): number => {\n    const times = [\n      dbHealth.data?.responseTime,\n      externalApiHealth.data?.responseTime,\n    ].filter((time): time is number => typeof time === 'number');\n\n    if (times.length === 0) return 0;\n    return times.reduce((sum, time) => sum + time, 0) / times.length;\n  };\n\n  return {\n    // Individual health checks\n    api: apiHealth,\n    database: dbHealth,\n    externalApi: externalApiHealth,\n    \n    // Overall status\n    isLoading,\n    hasErrors,\n    overallStatus: getOverallStatus(),\n    averageResponseTime: getAverageResponseTime(),\n    \n    // Health data\n    healthData: {\n      api: apiHealth.data,\n      database: dbHealth.data,\n      externalApi: externalApiHealth.data,\n    },\n    \n    // Error information\n    errors: {\n      api: apiHealth.error,\n      database: dbHealth.error,\n      externalApi: externalApiHealth.error,\n    },\n    \n    // Refetch functions\n    refetchAll: () => {\n      apiHealth.refetch();\n      dbHealth.refetch();\n      externalApiHealth.refetch();\n    },\n  };\n}\n\n/**\n * Hook for monitoring API performance\n */\nexport function useApiPerformance() {\n  const systemHealth = useSystemHealth();\n\n  const getPerformanceMetrics = () => {\n    const { healthData } = systemHealth;\n    \n    return {\n      uptime: healthData.api?.uptime || 0,\n      responseTime: systemHealth.averageResponseTime,\n      status: systemHealth.overallStatus,\n      services: {\n        database: healthData.database?.status || 'unknown',\n        externalApi: healthData.externalApi?.status || 'unknown',\n      },\n      lastCheck: new Date().toISOString(),\n    };\n  };\n\n  return {\n    ...systemHealth,\n    performanceMetrics: getPerformanceMetrics(),\n    \n    // Performance indicators\n    isPerformanceGood: systemHealth.averageResponseTime < 1000, // Less than 1 second\n    isPerformanceFair: systemHealth.averageResponseTime < 3000, // Less than 3 seconds\n    isPerformancePoor: systemHealth.averageResponseTime >= 3000, // 3+ seconds\n  };\n}\n\n/**\n * Hook for health monitoring dashboard\n */\nexport function useHealthDashboard() {\n  const performance = useApiPerformance();\n  \n  const getDashboardData = () => {\n    const { healthData, overallStatus, averageResponseTime } = performance;\n    \n    return {\n      status: overallStatus,\n      uptime: healthData.api?.uptime || 0,\n      version: healthData.api?.version || 'unknown',\n      responseTime: averageResponseTime,\n      services: [\n        {\n          name: 'Database',\n          status: healthData.database?.status || 'unknown',\n          responseTime: healthData.database?.responseTime || 0,\n        },\n        {\n          name: 'External API',\n          status: healthData.externalApi?.status || 'unknown',\n          responseTime: healthData.externalApi?.responseTime || 0,\n        },\n      ],\n      lastUpdated: new Date().toISOString(),\n    };\n  };\n\n  return {\n    ...performance,\n    dashboardData: getDashboardData(),\n    \n    // Dashboard actions\n    refreshDashboard: performance.refetchAll,\n    \n    // Status indicators\n    statusColor: {\n      healthy: '#10b981', // green\n      degraded: '#f59e0b', // yellow\n      unhealthy: '#ef4444', // red\n    }[performance.overallStatus],\n    \n    statusIcon: {\n      healthy: '✅',\n      degraded: '⚠️',\n      unhealthy: '❌',\n    }[performance.overallStatus],\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAID;AAEA;;AAJA;;;AASO,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAC1B,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,GAAG;+CACpB;YACE,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,SAAS,UAAU,EAAE;YAC/D;YAEA,OAAO,SAAS,IAAI;QACtB;8CACA;QACE,WAAW,KAAK;QAChB,iBAAiB,KAAK;QACtB,OAAO;QACP,UAAU;mDAAE,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;;IACnE;AAEJ;GAnBgB;;QACP,uIAAA,CAAA,yBAAsB;;;AAuBxB,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,GAAG;QAAE;KAAW;0CACrC;YACE,MAAM,YAAY,YAAY,GAAG;YACjC,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,UAAU,YAAY,GAAG;YAE/B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,SAAS,UAAU,EAAE;YACxE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBACL,GAAG,IAAI;gBACP,cAAc,UAAU;YAC1B;QACF;yCACA;QACE,WAAW,KAAK;QAChB,OAAO;IACT;AAEJ;IAvBgB;;QACP,uIAAA,CAAA,eAAY;;;AA2Bd,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,GAAG;QAAE;KAAe;6CACzC;YACE,MAAM,YAAY,YAAY,GAAG;YACjC,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,UAAU,YAAY,GAAG;YAE/B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,SAAS,UAAU,EAAE;YAC5E;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBACL,GAAG,IAAI;gBACP,cAAc,UAAU;YAC1B;QACF;4CACA;QACE,WAAW,KAAK;QAChB,OAAO;IACT;AAEJ;IAvBgB;;QACP,uIAAA,CAAA,eAAY;;;AA2Bd,SAAS;;IACd,MAAM,YAAY;IAClB,MAAM,WAAW;IACjB,MAAM,oBAAoB;IAE1B,MAAM,YAAY,UAAU,SAAS,IAAI,SAAS,SAAS,IAAI,kBAAkB,SAAS;IAC1F,MAAM,YAAY,UAAU,OAAO,IAAI,SAAS,OAAO,IAAI,kBAAkB,OAAO;IAEpF,kCAAkC;IAClC,MAAM,mBAAmB;QACvB,IAAI,WAAW,OAAO;QAEtB,MAAM,YAAY,UAAU,IAAI,EAAE;QAClC,MAAM,WAAW,SAAS,IAAI,EAAE;QAChC,MAAM,iBAAiB,kBAAkB,IAAI,EAAE;QAE/C,IAAI,cAAc,aAAa,aAAa,QAAQ,mBAAmB,MAAM;YAC3E,OAAO;QACT;QAEA,IAAI,cAAc,eAAe,aAAa,QAAQ;YACpD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,kCAAkC;IAClC,MAAM,yBAAyB;QAC7B,MAAM,QAAQ;YACZ,SAAS,IAAI,EAAE;YACf,kBAAkB,IAAI,EAAE;SACzB,CAAC,MAAM,CAAC,CAAC,OAAyB,OAAO,SAAS;QAEnD,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;QAC/B,OAAO,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,MAAM,KAAK,MAAM,MAAM;IAClE;IAEA,OAAO;QACL,2BAA2B;QAC3B,KAAK;QACL,UAAU;QACV,aAAa;QAEb,iBAAiB;QACjB;QACA;QACA,eAAe;QACf,qBAAqB;QAErB,cAAc;QACd,YAAY;YACV,KAAK,UAAU,IAAI;YACnB,UAAU,SAAS,IAAI;YACvB,aAAa,kBAAkB,IAAI;QACrC;QAEA,oBAAoB;QACpB,QAAQ;YACN,KAAK,UAAU,KAAK;YACpB,UAAU,SAAS,KAAK;YACxB,aAAa,kBAAkB,KAAK;QACtC;QAEA,oBAAoB;QACpB,YAAY;YACV,UAAU,OAAO;YACjB,SAAS,OAAO;YAChB,kBAAkB,OAAO;QAC3B;IACF;AACF;IAvEgB;;QACI;QACD;QACS;;;AAyErB,SAAS;;IACd,MAAM,eAAe;IAErB,MAAM,wBAAwB;QAC5B,MAAM,EAAE,UAAU,EAAE,GAAG;QAEvB,OAAO;YACL,QAAQ,WAAW,GAAG,EAAE,UAAU;YAClC,cAAc,aAAa,mBAAmB;YAC9C,QAAQ,aAAa,aAAa;YAClC,UAAU;gBACR,UAAU,WAAW,QAAQ,EAAE,UAAU;gBACzC,aAAa,WAAW,WAAW,EAAE,UAAU;YACjD;YACA,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;IAEA,OAAO;QACL,GAAG,YAAY;QACf,oBAAoB;QAEpB,yBAAyB;QACzB,mBAAmB,aAAa,mBAAmB,GAAG;QACtD,mBAAmB,aAAa,mBAAmB,GAAG;QACtD,mBAAmB,aAAa,mBAAmB,IAAI;IACzD;AACF;IA3BgB;;QACO;;;AA+BhB,SAAS;;IACd,MAAM,eAAc;IAEpB,MAAM,mBAAmB;QACvB,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,mBAAmB,EAAE,GAAG;QAE3D,OAAO;YACL,QAAQ;YACR,QAAQ,WAAW,GAAG,EAAE,UAAU;YAClC,SAAS,WAAW,GAAG,EAAE,WAAW;YACpC,cAAc;YACd,UAAU;gBACR;oBACE,MAAM;oBACN,QAAQ,WAAW,QAAQ,EAAE,UAAU;oBACvC,cAAc,WAAW,QAAQ,EAAE,gBAAgB;gBACrD;gBACA;oBACE,MAAM;oBACN,QAAQ,WAAW,WAAW,EAAE,UAAU;oBAC1C,cAAc,WAAW,WAAW,EAAE,gBAAgB;gBACxD;aACD;YACD,aAAa,IAAI,OAAO,WAAW;QACrC;IACF;IAEA,OAAO;QACL,GAAG,YAAW;QACd,eAAe;QAEf,oBAAoB;QACpB,kBAAkB,aAAY,UAAU;QAExC,oBAAoB;QACpB,aAAa,CAAA;YACX,SAAS;YACT,UAAU;YACV,WAAW;QACb,CAAA,CAAC,CAAC,aAAY,aAAa,CAAC;QAE5B,YAAY,CAAA;YACV,SAAS;YACT,UAAU;YACV,WAAW;QACb,CAAA,CAAC,CAAC,aAAY,aAAa,CAAC;IAC9B;AACF;IA/CgB;;QACM"}}, {"offset": {"line": 3210, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3216, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/index.ts"], "sourcesContent": ["/**\n * API Hooks Index\n * Central export for all API hooks\n */\n\n// Base hooks and utilities\nexport * from './base-hooks';\n\n// Domain-specific hooks\nexport * from './auth-hooks';\nexport * from './football-hooks';\nexport * from './broadcast-hooks';\nexport * from './health-hooks';\n\n// Re-export TanStack Query hooks for convenience\nexport {\n  useQuery,\n  useMutation,\n  useQueryClient,\n  useInfiniteQuery,\n} from '@tanstack/react-query';\n\n/**\n * API hooks library metadata\n */\nexport const API_HOOKS_VERSION = '1.0.0';\nexport const API_HOOKS_NAME = 'APISportsGame API Hooks';\n\n/**\n * Quick setup function for API hooks\n */\nexport function setupApiHooks() {\n  console.log(`${API_HOOKS_NAME} v${API_HOOKS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,2BAA2B;;;;;;;;;;;;AAoBpB,MAAM,oBAAoB;AAC1B,MAAM,iBAAiB;AAKvB,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,eAAe,EAAE,EAAE,kBAAkB,YAAY,CAAC;AACnE"}}, {"offset": {"line": 3239, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3258, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-header.tsx"], "sourcesContent": ["/**\n * App Header Component\n * Main header for the APISportsGame CMS\n */\n\n'use client';\n\nimport React from 'react';\nimport { Layout, Space, Button, Dropdown, Avatar, Badge, Tooltip, Typography } from 'antd';\nimport {\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  BellOutlined,\n  UserOutlined,\n  SettingOutlined,\n  LogoutOutlined,\n  SunOutlined,\n  MoonOutlined,\n  GlobalOutlined,\n} from '@ant-design/icons';\nimport { useTheme, useThemeStyles } from '@/theme';\nimport { useAuth } from '@/hooks/api';\n\nconst { Header } = Layout;\nconst { Text } = Typography;\n\n/**\n * App header props\n */\nexport interface AppHeaderProps {\n  sidebarCollapsed: boolean;\n  onSidebarToggle: () => void;\n  isMobile: boolean;\n  showSidebarToggle?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * App Header component\n */\nexport function AppHeader({\n  sidebarCollapsed,\n  onSidebarToggle,\n  isMobile,\n  showSidebarToggle = true,\n  className,\n  style,\n}: AppHeaderProps) {\n  const { theme, toggleTheme, isDark } = useTheme();\n  const themeStyles = useThemeStyles();\n  const auth = useAuth();\n\n  // User menu items\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: 'Profile',\n      onClick: () => console.log('Profile clicked'),\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: 'Settings',\n      onClick: () => console.log('Settings clicked'),\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: 'Logout',\n      onClick: () => auth.logoutUser(),\n      danger: true,\n    },\n  ];\n\n  // Notification menu items\n  const notificationItems = [\n    {\n      key: '1',\n      label: (\n        <div style={{ padding: '8px 0' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>\n            New fixture sync completed\n          </div>\n          <div style={{ fontSize: '12px', color: themeStyles.getTextColor('secondary') }}>\n            2 minutes ago\n          </div>\n        </div>\n      ),\n    },\n    {\n      key: '2',\n      label: (\n        <div style={{ padding: '8px 0' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>\n            User John Doe registered\n          </div>\n          <div style={{ fontSize: '12px', color: themeStyles.getTextColor('secondary') }}>\n            5 minutes ago\n          </div>\n        </div>\n      ),\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'view-all',\n      label: (\n        <div style={{ textAlign: 'center', padding: '8px 0' }}>\n          <Button type=\"link\" size=\"small\">\n            View All Notifications\n          </Button>\n        </div>\n      ),\n    },\n  ];\n\n  const headerStyle: React.CSSProperties = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    zIndex: 1000,\n    height: '64px',\n    padding: '0 24px',\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    ...style,\n  };\n\n  return (\n    <Header className={className} style={headerStyle}>\n      {/* Left section */}\n      <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>\n        {/* Sidebar toggle */}\n        {showSidebarToggle && (\n          <Button\n            type=\"text\"\n            icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n            onClick={onSidebarToggle}\n            style={{\n              fontSize: '16px',\n              width: '40px',\n              height: '40px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n            }}\n          />\n        )}\n\n        {/* Logo and title */}\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <div\n            style={{\n              width: '32px',\n              height: '32px',\n              backgroundColor: themeStyles.getColor('primary'),\n              borderRadius: '6px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontWeight: 'bold',\n              fontSize: '16px',\n            }}\n          >\n            ⚽\n          </div>\n          {(!isMobile || sidebarCollapsed) && (\n            <div>\n              <Text\n                style={{\n                  fontSize: '18px',\n                  fontWeight: 'bold',\n                  color: themeStyles.getTextColor('primary'),\n                }}\n              >\n                APISportsGame\n              </Text>\n              <div\n                style={{\n                  fontSize: '12px',\n                  color: themeStyles.getTextColor('secondary'),\n                  lineHeight: 1,\n                }}\n              >\n                CMS Dashboard\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Right section */}\n      <Space size=\"middle\">\n        {/* Theme toggle */}\n        <Tooltip title={`Switch to ${isDark ? 'light' : 'dark'} mode`}>\n          <Button\n            type=\"text\"\n            icon={isDark ? <SunOutlined /> : <MoonOutlined />}\n            onClick={toggleTheme}\n            style={{\n              fontSize: '16px',\n              width: '40px',\n              height: '40px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n            }}\n          />\n        </Tooltip>\n\n        {/* Language selector */}\n        <Tooltip title=\"Language\">\n          <Button\n            type=\"text\"\n            icon={<GlobalOutlined />}\n            style={{\n              fontSize: '16px',\n              width: '40px',\n              height: '40px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n            }}\n          />\n        </Tooltip>\n\n        {/* Notifications */}\n        <Dropdown\n          menu={{ items: notificationItems }}\n          trigger={['click']}\n          placement=\"bottomRight\"\n        >\n          <Badge count={2} size=\"small\">\n            <Button\n              type=\"text\"\n              icon={<BellOutlined />}\n              style={{\n                fontSize: '16px',\n                width: '40px',\n                height: '40px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n              }}\n            />\n          </Badge>\n        </Dropdown>\n\n        {/* User menu */}\n        <Dropdown\n          menu={{ items: userMenuItems }}\n          trigger={['click']}\n          placement=\"bottomRight\"\n        >\n          <div\n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px',\n              cursor: 'pointer',\n              padding: '4px 8px',\n              borderRadius: '6px',\n              transition: 'background-color 0.2s ease',\n            }}\n          >\n            <Avatar\n              size=\"small\"\n              icon={<UserOutlined />}\n              style={{\n                backgroundColor: themeStyles.getColor('primary'),\n              }}\n            />\n            {!isMobile && (\n              <div>\n                <div\n                  style={{\n                    fontSize: '14px',\n                    fontWeight: 'bold',\n                    color: themeStyles.getTextColor('primary'),\n                    lineHeight: 1.2,\n                  }}\n                >\n                  {auth.user?.username || 'Admin'}\n                </div>\n                <div\n                  style={{\n                    fontSize: '12px',\n                    color: themeStyles.getTextColor('secondary'),\n                    lineHeight: 1,\n                  }}\n                >\n                  {auth.user?.role || 'Administrator'}\n                </div>\n              </div>\n            )}\n          </div>\n        </Dropdown>\n      </Space>\n    </Header>\n  );\n}\n\n/**\n * Header breadcrumb component\n */\nexport interface HeaderBreadcrumbProps {\n  items: Array<{\n    title: string;\n    href?: string;\n  }>;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function HeaderBreadcrumb({ items, className, style }: HeaderBreadcrumbProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div\n      className={className}\n      style={{\n        padding: '8px 24px',\n        backgroundColor: themeStyles.getBackgroundColor('elevated'),\n        borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        ...style,\n      }}\n    >\n      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n        {items.map((item, index) => (\n          <React.Fragment key={index}>\n            {index > 0 && (\n              <span style={{ color: themeStyles.getTextColor('tertiary') }}>\n                /\n              </span>\n            )}\n            {item.href ? (\n              <a\n                href={item.href}\n                style={{\n                  color: themeStyles.getColor('primary'),\n                  textDecoration: 'none',\n                  fontSize: '14px',\n                }}\n              >\n                {item.title}\n              </a>\n            ) : (\n              <span\n                style={{\n                  color: themeStyles.getTextColor('primary'),\n                  fontSize: '14px',\n                  fontWeight: index === items.length - 1 ? 'bold' : 'normal',\n                }}\n              >\n                {item.title}\n              </span>\n            )}\n          </React.Fragment>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAID;AAaA;AACA;AADA;AAZA;AAAA;AAaA;AAZA;AAAA;AAAA;AADA;AACA;AAAA;AADA;AAAA;AACA;AAAA;AAAA;AADA;AAAA;AACA;AADA;;;AAHA;;;;;;AAkBA,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAiBpB,SAAS,UAAU,EACxB,gBAAgB,EAChB,eAAe,EACf,QAAQ,EACR,oBAAoB,IAAI,EACxB,SAAS,EACT,KAAK,EACU;;IACf,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAC9C,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,OAAO,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD;IAEnB,kBAAkB;IAClB,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,SAAS,IAAM,KAAK,UAAU;YAC9B,QAAQ;QACV;KACD;IAED,0BAA0B;IAC1B,MAAM,oBAAoB;QACxB;YACE,KAAK;YACL,qBACE,6LAAC;gBAAI,OAAO;oBAAE,SAAS;gBAAQ;;kCAC7B,6LAAC;wBAAI,OAAO;4BAAE,YAAY;4BAAQ,cAAc;wBAAM;kCAAG;;;;;;kCAGzD,6LAAC;wBAAI,OAAO;4BAAE,UAAU;4BAAQ,OAAO,YAAY,YAAY,CAAC;wBAAa;kCAAG;;;;;;;;;;;;QAKtF;QACA;YACE,KAAK;YACL,qBACE,6LAAC;gBAAI,OAAO;oBAAE,SAAS;gBAAQ;;kCAC7B,6LAAC;wBAAI,OAAO;4BAAE,YAAY;4BAAQ,cAAc;wBAAM;kCAAG;;;;;;kCAGzD,6LAAC;wBAAI,OAAO;4BAAE,UAAU;4BAAQ,OAAO,YAAY,YAAY,CAAC;wBAAa;kCAAG;;;;;;;;;;;;QAKtF;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,qBACE,6LAAC;gBAAI,OAAO;oBAAE,WAAW;oBAAU,SAAS;gBAAQ;0BAClD,cAAA,6LAAC,qMAAA,CAAA,SAAM;oBAAC,MAAK;oBAAO,MAAK;8BAAQ;;;;;;;;;;;QAKvC;KACD;IAED,MAAM,cAAmC;QACvC,UAAU;QACV,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;QAClE,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QAAO,WAAW;QAAW,OAAO;;0BAEnC,6LAAC;gBAAI,OAAO;oBAAE,SAAS;oBAAQ,YAAY;oBAAU,KAAK;gBAAO;;oBAE9D,mCACC,6LAAC,qMAAA,CAAA,SAAM;wBACL,MAAK;wBACL,MAAM,iCAAmB,6LAAC,iOAAA,CAAA,qBAAkB;;;;mDAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wBACnE,SAAS;wBACT,OAAO;4BACL,UAAU;4BACV,OAAO;4BACP,QAAQ;4BACR,SAAS;4BACT,YAAY;4BACZ,gBAAgB;wBAClB;;;;;;kCAKJ,6LAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,YAAY;4BAAU,KAAK;wBAAO;;0CAC/D,6LAAC;gCACC,OAAO;oCACL,OAAO;oCACP,QAAQ;oCACR,iBAAiB,YAAY,QAAQ,CAAC;oCACtC,cAAc;oCACd,SAAS;oCACT,YAAY;oCACZ,gBAAgB;oCAChB,OAAO;oCACP,YAAY;oCACZ,UAAU;gCACZ;0CACD;;;;;;4BAGA,CAAC,CAAC,YAAY,gBAAgB,mBAC7B,6LAAC;;kDACC,6LAAC;wCACC,OAAO;4CACL,UAAU;4CACV,YAAY;4CACZ,OAAO,YAAY,YAAY,CAAC;wCAClC;kDACD;;;;;;kDAGD,6LAAC;wCACC,OAAO;4CACL,UAAU;4CACV,OAAO,YAAY,YAAY,CAAC;4CAChC,YAAY;wCACd;kDACD;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC,mMAAA,CAAA,QAAK;gBAAC,MAAK;;kCAEV,6LAAC,uLAAA,CAAA,UAAO;wBAAC,OAAO,CAAC,UAAU,EAAE,SAAS,UAAU,OAAO,KAAK,CAAC;kCAC3D,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,MAAM,uBAAS,6LAAC,mNAAA,CAAA,cAAW;;;;uDAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BAC9C,SAAS;4BACT,OAAO;gCACL,UAAU;gCACV,OAAO;gCACP,QAAQ;gCACR,SAAS;gCACT,YAAY;gCACZ,gBAAgB;4BAClB;;;;;;;;;;;kCAKJ,6LAAC,uLAAA,CAAA,UAAO;wBAAC,OAAM;kCACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;4BACrB,OAAO;gCACL,UAAU;gCACV,OAAO;gCACP,QAAQ;gCACR,SAAS;gCACT,YAAY;gCACZ,gBAAgB;4BAClB;;;;;;;;;;;kCAKJ,6LAAC,yLAAA,CAAA,WAAQ;wBACP,MAAM;4BAAE,OAAO;wBAAkB;wBACjC,SAAS;4BAAC;yBAAQ;wBAClB,WAAU;kCAEV,cAAA,6LAAC,mLAAA,CAAA,QAAK;4BAAC,OAAO;4BAAG,MAAK;sCACpB,cAAA,6LAAC,qMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gCACnB,OAAO;oCACL,UAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,SAAS;oCACT,YAAY;oCACZ,gBAAgB;gCAClB;;;;;;;;;;;;;;;;kCAMN,6LAAC,yLAAA,CAAA,WAAQ;wBACP,MAAM;4BAAE,OAAO;wBAAc;wBAC7B,SAAS;4BAAC;yBAAQ;wBAClB,WAAU;kCAEV,cAAA,6LAAC;4BACC,OAAO;gCACL,SAAS;gCACT,YAAY;gCACZ,KAAK;gCACL,QAAQ;gCACR,SAAS;gCACT,cAAc;gCACd,YAAY;4BACd;;8CAEA,6LAAC,qLAAA,CAAA,SAAM;oCACL,MAAK;oCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACnB,OAAO;wCACL,iBAAiB,YAAY,QAAQ,CAAC;oCACxC;;;;;;gCAED,CAAC,0BACA,6LAAC;;sDACC,6LAAC;4CACC,OAAO;gDACL,UAAU;gDACV,YAAY;gDACZ,OAAO,YAAY,YAAY,CAAC;gDAChC,YAAY;4CACd;sDAEC,KAAK,IAAI,EAAE,YAAY;;;;;;sDAE1B,6LAAC;4CACC,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;gDAChC,YAAY;4CACd;sDAEC,KAAK,IAAI,EAAE,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStC;GA9QgB;;QAQyB,wHAAA,CAAA,WAAQ;QAC3B,wHAAA,CAAA,iBAAc;QACrB,uIAAA,CAAA,UAAO;;;KAVN;AA4RT,SAAS,iBAAiB,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAyB;;IACjF,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAClE,GAAG,KAAK;QACV;kBAEA,cAAA,6LAAC;YAAI,OAAO;gBAAE,SAAS;gBAAQ,YAAY;gBAAU,KAAK;YAAM;sBAC7D,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;wBACZ,QAAQ,mBACP,6LAAC;4BAAK,OAAO;gCAAE,OAAO,YAAY,YAAY,CAAC;4BAAY;sCAAG;;;;;;wBAI/D,KAAK,IAAI,iBACR,6LAAC;4BACC,MAAM,KAAK,IAAI;4BACf,OAAO;gCACL,OAAO,YAAY,QAAQ,CAAC;gCAC5B,gBAAgB;gCAChB,UAAU;4BACZ;sCAEC,KAAK,KAAK;;;;;iDAGb,6LAAC;4BACC,OAAO;gCACL,OAAO,YAAY,YAAY,CAAC;gCAChC,UAAU;gCACV,YAAY,UAAU,MAAM,MAAM,GAAG,IAAI,SAAS;4BACpD;sCAEC,KAAK,KAAK;;;;;;;mBAzBI;;;;;;;;;;;;;;;AAiC/B;IAhDgB;;QACM,wHAAA,CAAA,iBAAc;;;MADpB"}}, {"offset": {"line": 3845, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3851, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-sidebar.tsx"], "sourcesContent": ["/**\n * App Sidebar Component\n * Navigation sidebar for the APISportsGame CMS\n */\n\n'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Layout, Menu, Divider } from 'antd';\nimport {\n  DashboardOutlined,\n  UserOutlined,\n  TeamOutlined,\n  TrophyOutlined,\n  CalendarOutlined,\n  LinkOutlined,\n  SettingOutlined,\n  BarChartOutlined,\n  FileTextOutlined,\n  DatabaseOutlined,\n  ApiOutlined,\n  HeartOutlined,\n} from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\nimport { useRouter, usePathname } from 'next/navigation';\n\nconst { Sider } = Layout;\n\n/**\n * App sidebar props\n */\nexport interface AppSidebarProps {\n  collapsed: boolean;\n  isMobile: boolean;\n  onCollapse: (collapsed: boolean) => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Menu item interface\n */\ninterface MenuItem {\n  key: string;\n  icon: React.ReactNode;\n  label: string;\n  path?: string;\n  children?: MenuItem[];\n  disabled?: boolean;\n}\n\n/**\n * App Sidebar component\n */\nexport function AppSidebar({\n  collapsed,\n  isMobile,\n  onCollapse,\n  className,\n  style,\n}: AppSidebarProps) {\n  const themeStyles = useThemeStyles();\n  const router = useRouter();\n  const pathname = usePathname();\n  \n  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);\n  const [openKeys, setOpenKeys] = useState<string[]>([]);\n\n  // Menu items configuration\n  const menuItems: MenuItem[] = [\n    {\n      key: 'dashboard',\n      icon: <DashboardOutlined />,\n      label: 'Dashboard',\n      path: '/',\n    },\n    {\n      key: 'divider-1',\n      icon: null,\n      label: '',\n    },\n    {\n      key: 'user-management',\n      icon: <UserOutlined />,\n      label: 'User System',\n      children: [\n        {\n          key: 'system-users',\n          icon: <TeamOutlined />,\n          label: 'System Users',\n          path: '/users/system',\n        },\n        {\n          key: 'user-roles',\n          icon: <SettingOutlined />,\n          label: 'Roles & Permissions',\n          path: '/users/roles',\n        },\n      ],\n    },\n    {\n      key: 'football-management',\n      icon: <TrophyOutlined />,\n      label: 'Football Data',\n      children: [\n        {\n          key: 'leagues',\n          icon: <TrophyOutlined />,\n          label: 'Leagues',\n          path: '/football/leagues',\n        },\n        {\n          key: 'teams',\n          icon: <TeamOutlined />,\n          label: 'Teams',\n          path: '/football/teams',\n        },\n        {\n          key: 'fixtures',\n          icon: <CalendarOutlined />,\n          label: 'Fixtures',\n          path: '/football/fixtures',\n        },\n        {\n          key: 'sync-status',\n          icon: <DatabaseOutlined />,\n          label: 'Sync Status',\n          path: '/football/sync',\n        },\n      ],\n    },\n    {\n      key: 'broadcast-management',\n      icon: <LinkOutlined />,\n      label: 'Broadcast Links',\n      children: [\n        {\n          key: 'broadcast-links',\n          icon: <LinkOutlined />,\n          label: 'Manage Links',\n          path: '/broadcast/links',\n        },\n        {\n          key: 'broadcast-quality',\n          icon: <BarChartOutlined />,\n          label: 'Quality Control',\n          path: '/broadcast/quality',\n        },\n      ],\n    },\n    {\n      key: 'divider-2',\n      icon: null,\n      label: '',\n    },\n    {\n      key: 'system',\n      icon: <SettingOutlined />,\n      label: 'System',\n      children: [\n        {\n          key: 'api-health',\n          icon: <HeartOutlined />,\n          label: 'API Health',\n          path: '/system/health',\n        },\n        {\n          key: 'api-docs',\n          icon: <ApiOutlined />,\n          label: 'API Documentation',\n          path: '/system/api-docs',\n        },\n        {\n          key: 'logs',\n          icon: <FileTextOutlined />,\n          label: 'System Logs',\n          path: '/system/logs',\n        },\n        {\n          key: 'settings',\n          icon: <SettingOutlined />,\n          label: 'Settings',\n          path: '/system/settings',\n        },\n      ],\n    },\n    {\n      key: 'divider-3',\n      icon: null,\n      label: '',\n    },\n    {\n      key: 'demos',\n      icon: <BarChartOutlined />,\n      label: 'Demos',\n      children: [\n        {\n          key: 'components-demo',\n          icon: <BarChartOutlined />,\n          label: 'Components',\n          path: '/components-demo',\n        },\n        {\n          key: 'theme-demo',\n          icon: <BarChartOutlined />,\n          label: 'Theme System',\n          path: '/theme-demo',\n        },\n        {\n          key: 'api-hooks-demo',\n          icon: <ApiOutlined />,\n          label: 'API Hooks',\n          path: '/api-hooks-demo',\n        },\n      ],\n    },\n  ];\n\n  // Update selected keys based on current pathname\n  useEffect(() => {\n    const findSelectedKey = (items: MenuItem[], path: string): string | null => {\n      for (const item of items) {\n        if (item.path === path) {\n          return item.key;\n        }\n        if (item.children) {\n          const childKey = findSelectedKey(item.children, path);\n          if (childKey) {\n            return childKey;\n          }\n        }\n      }\n      return null;\n    };\n\n    const selectedKey = findSelectedKey(menuItems, pathname);\n    if (selectedKey) {\n      setSelectedKeys([selectedKey]);\n      \n      // Auto-expand parent menu\n      const findParentKey = (items: MenuItem[], targetKey: string): string | null => {\n        for (const item of items) {\n          if (item.children) {\n            const hasChild = item.children.some(child => child.key === targetKey);\n            if (hasChild) {\n              return item.key;\n            }\n          }\n        }\n        return null;\n      };\n\n      const parentKey = findParentKey(menuItems, selectedKey);\n      if (parentKey && !collapsed) {\n        setOpenKeys([parentKey]);\n      }\n    }\n  }, [pathname, collapsed]);\n\n  // Handle menu click\n  const handleMenuClick = ({ key }: { key: string }) => {\n    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {\n      for (const item of items) {\n        if (item.key === targetKey) {\n          return item;\n        }\n        if (item.children) {\n          const childItem = findMenuItem(item.children, targetKey);\n          if (childItem) {\n            return childItem;\n          }\n        }\n      }\n      return null;\n    };\n\n    const menuItem = findMenuItem(menuItems, key);\n    if (menuItem?.path) {\n      router.push(menuItem.path);\n      \n      // Close sidebar on mobile after navigation\n      if (isMobile) {\n        onCollapse(true);\n      }\n    }\n  };\n\n  // Handle submenu open/close\n  const handleOpenChange = (keys: string[]) => {\n    setOpenKeys(keys);\n  };\n\n  // Convert menu items to Ant Design menu format\n  const convertToAntMenuItems = (items: MenuItem[]): any[] => {\n    return items.map(item => {\n      // Handle dividers\n      if (item.key.startsWith('divider')) {\n        return {\n          type: 'divider',\n          key: item.key,\n        };\n      }\n\n      // Handle regular items\n      if (item.children) {\n        return {\n          key: item.key,\n          icon: item.icon,\n          label: item.label,\n          children: convertToAntMenuItems(item.children),\n          disabled: item.disabled,\n        };\n      }\n\n      return {\n        key: item.key,\n        icon: item.icon,\n        label: item.label,\n        disabled: item.disabled,\n      };\n    });\n  };\n\n  const siderStyle: React.CSSProperties = {\n    position: 'fixed',\n    left: 0,\n    top: '64px', // Header height\n    bottom: 0,\n    zIndex: isMobile ? 1000 : 100,\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    borderRight: `1px solid ${themeStyles.getBorderColor('primary')}`,\n    overflow: 'auto',\n    ...style,\n  };\n\n  return (\n    <Sider\n      className={className}\n      style={siderStyle}\n      collapsed={collapsed}\n      collapsible={false}\n      width={250}\n      collapsedWidth={80}\n      theme=\"light\"\n    >\n      {/* Sidebar content */}\n      <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n        {/* Main navigation menu */}\n        <Menu\n          mode=\"inline\"\n          selectedKeys={selectedKeys}\n          openKeys={collapsed ? [] : openKeys}\n          onOpenChange={handleOpenChange}\n          onClick={handleMenuClick}\n          items={convertToAntMenuItems(menuItems)}\n          style={{\n            flex: 1,\n            border: 'none',\n            backgroundColor: 'transparent',\n          }}\n        />\n\n        {/* Sidebar footer */}\n        {!collapsed && (\n          <div\n            style={{\n              padding: '16px',\n              borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,\n              textAlign: 'center',\n            }}\n          >\n            <div\n              style={{\n                fontSize: '12px',\n                color: themeStyles.getTextColor('tertiary'),\n                marginBottom: '4px',\n              }}\n            >\n              APISportsGame CMS\n            </div>\n            <div\n              style={{\n                fontSize: '10px',\n                color: themeStyles.getTextColor('tertiary'),\n              }}\n            >\n              v1.0.0\n            </div>\n          </div>\n        )}\n      </div>\n    </Sider>\n  );\n}\n\n/**\n * Sidebar menu item component for custom rendering\n */\nexport interface SidebarMenuItemProps {\n  icon: React.ReactNode;\n  label: string;\n  active?: boolean;\n  collapsed?: boolean;\n  onClick?: () => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function SidebarMenuItem({\n  icon,\n  label,\n  active = false,\n  collapsed = false,\n  onClick,\n  className,\n  style,\n}: SidebarMenuItemProps) {\n  const themeStyles = useThemeStyles();\n\n  const itemStyle: React.CSSProperties = {\n    display: 'flex',\n    alignItems: 'center',\n    gap: collapsed ? '0' : '12px',\n    padding: '12px 16px',\n    cursor: 'pointer',\n    borderRadius: '6px',\n    margin: '2px 8px',\n    transition: 'all 0.2s ease',\n    backgroundColor: active ? themeStyles.getColor('primary') + '10' : 'transparent',\n    color: active ? themeStyles.getColor('primary') : themeStyles.getTextColor('primary'),\n    ...style,\n  };\n\n  return (\n    <div\n      className={className}\n      style={itemStyle}\n      onClick={onClick}\n    >\n      <div style={{ fontSize: '16px', minWidth: '16px' }}>\n        {icon}\n      </div>\n      {!collapsed && (\n        <div style={{ fontSize: '14px', fontWeight: active ? 'bold' : 'normal' }}>\n          {label}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAID;AAgBA;AACA;AADA;AAfA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADA;;;AAHA;;;;;;AAqBA,MAAM,EAAE,KAAK,EAAE,GAAG,qLAAA,CAAA,SAAM;AA4BjB,SAAS,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,UAAU,EACV,SAAS,EACT,KAAK,EACW;;IAChB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAErD,2BAA2B;IAC3B,MAAM,YAAwB;QAC5B;YACE,KAAK;YACL,oBAAM,6LAAC,+NAAA,CAAA,oBAAiB;;;;;YACxB,OAAO;YACP,MAAM;QACR;QACA;YACE,KAAK;YACL,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;oBACtB,OAAO;oBACP,MAAM;gBACR;aACD;QACH;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;oBACrB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;oBACP,MAAM;gBACR;aACD;QACH;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;oBACP,MAAM;gBACR;aACD;QACH;QACA;YACE,KAAK;YACL,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;oBACpB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,mNAAA,CAAA,cAAW;;;;;oBAClB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;oBACtB,OAAO;oBACP,MAAM;gBACR;aACD;QACH;QACA;YACE,KAAK;YACL,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,mNAAA,CAAA,cAAW;;;;;oBAClB,OAAO;oBACP,MAAM;gBACR;aACD;QACH;KACD;IAED,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;wDAAkB,CAAC,OAAmB;oBAC1C,KAAK,MAAM,QAAQ,MAAO;wBACxB,IAAI,KAAK,IAAI,KAAK,MAAM;4BACtB,OAAO,KAAK,GAAG;wBACjB;wBACA,IAAI,KAAK,QAAQ,EAAE;4BACjB,MAAM,WAAW,gBAAgB,KAAK,QAAQ,EAAE;4BAChD,IAAI,UAAU;gCACZ,OAAO;4BACT;wBACF;oBACF;oBACA,OAAO;gBACT;;YAEA,MAAM,cAAc,gBAAgB,WAAW;YAC/C,IAAI,aAAa;gBACf,gBAAgB;oBAAC;iBAAY;gBAE7B,0BAA0B;gBAC1B,MAAM;0DAAgB,CAAC,OAAmB;wBACxC,KAAK,MAAM,QAAQ,MAAO;4BACxB,IAAI,KAAK,QAAQ,EAAE;gCACjB,MAAM,WAAW,KAAK,QAAQ,CAAC,IAAI;mFAAC,CAAA,QAAS,MAAM,GAAG,KAAK;;gCAC3D,IAAI,UAAU;oCACZ,OAAO,KAAK,GAAG;gCACjB;4BACF;wBACF;wBACA,OAAO;oBACT;;gBAEA,MAAM,YAAY,cAAc,WAAW;gBAC3C,IAAI,aAAa,CAAC,WAAW;oBAC3B,YAAY;wBAAC;qBAAU;gBACzB;YACF;QACF;+BAAG;QAAC;QAAU;KAAU;IAExB,oBAAoB;IACpB,MAAM,kBAAkB,CAAC,EAAE,GAAG,EAAmB;QAC/C,MAAM,eAAe,CAAC,OAAmB;YACvC,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,KAAK,GAAG,KAAK,WAAW;oBAC1B,OAAO;gBACT;gBACA,IAAI,KAAK,QAAQ,EAAE;oBACjB,MAAM,YAAY,aAAa,KAAK,QAAQ,EAAE;oBAC9C,IAAI,WAAW;wBACb,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,MAAM,WAAW,aAAa,WAAW;QACzC,IAAI,UAAU,MAAM;YAClB,OAAO,IAAI,CAAC,SAAS,IAAI;YAEzB,2CAA2C;YAC3C,IAAI,UAAU;gBACZ,WAAW;YACb;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,mBAAmB,CAAC;QACxB,YAAY;IACd;IAEA,+CAA+C;IAC/C,MAAM,wBAAwB,CAAC;QAC7B,OAAO,MAAM,GAAG,CAAC,CAAA;YACf,kBAAkB;YAClB,IAAI,KAAK,GAAG,CAAC,UAAU,CAAC,YAAY;gBAClC,OAAO;oBACL,MAAM;oBACN,KAAK,KAAK,GAAG;gBACf;YACF;YAEA,uBAAuB;YACvB,IAAI,KAAK,QAAQ,EAAE;gBACjB,OAAO;oBACL,KAAK,KAAK,GAAG;oBACb,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,UAAU,sBAAsB,KAAK,QAAQ;oBAC7C,UAAU,KAAK,QAAQ;gBACzB;YACF;YAEA,OAAO;gBACL,KAAK,KAAK,GAAG;gBACb,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;YACzB;QACF;IACF;IAEA,MAAM,aAAkC;QACtC,UAAU;QACV,MAAM;QACN,KAAK;QACL,QAAQ;QACR,QAAQ,WAAW,OAAO;QAC1B,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,aAAa,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;QACjE,UAAU;QACV,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;QACP,WAAW;QACX,aAAa;QACb,OAAO;QACP,gBAAgB;QAChB,OAAM;kBAGN,cAAA,6LAAC;YAAI,OAAO;gBAAE,QAAQ;gBAAQ,SAAS;gBAAQ,eAAe;YAAS;;8BAErE,6LAAC,iLAAA,CAAA,OAAI;oBACH,MAAK;oBACL,cAAc;oBACd,UAAU,YAAY,EAAE,GAAG;oBAC3B,cAAc;oBACd,SAAS;oBACT,OAAO,sBAAsB;oBAC7B,OAAO;wBACL,MAAM;wBACN,QAAQ;wBACR,iBAAiB;oBACnB;;;;;;gBAID,CAAC,2BACA,6LAAC;oBACC,OAAO;wBACL,SAAS;wBACT,WAAW,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;wBAC/D,WAAW;oBACb;;sCAEA,6LAAC;4BACC,OAAO;gCACL,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;gCAChC,cAAc;4BAChB;sCACD;;;;;;sCAGD,6LAAC;4BACC,OAAO;gCACL,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;4BAClC;sCACD;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAnVgB;;QAOM,wHAAA,CAAA,iBAAc;QACnB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KATd;AAkWT,SAAS,gBAAgB,EAC9B,IAAI,EACJ,KAAK,EACL,SAAS,KAAK,EACd,YAAY,KAAK,EACjB,OAAO,EACP,SAAS,EACT,KAAK,EACgB;;IACrB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,YAAiC;QACrC,SAAS;QACT,YAAY;QACZ,KAAK,YAAY,MAAM;QACvB,SAAS;QACT,QAAQ;QACR,cAAc;QACd,QAAQ;QACR,YAAY;QACZ,iBAAiB,SAAS,YAAY,QAAQ,CAAC,aAAa,OAAO;QACnE,OAAO,SAAS,YAAY,QAAQ,CAAC,aAAa,YAAY,YAAY,CAAC;QAC3E,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;QACP,SAAS;;0BAET,6LAAC;gBAAI,OAAO;oBAAE,UAAU;oBAAQ,UAAU;gBAAO;0BAC9C;;;;;;YAEF,CAAC,2BACA,6LAAC;gBAAI,OAAO;oBAAE,UAAU;oBAAQ,YAAY,SAAS,SAAS;gBAAS;0BACpE;;;;;;;;;;;;AAKX;IAzCgB;;QASM,wHAAA,CAAA,iBAAc;;;MATpB"}}, {"offset": {"line": 4396, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4402, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-footer.tsx"], "sourcesContent": ["/**\n * App Footer Component\n * Footer for the APISportsGame CMS\n */\n\n'use client';\n\nimport React from 'react';\nimport { Layout, Space, Typography, Divider } from 'antd';\nimport { \n  GithubOutlined, \n  TwitterOutlined, \n  LinkedinOutlined,\n  HeartFilled,\n  ApiOutlined,\n  GlobalOutlined,\n} from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\n\nconst { Footer } = Layout;\nconst { Text, Link } = Typography;\n\n/**\n * App footer props\n */\nexport interface AppFooterProps {\n  className?: string;\n  style?: React.CSSProperties;\n  compact?: boolean;\n}\n\n/**\n * App Footer component\n */\nexport function AppFooter({ className, style, compact = false }: AppFooterProps) {\n  const themeStyles = useThemeStyles();\n\n  const footerStyle: React.CSSProperties = {\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,\n    padding: compact ? '12px 24px' : '24px',\n    textAlign: 'center',\n    ...style,\n  };\n\n  const currentYear = new Date().getFullYear();\n\n  if (compact) {\n    return (\n      <Footer className={className} style={footerStyle}>\n        <Text\n          style={{\n            fontSize: '12px',\n            color: themeStyles.getTextColor('tertiary'),\n          }}\n        >\n          © {currentYear} APISportsGame CMS. Built with{' '}\n          <HeartFilled style={{ color: themeStyles.getColor('error') }} /> by Augment Code\n        </Text>\n      </Footer>\n    );\n  }\n\n  return (\n    <Footer className={className} style={footerStyle}>\n      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n        {/* Main footer content */}\n        <div\n          style={{\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n            gap: '32px',\n            marginBottom: '24px',\n            textAlign: 'left',\n          }}\n        >\n          {/* About section */}\n          <div>\n            <div\n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                marginBottom: '12px',\n              }}\n            >\n              <div\n                style={{\n                  width: '24px',\n                  height: '24px',\n                  backgroundColor: themeStyles.getColor('primary'),\n                  borderRadius: '4px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: 'white',\n                  fontSize: '14px',\n                  fontWeight: 'bold',\n                }}\n              >\n                ⚽\n              </div>\n              <Text\n                style={{\n                  fontSize: '16px',\n                  fontWeight: 'bold',\n                  color: themeStyles.getTextColor('primary'),\n                }}\n              >\n                APISportsGame\n              </Text>\n            </div>\n            <Text\n              style={{\n                fontSize: '14px',\n                color: themeStyles.getTextColor('secondary'),\n                lineHeight: 1.6,\n              }}\n            >\n              A comprehensive CMS for managing football data, broadcast links, and user systems.\n              Built with modern technologies for optimal performance.\n            </Text>\n          </div>\n\n          {/* Quick links */}\n          <div>\n            <Text\n              style={{\n                fontSize: '14px',\n                fontWeight: 'bold',\n                color: themeStyles.getTextColor('primary'),\n                marginBottom: '12px',\n                display: 'block',\n              }}\n            >\n              Quick Links\n            </Text>\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n              <Link\n                href=\"/\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                Dashboard\n              </Link>\n              <Link\n                href=\"/football/fixtures\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                Fixtures\n              </Link>\n              <Link\n                href=\"/broadcast/links\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                Broadcast Links\n              </Link>\n              <Link\n                href=\"/system/health\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                System Health\n              </Link>\n            </div>\n          </div>\n\n          {/* Resources */}\n          <div>\n            <Text\n              style={{\n                fontSize: '14px',\n                fontWeight: 'bold',\n                color: themeStyles.getTextColor('primary'),\n                marginBottom: '12px',\n                display: 'block',\n              }}\n            >\n              Resources\n            </Text>\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n              <Link\n                href=\"/system/api-docs\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <ApiOutlined style={{ marginRight: '4px' }} />\n                API Documentation\n              </Link>\n              <Link\n                href=\"/components-demo\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                Component Library\n              </Link>\n              <Link\n                href=\"/theme-demo\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                Theme System\n              </Link>\n              <Link\n                href=\"https://github.com/apisportsgame\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <GithubOutlined style={{ marginRight: '4px' }} />\n                GitHub Repository\n              </Link>\n            </div>\n          </div>\n\n          {/* Contact */}\n          <div>\n            <Text\n              style={{\n                fontSize: '14px',\n                fontWeight: 'bold',\n                color: themeStyles.getTextColor('primary'),\n                marginBottom: '12px',\n                display: 'block',\n              }}\n            >\n              Connect\n            </Text>\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n              <Link\n                href=\"https://github.com/apisportsgame\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <GithubOutlined style={{ marginRight: '4px' }} />\n                GitHub\n              </Link>\n              <Link\n                href=\"https://twitter.com/apisportsgame\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <TwitterOutlined style={{ marginRight: '4px' }} />\n                Twitter\n              </Link>\n              <Link\n                href=\"https://linkedin.com/company/apisportsgame\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <LinkedinOutlined style={{ marginRight: '4px' }} />\n                LinkedIn\n              </Link>\n              <Link\n                href=\"https://apisportsgame.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <GlobalOutlined style={{ marginRight: '4px' }} />\n                Website\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        <Divider style={{ margin: '24px 0 16px 0' }} />\n\n        {/* Bottom footer */}\n        <div\n          style={{\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            flexWrap: 'wrap',\n            gap: '16px',\n          }}\n        >\n          <Text\n            style={{\n              fontSize: '13px',\n              color: themeStyles.getTextColor('tertiary'),\n            }}\n          >\n            © {currentYear} APISportsGame CMS. All rights reserved. Built with{' '}\n            <HeartFilled style={{ color: themeStyles.getColor('error') }} /> by Augment Code\n          </Text>\n\n          <Space size=\"middle\">\n            <Link\n              href=\"/privacy\"\n              style={{\n                fontSize: '13px',\n                color: themeStyles.getTextColor('tertiary'),\n              }}\n            >\n              Privacy Policy\n            </Link>\n            <Link\n              href=\"/terms\"\n              style={{\n                fontSize: '13px',\n                color: themeStyles.getTextColor('tertiary'),\n              }}\n            >\n              Terms of Service\n            </Link>\n            <Text\n              style={{\n                fontSize: '13px',\n                color: themeStyles.getTextColor('tertiary'),\n              }}\n            >\n              v1.0.0\n            </Text>\n          </Space>\n        </div>\n      </div>\n    </Footer>\n  );\n}\n\n/**\n * Simple footer for minimal layouts\n */\nexport interface SimpleFooterProps {\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function SimpleFooter({ className, style }: SimpleFooterProps) {\n  const themeStyles = useThemeStyles();\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <div\n      className={className}\n      style={{\n        padding: '16px 24px',\n        textAlign: 'center',\n        backgroundColor: themeStyles.getBackgroundColor('container'),\n        borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        ...style,\n      }}\n    >\n      <Text\n        style={{\n          fontSize: '12px',\n          color: themeStyles.getTextColor('tertiary'),\n        }}\n      >\n        © {currentYear} APISportsGame CMS. Built with{' '}\n        <HeartFilled style={{ color: themeStyles.getColor('error') }} /> by Augment Code\n      </Text>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAcD;AAAA;AATA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AADA;AACA;AADA;;;AAHA;;;;AAcA,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAc1B,SAAS,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,KAAK,EAAkB;;IAC7E,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,cAAmC;QACvC,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,WAAW,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;QAC/D,SAAS,UAAU,cAAc;QACjC,WAAW;QACX,GAAG,KAAK;IACV;IAEA,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,IAAI,SAAS;QACX,qBACE,6LAAC;YAAO,WAAW;YAAW,OAAO;sBACnC,cAAA,6LAAC;gBACC,OAAO;oBACL,UAAU;oBACV,OAAO,YAAY,YAAY,CAAC;gBAClC;;oBACD;oBACI;oBAAY;oBAA+B;kCAC9C,6LAAC,mNAAA,CAAA,cAAW;wBAAC,OAAO;4BAAE,OAAO,YAAY,QAAQ,CAAC;wBAAS;;;;;;oBAAK;;;;;;;;;;;;IAIxE;IAEA,qBACE,6LAAC;QAAO,WAAW;QAAW,OAAO;kBACnC,cAAA,6LAAC;YAAI,OAAO;gBAAE,UAAU;gBAAU,QAAQ;YAAS;;8BAEjD,6LAAC;oBACC,OAAO;wBACL,SAAS;wBACT,qBAAqB;wBACrB,KAAK;wBACL,cAAc;wBACd,WAAW;oBACb;;sCAGA,6LAAC;;8CACC,6LAAC;oCACC,OAAO;wCACL,SAAS;wCACT,YAAY;wCACZ,KAAK;wCACL,cAAc;oCAChB;;sDAEA,6LAAC;4CACC,OAAO;gDACL,OAAO;gDACP,QAAQ;gDACR,iBAAiB,YAAY,QAAQ,CAAC;gDACtC,cAAc;gDACd,SAAS;gDACT,YAAY;gDACZ,gBAAgB;gDAChB,OAAO;gDACP,UAAU;gDACV,YAAY;4CACd;sDACD;;;;;;sDAGD,6LAAC;4CACC,OAAO;gDACL,UAAU;gDACV,YAAY;gDACZ,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;;;;;;;8CAIH,6LAAC;oCACC,OAAO;wCACL,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;wCAChC,YAAY;oCACd;8CACD;;;;;;;;;;;;sCAOH,6LAAC;;8CACC,6LAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO,YAAY,YAAY,CAAC;wCAChC,cAAc;wCACd,SAAS;oCACX;8CACD;;;;;;8CAGD,6LAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,eAAe;wCAAU,KAAK;oCAAM;;sDACjE,6LAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;;;;;;;;;;;;;sCAOL,6LAAC;;8CACC,6LAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO,YAAY,YAAY,CAAC;wCAChC,cAAc;wCACd,SAAS;oCACX;8CACD;;;;;;8CAGD,6LAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,eAAe;wCAAU,KAAK;oCAAM;;sDACjE,6LAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,6LAAC,mNAAA,CAAA,cAAW;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;sDAGhD,6LAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,6LAAC,yNAAA,CAAA,iBAAc;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;;;;;;;;;;;;;sCAOvD,6LAAC;;8CACC,6LAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO,YAAY,YAAY,CAAC;wCAChC,cAAc;wCACd,SAAS;oCACX;8CACD;;;;;;8CAGD,6LAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,eAAe;wCAAU,KAAK;oCAAM;;sDACjE,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,6LAAC,yNAAA,CAAA,iBAAc;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;sDAGnD,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,6LAAC,2NAAA,CAAA,kBAAe;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;sDAGpD,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,6LAAC,6NAAA,CAAA,mBAAgB;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;sDAGrD,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,6LAAC,yNAAA,CAAA,iBAAc;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;;;;;;;;;;;;;;;;;;;8BAOzD,6LAAC,uLAAA,CAAA,UAAO;oBAAC,OAAO;wBAAE,QAAQ;oBAAgB;;;;;;8BAG1C,6LAAC;oBACC,OAAO;wBACL,SAAS;wBACT,gBAAgB;wBAChB,YAAY;wBACZ,UAAU;wBACV,KAAK;oBACP;;sCAEA,6LAAC;4BACC,OAAO;gCACL,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;4BAClC;;gCACD;gCACI;gCAAY;gCAAoD;8CACnE,6LAAC,mNAAA,CAAA,cAAW;oCAAC,OAAO;wCAAE,OAAO,YAAY,QAAQ,CAAC;oCAAS;;;;;;gCAAK;;;;;;;sCAGlE,6LAAC,mMAAA,CAAA,QAAK;4BAAC,MAAK;;8CACV,6LAAC;oCACC,MAAK;oCACL,OAAO;wCACL,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;oCAClC;8CACD;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,OAAO;wCACL,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;oCAClC;8CACD;;;;;;8CAGD,6LAAC;oCACC,OAAO;wCACL,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;oCAClC;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAhUgB;;QACM,wHAAA,CAAA,iBAAc;;;KADpB;AA0UT,SAAS,aAAa,EAAE,SAAS,EAAE,KAAK,EAAqB;;IAClE,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,WAAW;YACX,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,WAAW,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAC/D,GAAG,KAAK;QACV;kBAEA,cAAA,6LAAC;YACC,OAAO;gBACL,UAAU;gBACV,OAAO,YAAY,YAAY,CAAC;YAClC;;gBACD;gBACI;gBAAY;gBAA+B;8BAC9C,6LAAC,mNAAA,CAAA,cAAW;oBAAC,OAAO;wBAAE,OAAO,YAAY,QAAQ,CAAC;oBAAS;;;;;;gBAAK;;;;;;;;;;;;AAIxE;IA1BgB;;QACM,wHAAA,CAAA,iBAAc;;;MADpB"}}, {"offset": {"line": 5060, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5066, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-layout.tsx"], "sourcesContent": ["/**\n * Main App Layout Component\n * Primary layout structure for the APISportsGame CMS\n */\n\n'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Layout } from 'antd';\nimport { useThemeStyles } from '@/theme';\nimport { useAppProvider } from '@/stores';\nimport { AppHeader } from './app-header';\nimport { AppSidebar } from './app-sidebar';\nimport { AppFooter } from './app-footer';\n\nconst { Content } = Layout;\n\n/**\n * App layout props\n */\nexport interface AppLayoutProps {\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Main App Layout component\n */\nexport function AppLayout({ children, className, style }: AppLayoutProps) {\n  const themeStyles = useThemeStyles();\n  const app = useAppProvider();\n  \n  // Layout state\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Handle responsive behavior\n  useEffect(() => {\n    const handleResize = () => {\n      const mobile = window.innerWidth < 768;\n      setIsMobile(mobile);\n      \n      // Auto-collapse sidebar on mobile\n      if (mobile && !sidebarCollapsed) {\n        setSidebarCollapsed(true);\n      }\n    };\n\n    // Initial check\n    handleResize();\n\n    // Add event listener\n    window.addEventListener('resize', handleResize);\n    \n    // Cleanup\n    return () => window.removeEventListener('resize', handleResize);\n  }, [sidebarCollapsed]);\n\n  // Handle sidebar toggle\n  const handleSidebarToggle = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  // Handle mobile sidebar overlay\n  const handleMobileOverlayClick = () => {\n    if (isMobile && !sidebarCollapsed) {\n      setSidebarCollapsed(true);\n    }\n  };\n\n  const layoutStyle: React.CSSProperties = {\n    minHeight: '100vh',\n    backgroundColor: themeStyles.getBackgroundColor('layout'),\n    ...style,\n  };\n\n  const contentStyle: React.CSSProperties = {\n    marginLeft: isMobile ? 0 : (sidebarCollapsed ? '80px' : '250px'),\n    transition: 'margin-left 0.2s ease',\n    minHeight: 'calc(100vh - 64px)', // Header height\n    backgroundColor: themeStyles.getBackgroundColor('layout'),\n  };\n\n  return (\n    <Layout className={className} style={layoutStyle}>\n      {/* Header */}\n      <AppHeader\n        sidebarCollapsed={sidebarCollapsed}\n        onSidebarToggle={handleSidebarToggle}\n        isMobile={isMobile}\n      />\n\n      {/* Sidebar */}\n      <AppSidebar\n        collapsed={sidebarCollapsed}\n        isMobile={isMobile}\n        onCollapse={setSidebarCollapsed}\n      />\n\n      {/* Mobile overlay */}\n      {isMobile && !sidebarCollapsed && (\n        <div\n          style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            zIndex: 999,\n          }}\n          onClick={handleMobileOverlayClick}\n        />\n      )}\n\n      {/* Main content */}\n      <Layout style={contentStyle}>\n        <Content\n          style={{\n            padding: '24px',\n            backgroundColor: themeStyles.getBackgroundColor('layout'),\n            overflow: 'auto',\n          }}\n        >\n          {children}\n        </Content>\n\n        {/* Footer */}\n        <AppFooter />\n      </Layout>\n    </Layout>\n  );\n}\n\n/**\n * Layout provider for layout state management\n */\nexport interface LayoutContextType {\n  sidebarCollapsed: boolean;\n  setSidebarCollapsed: (collapsed: boolean) => void;\n  isMobile: boolean;\n  toggleSidebar: () => void;\n}\n\nconst LayoutContext = React.createContext<LayoutContextType | undefined>(undefined);\n\nexport function LayoutProvider({ children }: { children: React.ReactNode }) {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n\n  useEffect(() => {\n    const handleResize = () => {\n      const mobile = window.innerWidth < 768;\n      setIsMobile(mobile);\n    };\n\n    handleResize();\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  const value: LayoutContextType = {\n    sidebarCollapsed,\n    setSidebarCollapsed,\n    isMobile,\n    toggleSidebar,\n  };\n\n  return (\n    <LayoutContext.Provider value={value}>\n      {children}\n    </LayoutContext.Provider>\n  );\n}\n\n/**\n * Hook to use layout context\n */\nexport function useLayout() {\n  const context = React.useContext(LayoutContext);\n  if (context === undefined) {\n    throw new Error('useLayout must be used within a LayoutProvider');\n  }\n  return context;\n}\n\n/**\n * Simple layout for pages that don't need sidebar\n */\nexport interface SimpleLayoutProps {\n  children: React.ReactNode;\n  showHeader?: boolean;\n  showFooter?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function SimpleLayout({\n  children,\n  showHeader = true,\n  showFooter = true,\n  className,\n  style,\n}: SimpleLayoutProps) {\n  const themeStyles = useThemeStyles();\n\n  const layoutStyle: React.CSSProperties = {\n    minHeight: '100vh',\n    backgroundColor: themeStyles.getBackgroundColor('layout'),\n    display: 'flex',\n    flexDirection: 'column',\n    ...style,\n  };\n\n  return (\n    <Layout className={className} style={layoutStyle}>\n      {showHeader && (\n        <AppHeader\n          sidebarCollapsed={true}\n          onSidebarToggle={() => {}}\n          isMobile={false}\n          showSidebarToggle={false}\n        />\n      )}\n\n      <Content\n        style={{\n          flex: 1,\n          padding: '24px',\n          backgroundColor: themeStyles.getBackgroundColor('layout'),\n        }}\n      >\n        {children}\n      </Content>\n\n      {showFooter && <AppFooter />}\n    </Layout>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAID;AAEA;AACA;AACA;AACA;AACA;AAJA;AADA;AAEA;;;AALA;;;;;;;;AAUA,MAAM,EAAE,OAAO,EAAE,GAAG,qLAAA,CAAA,SAAM;AAcnB,SAAS,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAkB;;IACtE,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,MAAM,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD;IAEzB,eAAe;IACf,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;oDAAe;oBACnB,MAAM,SAAS,OAAO,UAAU,GAAG;oBACnC,YAAY;oBAEZ,kCAAkC;oBAClC,IAAI,UAAU,CAAC,kBAAkB;wBAC/B,oBAAoB;oBACtB;gBACF;;YAEA,gBAAgB;YAChB;YAEA,qBAAqB;YACrB,OAAO,gBAAgB,CAAC,UAAU;YAElC,UAAU;YACV;uCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;8BAAG;QAAC;KAAiB;IAErB,wBAAwB;IACxB,MAAM,sBAAsB;QAC1B,oBAAoB,CAAC;IACvB;IAEA,gCAAgC;IAChC,MAAM,2BAA2B;QAC/B,IAAI,YAAY,CAAC,kBAAkB;YACjC,oBAAoB;QACtB;IACF;IAEA,MAAM,cAAmC;QACvC,WAAW;QACX,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,GAAG,KAAK;IACV;IAEA,MAAM,eAAoC;QACxC,YAAY,WAAW,IAAK,mBAAmB,SAAS;QACxD,YAAY;QACZ,WAAW;QACX,iBAAiB,YAAY,kBAAkB,CAAC;IAClD;IAEA,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,WAAW;QAAW,OAAO;;0BAEnC,6LAAC,gJAAA,CAAA,YAAS;gBACR,kBAAkB;gBAClB,iBAAiB;gBACjB,UAAU;;;;;;0BAIZ,6LAAC,iJAAA,CAAA,aAAU;gBACT,WAAW;gBACX,UAAU;gBACV,YAAY;;;;;;YAIb,YAAY,CAAC,kCACZ,6LAAC;gBACC,OAAO;oBACL,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,QAAQ;gBACV;gBACA,SAAS;;;;;;0BAKb,6LAAC,qLAAA,CAAA,SAAM;gBAAC,OAAO;;kCACb,6LAAC;wBACC,OAAO;4BACL,SAAS;4BACT,iBAAiB,YAAY,kBAAkB,CAAC;4BAChD,UAAU;wBACZ;kCAEC;;;;;;kCAIH,6LAAC,gJAAA,CAAA,YAAS;;;;;;;;;;;;;;;;;AAIlB;GAxGgB;;QACM,wHAAA,CAAA,iBAAc;QACtB,qIAAA,CAAA,iBAAc;;;KAFZ;AAoHhB,MAAM,8BAAgB,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAgC;AAElE,SAAS,eAAe,EAAE,QAAQ,EAAiC;;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;yDAAe;oBACnB,MAAM,SAAS,OAAO,UAAU,GAAG;oBACnC,YAAY;gBACd;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;4CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;mCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,oBAAoB,CAAC;IACvB;IAEA,MAAM,QAA2B;QAC/B;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,cAAc,QAAQ;QAAC,OAAO;kBAC5B;;;;;;AAGP;IA/BgB;MAAA;AAoCT,SAAS;;IACd,MAAM,UAAU,6JAAA,CAAA,UAAK,CAAC,UAAU,CAAC;IACjC,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAmBT,SAAS,aAAa,EAC3B,QAAQ,EACR,aAAa,IAAI,EACjB,aAAa,IAAI,EACjB,SAAS,EACT,KAAK,EACa;;IAClB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,cAAmC;QACvC,WAAW;QACX,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,SAAS;QACT,eAAe;QACf,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,WAAW;QAAW,OAAO;;YAClC,4BACC,6LAAC,gJAAA,CAAA,YAAS;gBACR,kBAAkB;gBAClB,iBAAiB,KAAO;gBACxB,UAAU;gBACV,mBAAmB;;;;;;0BAIvB,6LAAC;gBACC,OAAO;oBACL,MAAM;oBACN,SAAS;oBACT,iBAAiB,YAAY,kBAAkB,CAAC;gBAClD;0BAEC;;;;;;YAGF,4BAAc,6LAAC,gJAAA,CAAA,YAAS;;;;;;;;;;;AAG/B;IAzCgB;;QAOM,wHAAA,CAAA,iBAAc;;;MAPpB"}}, {"offset": {"line": 5337, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5343, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/page-header.tsx"], "sourcesContent": ["/**\n * Page Header Component\n * Reusable page header with breadcrumbs, title, and actions\n */\n\n'use client';\n\nimport React from 'react';\nimport { PageHeader as AntPageHeader, Breadcrumb, Space, Divider } from 'antd';\nimport { HomeOutlined } from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Breadcrumb item interface\n */\nexport interface BreadcrumbItem {\n  title: string;\n  href?: string;\n  icon?: React.ReactNode;\n}\n\n/**\n * Page header props\n */\nexport interface PageHeaderProps {\n  title: string;\n  subtitle?: string;\n  breadcrumbs?: BreadcrumbItem[];\n  actions?: React.ReactNode[];\n  extra?: React.ReactNode;\n  children?: React.ReactNode;\n  showDivider?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Page Header component\n */\nexport function PageHeader({\n  title,\n  subtitle,\n  breadcrumbs = [],\n  actions = [],\n  extra,\n  children,\n  showDivider = true,\n  className,\n  style,\n}: PageHeaderProps) {\n  const themeStyles = useThemeStyles();\n\n  // Add home breadcrumb if not present\n  const allBreadcrumbs = breadcrumbs.length > 0 && breadcrumbs[0].href !== '/' \n    ? [{ title: 'Home', href: '/', icon: <HomeOutlined /> }, ...breadcrumbs]\n    : breadcrumbs;\n\n  return (\n    <div className={className} style={style}>\n      <div style={{\n        padding: '16px 24px',\n        backgroundColor: themeStyles.getBackgroundColor('container'),\n        borderBottom: showDivider ? `1px solid ${themeStyles.getBorderColor('primary')}` : 'none',\n      }}>\n        {/* Breadcrumbs */}\n        {allBreadcrumbs.length > 0 && (\n          <Breadcrumb style={{ marginBottom: '8px' }}>\n            {allBreadcrumbs.map((item, index) => (\n              <Breadcrumb.Item key={index} href={item.href}>\n                {item.icon && <span style={{ marginRight: '4px' }}>{item.icon}</span>}\n                {item.title}\n              </Breadcrumb.Item>\n            ))}\n          </Breadcrumb>\n        )}\n\n        {/* Header content */}\n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          flexWrap: 'wrap',\n          gap: '16px',\n        }}>\n          {/* Title section */}\n          <div style={{ flex: 1, minWidth: '200px' }}>\n            <h1 style={{\n              margin: 0,\n              fontSize: '24px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n              lineHeight: 1.2,\n            }}>\n              {title}\n            </h1>\n            {subtitle && (\n              <p style={{\n                margin: '4px 0 0 0',\n                fontSize: '14px',\n                color: themeStyles.getTextColor('secondary'),\n                lineHeight: 1.4,\n              }}>\n                {subtitle}\n              </p>\n            )}\n          </div>\n\n          {/* Actions and extra content */}\n          {(actions.length > 0 || extra) && (\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '12px',\n              flexWrap: 'wrap',\n            }}>\n              {actions.length > 0 && (\n                <Space size=\"middle\">\n                  {actions}\n                </Space>\n              )}\n              {extra}\n            </div>\n          )}\n        </div>\n\n        {/* Children content */}\n        {children && (\n          <div style={{ marginTop: '16px' }}>\n            {children}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\n/**\n * Simple page header for basic pages\n */\nexport interface SimplePageHeaderProps {\n  title: string;\n  subtitle?: string;\n  backButton?: boolean;\n  onBack?: () => void;\n  actions?: React.ReactNode[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function SimplePageHeader({\n  title,\n  subtitle,\n  backButton = false,\n  onBack,\n  actions = [],\n  className,\n  style,\n}: SimplePageHeaderProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div \n      className={className} \n      style={{\n        padding: '16px 24px',\n        backgroundColor: themeStyles.getBackgroundColor('container'),\n        borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        ...style,\n      }}\n    >\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        gap: '16px',\n      }}>\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flex: 1 }}>\n          {backButton && (\n            <button\n              onClick={onBack}\n              style={{\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                fontSize: '16px',\n                color: themeStyles.getTextColor('secondary'),\n                padding: '4px',\n              }}\n            >\n              ←\n            </button>\n          )}\n          <div>\n            <h1 style={{\n              margin: 0,\n              fontSize: '20px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n            }}>\n              {title}\n            </h1>\n            {subtitle && (\n              <p style={{\n                margin: '2px 0 0 0',\n                fontSize: '12px',\n                color: themeStyles.getTextColor('secondary'),\n              }}>\n                {subtitle}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {actions.length > 0 && (\n          <Space size=\"small\">\n            {actions}\n          </Space>\n        )}\n      </div>\n    </div>\n  );\n}\n\n/**\n * Section header for content sections\n */\nexport interface SectionHeaderProps {\n  title: string;\n  subtitle?: string;\n  actions?: React.ReactNode[];\n  divider?: boolean;\n  size?: 'small' | 'medium' | 'large';\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function SectionHeader({\n  title,\n  subtitle,\n  actions = [],\n  divider = false,\n  size = 'medium',\n  className,\n  style,\n}: SectionHeaderProps) {\n  const themeStyles = useThemeStyles();\n\n  const sizeMap = {\n    small: { fontSize: '16px', marginBottom: '12px' },\n    medium: { fontSize: '18px', marginBottom: '16px' },\n    large: { fontSize: '20px', marginBottom: '20px' },\n  };\n\n  return (\n    <div className={className} style={style}>\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        marginBottom: sizeMap[size].marginBottom,\n      }}>\n        <div>\n          <h2 style={{\n            margin: 0,\n            fontSize: sizeMap[size].fontSize,\n            fontWeight: 'bold',\n            color: themeStyles.getTextColor('primary'),\n          }}>\n            {title}\n          </h2>\n          {subtitle && (\n            <p style={{\n              margin: '4px 0 0 0',\n              fontSize: '12px',\n              color: themeStyles.getTextColor('secondary'),\n            }}>\n              {subtitle}\n            </p>\n          )}\n        </div>\n\n        {actions.length > 0 && (\n          <Space size=\"small\">\n            {actions}\n          </Space>\n        )}\n      </div>\n\n      {divider && <Divider style={{ margin: '0 0 16px 0' }} />}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAOD;AAAA;AADA;AADA;AAAA;AAAA;;;AAHA;;;;AAkCO,SAAS,WAAW,EACzB,KAAK,EACL,QAAQ,EACR,cAAc,EAAE,EAChB,UAAU,EAAE,EACZ,KAAK,EACL,QAAQ,EACR,cAAc,IAAI,EAClB,SAAS,EACT,KAAK,EACW;;IAChB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qCAAqC;IACrC,MAAM,iBAAiB,YAAY,MAAM,GAAG,KAAK,WAAW,CAAC,EAAE,CAAC,IAAI,KAAK,MACrE;QAAC;YAAE,OAAO;YAAQ,MAAM;YAAK,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;QAAI;WAAM;KAAY,GACtE;IAEJ,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;kBAChC,cAAA,6LAAC;YAAI,OAAO;gBACV,SAAS;gBACT,iBAAiB,YAAY,kBAAkB,CAAC;gBAChD,cAAc,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY,GAAG;YACrF;;gBAEG,eAAe,MAAM,GAAG,mBACvB,6LAAC,6LAAA,CAAA,aAAU;oBAAC,OAAO;wBAAE,cAAc;oBAAM;8BACtC,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,6LAAC,6LAAA,CAAA,aAAU,CAAC,IAAI;4BAAa,MAAM,KAAK,IAAI;;gCACzC,KAAK,IAAI,kBAAI,6LAAC;oCAAK,OAAO;wCAAE,aAAa;oCAAM;8CAAI,KAAK,IAAI;;;;;;gCAC5D,KAAK,KAAK;;2BAFS;;;;;;;;;;8BAS5B,6LAAC;oBAAI,OAAO;wBACV,SAAS;wBACT,YAAY;wBACZ,gBAAgB;wBAChB,UAAU;wBACV,KAAK;oBACP;;sCAEE,6LAAC;4BAAI,OAAO;gCAAE,MAAM;gCAAG,UAAU;4BAAQ;;8CACvC,6LAAC;oCAAG,OAAO;wCACT,QAAQ;wCACR,UAAU;wCACV,YAAY;wCACZ,OAAO,YAAY,YAAY,CAAC;wCAChC,YAAY;oCACd;8CACG;;;;;;gCAEF,0BACC,6LAAC;oCAAE,OAAO;wCACR,QAAQ;wCACR,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;wCAChC,YAAY;oCACd;8CACG;;;;;;;;;;;;wBAMN,CAAC,QAAQ,MAAM,GAAG,KAAK,KAAK,mBAC3B,6LAAC;4BAAI,OAAO;gCACV,SAAS;gCACT,YAAY;gCACZ,KAAK;gCACL,UAAU;4BACZ;;gCACG,QAAQ,MAAM,GAAG,mBAChB,6LAAC,mMAAA,CAAA,QAAK;oCAAC,MAAK;8CACT;;;;;;gCAGJ;;;;;;;;;;;;;gBAMN,0BACC,6LAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAO;8BAC7B;;;;;;;;;;;;;;;;;AAMb;GA/FgB;;QAWM,wHAAA,CAAA,iBAAc;;;KAXpB;AA8GT,SAAS,iBAAiB,EAC/B,KAAK,EACL,QAAQ,EACR,aAAa,KAAK,EAClB,MAAM,EACN,UAAU,EAAE,EACZ,SAAS,EACT,KAAK,EACiB;;IACtB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAClE,GAAG,KAAK;QACV;kBAEA,cAAA,6LAAC;YAAI,OAAO;gBACV,SAAS;gBACT,YAAY;gBACZ,gBAAgB;gBAChB,KAAK;YACP;;8BACE,6LAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,YAAY;wBAAU,KAAK;wBAAQ,MAAM;oBAAE;;wBACvE,4BACC,6LAAC;4BACC,SAAS;4BACT,OAAO;gCACL,YAAY;gCACZ,QAAQ;gCACR,QAAQ;gCACR,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;gCAChC,SAAS;4BACX;sCACD;;;;;;sCAIH,6LAAC;;8CACC,6LAAC;oCAAG,OAAO;wCACT,QAAQ;wCACR,UAAU;wCACV,YAAY;wCACZ,OAAO,YAAY,YAAY,CAAC;oCAClC;8CACG;;;;;;gCAEF,0BACC,6LAAC;oCAAE,OAAO;wCACR,QAAQ;wCACR,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;oCAClC;8CACG;;;;;;;;;;;;;;;;;;gBAMR,QAAQ,MAAM,GAAG,mBAChB,6LAAC,mMAAA,CAAA,QAAK;oBAAC,MAAK;8BACT;;;;;;;;;;;;;;;;;AAMb;IAxEgB;;QASM,wHAAA,CAAA,iBAAc;;;MATpB;AAuFT,SAAS,cAAc,EAC5B,KAAK,EACL,QAAQ,EACR,UAAU,EAAE,EACZ,UAAU,KAAK,EACf,OAAO,QAAQ,EACf,SAAS,EACT,KAAK,EACc;;IACnB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,UAAU;QACd,OAAO;YAAE,UAAU;YAAQ,cAAc;QAAO;QAChD,QAAQ;YAAE,UAAU;YAAQ,cAAc;QAAO;QACjD,OAAO;YAAE,UAAU;YAAQ,cAAc;QAAO;IAClD;IAEA,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;;0BAChC,6LAAC;gBAAI,OAAO;oBACV,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,cAAc,OAAO,CAAC,KAAK,CAAC,YAAY;gBAC1C;;kCACE,6LAAC;;0CACC,6LAAC;gCAAG,OAAO;oCACT,QAAQ;oCACR,UAAU,OAAO,CAAC,KAAK,CAAC,QAAQ;oCAChC,YAAY;oCACZ,OAAO,YAAY,YAAY,CAAC;gCAClC;0CACG;;;;;;4BAEF,0BACC,6LAAC;gCAAE,OAAO;oCACR,QAAQ;oCACR,UAAU;oCACV,OAAO,YAAY,YAAY,CAAC;gCAClC;0CACG;;;;;;;;;;;;oBAKN,QAAQ,MAAM,GAAG,mBAChB,6LAAC,mMAAA,CAAA,QAAK;wBAAC,MAAK;kCACT;;;;;;;;;;;;YAKN,yBAAW,6LAAC,uLAAA,CAAA,UAAO;gBAAC,OAAO;oBAAE,QAAQ;gBAAa;;;;;;;;;;;;AAGzD;IAvDgB;;QASM,wHAAA,CAAA,iBAAc;;;MATpB"}}, {"offset": {"line": 5737, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5743, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/content-layout.tsx"], "sourcesContent": ["/**\n * Content Layout Components\n * Reusable layout components for content organization\n */\n\n'use client';\n\nimport React from 'react';\nimport { Layout, Row, Col } from 'antd';\nimport { useThemeStyles } from '@/theme';\n\nconst { Content } = Layout;\n\n/**\n * Main content layout props\n */\nexport interface ContentLayoutProps {\n  children: React.ReactNode;\n  maxWidth?: number | string;\n  padding?: 'none' | 'small' | 'medium' | 'large';\n  centered?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Main content layout component\n */\nexport function ContentLayout({\n  children,\n  maxWidth = '1200px',\n  padding = 'medium',\n  centered = true,\n  className,\n  style,\n}: ContentLayoutProps) {\n  const themeStyles = useThemeStyles();\n\n  const paddingMap = {\n    none: '0',\n    small: '12px',\n    medium: '24px',\n    large: '32px',\n  };\n\n  const contentStyle: React.CSSProperties = {\n    maxWidth: typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth,\n    margin: centered ? '0 auto' : '0',\n    padding: paddingMap[padding],\n    backgroundColor: themeStyles.getBackgroundColor('layout'),\n    minHeight: 'calc(100vh - 64px)', // Assuming 64px header height\n    ...style,\n  };\n\n  return (\n    <Content className={className} style={contentStyle}>\n      {children}\n    </Content>\n  );\n}\n\n/**\n * Two column layout props\n */\nexport interface TwoColumnLayoutProps {\n  leftContent: React.ReactNode;\n  rightContent: React.ReactNode;\n  leftSpan?: number;\n  rightSpan?: number;\n  gutter?: number | [number, number];\n  responsive?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Two column layout component\n */\nexport function TwoColumnLayout({\n  leftContent,\n  rightContent,\n  leftSpan = 16,\n  rightSpan = 8,\n  gutter = 24,\n  responsive = true,\n  className,\n  style,\n}: TwoColumnLayoutProps) {\n  const responsiveProps = responsive ? {\n    xs: 24,\n    sm: 24,\n    md: leftSpan,\n    lg: leftSpan,\n    xl: leftSpan,\n  } : { span: leftSpan };\n\n  const rightResponsiveProps = responsive ? {\n    xs: 24,\n    sm: 24,\n    md: rightSpan,\n    lg: rightSpan,\n    xl: rightSpan,\n  } : { span: rightSpan };\n\n  return (\n    <Row gutter={gutter} className={className} style={style}>\n      <Col {...responsiveProps}>\n        {leftContent}\n      </Col>\n      <Col {...rightResponsiveProps}>\n        {rightContent}\n      </Col>\n    </Row>\n  );\n}\n\n/**\n * Three column layout props\n */\nexport interface ThreeColumnLayoutProps {\n  leftContent: React.ReactNode;\n  centerContent: React.ReactNode;\n  rightContent: React.ReactNode;\n  leftSpan?: number;\n  centerSpan?: number;\n  rightSpan?: number;\n  gutter?: number | [number, number];\n  responsive?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Three column layout component\n */\nexport function ThreeColumnLayout({\n  leftContent,\n  centerContent,\n  rightContent,\n  leftSpan = 6,\n  centerSpan = 12,\n  rightSpan = 6,\n  gutter = 24,\n  responsive = true,\n  className,\n  style,\n}: ThreeColumnLayoutProps) {\n  const getResponsiveProps = (span: number) => responsive ? {\n    xs: 24,\n    sm: 24,\n    md: span,\n    lg: span,\n    xl: span,\n  } : { span };\n\n  return (\n    <Row gutter={gutter} className={className} style={style}>\n      <Col {...getResponsiveProps(leftSpan)}>\n        {leftContent}\n      </Col>\n      <Col {...getResponsiveProps(centerSpan)}>\n        {centerContent}\n      </Col>\n      <Col {...getResponsiveProps(rightSpan)}>\n        {rightContent}\n      </Col>\n    </Row>\n  );\n}\n\n/**\n * Grid layout props\n */\nexport interface GridLayoutProps {\n  children: React.ReactNode;\n  columns?: number;\n  gutter?: number | [number, number];\n  responsive?: {\n    xs?: number;\n    sm?: number;\n    md?: number;\n    lg?: number;\n    xl?: number;\n    xxl?: number;\n  };\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Grid layout component\n */\nexport function GridLayout({\n  children,\n  columns = 3,\n  gutter = 24,\n  responsive,\n  className,\n  style,\n}: GridLayoutProps) {\n  const defaultResponsive = {\n    xs: 1,\n    sm: 1,\n    md: 2,\n    lg: columns,\n    xl: columns,\n    xxl: columns,\n  };\n\n  const responsiveConfig = responsive || defaultResponsive;\n  const span = 24 / columns;\n\n  return (\n    <Row gutter={gutter} className={className} style={style}>\n      {React.Children.map(children, (child, index) => (\n        <Col\n          key={index}\n          xs={24 / responsiveConfig.xs}\n          sm={24 / responsiveConfig.sm}\n          md={24 / responsiveConfig.md}\n          lg={24 / responsiveConfig.lg}\n          xl={24 / responsiveConfig.xl}\n          xxl={24 / responsiveConfig.xxl}\n        >\n          {child}\n        </Col>\n      ))}\n    </Row>\n  );\n}\n\n/**\n * Sidebar layout props\n */\nexport interface SidebarLayoutProps {\n  sidebar: React.ReactNode;\n  content: React.ReactNode;\n  sidebarWidth?: number;\n  sidebarPosition?: 'left' | 'right';\n  collapsible?: boolean;\n  collapsed?: boolean;\n  onCollapse?: (collapsed: boolean) => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Sidebar layout component\n */\nexport function SidebarLayout({\n  sidebar,\n  content,\n  sidebarWidth = 250,\n  sidebarPosition = 'left',\n  collapsible = false,\n  collapsed = false,\n  onCollapse,\n  className,\n  style,\n}: SidebarLayoutProps) {\n  const themeStyles = useThemeStyles();\n\n  const sidebarStyle: React.CSSProperties = {\n    width: collapsed ? '80px' : `${sidebarWidth}px`,\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    borderRight: sidebarPosition === 'left' ? `1px solid ${themeStyles.getBorderColor('primary')}` : 'none',\n    borderLeft: sidebarPosition === 'right' ? `1px solid ${themeStyles.getBorderColor('primary')}` : 'none',\n    transition: 'width 0.2s ease',\n    overflow: 'hidden',\n  };\n\n  const contentStyle: React.CSSProperties = {\n    flex: 1,\n    backgroundColor: themeStyles.getBackgroundColor('layout'),\n    overflow: 'auto',\n  };\n\n  const layoutStyle: React.CSSProperties = {\n    display: 'flex',\n    flexDirection: sidebarPosition === 'left' ? 'row' : 'row-reverse',\n    height: '100%',\n    ...style,\n  };\n\n  return (\n    <div className={className} style={layoutStyle}>\n      <div style={sidebarStyle}>\n        {collapsible && (\n          <div style={{\n            padding: '8px',\n            borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,\n            textAlign: 'center',\n          }}>\n            <button\n              onClick={() => onCollapse?.(!collapsed)}\n              style={{\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                fontSize: '16px',\n                color: themeStyles.getTextColor('secondary'),\n              }}\n            >\n              {collapsed ? '→' : '←'}\n            </button>\n          </div>\n        )}\n        {sidebar}\n      </div>\n      <div style={contentStyle}>\n        {content}\n      </div>\n    </div>\n  );\n}\n\n/**\n * Container component for consistent spacing\n */\nexport interface ContainerProps {\n  children: React.ReactNode;\n  size?: 'small' | 'medium' | 'large' | 'full';\n  padding?: boolean;\n  centered?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function Container({\n  children,\n  size = 'large',\n  padding = true,\n  centered = true,\n  className,\n  style,\n}: ContainerProps) {\n  const sizeMap = {\n    small: '600px',\n    medium: '900px',\n    large: '1200px',\n    full: '100%',\n  };\n\n  const containerStyle: React.CSSProperties = {\n    maxWidth: sizeMap[size],\n    margin: centered ? '0 auto' : '0',\n    padding: padding ? '0 24px' : '0',\n    width: '100%',\n    ...style,\n  };\n\n  return (\n    <div className={className} style={containerStyle}>\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAID;AAEA;AADA;AACA;AADA;AAAA;;;AAHA;;;;AAMA,MAAM,EAAE,OAAO,EAAE,GAAG,qLAAA,CAAA,SAAM;AAiBnB,SAAS,cAAc,EAC5B,QAAQ,EACR,WAAW,QAAQ,EACnB,UAAU,QAAQ,EAClB,WAAW,IAAI,EACf,SAAS,EACT,KAAK,EACc;;IACnB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,aAAa;QACjB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,eAAoC;QACxC,UAAU,OAAO,aAAa,WAAW,GAAG,SAAS,EAAE,CAAC,GAAG;QAC3D,QAAQ,WAAW,WAAW;QAC9B,SAAS,UAAU,CAAC,QAAQ;QAC5B,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,WAAW;QACX,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QAAQ,WAAW;QAAW,OAAO;kBACnC;;;;;;AAGP;GA/BgB;;QAQM,wHAAA,CAAA,iBAAc;;;KARpB;AAkDT,SAAS,gBAAgB,EAC9B,WAAW,EACX,YAAY,EACZ,WAAW,EAAE,EACb,YAAY,CAAC,EACb,SAAS,EAAE,EACX,aAAa,IAAI,EACjB,SAAS,EACT,KAAK,EACgB;IACrB,MAAM,kBAAkB,aAAa;QACnC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,IAAI;QAAE,MAAM;IAAS;IAErB,MAAM,uBAAuB,aAAa;QACxC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,IAAI;QAAE,MAAM;IAAU;IAEtB,qBACE,6LAAC,+KAAA,CAAA,MAAG;QAAC,QAAQ;QAAQ,WAAW;QAAW,OAAO;;0BAChD,6LAAC,+KAAA,CAAA,MAAG;gBAAE,GAAG,eAAe;0BACrB;;;;;;0BAEH,6LAAC,+KAAA,CAAA,MAAG;gBAAE,GAAG,oBAAoB;0BAC1B;;;;;;;;;;;;AAIT;MApCgB;AAyDT,SAAS,kBAAkB,EAChC,WAAW,EACX,aAAa,EACb,YAAY,EACZ,WAAW,CAAC,EACZ,aAAa,EAAE,EACf,YAAY,CAAC,EACb,SAAS,EAAE,EACX,aAAa,IAAI,EACjB,SAAS,EACT,KAAK,EACkB;IACvB,MAAM,qBAAqB,CAAC,OAAiB,aAAa;YACxD,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN,IAAI;YAAE;QAAK;IAEX,qBACE,6LAAC,+KAAA,CAAA,MAAG;QAAC,QAAQ;QAAQ,WAAW;QAAW,OAAO;;0BAChD,6LAAC,+KAAA,CAAA,MAAG;gBAAE,GAAG,mBAAmB,SAAS;0BAClC;;;;;;0BAEH,6LAAC,+KAAA,CAAA,MAAG;gBAAE,GAAG,mBAAmB,WAAW;0BACpC;;;;;;0BAEH,6LAAC,+KAAA,CAAA,MAAG;gBAAE,GAAG,mBAAmB,UAAU;0BACnC;;;;;;;;;;;;AAIT;MAjCgB;AAyDT,SAAS,WAAW,EACzB,QAAQ,EACR,UAAU,CAAC,EACX,SAAS,EAAE,EACX,UAAU,EACV,SAAS,EACT,KAAK,EACW;IAChB,MAAM,oBAAoB;QACxB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,KAAK;IACP;IAEA,MAAM,mBAAmB,cAAc;IACvC,MAAM,OAAO,KAAK;IAElB,qBACE,6LAAC,+KAAA,CAAA,MAAG;QAAC,QAAQ;QAAQ,WAAW;QAAW,OAAO;kBAC/C,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,sBACpC,6LAAC,+KAAA,CAAA,MAAG;gBAEF,IAAI,KAAK,iBAAiB,EAAE;gBAC5B,IAAI,KAAK,iBAAiB,EAAE;gBAC5B,IAAI,KAAK,iBAAiB,EAAE;gBAC5B,IAAI,KAAK,iBAAiB,EAAE;gBAC5B,IAAI,KAAK,iBAAiB,EAAE;gBAC5B,KAAK,KAAK,iBAAiB,GAAG;0BAE7B;eARI;;;;;;;;;;AAaf;MArCgB;AAyDT,SAAS,cAAc,EAC5B,OAAO,EACP,OAAO,EACP,eAAe,GAAG,EAClB,kBAAkB,MAAM,EACxB,cAAc,KAAK,EACnB,YAAY,KAAK,EACjB,UAAU,EACV,SAAS,EACT,KAAK,EACc;;IACnB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,eAAoC;QACxC,OAAO,YAAY,SAAS,GAAG,aAAa,EAAE,CAAC;QAC/C,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,aAAa,oBAAoB,SAAS,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY,GAAG;QACjG,YAAY,oBAAoB,UAAU,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY,GAAG;QACjG,YAAY;QACZ,UAAU;IACZ;IAEA,MAAM,eAAoC;QACxC,MAAM;QACN,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,UAAU;IACZ;IAEA,MAAM,cAAmC;QACvC,SAAS;QACT,eAAe,oBAAoB,SAAS,QAAQ;QACpD,QAAQ;QACR,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;;0BAChC,6LAAC;gBAAI,OAAO;;oBACT,6BACC,6LAAC;wBAAI,OAAO;4BACV,SAAS;4BACT,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;4BAClE,WAAW;wBACb;kCACE,cAAA,6LAAC;4BACC,SAAS,IAAM,aAAa,CAAC;4BAC7B,OAAO;gCACL,YAAY;gCACZ,QAAQ;gCACR,QAAQ;gCACR,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;4BAClC;sCAEC,YAAY,MAAM;;;;;;;;;;;oBAIxB;;;;;;;0BAEH,6LAAC;gBAAI,OAAO;0BACT;;;;;;;;;;;;AAIT;IAjEgB;;QAWM,wHAAA,CAAA,iBAAc;;;MAXpB;AA+ET,SAAS,UAAU,EACxB,QAAQ,EACR,OAAO,OAAO,EACd,UAAU,IAAI,EACd,WAAW,IAAI,EACf,SAAS,EACT,KAAK,EACU;IACf,MAAM,UAAU;QACd,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IAEA,MAAM,iBAAsC;QAC1C,UAAU,OAAO,CAAC,KAAK;QACvB,QAAQ,WAAW,WAAW;QAC9B,SAAS,UAAU,WAAW;QAC9B,OAAO;QACP,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;kBAC/B;;;;;;AAGP;MA5BgB"}}, {"offset": {"line": 6049, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6055, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/index.ts"], "sourcesContent": ["/**\n * Layout Components Index\n * Export all layout components\n */\n\n// Main app layout components\nexport * from './app-layout';\nexport * from './app-header';\nexport * from './app-sidebar';\nexport * from './app-footer';\n\n// Page header components\nexport * from './page-header';\n\n// Content layout components\nexport * from './content-layout';\n\n// Re-export Ant Design layout components for convenience\nexport {\n  Layout,\n  Header,\n  Footer,\n  Sider,\n  Content,\n} from 'antd';\n\n/**\n * Layout components metadata\n */\nexport const LAYOUT_COMPONENTS_VERSION = '1.0.0';\nexport const LAYOUT_COMPONENTS_NAME = 'APISportsGame Layout Components';\n\n/**\n * Setup function for layout components\n */\nexport function setupLayoutComponents() {\n  console.log(`${LAYOUT_COMPONENTS_NAME} v${LAYOUT_COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,6BAA6B;;;;;;;;;;;;;AAwBtB,MAAM,4BAA4B;AAClC,MAAM,yBAAyB;AAK/B,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,uBAAuB,EAAE,EAAE,0BAA0B,YAAY,CAAC;AACnF"}}, {"offset": {"line": 6079, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6099, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/forms/form-wrapper.tsx"], "sourcesContent": ["/**\n * Form Wrapper Components\n * Enhanced form components with validation and theme integration\n */\n\n'use client';\n\nimport React from 'react';\nimport { Form as AntForm, FormProps as AntFormProps, Space, Divider } from 'antd';\nimport { Button, LoadingButton } from '@/components/ui';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Enhanced form props\n */\nexport interface FormProps extends AntFormProps {\n  title?: string;\n  subtitle?: string;\n  loading?: boolean;\n  submitText?: string;\n  cancelText?: string;\n  showCancel?: boolean;\n  onCancel?: () => void;\n  actions?: React.ReactNode[];\n  footer?: React.ReactNode;\n  compact?: boolean;\n}\n\n/**\n * Enhanced Form component\n */\nexport function Form({\n  title,\n  subtitle,\n  loading = false,\n  submitText = 'Submit',\n  cancelText = 'Cancel',\n  showCancel = false,\n  onCancel,\n  actions = [],\n  footer,\n  compact = false,\n  children,\n  onFinish,\n  ...props\n}: FormProps) {\n  const themeStyles = useThemeStyles();\n\n  const formStyle: React.CSSProperties = {\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    padding: compact ? '16px' : '24px',\n    borderRadius: '8px',\n    border: `1px solid ${themeStyles.getBorderColor('primary')}`,\n  };\n\n  return (\n    <div style={formStyle}>\n      {/* Form header */}\n      {(title || subtitle) && (\n        <div style={{ marginBottom: '24px' }}>\n          {title && (\n            <h2 style={{\n              margin: 0,\n              fontSize: '20px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n            }}>\n              {title}\n            </h2>\n          )}\n          {subtitle && (\n            <p style={{\n              margin: '4px 0 0 0',\n              fontSize: '14px',\n              color: themeStyles.getTextColor('secondary'),\n            }}>\n              {subtitle}\n            </p>\n          )}\n          <Divider style={{ margin: '16px 0 0 0' }} />\n        </div>\n      )}\n\n      {/* Form content */}\n      <AntForm\n        layout=\"vertical\"\n        onFinish={onFinish}\n        disabled={loading}\n        {...props}\n      >\n        {children}\n\n        {/* Form actions */}\n        <div style={{ marginTop: '24px' }}>\n          <Space>\n            <LoadingButton\n              type=\"primary\"\n              htmlType=\"submit\"\n              isLoading={loading}\n              loadingText=\"Submitting...\"\n            >\n              {submitText}\n            </LoadingButton>\n            \n            {showCancel && (\n              <Button\n                type=\"default\"\n                onClick={onCancel}\n                disabled={loading}\n              >\n                {cancelText}\n              </Button>\n            )}\n            \n            {actions}\n          </Space>\n        </div>\n      </AntForm>\n\n      {/* Custom footer */}\n      {footer && (\n        <div style={{ marginTop: '16px', paddingTop: '16px', borderTop: `1px solid ${themeStyles.getBorderColor('primary')}` }}>\n          {footer}\n        </div>\n      )}\n    </div>\n  );\n}\n\n/**\n * Inline form props\n */\nexport interface InlineFormProps extends AntFormProps {\n  submitText?: string;\n  loading?: boolean;\n  actions?: React.ReactNode[];\n}\n\n/**\n * Inline form component for simple forms\n */\nexport function InlineForm({\n  submitText = 'Submit',\n  loading = false,\n  actions = [],\n  children,\n  onFinish,\n  ...props\n}: InlineFormProps) {\n  return (\n    <AntForm\n      layout=\"inline\"\n      onFinish={onFinish}\n      disabled={loading}\n      {...props}\n    >\n      {children}\n      \n      <AntForm.Item>\n        <Space>\n          <LoadingButton\n            type=\"primary\"\n            htmlType=\"submit\"\n            isLoading={loading}\n            compact\n          >\n            {submitText}\n          </LoadingButton>\n          {actions}\n        </Space>\n      </AntForm.Item>\n    </AntForm>\n  );\n}\n\n/**\n * Search form props\n */\nexport interface SearchFormProps {\n  onSearch: (values: any) => void;\n  onReset?: () => void;\n  loading?: boolean;\n  children: React.ReactNode;\n  collapsed?: boolean;\n  onToggleCollapse?: () => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Search form component\n */\nexport function SearchForm({\n  onSearch,\n  onReset,\n  loading = false,\n  children,\n  collapsed = false,\n  onToggleCollapse,\n  className,\n  style,\n}: SearchFormProps) {\n  const themeStyles = useThemeStyles();\n\n  const [form] = AntForm.useForm();\n\n  const handleReset = () => {\n    form.resetFields();\n    onReset?.();\n  };\n\n  return (\n    <div \n      className={className}\n      style={{\n        backgroundColor: themeStyles.getBackgroundColor('container'),\n        padding: '16px',\n        borderRadius: '8px',\n        border: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        marginBottom: '16px',\n        ...style,\n      }}\n    >\n      <AntForm\n        form={form}\n        layout=\"vertical\"\n        onFinish={onSearch}\n        disabled={loading}\n      >\n        <div style={{ \n          display: collapsed ? 'none' : 'block',\n          marginBottom: '16px',\n        }}>\n          {children}\n        </div>\n\n        <div style={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n        }}>\n          <Space>\n            <LoadingButton\n              type=\"primary\"\n              htmlType=\"submit\"\n              isLoading={loading}\n              compact\n            >\n              Search\n            </LoadingButton>\n            \n            <Button\n              onClick={handleReset}\n              disabled={loading}\n              compact\n            >\n              Reset\n            </Button>\n          </Space>\n\n          {onToggleCollapse && (\n            <Button\n              type=\"text\"\n              onClick={onToggleCollapse}\n              compact\n            >\n              {collapsed ? 'Expand' : 'Collapse'}\n            </Button>\n          )}\n        </div>\n      </AntForm>\n    </div>\n  );\n}\n\n/**\n * Modal form props\n */\nexport interface ModalFormProps extends FormProps {\n  visible: boolean;\n  onClose: () => void;\n  width?: number;\n  destroyOnClose?: boolean;\n}\n\n/**\n * Modal form component\n */\nexport function ModalForm({\n  visible,\n  onClose,\n  width = 600,\n  destroyOnClose = true,\n  title,\n  loading = false,\n  submitText = 'Save',\n  cancelText = 'Cancel',\n  children,\n  onFinish,\n  ...props\n}: ModalFormProps) {\n  const { Modal } = require('antd');\n  const [form] = AntForm.useForm();\n\n  const handleFinish = async (values: any) => {\n    try {\n      await onFinish?.(values);\n      form.resetFields();\n      onClose();\n    } catch (error) {\n      // Error handling is done by the parent component\n    }\n  };\n\n  const handleCancel = () => {\n    form.resetFields();\n    onClose();\n  };\n\n  return (\n    <Modal\n      title={title}\n      open={visible}\n      onCancel={handleCancel}\n      width={width}\n      destroyOnClose={destroyOnClose}\n      footer={null}\n    >\n      <AntForm\n        form={form}\n        layout=\"vertical\"\n        onFinish={handleFinish}\n        disabled={loading}\n        {...props}\n      >\n        {children}\n\n        <div style={{ \n          marginTop: '24px',\n          textAlign: 'right',\n          borderTop: '1px solid #f0f0f0',\n          paddingTop: '16px',\n        }}>\n          <Space>\n            <Button onClick={handleCancel} disabled={loading}>\n              {cancelText}\n            </Button>\n            <LoadingButton\n              type=\"primary\"\n              htmlType=\"submit\"\n              isLoading={loading}\n            >\n              {submitText}\n            </LoadingButton>\n          </Space>\n        </div>\n      </AntForm>\n    </Modal>\n  );\n}\n\n/**\n * Drawer form props\n */\nexport interface DrawerFormProps extends FormProps {\n  visible: boolean;\n  onClose: () => void;\n  width?: number;\n  placement?: 'left' | 'right';\n  destroyOnClose?: boolean;\n}\n\n/**\n * Drawer form component\n */\nexport function DrawerForm({\n  visible,\n  onClose,\n  width = 600,\n  placement = 'right',\n  destroyOnClose = true,\n  title,\n  loading = false,\n  submitText = 'Save',\n  cancelText = 'Cancel',\n  children,\n  onFinish,\n  ...props\n}: DrawerFormProps) {\n  const { Drawer } = require('antd');\n  const [form] = AntForm.useForm();\n\n  const handleFinish = async (values: any) => {\n    try {\n      await onFinish?.(values);\n      form.resetFields();\n      onClose();\n    } catch (error) {\n      // Error handling is done by the parent component\n    }\n  };\n\n  const handleClose = () => {\n    form.resetFields();\n    onClose();\n  };\n\n  return (\n    <Drawer\n      title={title}\n      open={visible}\n      onClose={handleClose}\n      width={width}\n      placement={placement}\n      destroyOnClose={destroyOnClose}\n      footer={\n        <div style={{ textAlign: 'right' }}>\n          <Space>\n            <Button onClick={handleClose} disabled={loading}>\n              {cancelText}\n            </Button>\n            <LoadingButton\n              type=\"primary\"\n              onClick={() => form.submit()}\n              isLoading={loading}\n            >\n              {submitText}\n            </LoadingButton>\n          </Space>\n        </div>\n      }\n    >\n      <AntForm\n        form={form}\n        layout=\"vertical\"\n        onFinish={handleFinish}\n        disabled={loading}\n        {...props}\n      >\n        {children}\n      </AntForm>\n    </Drawer>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAMD;AACA;AAFA;AAEA;AAFA;AAAA;AACA;;;AAJA;;;;AA0BO,SAAS,KAAK,EACnB,KAAK,EACL,QAAQ,EACR,UAAU,KAAK,EACf,aAAa,QAAQ,EACrB,aAAa,QAAQ,EACrB,aAAa,KAAK,EAClB,QAAQ,EACR,UAAU,EAAE,EACZ,MAAM,EACN,UAAU,KAAK,EACf,QAAQ,EACR,QAAQ,EACR,GAAG,OACO;;IACV,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,YAAiC;QACrC,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,SAAS,UAAU,SAAS;QAC5B,cAAc;QACd,QAAQ,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;IAC9D;IAEA,qBACE,6LAAC;QAAI,OAAO;;YAET,CAAC,SAAS,QAAQ,mBACjB,6LAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;;oBAChC,uBACC,6LAAC;wBAAG,OAAO;4BACT,QAAQ;4BACR,UAAU;4BACV,YAAY;4BACZ,OAAO,YAAY,YAAY,CAAC;wBAClC;kCACG;;;;;;oBAGJ,0BACC,6LAAC;wBAAE,OAAO;4BACR,QAAQ;4BACR,UAAU;4BACV,OAAO,YAAY,YAAY,CAAC;wBAClC;kCACG;;;;;;kCAGL,6LAAC,uLAAA,CAAA,UAAO;wBAAC,OAAO;4BAAE,QAAQ;wBAAa;;;;;;;;;;;;0BAK3C,6LAAC,iLAAA,CAAA,OAAO;gBACN,QAAO;gBACP,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;;oBAER;kCAGD,6LAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAO;kCAC9B,cAAA,6LAAC,mMAAA,CAAA,QAAK;;8CACJ,6LAAC,qIAAA,CAAA,gBAAa;oCACZ,MAAK;oCACL,UAAS;oCACT,WAAW;oCACX,aAAY;8CAEX;;;;;;gCAGF,4BACC,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;oCACT,UAAU;8CAET;;;;;;gCAIJ;;;;;;;;;;;;;;;;;;YAMN,wBACC,6LAAC;gBAAI,OAAO;oBAAE,WAAW;oBAAQ,YAAY;oBAAQ,WAAW,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;gBAAC;0BAClH;;;;;;;;;;;;AAKX;GAhGgB;;QAeM,wHAAA,CAAA,iBAAc;;;KAfpB;AA8GT,SAAS,WAAW,EACzB,aAAa,QAAQ,EACrB,UAAU,KAAK,EACf,UAAU,EAAE,EACZ,QAAQ,EACR,QAAQ,EACR,GAAG,OACa;IAChB,qBACE,6LAAC,iLAAA,CAAA,OAAO;QACN,QAAO;QACP,UAAU;QACV,UAAU;QACT,GAAG,KAAK;;YAER;0BAED,6LAAC,iLAAA,CAAA,OAAO,CAAC,IAAI;0BACX,cAAA,6LAAC,mMAAA,CAAA,QAAK;;sCACJ,6LAAC,qIAAA,CAAA,gBAAa;4BACZ,MAAK;4BACL,UAAS;4BACT,WAAW;4BACX,OAAO;sCAEN;;;;;;wBAEF;;;;;;;;;;;;;;;;;;AAKX;MAhCgB;AAmDT,SAAS,WAAW,EACzB,QAAQ,EACR,OAAO,EACP,UAAU,KAAK,EACf,QAAQ,EACR,YAAY,KAAK,EACjB,gBAAgB,EAChB,SAAS,EACT,KAAK,EACW;;IAChB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAO,CAAC,OAAO;IAE9B,MAAM,cAAc;QAClB,KAAK,WAAW;QAChB;IACF;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,SAAS;YACT,cAAc;YACd,QAAQ,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAC5D,cAAc;YACd,GAAG,KAAK;QACV;kBAEA,cAAA,6LAAC,iLAAA,CAAA,OAAO;YACN,MAAM;YACN,QAAO;YACP,UAAU;YACV,UAAU;;8BAEV,6LAAC;oBAAI,OAAO;wBACV,SAAS,YAAY,SAAS;wBAC9B,cAAc;oBAChB;8BACG;;;;;;8BAGH,6LAAC;oBAAI,OAAO;wBACV,SAAS;wBACT,gBAAgB;wBAChB,YAAY;oBACd;;sCACE,6LAAC,mMAAA,CAAA,QAAK;;8CACJ,6LAAC,qIAAA,CAAA,gBAAa;oCACZ,MAAK;oCACL,UAAS;oCACT,WAAW;oCACX,OAAO;8CACR;;;;;;8CAID,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU;oCACV,OAAO;8CACR;;;;;;;;;;;;wBAKF,kCACC,6LAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAS;4BACT,OAAO;sCAEN,YAAY,WAAW;;;;;;;;;;;;;;;;;;;;;;;AAOtC;IAjFgB;;QAUM,wHAAA,CAAA,iBAAc;QAEnB,iLAAA,CAAA,OAAO,CAAC;;;MAZT;AAgGT,SAAS,UAAU,EACxB,OAAO,EACP,OAAO,EACP,QAAQ,GAAG,EACX,iBAAiB,IAAI,EACrB,KAAK,EACL,UAAU,KAAK,EACf,aAAa,MAAM,EACnB,aAAa,QAAQ,EACrB,QAAQ,EACR,QAAQ,EACR,GAAG,OACY;;IACf,MAAM,EAAE,KAAK,EAAE;IACf,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAO,CAAC,OAAO;IAE9B,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW;YACjB,KAAK,WAAW;YAChB;QACF,EAAE,OAAO,OAAO;QACd,iDAAiD;QACnD;IACF;IAEA,MAAM,eAAe;QACnB,KAAK,WAAW;QAChB;IACF;IAEA,qBACE,6LAAC;QACC,OAAO;QACP,MAAM;QACN,UAAU;QACV,OAAO;QACP,gBAAgB;QAChB,QAAQ;kBAER,cAAA,6LAAC,iLAAA,CAAA,OAAO;YACN,MAAM;YACN,QAAO;YACP,UAAU;YACV,UAAU;YACT,GAAG,KAAK;;gBAER;8BAED,6LAAC;oBAAI,OAAO;wBACV,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,YAAY;oBACd;8BACE,cAAA,6LAAC,mMAAA,CAAA,QAAK;;0CACJ,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAc,UAAU;0CACtC;;;;;;0CAEH,6LAAC,qIAAA,CAAA,gBAAa;gCACZ,MAAK;gCACL,UAAS;gCACT,WAAW;0CAEV;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;IAvEgB;;QAcC,iLAAA,CAAA,OAAO,CAAC;;;MAdT;AAuFT,SAAS,WAAW,EACzB,OAAO,EACP,OAAO,EACP,QAAQ,GAAG,EACX,YAAY,OAAO,EACnB,iBAAiB,IAAI,EACrB,KAAK,EACL,UAAU,KAAK,EACf,aAAa,MAAM,EACnB,aAAa,QAAQ,EACrB,QAAQ,EACR,QAAQ,EACR,GAAG,OACa;;IAChB,MAAM,EAAE,MAAM,EAAE;IAChB,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAO,CAAC,OAAO;IAE9B,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW;YACjB,KAAK,WAAW;YAChB;QACF,EAAE,OAAO,OAAO;QACd,iDAAiD;QACnD;IACF;IAEA,MAAM,cAAc;QAClB,KAAK,WAAW;QAChB;IACF;IAEA,qBACE,6LAAC;QACC,OAAO;QACP,MAAM;QACN,SAAS;QACT,OAAO;QACP,WAAW;QACX,gBAAgB;QAChB,sBACE,6LAAC;YAAI,OAAO;gBAAE,WAAW;YAAQ;sBAC/B,cAAA,6LAAC,mMAAA,CAAA,QAAK;;kCACJ,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAa,UAAU;kCACrC;;;;;;kCAEH,6LAAC,qIAAA,CAAA,gBAAa;wBACZ,MAAK;wBACL,SAAS,IAAM,KAAK,MAAM;wBAC1B,WAAW;kCAEV;;;;;;;;;;;;;;;;;kBAMT,cAAA,6LAAC,iLAAA,CAAA,OAAO;YACN,MAAM;YACN,QAAO;YACP,UAAU;YACV,UAAU;YACT,GAAG,KAAK;sBAER;;;;;;;;;;;AAIT;IApEgB;;QAeC,iLAAA,CAAA,OAAO,CAAC;;;MAfT"}}, {"offset": {"line": 6587, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6593, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/forms/index.ts"], "sourcesContent": ["/**\n * Form Components Index\n * Export all form components\n */\n\n// Form wrapper components\nexport * from './form-wrapper';\n\n// Re-export Ant Design form components for convenience\nexport {\n  Form as AntForm,\n  FormInstance,\n  FormProps as AntFormProps,\n  FormItemProps,\n  FormListProps,\n} from 'antd';\n\n/**\n * Form components metadata\n */\nexport const FORM_COMPONENTS_VERSION = '1.0.0';\nexport const FORM_COMPONENTS_NAME = 'APISportsGame Form Components';\n\n/**\n * Setup function for form components\n */\nexport function setupFormComponents() {\n  console.log(`${FORM_COMPONENTS_NAME} v${FORM_COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,0BAA0B;;;;;;;;AAenB,MAAM,0BAA0B;AAChC,MAAM,uBAAuB;AAK7B,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,qBAAqB,EAAE,EAAE,wBAAwB,YAAY,CAAC;AAC/E"}}, {"offset": {"line": 6612, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6627, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/data-display/data-table.tsx"], "sourcesContent": ["/**\n * Enhanced Data Table Component\n * Extended Ant Design Table with theme integration and additional features\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { \n  Table as AntTable, \n  TableProps as AntTableProps,\n  Space,\n  Input,\n  Button,\n  Dropdown,\n  Menu,\n  Tooltip,\n  Tag,\n} from 'antd';\nimport { \n  SearchOutlined, \n  FilterOutlined, \n  DownloadOutlined,\n  ReloadOutlined,\n  SettingOutlined,\n} from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Enhanced table props\n */\nexport interface DataTableProps<T = any> extends AntTableProps<T> {\n  searchable?: boolean;\n  searchPlaceholder?: string;\n  onSearch?: (value: string) => void;\n  refreshable?: boolean;\n  onRefresh?: () => void;\n  exportable?: boolean;\n  onExport?: () => void;\n  settingsMenu?: React.ReactNode;\n  toolbar?: React.ReactNode;\n  actions?: React.ReactNode[];\n  compact?: boolean;\n  bordered?: boolean;\n}\n\n/**\n * Enhanced Data Table component\n */\nexport function DataTable<T = any>({\n  searchable = false,\n  searchPlaceholder = 'Search...',\n  onSearch,\n  refreshable = false,\n  onRefresh,\n  exportable = false,\n  onExport,\n  settingsMenu,\n  toolbar,\n  actions = [],\n  compact = false,\n  bordered = true,\n  className,\n  style,\n  ...props\n}: DataTableProps<T>) {\n  const themeStyles = useThemeStyles();\n  const [searchValue, setSearchValue] = useState('');\n\n  const handleSearch = (value: string) => {\n    setSearchValue(value);\n    onSearch?.(value);\n  };\n\n  const tableStyle: React.CSSProperties = {\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    borderRadius: '8px',\n    overflow: 'hidden',\n    ...style,\n  };\n\n  // Build toolbar actions\n  const toolbarActions = [\n    ...actions,\n    refreshable && (\n      <Tooltip title=\"Refresh\" key=\"refresh\">\n        <Button \n          icon={<ReloadOutlined />} \n          onClick={onRefresh}\n          size={compact ? 'small' : 'middle'}\n        />\n      </Tooltip>\n    ),\n    exportable && (\n      <Tooltip title=\"Export\" key=\"export\">\n        <Button \n          icon={<DownloadOutlined />} \n          onClick={onExport}\n          size={compact ? 'small' : 'middle'}\n        />\n      </Tooltip>\n    ),\n    settingsMenu && (\n      <Dropdown overlay={settingsMenu} key=\"settings\">\n        <Button \n          icon={<SettingOutlined />}\n          size={compact ? 'small' : 'middle'}\n        />\n      </Dropdown>\n    ),\n  ].filter(Boolean);\n\n  return (\n    <div className={className} style={tableStyle}>\n      {/* Table toolbar */}\n      {(searchable || toolbar || toolbarActions.length > 0) && (\n        <div style={{\n          padding: compact ? '12px' : '16px',\n          borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          gap: '12px',\n          flexWrap: 'wrap',\n        }}>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flex: 1 }}>\n            {searchable && (\n              <Input.Search\n                placeholder={searchPlaceholder}\n                value={searchValue}\n                onChange={(e) => setSearchValue(e.target.value)}\n                onSearch={handleSearch}\n                style={{ maxWidth: '300px' }}\n                size={compact ? 'small' : 'middle'}\n              />\n            )}\n            {toolbar}\n          </div>\n\n          {toolbarActions.length > 0 && (\n            <Space size=\"small\">\n              {toolbarActions}\n            </Space>\n          )}\n        </div>\n      )}\n\n      {/* Table content */}\n      <AntTable\n        size={compact ? 'small' : 'middle'}\n        bordered={bordered}\n        pagination={{\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => \n            `${range[0]}-${range[1]} of ${total} items`,\n          ...props.pagination,\n        }}\n        scroll={{ x: 'max-content' }}\n        {...props}\n      />\n    </div>\n  );\n}\n\n/**\n * Status column helper\n */\nexport interface StatusColumnProps {\n  value: string;\n  colorMap?: Record<string, string>;\n}\n\nexport function StatusColumn({ value, colorMap }: StatusColumnProps) {\n  const defaultColorMap: Record<string, string> = {\n    active: 'green',\n    inactive: 'red',\n    pending: 'orange',\n    draft: 'blue',\n    published: 'green',\n    archived: 'gray',\n  };\n\n  const colors = { ...defaultColorMap, ...colorMap };\n  const color = colors[value.toLowerCase()] || 'default';\n\n  return <Tag color={color}>{value}</Tag>;\n}\n\n/**\n * Actions column helper\n */\nexport interface ActionsColumnProps {\n  actions: Array<{\n    key: string;\n    label: string;\n    icon?: React.ReactNode;\n    onClick: () => void;\n    disabled?: boolean;\n    danger?: boolean;\n  }>;\n  compact?: boolean;\n}\n\nexport function ActionsColumn({ actions, compact = false }: ActionsColumnProps) {\n  if (actions.length <= 2) {\n    return (\n      <Space size=\"small\">\n        {actions.map((action) => (\n          <Button\n            key={action.key}\n            type=\"text\"\n            icon={action.icon}\n            onClick={action.onClick}\n            disabled={action.disabled}\n            danger={action.danger}\n            size={compact ? 'small' : 'middle'}\n          >\n            {!compact && action.label}\n          </Button>\n        ))}\n      </Space>\n    );\n  }\n\n  const menu = (\n    <Menu>\n      {actions.map((action) => (\n        <Menu.Item\n          key={action.key}\n          icon={action.icon}\n          onClick={action.onClick}\n          disabled={action.disabled}\n          danger={action.danger}\n        >\n          {action.label}\n        </Menu.Item>\n      ))}\n    </Menu>\n  );\n\n  return (\n    <Dropdown overlay={menu} trigger={['click']}>\n      <Button type=\"text\" icon={<SettingOutlined />} />\n    </Dropdown>\n  );\n}\n\n/**\n * Quick filter component\n */\nexport interface QuickFilterProps {\n  filters: Array<{\n    key: string;\n    label: string;\n    value: any;\n  }>;\n  activeFilter?: string;\n  onChange: (filterKey: string, filterValue: any) => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function QuickFilter({\n  filters,\n  activeFilter,\n  onChange,\n  className,\n  style,\n}: QuickFilterProps) {\n  return (\n    <div className={className} style={{ display: 'flex', gap: '8px', ...style }}>\n      {filters.map((filter) => (\n        <Button\n          key={filter.key}\n          type={activeFilter === filter.key ? 'primary' : 'default'}\n          size=\"small\"\n          onClick={() => onChange(filter.key, filter.value)}\n        >\n          {filter.label}\n        </Button>\n      ))}\n    </div>\n  );\n}\n\n/**\n * Table summary component\n */\nexport interface TableSummaryProps {\n  data: Array<{\n    label: string;\n    value: string | number;\n    type?: 'default' | 'primary' | 'success' | 'warning' | 'error';\n  }>;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function TableSummary({ data, className, style }: TableSummaryProps) {\n  const themeStyles = useThemeStyles();\n\n  const getTypeColor = (type: string = 'default') => {\n    switch (type) {\n      case 'primary': return themeStyles.getColor('primary');\n      case 'success': return themeStyles.getColor('success');\n      case 'warning': return themeStyles.getColor('warning');\n      case 'error': return themeStyles.getColor('error');\n      default: return themeStyles.getTextColor('primary');\n    }\n  };\n\n  return (\n    <div \n      className={className}\n      style={{\n        display: 'flex',\n        gap: '24px',\n        padding: '12px 16px',\n        backgroundColor: themeStyles.getBackgroundColor('elevated'),\n        borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        ...style,\n      }}\n    >\n      {data.map((item, index) => (\n        <div key={index} style={{ textAlign: 'center' }}>\n          <div style={{\n            fontSize: '12px',\n            color: themeStyles.getTextColor('secondary'),\n            marginBottom: '2px',\n          }}>\n            {item.label}\n          </div>\n          <div style={{\n            fontSize: '16px',\n            fontWeight: 'bold',\n            color: getTypeColor(item.type),\n          }}>\n            {item.value}\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAID;AAmBA;AAAA;AAlBA;AAAA;AAWA;AAAA;AAXA;AAWA;AAXA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;;;AA4CO,SAAS,UAAmB,EACjC,aAAa,KAAK,EAClB,oBAAoB,WAAW,EAC/B,QAAQ,EACR,cAAc,KAAK,EACnB,SAAS,EACT,aAAa,KAAK,EAClB,QAAQ,EACR,YAAY,EACZ,OAAO,EACP,UAAU,EAAE,EACZ,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,EACT,KAAK,EACL,GAAG,OACe;;IAClB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,eAAe;QACf,WAAW;IACb;IAEA,MAAM,aAAkC;QACtC,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,cAAc;QACd,UAAU;QACV,GAAG,KAAK;IACV;IAEA,wBAAwB;IACxB,MAAM,iBAAiB;WAClB;QACH,6BACE,6LAAC,uLAAA,CAAA,UAAO;YAAC,OAAM;sBACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;gBACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;gBACrB,SAAS;gBACT,MAAM,UAAU,UAAU;;;;;;WAJD;;;;;QAQ/B,4BACE,6LAAC,uLAAA,CAAA,UAAO;YAAC,OAAM;sBACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;gBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gBACvB,SAAS;gBACT,MAAM,UAAU,UAAU;;;;;;WAJF;;;;;QAQ9B,8BACE,6LAAC,yLAAA,CAAA,WAAQ;YAAC,SAAS;sBACjB,cAAA,6LAAC,qMAAA,CAAA,SAAM;gBACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;gBACtB,MAAM,UAAU,UAAU;;;;;;WAHO;;;;;KAOxC,CAAC,MAAM,CAAC;IAET,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;;YAE/B,CAAC,cAAc,WAAW,eAAe,MAAM,GAAG,CAAC,mBAClD,6LAAC;gBAAI,OAAO;oBACV,SAAS,UAAU,SAAS;oBAC5B,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;oBAClE,SAAS;oBACT,gBAAgB;oBAChB,YAAY;oBACZ,KAAK;oBACL,UAAU;gBACZ;;kCACE,6LAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,YAAY;4BAAU,KAAK;4BAAQ,MAAM;wBAAE;;4BACvE,4BACC,6LAAC,mLAAA,CAAA,QAAK,CAAC,MAAM;gCACX,aAAa;gCACb,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,UAAU;gCACV,OAAO;oCAAE,UAAU;gCAAQ;gCAC3B,MAAM,UAAU,UAAU;;;;;;4BAG7B;;;;;;;oBAGF,eAAe,MAAM,GAAG,mBACvB,6LAAC,mMAAA,CAAA,QAAK;wBAAC,MAAK;kCACT;;;;;;;;;;;;0BAOT,6LAAC,mLAAA,CAAA,QAAQ;gBACP,MAAM,UAAU,UAAU;gBAC1B,UAAU;gBACV,YAAY;oBACV,iBAAiB;oBACjB,iBAAiB;oBACjB,WAAW,CAAC,OAAO,QACjB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC;oBAC7C,GAAG,MAAM,UAAU;gBACrB;gBACA,QAAQ;oBAAE,GAAG;gBAAc;gBAC1B,GAAG,KAAK;;;;;;;;;;;;AAIjB;GAlHgB;;QAiBM,wHAAA,CAAA,iBAAc;;;KAjBpB;AA4HT,SAAS,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAqB;IACjE,MAAM,kBAA0C;QAC9C,QAAQ;QACR,UAAU;QACV,SAAS;QACT,OAAO;QACP,WAAW;QACX,UAAU;IACZ;IAEA,MAAM,SAAS;QAAE,GAAG,eAAe;QAAE,GAAG,QAAQ;IAAC;IACjD,MAAM,QAAQ,MAAM,CAAC,MAAM,WAAW,GAAG,IAAI;IAE7C,qBAAO,6LAAC,+KAAA,CAAA,MAAG;QAAC,OAAO;kBAAQ;;;;;;AAC7B;MAdgB;AA+BT,SAAS,cAAc,EAAE,OAAO,EAAE,UAAU,KAAK,EAAsB;IAC5E,IAAI,QAAQ,MAAM,IAAI,GAAG;QACvB,qBACE,6LAAC,mMAAA,CAAA,QAAK;YAAC,MAAK;sBACT,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,qMAAA,CAAA,SAAM;oBAEL,MAAK;oBACL,MAAM,OAAO,IAAI;oBACjB,SAAS,OAAO,OAAO;oBACvB,UAAU,OAAO,QAAQ;oBACzB,QAAQ,OAAO,MAAM;oBACrB,MAAM,UAAU,UAAU;8BAEzB,CAAC,WAAW,OAAO,KAAK;mBARpB,OAAO,GAAG;;;;;;;;;;IAazB;IAEA,MAAM,qBACJ,6LAAC,iLAAA,CAAA,OAAI;kBACF,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;gBAER,MAAM,OAAO,IAAI;gBACjB,SAAS,OAAO,OAAO;gBACvB,UAAU,OAAO,QAAQ;gBACzB,QAAQ,OAAO,MAAM;0BAEpB,OAAO,KAAK;eANR,OAAO,GAAG;;;;;;;;;;IAYvB,qBACE,6LAAC,yLAAA,CAAA,WAAQ;QAAC,SAAS;QAAM,SAAS;YAAC;SAAQ;kBACzC,cAAA,6LAAC,qMAAA,CAAA,SAAM;YAAC,MAAK;YAAO,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;;;;;;;;;;;AAGhD;MA1CgB;AA2DT,SAAS,YAAY,EAC1B,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,SAAS,EACT,KAAK,EACY;IACjB,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;YAAE,SAAS;YAAQ,KAAK;YAAO,GAAG,KAAK;QAAC;kBACvE,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,qMAAA,CAAA,SAAM;gBAEL,MAAM,iBAAiB,OAAO,GAAG,GAAG,YAAY;gBAChD,MAAK;gBACL,SAAS,IAAM,SAAS,OAAO,GAAG,EAAE,OAAO,KAAK;0BAE/C,OAAO,KAAK;eALR,OAAO,GAAG;;;;;;;;;;AAUzB;MArBgB;AAoCT,SAAS,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAqB;;IACxE,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,eAAe,CAAC,OAAe,SAAS;QAC5C,OAAQ;YACN,KAAK;gBAAW,OAAO,YAAY,QAAQ,CAAC;YAC5C,KAAK;gBAAW,OAAO,YAAY,QAAQ,CAAC;YAC5C,KAAK;gBAAW,OAAO,YAAY,QAAQ,CAAC;YAC5C,KAAK;gBAAS,OAAO,YAAY,QAAQ,CAAC;YAC1C;gBAAS,OAAO,YAAY,YAAY,CAAC;QAC3C;IACF;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,KAAK;YACL,SAAS;YACT,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,WAAW,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAC/D,GAAG,KAAK;QACV;kBAEC,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,6LAAC;gBAAgB,OAAO;oBAAE,WAAW;gBAAS;;kCAC5C,6LAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,OAAO,YAAY,YAAY,CAAC;4BAChC,cAAc;wBAChB;kCACG,KAAK,KAAK;;;;;;kCAEb,6LAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,YAAY;4BACZ,OAAO,aAAa,KAAK,IAAI;wBAC/B;kCACG,KAAK,KAAK;;;;;;;eAbL;;;;;;;;;;AAmBlB;IA7CgB;;QACM,wHAAA,CAAA,iBAAc;;;MADpB"}}, {"offset": {"line": 7021, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7027, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/data-display/index.ts"], "sourcesContent": ["/**\n * Data Display Components Index\n * Export all data display components\n */\n\n// Data table components\nexport * from './data-table';\n\n// Re-export Ant Design data display components for convenience\nexport {\n  Table,\n  List,\n  Descriptions,\n  Tree,\n  Timeline,\n  Collapse,\n  Tabs,\n  Carousel,\n  Image,\n  Calendar,\n  Statistic,\n} from 'antd';\n\n/**\n * Data display components metadata\n */\nexport const DATA_DISPLAY_COMPONENTS_VERSION = '1.0.0';\nexport const DATA_DISPLAY_COMPONENTS_NAME = 'APISportsGame Data Display Components';\n\n/**\n * Setup function for data display components\n */\nexport function setupDataDisplayComponents() {\n  console.log(`${DATA_DISPLAY_COMPONENTS_NAME} v${DATA_DISPLAY_COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB;;;;;;;;AAqBjB,MAAM,kCAAkC;AACxC,MAAM,+BAA+B;AAKrC,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,6BAA6B,EAAE,EAAE,gCAAgC,YAAY,CAAC;AAC/F"}}, {"offset": {"line": 7046, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7061, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/feedback/loading.tsx"], "sourcesContent": ["/**\n * Loading Components\n * Various loading states and spinners with theme integration\n */\n\n'use client';\n\nimport React from 'react';\nimport { Spin, Skeleton, Empty, Result } from 'antd';\nimport { LoadingOutlined, InboxOutlined } from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Loading spinner props\n */\nexport interface LoadingSpinnerProps {\n  size?: 'small' | 'default' | 'large';\n  tip?: string;\n  spinning?: boolean;\n  children?: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Loading spinner component\n */\nexport function LoadingSpinner({\n  size = 'default',\n  tip,\n  spinning = true,\n  children,\n  className,\n  style,\n}: LoadingSpinnerProps) {\n  const themeStyles = useThemeStyles();\n\n  const indicator = <LoadingOutlined style={{ fontSize: 24, color: themeStyles.getColor('primary') }} spin />;\n\n  if (children) {\n    return (\n      <Spin \n        indicator={indicator}\n        size={size}\n        tip={tip}\n        spinning={spinning}\n        className={className}\n        style={style}\n      >\n        {children}\n      </Spin>\n    );\n  }\n\n  return (\n    <div \n      className={className}\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        padding: '40px',\n        ...style,\n      }}\n    >\n      <Spin indicator={indicator} size={size} />\n      {tip && (\n        <div style={{\n          marginTop: '12px',\n          color: themeStyles.getTextColor('secondary'),\n          fontSize: '14px',\n        }}>\n          {tip}\n        </div>\n      )}\n    </div>\n  );\n}\n\n/**\n * Page loading props\n */\nexport interface PageLoadingProps {\n  message?: string;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Full page loading component\n */\nexport function PageLoading({\n  message = 'Loading...',\n  className,\n  style,\n}: PageLoadingProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div \n      className={className}\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '60vh',\n        backgroundColor: themeStyles.getBackgroundColor('layout'),\n        ...style,\n      }}\n    >\n      <LoadingSpinner size=\"large\" tip={message} />\n    </div>\n  );\n}\n\n/**\n * Content loading props\n */\nexport interface ContentLoadingProps {\n  rows?: number;\n  avatar?: boolean;\n  title?: boolean;\n  paragraph?: boolean;\n  active?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Content loading skeleton component\n */\nexport function ContentLoading({\n  rows = 3,\n  avatar = false,\n  title = true,\n  paragraph = true,\n  active = true,\n  className,\n  style,\n}: ContentLoadingProps) {\n  return (\n    <div className={className} style={style}>\n      <Skeleton\n        avatar={avatar}\n        title={title}\n        paragraph={{ rows }}\n        active={active}\n      />\n    </div>\n  );\n}\n\n/**\n * List loading props\n */\nexport interface ListLoadingProps {\n  count?: number;\n  avatar?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * List loading skeleton component\n */\nexport function ListLoading({\n  count = 5,\n  avatar = true,\n  className,\n  style,\n}: ListLoadingProps) {\n  return (\n    <div className={className} style={style}>\n      {Array.from({ length: count }).map((_, index) => (\n        <div key={index} style={{ marginBottom: '16px' }}>\n          <Skeleton\n            avatar={avatar}\n            title={{ width: '60%' }}\n            paragraph={{ rows: 2, width: ['100%', '80%'] }}\n            active\n          />\n        </div>\n      ))}\n    </div>\n  );\n}\n\n/**\n * Card loading props\n */\nexport interface CardLoadingProps {\n  count?: number;\n  columns?: number;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Card loading skeleton component\n */\nexport function CardLoading({\n  count = 6,\n  columns = 3,\n  className,\n  style,\n}: CardLoadingProps) {\n  return (\n    <div \n      className={className}\n      style={{\n        display: 'grid',\n        gridTemplateColumns: `repeat(${columns}, 1fr)`,\n        gap: '16px',\n        ...style,\n      }}\n    >\n      {Array.from({ length: count }).map((_, index) => (\n        <div key={index} style={{ padding: '16px', border: '1px solid #f0f0f0', borderRadius: '8px' }}>\n          <Skeleton\n            title={{ width: '80%' }}\n            paragraph={{ rows: 3, width: ['100%', '90%', '70%'] }}\n            active\n          />\n        </div>\n      ))}\n    </div>\n  );\n}\n\n/**\n * Empty state props\n */\nexport interface EmptyStateProps {\n  title?: string;\n  description?: string;\n  image?: React.ReactNode;\n  actions?: React.ReactNode[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Empty state component\n */\nexport function EmptyState({\n  title = 'No data',\n  description,\n  image,\n  actions = [],\n  className,\n  style,\n}: EmptyStateProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div \n      className={className}\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        padding: '40px 20px',\n        backgroundColor: themeStyles.getBackgroundColor('container'),\n        borderRadius: '8px',\n        border: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        ...style,\n      }}\n    >\n      <Empty\n        image={image || <InboxOutlined style={{ fontSize: '64px', color: themeStyles.getTextColor('tertiary') }} />}\n        description={\n          <div>\n            <div style={{\n              fontSize: '16px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n              marginBottom: '4px',\n            }}>\n              {title}\n            </div>\n            {description && (\n              <div style={{\n                fontSize: '14px',\n                color: themeStyles.getTextColor('secondary'),\n              }}>\n                {description}\n              </div>\n            )}\n          </div>\n        }\n      >\n        {actions.length > 0 && (\n          <div style={{ marginTop: '16px', display: 'flex', gap: '8px', justifyContent: 'center' }}>\n            {actions}\n          </div>\n        )}\n      </Empty>\n    </div>\n  );\n}\n\n/**\n * Error state props\n */\nexport interface ErrorStateProps {\n  title?: string;\n  subtitle?: string;\n  error?: Error | string;\n  showError?: boolean;\n  actions?: React.ReactNode[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Error state component\n */\nexport function ErrorState({\n  title = 'Something went wrong',\n  subtitle = 'An error occurred while loading the content.',\n  error,\n  showError = false,\n  actions = [],\n  className,\n  style,\n}: ErrorStateProps) {\n  const themeStyles = useThemeStyles();\n\n  const errorMessage = error instanceof Error ? error.message : String(error);\n\n  return (\n    <div className={className} style={style}>\n      <Result\n        status=\"error\"\n        title={title}\n        subTitle={subtitle}\n        extra={actions}\n      >\n        {showError && error && (\n          <div style={{\n            marginTop: '16px',\n            padding: '12px',\n            backgroundColor: themeStyles.getBackgroundColor('elevated'),\n            border: `1px solid ${themeStyles.getColor('error')}`,\n            borderRadius: '6px',\n            textAlign: 'left',\n          }}>\n            <div style={{\n              fontSize: '12px',\n              fontWeight: 'bold',\n              color: themeStyles.getColor('error'),\n              marginBottom: '4px',\n            }}>\n              Error Details:\n            </div>\n            <div style={{\n              fontSize: '12px',\n              color: themeStyles.getTextColor('secondary'),\n              fontFamily: 'monospace',\n              whiteSpace: 'pre-wrap',\n            }}>\n              {errorMessage}\n            </div>\n          </div>\n        )}\n      </Result>\n    </div>\n  );\n}\n\n/**\n * Loading overlay props\n */\nexport interface LoadingOverlayProps {\n  visible: boolean;\n  message?: string;\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Loading overlay component\n */\nexport function LoadingOverlay({\n  visible,\n  message = 'Loading...',\n  children,\n  className,\n  style,\n}: LoadingOverlayProps) {\n  return (\n    <div className={className} style={{ position: 'relative', ...style }}>\n      {children}\n      {visible && (\n        <div style={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(255, 255, 255, 0.8)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n        }}>\n          <LoadingSpinner tip={message} />\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;AAOD;AAAA;AADA;AADA;AAAA;AAAA;AACA;AADA;;;AAHA;;;;AAsBO,SAAS,eAAe,EAC7B,OAAO,SAAS,EAChB,GAAG,EACH,WAAW,IAAI,EACf,QAAQ,EACR,SAAS,EACT,KAAK,EACe;;IACpB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,0BAAY,6LAAC,2NAAA,CAAA,kBAAe;QAAC,OAAO;YAAE,UAAU;YAAI,OAAO,YAAY,QAAQ,CAAC;QAAW;QAAG,IAAI;;;;;;IAExG,IAAI,UAAU;QACZ,qBACE,6LAAC,iLAAA,CAAA,OAAI;YACH,WAAW;YACX,MAAM;YACN,KAAK;YACL,UAAU;YACV,WAAW;YACX,OAAO;sBAEN;;;;;;IAGP;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,SAAS;YACT,GAAG,KAAK;QACV;;0BAEA,6LAAC,iLAAA,CAAA,OAAI;gBAAC,WAAW;gBAAW,MAAM;;;;;;YACjC,qBACC,6LAAC;gBAAI,OAAO;oBACV,WAAW;oBACX,OAAO,YAAY,YAAY,CAAC;oBAChC,UAAU;gBACZ;0BACG;;;;;;;;;;;;AAKX;GAnDgB;;QAQM,wHAAA,CAAA,iBAAc;;;KARpB;AAiET,SAAS,YAAY,EAC1B,UAAU,YAAY,EACtB,SAAS,EACT,KAAK,EACY;;IACjB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,GAAG,KAAK;QACV;kBAEA,cAAA,6LAAC;YAAe,MAAK;YAAQ,KAAK;;;;;;;;;;;AAGxC;IAvBgB;;QAKM,wHAAA,CAAA,iBAAc;;;MALpB;AAyCT,SAAS,eAAe,EAC7B,OAAO,CAAC,EACR,SAAS,KAAK,EACd,QAAQ,IAAI,EACZ,YAAY,IAAI,EAChB,SAAS,IAAI,EACb,SAAS,EACT,KAAK,EACe;IACpB,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;kBAChC,cAAA,6LAAC,yLAAA,CAAA,WAAQ;YACP,QAAQ;YACR,OAAO;YACP,WAAW;gBAAE;YAAK;YAClB,QAAQ;;;;;;;;;;;AAIhB;MAnBgB;AAkCT,SAAS,YAAY,EAC1B,QAAQ,CAAC,EACT,SAAS,IAAI,EACb,SAAS,EACT,KAAK,EACY;IACjB,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;kBAC/B,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC;gBAAgB,OAAO;oBAAE,cAAc;gBAAO;0BAC7C,cAAA,6LAAC,yLAAA,CAAA,WAAQ;oBACP,QAAQ;oBACR,OAAO;wBAAE,OAAO;oBAAM;oBACtB,WAAW;wBAAE,MAAM;wBAAG,OAAO;4BAAC;4BAAQ;yBAAM;oBAAC;oBAC7C,MAAM;;;;;;eALA;;;;;;;;;;AAWlB;MApBgB;AAmCT,SAAS,YAAY,EAC1B,QAAQ,CAAC,EACT,UAAU,CAAC,EACX,SAAS,EACT,KAAK,EACY;IACjB,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,qBAAqB,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC;YAC9C,KAAK;YACL,GAAG,KAAK;QACV;kBAEC,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC;gBAAgB,OAAO;oBAAE,SAAS;oBAAQ,QAAQ;oBAAqB,cAAc;gBAAM;0BAC1F,cAAA,6LAAC,yLAAA,CAAA,WAAQ;oBACP,OAAO;wBAAE,OAAO;oBAAM;oBACtB,WAAW;wBAAE,MAAM;wBAAG,OAAO;4BAAC;4BAAQ;4BAAO;yBAAM;oBAAC;oBACpD,MAAM;;;;;;eAJA;;;;;;;;;;AAUlB;MA3BgB;AA4CT,SAAS,WAAW,EACzB,QAAQ,SAAS,EACjB,WAAW,EACX,KAAK,EACL,UAAU,EAAE,EACZ,SAAS,EACT,KAAK,EACW;;IAChB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,SAAS;YACT,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,cAAc;YACd,QAAQ,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAC5D,GAAG,KAAK;QACV;kBAEA,cAAA,6LAAC,mLAAA,CAAA,QAAK;YACJ,OAAO,uBAAS,6LAAC,uNAAA,CAAA,gBAAa;gBAAC,OAAO;oBAAE,UAAU;oBAAQ,OAAO,YAAY,YAAY,CAAC;gBAAY;;;;;;YACtG,2BACE,6LAAC;;kCACC,6LAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,YAAY;4BACZ,OAAO,YAAY,YAAY,CAAC;4BAChC,cAAc;wBAChB;kCACG;;;;;;oBAEF,6BACC,6LAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,OAAO,YAAY,YAAY,CAAC;wBAClC;kCACG;;;;;;;;;;;;sBAMR,QAAQ,MAAM,GAAG,mBAChB,6LAAC;gBAAI,OAAO;oBAAE,WAAW;oBAAQ,SAAS;oBAAQ,KAAK;oBAAO,gBAAgB;gBAAS;0BACpF;;;;;;;;;;;;;;;;AAMb;IAxDgB;;QAQM,wHAAA,CAAA,iBAAc;;;MARpB;AA0ET,SAAS,WAAW,EACzB,QAAQ,sBAAsB,EAC9B,WAAW,8CAA8C,EACzD,KAAK,EACL,YAAY,KAAK,EACjB,UAAU,EAAE,EACZ,SAAS,EACT,KAAK,EACW;;IAChB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;IAErE,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;kBAChC,cAAA,6LAAC,qLAAA,CAAA,SAAM;YACL,QAAO;YACP,OAAO;YACP,UAAU;YACV,OAAO;sBAEN,aAAa,uBACZ,6LAAC;gBAAI,OAAO;oBACV,WAAW;oBACX,SAAS;oBACT,iBAAiB,YAAY,kBAAkB,CAAC;oBAChD,QAAQ,CAAC,UAAU,EAAE,YAAY,QAAQ,CAAC,UAAU;oBACpD,cAAc;oBACd,WAAW;gBACb;;kCACE,6LAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,YAAY;4BACZ,OAAO,YAAY,QAAQ,CAAC;4BAC5B,cAAc;wBAChB;kCAAG;;;;;;kCAGH,6LAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,OAAO,YAAY,YAAY,CAAC;4BAChC,YAAY;4BACZ,YAAY;wBACd;kCACG;;;;;;;;;;;;;;;;;;;;;;AAOf;IAnDgB;;QASM,wHAAA,CAAA,iBAAc;;;MATpB;AAmET,SAAS,eAAe,EAC7B,OAAO,EACP,UAAU,YAAY,EACtB,QAAQ,EACR,SAAS,EACT,KAAK,EACe;IACpB,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;YAAE,UAAU;YAAY,GAAG,KAAK;QAAC;;YAChE;YACA,yBACC,6LAAC;gBAAI,OAAO;oBACV,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;gBACV;0BACE,cAAA,6LAAC;oBAAe,KAAK;;;;;;;;;;;;;;;;;AAK/B;MA5BgB"}}, {"offset": {"line": 7520, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7526, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/feedback/index.ts"], "sourcesContent": ["/**\n * Feedback Components Index\n * Export all feedback components\n */\n\n// Loading components\nexport * from './loading';\n\n// Re-export Ant Design feedback components for convenience\nexport {\n  Alert,\n  Message,\n  Notification,\n  Progress,\n  Result,\n  Skeleton,\n  Spin,\n  Empty,\n} from 'antd';\n\n/**\n * Feedback components metadata\n */\nexport const FEEDBACK_COMPONENTS_VERSION = '1.0.0';\nexport const FEEDBACK_COMPONENTS_NAME = 'APISportsGame Feedback Components';\n\n/**\n * Setup function for feedback components\n */\nexport function setupFeedbackComponents() {\n  console.log(`${FEEDBACK_COMPONENTS_NAME} v${FEEDBACK_COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,qBAAqB;;;;;;;;AAkBd,MAAM,8BAA8B;AACpC,MAAM,2BAA2B;AAKjC,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,yBAAyB,EAAE,EAAE,4BAA4B,YAAY,CAAC;AACvF"}}, {"offset": {"line": 7545, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7560, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/index.ts"], "sourcesContent": ["/**\n * Component Library Index\n * Central export for all reusable components\n */\n\n// Common UI Components\nexport * from './ui';\n\n// Layout Components\nexport * from './layout';\n\n// Form Components\nexport * from './forms';\n\n// Data Display Components\nexport * from './data-display';\n\n// Feedback Components\nexport * from './feedback';\n\n// Future component categories:\n// export * from './navigation';\n// export * from './charts';\n// export * from './media';\n\n/**\n * Component library metadata\n */\nexport const COMPONENTS_VERSION = '1.0.0';\nexport const COMPONENTS_NAME = 'APISportsGame Component Library';\n\n/**\n * Setup function for component library\n */\nexport function setupComponents() {\n  console.log(`${COMPONENTS_NAME} v${COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,uBAAuB;;;;;;;;;;;AAuBhB,MAAM,qBAAqB;AAC3B,MAAM,kBAAkB;AAKxB,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,gBAAgB,EAAE,EAAE,mBAAmB,YAAY,CAAC;AACrE"}}, {"offset": {"line": 7582, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7601, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/components-demo/page.tsx"], "sourcesContent": ["/**\n * Components Demo Page - Test page for Component Library functionality\n * Demonstrates usage of various custom components\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { \n  Button, \n  ButtonGroup, \n  IconButton, \n  LoadingButton,\n  Input,\n  SearchInput,\n  PasswordInput,\n  Select,\n  Card,\n  StatCard,\n  InfoCard,\n  PageHeader,\n  SimplePageHeader,\n  SectionHeader,\n  ContentLayout,\n  TwoColumnLayout,\n  Container,\n  Form,\n  InlineForm,\n  SearchForm,\n  DataTable,\n  StatusColumn,\n  ActionsColumn,\n  QuickFilter,\n  LoadingSpinner,\n  PageLoading,\n  ContentLoading,\n  EmptyState,\n  ErrorState,\n  Space,\n  Divider,\n  Typography,\n} from '@/components';\nimport { \n  UserOutlined, \n  SettingOutlined, \n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  PlusOutlined,\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\n\nexport default function ComponentsDemoPage() {\n  const [loading, setLoading] = useState(false);\n  const [searchCollapsed, setSearchCollapsed] = useState(false);\n\n  // Sample data for table\n  const tableData = [\n    {\n      key: '1',\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      status: 'active',\n      role: 'Admin',\n    },\n    {\n      key: '2',\n      name: '<PERSON> <PERSON>',\n      email: '<EMAIL>',\n      status: 'inactive',\n      role: 'Editor',\n    },\n    {\n      key: '3',\n      name: 'Bob Johnson',\n      email: '<EMAIL>',\n      status: 'pending',\n      role: 'User',\n    },\n  ];\n\n  const tableColumns = [\n    {\n      title: 'Name',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: 'Email',\n      dataIndex: 'email',\n      key: 'email',\n    },\n    {\n      title: 'Status',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status: string) => <StatusColumn value={status} />,\n    },\n    {\n      title: 'Role',\n      dataIndex: 'role',\n      key: 'role',\n    },\n    {\n      title: 'Actions',\n      key: 'actions',\n      render: () => (\n        <ActionsColumn\n          actions={[\n            {\n              key: 'edit',\n              label: 'Edit',\n              icon: <EditOutlined />,\n              onClick: () => console.log('Edit clicked'),\n            },\n            {\n              key: 'delete',\n              label: 'Delete',\n              icon: <DeleteOutlined />,\n              onClick: () => console.log('Delete clicked'),\n              danger: true,\n            },\n          ]}\n        />\n      ),\n    },\n  ];\n\n  const quickFilters = [\n    { key: 'all', label: 'All', value: null },\n    { key: 'active', label: 'Active', value: 'active' },\n    { key: 'inactive', label: 'Inactive', value: 'inactive' },\n    { key: 'pending', label: 'Pending', value: 'pending' },\n  ];\n\n  const handleFormSubmit = (values: any) => {\n    console.log('Form submitted:', values);\n    setLoading(true);\n    setTimeout(() => setLoading(false), 2000);\n  };\n\n  return (\n    <ContentLayout>\n      {/* Page Header */}\n      <PageHeader\n        title=\"Component Library Demo\"\n        subtitle=\"Showcase of all custom components in the APISportsGame CMS\"\n        breadcrumbs={[\n          { title: 'Home', href: '/' },\n          { title: 'Demos', href: '#' },\n          { title: 'Components' },\n        ]}\n        actions={[\n          <Button key=\"refresh\" icon={<SettingOutlined />}>\n            Settings\n          </Button>,\n        ]}\n      />\n\n      <Container>\n        {/* UI Components Section */}\n        <SectionHeader\n          title=\"UI Components\"\n          subtitle=\"Basic UI components with theme integration\"\n          divider\n        />\n\n        <TwoColumnLayout\n          leftContent={\n            <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\n              {/* Buttons */}\n              <Card title=\"Buttons\">\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <ButtonGroup>\n                    <Button variant=\"primary\">Primary</Button>\n                    <Button variant=\"secondary\">Secondary</Button>\n                    <Button variant=\"success\">Success</Button>\n                    <Button variant=\"warning\">Warning</Button>\n                    <Button variant=\"error\">Error</Button>\n                  </ButtonGroup>\n                  \n                  <Space>\n                    <LoadingButton isLoading={loading}>\n                      {loading ? 'Loading...' : 'Load Data'}\n                    </LoadingButton>\n                    <IconButton icon={<UserOutlined />} tooltip=\"User Profile\" />\n                    <IconButton icon={<SettingOutlined />} size=\"large\" variant=\"success\" />\n                  </Space>\n                </Space>\n              </Card>\n\n              {/* Inputs */}\n              <Card title=\"Inputs\">\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <Input \n                    label=\"Username\" \n                    placeholder=\"Enter username\"\n                    helperText=\"This will be your unique identifier\"\n                    fullWidth\n                  />\n                  <SearchInput \n                    placeholder=\"Search anything...\"\n                    onSearch={(value) => console.log('Search:', value)}\n                    fullWidth\n                  />\n                  <PasswordInput \n                    label=\"Password\"\n                    placeholder=\"Enter password\"\n                    strength\n                    fullWidth\n                  />\n                  <Select\n                    label=\"Role\"\n                    placeholder=\"Select role\"\n                    options={[\n                      { label: 'Admin', value: 'admin' },\n                      { label: 'Editor', value: 'editor' },\n                      { label: 'User', value: 'user' },\n                    ]}\n                    fullWidth\n                  />\n                </Space>\n              </Card>\n            </Space>\n          }\n          rightContent={\n            <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\n              {/* Cards */}\n              <StatCard\n                title=\"Total Users\"\n                value=\"1,234\"\n                subtitle=\"Active users\"\n                icon={<UserOutlined />}\n                trend={{ value: 12, isPositive: true }}\n              />\n\n              <InfoCard\n                title=\"System Status\"\n                description=\"All systems operational\"\n                icon={<SettingOutlined />}\n                actions={[\n                  <Button key=\"details\" type=\"link\">View Details</Button>\n                ]}\n              >\n                <Text>Last updated: 2 minutes ago</Text>\n              </InfoCard>\n\n              {/* Loading States */}\n              <Card title=\"Loading States\">\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <LoadingSpinner tip=\"Loading data...\" />\n                  <ContentLoading rows={2} />\n                </Space>\n              </Card>\n            </Space>\n          }\n        />\n\n        <Divider />\n\n        {/* Forms Section */}\n        <SectionHeader\n          title=\"Forms\"\n          subtitle=\"Form components with validation\"\n          divider\n        />\n\n        <TwoColumnLayout\n          leftContent={\n            <Form\n              title=\"User Registration\"\n              subtitle=\"Create a new user account\"\n              loading={loading}\n              onFinish={handleFormSubmit}\n              showCancel\n              onCancel={() => console.log('Cancel clicked')}\n            >\n              <Input \n                name=\"username\"\n                label=\"Username\"\n                placeholder=\"Enter username\"\n                required\n                fullWidth\n              />\n              <Input \n                name=\"email\"\n                label=\"Email\"\n                placeholder=\"Enter email\"\n                required\n                fullWidth\n              />\n              <Select\n                name=\"role\"\n                label=\"Role\"\n                placeholder=\"Select role\"\n                options={[\n                  { label: 'Admin', value: 'admin' },\n                  { label: 'Editor', value: 'editor' },\n                  { label: 'User', value: 'user' },\n                ]}\n                required\n                fullWidth\n              />\n            </Form>\n          }\n          rightContent={\n            <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\n              <SearchForm\n                onSearch={(values) => console.log('Search:', values)}\n                onReset={() => console.log('Reset')}\n                collapsed={searchCollapsed}\n                onToggleCollapse={() => setSearchCollapsed(!searchCollapsed)}\n              >\n                <Input name=\"query\" placeholder=\"Search query\" fullWidth />\n                <Select\n                  name=\"category\"\n                  placeholder=\"Category\"\n                  options={[\n                    { label: 'All', value: 'all' },\n                    { label: 'Users', value: 'users' },\n                    { label: 'Posts', value: 'posts' },\n                  ]}\n                  fullWidth\n                />\n              </SearchForm>\n\n              <InlineForm onFinish={(values) => console.log('Inline:', values)}>\n                <Input name=\"quickSearch\" placeholder=\"Quick search\" />\n              </InlineForm>\n            </Space>\n          }\n        />\n\n        <Divider />\n\n        {/* Data Display Section */}\n        <SectionHeader\n          title=\"Data Display\"\n          subtitle=\"Tables and data visualization\"\n          divider\n        />\n\n        <Card>\n          <QuickFilter\n            filters={quickFilters}\n            onChange={(key, value) => console.log('Filter:', key, value)}\n            style={{ marginBottom: '16px' }}\n          />\n          \n          <DataTable\n            columns={tableColumns}\n            dataSource={tableData}\n            searchable\n            refreshable\n            exportable\n            onSearch={(value) => console.log('Table search:', value)}\n            onRefresh={() => console.log('Table refresh')}\n            onExport={() => console.log('Table export')}\n            toolbar={\n              <Button type=\"primary\" icon={<PlusOutlined />}>\n                Add User\n              </Button>\n            }\n          />\n        </Card>\n\n        <Divider />\n\n        {/* Feedback Section */}\n        <SectionHeader\n          title=\"Feedback Components\"\n          subtitle=\"Loading states, empty states, and error handling\"\n          divider\n        />\n\n        <TwoColumnLayout\n          leftContent={\n            <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\n              <EmptyState\n                title=\"No data found\"\n                description=\"Try adjusting your search criteria\"\n                actions={[\n                  <Button key=\"retry\" type=\"primary\">Retry</Button>,\n                  <Button key=\"clear\">Clear Filters</Button>,\n                ]}\n              />\n            </Space>\n          }\n          rightContent={\n            <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\n              <ErrorState\n                title=\"Failed to load data\"\n                subtitle=\"Please try again later\"\n                actions={[\n                  <Button key=\"retry\" type=\"primary\">Retry</Button>,\n                  <Button key=\"support\">Contact Support</Button>,\n                ]}\n              />\n            </Space>\n          }\n        />\n\n        {/* Navigation */}\n        <Divider />\n        <Card>\n          <Title level={4}>Navigation</Title>\n          <Text>\n            This demo page shows the Component Library functionality with theme integration.\n            All components automatically adapt to the current theme and provide consistent styling.\n          </Text>\n          <div style={{ marginTop: '16px' }}>\n            <Space>\n              <Button type=\"link\" href=\"/\">← Back to Home</Button>\n              <Button type=\"link\" href=\"/theme-demo\">Theme Demo</Button>\n              <Button type=\"link\" href=\"/api-hooks-demo\">API Hooks Demo</Button>\n            </Space>\n          </div>\n        </Card>\n      </Container>\n    </ContentLayout>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AACA;AAAA;AAAA;AAkCA;AAAA;AAlCA;AAAA;AAAA;AAkCA;AAlCA;AAAA;AAkCA;AAlCA;AAAA;AAAA;AAAA;AAkCA;;;AArCA;;;;AA8CA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAEnB,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,wBAAwB;IACxB,MAAM,YAAY;QAChB;YACE,KAAK;YACL,MAAM;YACN,OAAO;YACP,QAAQ;YACR,MAAM;QACR;QACA;YACE,KAAK;YACL,MAAM;YACN,OAAO;YACP,QAAQ;YACR,MAAM;QACR;QACA;YACE,KAAK;YACL,MAAM;YACN,OAAO;YACP,QAAQ;YACR,MAAM;QACR;KACD;IAED,MAAM,eAAe;QACnB;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,uBAAmB,6LAAC,yJAAA,CAAA,eAAY;oBAAC,OAAO;;;;;;QACnD;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,kBACN,6LAAC,yJAAA,CAAA,gBAAa;oBACZ,SAAS;wBACP;4BACE,KAAK;4BACL,OAAO;4BACP,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,SAAS,IAAM,QAAQ,GAAG,CAAC;wBAC7B;wBACA;4BACE,KAAK;4BACL,OAAO;4BACP,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;4BACrB,SAAS,IAAM,QAAQ,GAAG,CAAC;4BAC3B,QAAQ;wBACV;qBACD;;;;;;QAGP;KACD;IAED,MAAM,eAAe;QACnB;YAAE,KAAK;YAAO,OAAO;YAAO,OAAO;QAAK;QACxC;YAAE,KAAK;YAAU,OAAO;YAAU,OAAO;QAAS;QAClD;YAAE,KAAK;YAAY,OAAO;YAAY,OAAO;QAAW;QACxD;YAAE,KAAK;YAAW,OAAO;YAAW,OAAO;QAAU;KACtD;IAED,MAAM,mBAAmB,CAAC;QACxB,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,WAAW;QACX,WAAW,IAAM,WAAW,QAAQ;IACtC;IAEA,qBACE,6LAAC,oJAAA,CAAA,gBAAa;;0BAEZ,6LAAC,iJAAA,CAAA,aAAU;gBACT,OAAM;gBACN,UAAS;gBACT,aAAa;oBACX;wBAAE,OAAO;wBAAQ,MAAM;oBAAI;oBAC3B;wBAAE,OAAO;wBAAS,MAAM;oBAAI;oBAC5B;wBAAE,OAAO;oBAAa;iBACvB;gBACD,SAAS;kCACP,6LAAC,qIAAA,CAAA,SAAM;wBAAe,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;kCAAK;uBAArC;;;;;iBAGb;;;;;;0BAGH,6LAAC,oJAAA,CAAA,YAAS;;kCAER,6LAAC,iJAAA,CAAA,gBAAa;wBACZ,OAAM;wBACN,UAAS;wBACT,OAAO;;;;;;kCAGT,6LAAC,oJAAA,CAAA,kBAAe;wBACd,2BACE,6LAAC,mMAAA,CAAA,QAAK;4BAAC,WAAU;4BAAW,OAAO;gCAAE,OAAO;4BAAO;4BAAG,MAAK;;8CAEzD,6LAAC,mIAAA,CAAA,OAAI;oCAAC,OAAM;8CACV,cAAA,6LAAC,mMAAA,CAAA,QAAK;wCAAC,WAAU;wCAAW,OAAO;4CAAE,OAAO;wCAAO;;0DACjD,6LAAC,qIAAA,CAAA,cAAW;;kEACV,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;kEAAU;;;;;;kEAC1B,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;kEAAY;;;;;;kEAC5B,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;kEAAU;;;;;;kEAC1B,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;kEAAU;;;;;;kEAC1B,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;kEAAQ;;;;;;;;;;;;0DAG1B,6LAAC,mMAAA,CAAA,QAAK;;kEACJ,6LAAC,qIAAA,CAAA,gBAAa;wDAAC,WAAW;kEACvB,UAAU,eAAe;;;;;;kEAE5B,6LAAC,qIAAA,CAAA,aAAU;wDAAC,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wDAAK,SAAQ;;;;;;kEAC5C,6LAAC,qIAAA,CAAA,aAAU;wDAAC,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;wDAAK,MAAK;wDAAQ,SAAQ;;;;;;;;;;;;;;;;;;;;;;;8CAMlE,6LAAC,mIAAA,CAAA,OAAI;oCAAC,OAAM;8CACV,cAAA,6LAAC,mMAAA,CAAA,QAAK;wCAAC,WAAU;wCAAW,OAAO;4CAAE,OAAO;wCAAO;;0DACjD,6LAAC,oIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,aAAY;gDACZ,YAAW;gDACX,SAAS;;;;;;0DAEX,6LAAC,oIAAA,CAAA,cAAW;gDACV,aAAY;gDACZ,UAAU,CAAC,QAAU,QAAQ,GAAG,CAAC,WAAW;gDAC5C,SAAS;;;;;;0DAEX,6LAAC,oIAAA,CAAA,gBAAa;gDACZ,OAAM;gDACN,aAAY;gDACZ,QAAQ;gDACR,SAAS;;;;;;0DAEX,6LAAC,oIAAA,CAAA,SAAM;gDACL,OAAM;gDACN,aAAY;gDACZ,SAAS;oDACP;wDAAE,OAAO;wDAAS,OAAO;oDAAQ;oDACjC;wDAAE,OAAO;wDAAU,OAAO;oDAAS;oDACnC;wDAAE,OAAO;wDAAQ,OAAO;oDAAO;iDAChC;gDACD,SAAS;;;;;;;;;;;;;;;;;;;;;;;wBAMnB,4BACE,6LAAC,mMAAA,CAAA,QAAK;4BAAC,WAAU;4BAAW,OAAO;gCAAE,OAAO;4BAAO;4BAAG,MAAK;;8CAEzD,6LAAC,mIAAA,CAAA,WAAQ;oCACP,OAAM;oCACN,OAAM;oCACN,UAAS;oCACT,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACnB,OAAO;wCAAE,OAAO;wCAAI,YAAY;oCAAK;;;;;;8CAGvC,6LAAC,mIAAA,CAAA,WAAQ;oCACP,OAAM;oCACN,aAAY;oCACZ,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;oCACtB,SAAS;sDACP,6LAAC,qIAAA,CAAA,SAAM;4CAAe,MAAK;sDAAO;2CAAtB;;;;;qCACb;8CAED,cAAA,6LAAC;kDAAK;;;;;;;;;;;8CAIR,6LAAC,mIAAA,CAAA,OAAI;oCAAC,OAAM;8CACV,cAAA,6LAAC,mMAAA,CAAA,QAAK;wCAAC,WAAU;wCAAW,OAAO;4CAAE,OAAO;wCAAO;;0DACjD,6LAAC,4IAAA,CAAA,iBAAc;gDAAC,KAAI;;;;;;0DACpB,6LAAC,4IAAA,CAAA,iBAAc;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhC,6LAAC,uLAAA,CAAA,UAAO;;;;;kCAGR,6LAAC,iJAAA,CAAA,gBAAa;wBACZ,OAAM;wBACN,UAAS;wBACT,OAAO;;;;;;kCAGT,6LAAC,oJAAA,CAAA,kBAAe;wBACd,2BACE,6LAAC,iJAAA,CAAA,OAAI;4BACH,OAAM;4BACN,UAAS;4BACT,SAAS;4BACT,UAAU;4BACV,UAAU;4BACV,UAAU,IAAM,QAAQ,GAAG,CAAC;;8CAE5B,6LAAC,oIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,QAAQ;oCACR,SAAS;;;;;;8CAEX,6LAAC,oIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,QAAQ;oCACR,SAAS;;;;;;8CAEX,6LAAC,oIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,SAAS;wCACP;4CAAE,OAAO;4CAAS,OAAO;wCAAQ;wCACjC;4CAAE,OAAO;4CAAU,OAAO;wCAAS;wCACnC;4CAAE,OAAO;4CAAQ,OAAO;wCAAO;qCAChC;oCACD,QAAQ;oCACR,SAAS;;;;;;;;;;;;wBAIf,4BACE,6LAAC,mMAAA,CAAA,QAAK;4BAAC,WAAU;4BAAW,OAAO;gCAAE,OAAO;4BAAO;4BAAG,MAAK;;8CACzD,6LAAC,iJAAA,CAAA,aAAU;oCACT,UAAU,CAAC,SAAW,QAAQ,GAAG,CAAC,WAAW;oCAC7C,SAAS,IAAM,QAAQ,GAAG,CAAC;oCAC3B,WAAW;oCACX,kBAAkB,IAAM,mBAAmB,CAAC;;sDAE5C,6LAAC,oIAAA,CAAA,QAAK;4CAAC,MAAK;4CAAQ,aAAY;4CAAe,SAAS;;;;;;sDACxD,6LAAC,oIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,aAAY;4CACZ,SAAS;gDACP;oDAAE,OAAO;oDAAO,OAAO;gDAAM;gDAC7B;oDAAE,OAAO;oDAAS,OAAO;gDAAQ;gDACjC;oDAAE,OAAO;oDAAS,OAAO;gDAAQ;6CAClC;4CACD,SAAS;;;;;;;;;;;;8CAIb,6LAAC,iJAAA,CAAA,aAAU;oCAAC,UAAU,CAAC,SAAW,QAAQ,GAAG,CAAC,WAAW;8CACvD,cAAA,6LAAC,oIAAA,CAAA,QAAK;wCAAC,MAAK;wCAAc,aAAY;;;;;;;;;;;;;;;;;;;;;;kCAM9C,6LAAC,uLAAA,CAAA,UAAO;;;;;kCAGR,6LAAC,iJAAA,CAAA,gBAAa;wBACZ,OAAM;wBACN,UAAS;wBACT,OAAO;;;;;;kCAGT,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,yJAAA,CAAA,cAAW;gCACV,SAAS;gCACT,UAAU,CAAC,KAAK,QAAU,QAAQ,GAAG,CAAC,WAAW,KAAK;gCACtD,OAAO;oCAAE,cAAc;gCAAO;;;;;;0CAGhC,6LAAC,yJAAA,CAAA,YAAS;gCACR,SAAS;gCACT,YAAY;gCACZ,UAAU;gCACV,WAAW;gCACX,UAAU;gCACV,UAAU,CAAC,QAAU,QAAQ,GAAG,CAAC,iBAAiB;gCAClD,WAAW,IAAM,QAAQ,GAAG,CAAC;gCAC7B,UAAU,IAAM,QAAQ,GAAG,CAAC;gCAC5B,uBACE,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAU,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;8CAAK;;;;;;;;;;;;;;;;;kCAOrD,6LAAC,uLAAA,CAAA,UAAO;;;;;kCAGR,6LAAC,iJAAA,CAAA,gBAAa;wBACZ,OAAM;wBACN,UAAS;wBACT,OAAO;;;;;;kCAGT,6LAAC,oJAAA,CAAA,kBAAe;wBACd,2BACE,6LAAC,mMAAA,CAAA,QAAK;4BAAC,WAAU;4BAAW,OAAO;gCAAE,OAAO;4BAAO;4BAAG,MAAK;sCACzD,cAAA,6LAAC,4IAAA,CAAA,aAAU;gCACT,OAAM;gCACN,aAAY;gCACZ,SAAS;kDACP,6LAAC,qIAAA,CAAA,SAAM;wCAAa,MAAK;kDAAU;uCAAvB;;;;;kDACZ,6LAAC,qIAAA,CAAA,SAAM;kDAAa;uCAAR;;;;;iCACb;;;;;;;;;;;wBAIP,4BACE,6LAAC,mMAAA,CAAA,QAAK;4BAAC,WAAU;4BAAW,OAAO;gCAAE,OAAO;4BAAO;4BAAG,MAAK;sCACzD,cAAA,6LAAC,4IAAA,CAAA,aAAU;gCACT,OAAM;gCACN,UAAS;gCACT,SAAS;kDACP,6LAAC,qIAAA,CAAA,SAAM;wCAAa,MAAK;kDAAU;uCAAvB;;;;;kDACZ,6LAAC,qIAAA,CAAA,SAAM;kDAAe;uCAAV;;;;;iCACb;;;;;;;;;;;;;;;;kCAOT,6LAAC,uLAAA,CAAA,UAAO;;;;;kCACR,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,6LAAC;0CAAK;;;;;;0CAIN,6LAAC;gCAAI,OAAO;oCAAE,WAAW;gCAAO;0CAC9B,cAAA,6LAAC,mMAAA,CAAA,QAAK;;sDACJ,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAO,MAAK;sDAAI;;;;;;sDAC7B,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAO,MAAK;sDAAc;;;;;;sDACvC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAO,MAAK;sDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzD;GAjXwB;KAAA"}}, {"offset": {"line": 8487, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}