(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_fb9ea2._.js", {

"[project]/src/stores/types.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Types and Interfaces
 * Defines TypeScript types for all store modules
 */ // ============================================================================
// Base Store Types
// ============================================================================
/**
 * Base store state interface
 * All stores should extend this interface
 */ __turbopack_esm__({});
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Utilities
 * Helper functions and utilities for store management
 */ __turbopack_esm__({
    "clearStoredTokens": (()=>clearStoredTokens),
    "createBaseStoreActions": (()=>createBaseStoreActions),
    "createBaseStoreState": (()=>createBaseStoreState),
    "createErrorObject": (()=>createErrorObject),
    "createStoreWithMiddleware": (()=>createStoreWithMiddleware),
    "extractErrorMessage": (()=>extractErrorMessage),
    "generateNotificationId": (()=>generateNotificationId),
    "getDefaultNotificationDuration": (()=>getDefaultNotificationDuration),
    "getSessionRemainingTime": (()=>getSessionRemainingTime),
    "getTokenExpiration": (()=>getTokenExpiration),
    "isSessionValid": (()=>isSessionValid),
    "isTokenExpired": (()=>isTokenExpired),
    "logStoreAction": (()=>logStoreAction),
    "storage": (()=>storage),
    "validateStoreState": (()=>validateStoreState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
;
function createStoreWithMiddleware(storeCreator, config) {
    let store = storeCreator;
    // Apply persistence middleware if configured
    if (config.persist) {
        store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])(store, {
            name: config.persist.name,
            version: config.persist.version,
            storage: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createJSONStorage"])(()=>localStorage),
            partialize: config.persist.partialize || ((state)=>state),
            skipHydration: config.persist.skipHydration || false,
            onRehydrateStorage: ()=>(state)=>{
                    if (state) {
                        state.setHasHydrated(true);
                    }
                }
        });
    }
    // Apply devtools middleware if configured
    if (config.devtools) {
        store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devtools"])(store, {
            name: config.devtools.name,
            enabled: config.devtools.enabled && ("TURBOPACK compile-time value", "development") === 'development'
        });
    }
    return store;
}
function createBaseStoreState() {
    return {
        _hasHydrated: false,
        setHasHydrated: (hasHydrated)=>{
        // This will be implemented by the actual store
        }
    };
}
function createBaseStoreActions(set) {
    return {
        setHasHydrated: (hasHydrated)=>{
            set({
                _hasHydrated: hasHydrated
            });
        }
    };
}
function isTokenExpired(expiresAt) {
    return Date.now() >= expiresAt;
}
function getTokenExpiration(token, defaultMinutes = 60) {
    try {
        // Try to decode JWT token to get expiration
        const payload = JSON.parse(atob(token.split('.')[1]));
        if (payload.exp) {
            return payload.exp * 1000; // Convert to milliseconds
        }
    } catch (error) {
    // If JWT parsing fails, use default expiration
    }
    // Default expiration: current time + defaultMinutes
    return Date.now() + defaultMinutes * 60 * 1000;
}
function clearStoredTokens() {
    if ("TURBOPACK compile-time truthy", 1) {
        localStorage.removeItem('auth-storage');
        sessionStorage.removeItem('auth-storage');
    }
}
function isSessionValid(lastActivity, sessionTimeout) {
    const now = Date.now();
    const timeoutMs = sessionTimeout * 60 * 1000; // Convert minutes to milliseconds
    return now - lastActivity < timeoutMs;
}
function getSessionRemainingTime(lastActivity, sessionTimeout) {
    const now = Date.now();
    const timeoutMs = sessionTimeout * 60 * 1000;
    const elapsed = now - lastActivity;
    const remaining = timeoutMs - elapsed;
    return Math.max(0, Math.floor(remaining / (60 * 1000)));
}
function extractErrorMessage(error) {
    if (typeof error === 'string') {
        return error;
    }
    if (error?.response?.data?.message) {
        return error.response.data.message;
    }
    if (error?.message) {
        return error.message;
    }
    if (error?.error) {
        return error.error;
    }
    return 'An unexpected error occurred';
}
function createErrorObject(message, details) {
    return {
        message,
        details: details || null
    };
}
function generateNotificationId() {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
function getDefaultNotificationDuration(type) {
    switch(type){
        case 'success':
            return 3000; // 3 seconds
        case 'error':
            return 5000; // 5 seconds
        case 'warning':
            return 4000; // 4 seconds
        case 'info':
            return 3000; // 3 seconds
        default:
            return 3000;
    }
}
const storage = {
    get: (key)=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : null;
        } catch (error) {
            console.warn(`Error reading from localStorage key "${key}":`, error);
            return null;
        }
    },
    set: (key, value)=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.warn(`Error writing to localStorage key "${key}":`, error);
        }
    },
    remove: (key)=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.warn(`Error removing localStorage key "${key}":`, error);
        }
    },
    clear: ()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        try {
            localStorage.clear();
        } catch (error) {
            console.warn('Error clearing localStorage:', error);
        }
    }
};
function logStoreAction(storeName, actionName, payload) {
    if ("TURBOPACK compile-time truthy", 1) {
        console.group(`🐻 [${storeName}] ${actionName}`);
        if (payload !== undefined) {
            console.log('Payload:', payload);
        }
        console.log('Timestamp:', new Date().toISOString());
        console.groupEnd();
    }
}
function validateStoreState(state, requiredKeys) {
    for (const key of requiredKeys){
        if (!(key in state)) {
            console.error(`Missing required store state key: ${String(key)}`);
            return false;
        }
    }
    return true;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/constants.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Constants
 * Defines constants used across all stores
 */ // ============================================================================
// Store Names
// ============================================================================
__turbopack_esm__({
    "ACTIVITY_TRACKING_INTERVAL": (()=>ACTIVITY_TRACKING_INTERVAL),
    "DEFAULT_APP_SETTINGS": (()=>DEFAULT_APP_SETTINGS),
    "DEFAULT_NAVIGATION": (()=>DEFAULT_NAVIGATION),
    "DEFAULT_THEME": (()=>DEFAULT_THEME),
    "DEFAULT_UI_STATE": (()=>DEFAULT_UI_STATE),
    "DEV_CONFIG": (()=>DEV_CONFIG),
    "ERROR_MESSAGES": (()=>ERROR_MESSAGES),
    "FEATURE_FLAGS": (()=>FEATURE_FLAGS),
    "SESSION_TIMEOUT": (()=>SESSION_TIMEOUT),
    "STORAGE_KEYS": (()=>STORAGE_KEYS),
    "STORE_API_ENDPOINTS": (()=>STORE_API_ENDPOINTS),
    "STORE_NAMES": (()=>STORE_NAMES),
    "STORE_VERSIONS": (()=>STORE_VERSIONS),
    "SUCCESS_MESSAGES": (()=>SUCCESS_MESSAGES),
    "TOKEN_REFRESH_THRESHOLD": (()=>TOKEN_REFRESH_THRESHOLD),
    "VALIDATION_RULES": (()=>VALIDATION_RULES)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const STORE_NAMES = {
    AUTH: 'auth-store',
    APP: 'app-store'
};
const STORAGE_KEYS = {
    AUTH: 'auth-storage',
    APP: 'app-storage',
    THEME: 'theme-storage',
    SETTINGS: 'settings-storage'
};
const DEFAULT_THEME = {
    mode: 'light',
    primaryColor: '#1890ff',
    borderRadius: 6,
    compactMode: false
};
const DEFAULT_APP_SETTINGS = {
    language: 'en',
    timezone: 'UTC',
    dateFormat: 'YYYY-MM-DD',
    pageSize: 20,
    autoRefresh: true,
    refreshInterval: 30,
    features: {
        darkMode: true,
        notifications: true,
        autoSave: true,
        advancedFilters: true
    }
};
const DEFAULT_NAVIGATION = {
    currentPath: '/',
    breadcrumbs: [],
    sidebarCollapsed: false,
    activeMenuKey: 'dashboard'
};
const DEFAULT_UI_STATE = {
    globalLoading: false,
    loadingMessage: '',
    globalError: null,
    errorDetails: null,
    notifications: [],
    modals: {}
};
const SESSION_TIMEOUT = 60; // 1 hour
const TOKEN_REFRESH_THRESHOLD = 5; // 5 minutes
const ACTIVITY_TRACKING_INTERVAL = 60000; // 1 minute
const STORE_API_ENDPOINTS = {
    AUTH: {
        LOGIN: '/api/system-auth/login',
        LOGOUT: '/api/system-auth/logout',
        LOGOUT_ALL: '/api/system-auth/logout-all',
        PROFILE: '/api/system-auth/profile',
        REFRESH: '/api/system-auth/refresh'
    }
};
const ERROR_MESSAGES = {
    AUTH: {
        LOGIN_FAILED: 'Login failed. Please check your credentials.',
        LOGOUT_FAILED: 'Logout failed. Please try again.',
        SESSION_EXPIRED: 'Your session has expired. Please log in again.',
        TOKEN_REFRESH_FAILED: 'Failed to refresh authentication token.',
        PROFILE_UPDATE_FAILED: 'Failed to update profile. Please try again.',
        UNAUTHORIZED: 'You are not authorized to perform this action.'
    },
    APP: {
        SETTINGS_SAVE_FAILED: 'Failed to save settings. Please try again.',
        THEME_LOAD_FAILED: 'Failed to load theme configuration.',
        NETWORK_ERROR: 'Network error. Please check your connection.',
        UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.'
    }
};
const SUCCESS_MESSAGES = {
    AUTH: {
        LOGIN_SUCCESS: 'Successfully logged in.',
        LOGOUT_SUCCESS: 'Successfully logged out.',
        PROFILE_UPDATED: 'Profile updated successfully.'
    },
    APP: {
        SETTINGS_SAVED: 'Settings saved successfully.',
        THEME_UPDATED: 'Theme updated successfully.'
    }
};
const STORE_VERSIONS = {
    AUTH: 1,
    APP: 1
};
const DEV_CONFIG = {
    ENABLE_DEVTOOLS: ("TURBOPACK compile-time value", "development") === 'development',
    ENABLE_LOGGING: ("TURBOPACK compile-time value", "development") === 'development',
    MOCK_API_DELAY: 1000
};
const FEATURE_FLAGS = {
    ENABLE_DARK_MODE: true,
    ENABLE_NOTIFICATIONS: true,
    ENABLE_AUTO_SAVE: true,
    ENABLE_ADVANCED_FILTERS: true,
    ENABLE_REAL_TIME_UPDATES: false,
    ENABLE_OFFLINE_MODE: false
};
const VALIDATION_RULES = {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    PASSWORD_MIN_LENGTH: 8,
    NAME_MIN_LENGTH: 2,
    NAME_MAX_LENGTH: 50,
    PAGE_SIZE_MIN: 5,
    PAGE_SIZE_MAX: 100,
    REFRESH_INTERVAL_MIN: 10,
    REFRESH_INTERVAL_MAX: 300
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/auth-store.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Authentication Store
 * Manages user authentication state, tokens, and session
 */ __turbopack_esm__({
    "createAuthStore": (()=>createAuthStore),
    "useAuthStore": (()=>useAuthStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
;
;
;
// ============================================================================
// Initial State
// ============================================================================
const initialAuthState = {
    // Base store state
    _hasHydrated: false,
    // User data
    user: null,
    tokens: null,
    // Authentication status
    isAuthenticated: false,
    isLoading: false,
    // Error handling
    error: null,
    // Session management
    lastActivity: Date.now(),
    sessionTimeout: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SESSION_TIMEOUT"]
};
// ============================================================================
// Store Implementation
// ============================================================================
/**
 * Authentication Store Creator
 */ const createAuthStore = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createStoreWithMiddleware"])((set, get)=>({
            ...initialAuthState,
            // Base store actions
            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createBaseStoreActions"])(set),
            // ========================================================================
            // Authentication Actions
            // ========================================================================
            /**
         * Login user with email and password
         */ login: async (email, password)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'login', {
                    email
                });
                set({
                    isLoading: true,
                    error: null
                });
                try {
                    const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.LOGIN, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            email,
                            password
                        })
                    });
                    if (!response.ok) {
                        const errorData = await response.json().catch(()=>({}));
                        throw new Error(errorData.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.LOGIN_FAILED);
                    }
                    const data = await response.json();
                    if (data.success && data.data) {
                        const { user, accessToken, refreshToken } = data.data;
                        // Create tokens object
                        const tokens = {
                            accessToken,
                            refreshToken,
                            expiresAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTokenExpiration"])(accessToken)
                        };
                        // Update store state
                        set({
                            user,
                            tokens,
                            isAuthenticated: true,
                            isLoading: false,
                            error: null,
                            lastActivity: Date.now()
                        });
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'login_success', {
                            userId: user.id
                        });
                    } else {
                        throw new Error(data.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.LOGIN_FAILED);
                    }
                } catch (error) {
                    const errorMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'login_error', {
                        error: errorMessage
                    });
                    set({
                        isLoading: false,
                        error: errorMessage,
                        isAuthenticated: false,
                        user: null,
                        tokens: null
                    });
                    throw error;
                }
            },
            /**
         * Logout user from current session
         */ logout: async ()=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout');
                const { tokens } = get();
                try {
                    // Call logout API if we have tokens
                    if (tokens?.accessToken) {
                        await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.LOGOUT, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${tokens.accessToken}`
                            }
                        });
                    }
                } catch (error) {
                    // Log error but don't prevent logout
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_api_error', {
                        error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error)
                    });
                }
                // Clear local state regardless of API call result
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearStoredTokens"])();
                set({
                    user: null,
                    tokens: null,
                    isAuthenticated: false,
                    error: null,
                    lastActivity: Date.now()
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_success');
            },
            /**
         * Logout user from all devices
         */ logoutAll: async ()=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_all');
                const { tokens } = get();
                try {
                    if (tokens?.accessToken) {
                        await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.LOGOUT_ALL, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${tokens.accessToken}`
                            }
                        });
                    }
                } catch (error) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_all_api_error', {
                        error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error)
                    });
                }
                // Clear local state
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearStoredTokens"])();
                set({
                    user: null,
                    tokens: null,
                    isAuthenticated: false,
                    error: null,
                    lastActivity: Date.now()
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_all_success');
            },
            // ========================================================================
            // User Profile Actions
            // ========================================================================
            /**
         * Update user profile
         */ updateProfile: async (data)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'update_profile', data);
                const { tokens, user } = get();
                if (!tokens?.accessToken || !user) {
                    throw new Error(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.UNAUTHORIZED);
                }
                set({
                    isLoading: true,
                    error: null
                });
                try {
                    const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.PROFILE, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${tokens.accessToken}`
                        },
                        body: JSON.stringify(data)
                    });
                    if (!response.ok) {
                        const errorData = await response.json().catch(()=>({}));
                        throw new Error(errorData.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.PROFILE_UPDATE_FAILED);
                    }
                    const responseData = await response.json();
                    if (responseData.success && responseData.data) {
                        set({
                            user: {
                                ...user,
                                ...responseData.data
                            },
                            isLoading: false,
                            error: null,
                            lastActivity: Date.now()
                        });
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'update_profile_success');
                    } else {
                        throw new Error(responseData.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.PROFILE_UPDATE_FAILED);
                    }
                } catch (error) {
                    const errorMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'update_profile_error', {
                        error: errorMessage
                    });
                    set({
                        isLoading: false,
                        error: errorMessage
                    });
                    throw error;
                }
            },
            /**
         * Refresh authentication tokens
         */ refreshTokens: async ()=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'refresh_tokens');
                const { tokens } = get();
                if (!tokens?.refreshToken) {
                    throw new Error(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.TOKEN_REFRESH_FAILED);
                }
                try {
                    const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.REFRESH, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            refreshToken: tokens.refreshToken
                        })
                    });
                    if (!response.ok) {
                        throw new Error(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.TOKEN_REFRESH_FAILED);
                    }
                    const data = await response.json();
                    if (data.success && data.data) {
                        const { accessToken, refreshToken } = data.data;
                        const newTokens = {
                            accessToken,
                            refreshToken,
                            expiresAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTokenExpiration"])(accessToken)
                        };
                        set({
                            tokens: newTokens,
                            lastActivity: Date.now()
                        });
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'refresh_tokens_success');
                    } else {
                        throw new Error(data.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.TOKEN_REFRESH_FAILED);
                    }
                } catch (error) {
                    const errorMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'refresh_tokens_error', {
                        error: errorMessage
                    });
                    // If refresh fails, logout user
                    get().logout();
                    throw error;
                }
            },
            // ========================================================================
            // State Management Actions
            // ========================================================================
            /**
         * Set user data
         */ setUser: (user)=>{
                set({
                    user,
                    isAuthenticated: !!user
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'set_user', {
                    userId: user?.id
                });
            },
            /**
         * Set authentication tokens
         */ setTokens: (tokens)=>{
                set({
                    tokens
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'set_tokens', {
                    hasTokens: !!tokens
                });
            },
            /**
         * Set loading state
         */ setLoading: (loading)=>{
                set({
                    isLoading: loading
                });
            },
            /**
         * Set error message
         */ setError: (error)=>{
                set({
                    error
                });
                if (error) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'set_error', {
                        error
                    });
                }
            },
            /**
         * Clear error message
         */ clearError: ()=>{
                set({
                    error: null
                });
            },
            // ========================================================================
            // Session Management Actions
            // ========================================================================
            /**
         * Update last activity timestamp
         */ updateLastActivity: ()=>{
                set({
                    lastActivity: Date.now()
                });
            },
            /**
         * Check if current session is valid
         */ checkSession: ()=>{
                const { lastActivity, sessionTimeout, tokens } = get();
                // Check session timeout
                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSessionValid"])(lastActivity, sessionTimeout)) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'session_expired');
                    get().logout();
                    return false;
                }
                // Check token expiration
                if (tokens && (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTokenExpired"])(tokens.expiresAt)) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'token_expired');
                    // Try to refresh tokens
                    get().refreshTokens().catch(()=>{
                    // If refresh fails, logout will be called automatically
                    });
                    return false;
                }
                return true;
            },
            /**
         * Hydrate store from persisted state
         */ hydrate: ()=>{
                const state = get();
                // Validate persisted session
                if (state.isAuthenticated && state.user && state.tokens) {
                    const isValid = state.checkSession();
                    if (!isValid) {
                        // Session is invalid, clear state
                        set({
                            user: null,
                            tokens: null,
                            isAuthenticated: false,
                            error: null
                        });
                    }
                }
                set({
                    _hasHydrated: true
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'hydrated');
            }
        }), {
        persist: {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].AUTH,
            version: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_VERSIONS"].AUTH,
            partialize: (state)=>({
                    user: state.user,
                    tokens: state.tokens,
                    isAuthenticated: state.isAuthenticated,
                    lastActivity: state.lastActivity,
                    sessionTimeout: state.sessionTimeout
                })
        },
        devtools: {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH,
            enabled: ("TURBOPACK compile-time value", "development") === 'development'
        }
    }));
};
const useAuthStore = createAuthStore();
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/auth-hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Authentication Hooks
 * Custom hooks for easy authentication state access
 */ __turbopack_esm__({
    "useAuth": (()=>useAuth),
    "useAuthDebug": (()=>useAuthDebug),
    "useAuthError": (()=>useAuthError),
    "useAuthLoading": (()=>useAuthLoading),
    "useAuthTokens": (()=>useAuthTokens),
    "useAuthWithSession": (()=>useAuthWithSession),
    "useCanAdmin": (()=>useCanAdmin),
    "useCanEdit": (()=>useCanEdit),
    "useCheckSession": (()=>useCheckSession),
    "useClearAuthError": (()=>useClearAuthError),
    "useHasRole": (()=>useHasRole),
    "useIsAdmin": (()=>useIsAdmin),
    "useIsAuthenticated": (()=>useIsAuthenticated),
    "useIsEditor": (()=>useIsEditor),
    "useIsModerator": (()=>useIsModerator),
    "useLogin": (()=>useLogin),
    "useLogout": (()=>useLogout),
    "useLogoutAll": (()=>useLogoutAll),
    "usePermissions": (()=>usePermissions),
    "useRefreshTokens": (()=>useRefreshTokens),
    "useRouteProtection": (()=>useRouteProtection),
    "useUpdateActivity": (()=>useUpdateActivity),
    "useUpdateProfile": (()=>useUpdateProfile),
    "useUser": (()=>useUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature(), _s5 = __turbopack_refresh__.signature(), _s6 = __turbopack_refresh__.signature(), _s7 = __turbopack_refresh__.signature(), _s8 = __turbopack_refresh__.signature(), _s9 = __turbopack_refresh__.signature(), _s10 = __turbopack_refresh__.signature(), _s11 = __turbopack_refresh__.signature(), _s12 = __turbopack_refresh__.signature(), _s13 = __turbopack_refresh__.signature(), _s14 = __turbopack_refresh__.signature(), _s15 = __turbopack_refresh__.signature(), _s16 = __turbopack_refresh__.signature(), _s17 = __turbopack_refresh__.signature(), _s18 = __turbopack_refresh__.signature(), _s19 = __turbopack_refresh__.signature(), _s20 = __turbopack_refresh__.signature(), _s21 = __turbopack_refresh__.signature(), _s22 = __turbopack_refresh__.signature(), _s23 = __turbopack_refresh__.signature();
'use client';
;
;
;
const useUser = ()=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useUser.useAuthStore": (state)=>state.user
    }["useUser.useAuthStore"]);
};
_s(useUser, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useIsAuthenticated = ()=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useIsAuthenticated.useAuthStore": (state)=>state.isAuthenticated
    }["useIsAuthenticated.useAuthStore"]);
};
_s1(useIsAuthenticated, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useAuthLoading = ()=>{
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useAuthLoading.useAuthStore": (state)=>state.isLoading
    }["useAuthLoading.useAuthStore"]);
};
_s2(useAuthLoading, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useAuthError = ()=>{
    _s3();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useAuthError.useAuthStore": (state)=>state.error
    }["useAuthError.useAuthStore"]);
};
_s3(useAuthError, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useAuthTokens = ()=>{
    _s4();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useAuthTokens.useAuthStore": (state)=>state.tokens
    }["useAuthTokens.useAuthStore"]);
};
_s4(useAuthTokens, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useLogin = ()=>{
    _s5();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useLogin.useAuthStore": (state)=>state.login
    }["useLogin.useAuthStore"]);
};
_s5(useLogin, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useLogout = ()=>{
    _s6();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useLogout.useAuthStore": (state)=>state.logout
    }["useLogout.useAuthStore"]);
};
_s6(useLogout, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useLogoutAll = ()=>{
    _s7();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useLogoutAll.useAuthStore": (state)=>state.logoutAll
    }["useLogoutAll.useAuthStore"]);
};
_s7(useLogoutAll, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useUpdateProfile = ()=>{
    _s8();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useUpdateProfile.useAuthStore": (state)=>state.updateProfile
    }["useUpdateProfile.useAuthStore"]);
};
_s8(useUpdateProfile, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useRefreshTokens = ()=>{
    _s9();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useRefreshTokens.useAuthStore": (state)=>state.refreshTokens
    }["useRefreshTokens.useAuthStore"]);
};
_s9(useRefreshTokens, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useClearAuthError = ()=>{
    _s10();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useClearAuthError.useAuthStore": (state)=>state.clearError
    }["useClearAuthError.useAuthStore"]);
};
_s10(useClearAuthError, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useCheckSession = ()=>{
    _s11();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useCheckSession.useAuthStore": (state)=>state.checkSession
    }["useCheckSession.useAuthStore"]);
};
_s11(useCheckSession, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useUpdateActivity = ()=>{
    _s12();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useUpdateActivity.useAuthStore": (state)=>state.updateLastActivity
    }["useUpdateActivity.useAuthStore"]);
};
_s12(useUpdateActivity, "BSK3XewfuZPixDP8tbzcobpulFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
const useHasRole = (role)=>{
    _s13();
    const user = useUser();
    return user?.role === role;
};
_s13(useHasRole, "BPnln+wUpxLjLAxQmw7xYz9C+QI=", false, function() {
    return [
        useUser
    ];
});
const useIsAdmin = ()=>{
    _s14();
    return useHasRole('Admin');
};
_s14(useIsAdmin, "jeHUSpWuvMoZ10WaPJ/up4tFqRo=", false, function() {
    return [
        useHasRole
    ];
});
const useIsEditor = ()=>{
    _s15();
    return useHasRole('Editor');
};
_s15(useIsEditor, "jeHUSpWuvMoZ10WaPJ/up4tFqRo=", false, function() {
    return [
        useHasRole
    ];
});
const useIsModerator = ()=>{
    _s16();
    return useHasRole('Moderator');
};
_s16(useIsModerator, "jeHUSpWuvMoZ10WaPJ/up4tFqRo=", false, function() {
    return [
        useHasRole
    ];
});
const useCanEdit = ()=>{
    _s17();
    const user = useUser();
    return user?.role === 'Admin' || user?.role === 'Editor';
};
_s17(useCanEdit, "BPnln+wUpxLjLAxQmw7xYz9C+QI=", false, function() {
    return [
        useUser
    ];
});
const useCanAdmin = ()=>{
    _s18();
    return useIsAdmin();
};
_s18(useCanAdmin, "Dqqt7lFNmzjkMdLJsNoq64CBQOo=", false, function() {
    return [
        useIsAdmin
    ];
});
const useAuth = ()=>{
    _s19();
    const user = useUser();
    const isAuthenticated = useIsAuthenticated();
    const isLoading = useAuthLoading();
    const error = useAuthError();
    const tokens = useAuthTokens();
    const login = useLogin();
    const logout = useLogout();
    const logoutAll = useLogoutAll();
    const updateProfile = useUpdateProfile();
    const clearError = useClearAuthError();
    return {
        // State
        user,
        isAuthenticated,
        isLoading,
        error,
        tokens,
        // Actions
        login,
        logout,
        logoutAll,
        updateProfile,
        clearError,
        // Role checks
        isAdmin: useIsAdmin(),
        isEditor: useIsEditor(),
        isModerator: useIsModerator(),
        canEdit: useCanEdit(),
        canAdmin: useCanAdmin()
    };
};
_s19(useAuth, "h91lhtaB2wbjWbd8Or0OBXynugQ=", false, function() {
    return [
        useUser,
        useIsAuthenticated,
        useAuthLoading,
        useAuthError,
        useAuthTokens,
        useLogin,
        useLogout,
        useLogoutAll,
        useUpdateProfile,
        useClearAuthError,
        useIsAdmin,
        useIsEditor,
        useIsModerator,
        useCanEdit,
        useCanAdmin
    ];
});
const useAuthWithSession = ()=>{
    _s20();
    const auth = useAuth();
    const checkSession = useCheckSession();
    const updateActivity = useUpdateActivity();
    // Auto-check session validity
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useAuthWithSession.useEffect": ()=>{
            if (auth.isAuthenticated) {
                const interval = setInterval({
                    "useAuthWithSession.useEffect.interval": ()=>{
                        checkSession();
                    }
                }["useAuthWithSession.useEffect.interval"], __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTIVITY_TRACKING_INTERVAL"]);
                return ({
                    "useAuthWithSession.useEffect": ()=>clearInterval(interval)
                })["useAuthWithSession.useEffect"];
            }
        }
    }["useAuthWithSession.useEffect"], [
        auth.isAuthenticated,
        checkSession
    ]);
    // Update activity on user interaction
    const handleUserActivity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthWithSession.useCallback[handleUserActivity]": ()=>{
            if (auth.isAuthenticated) {
                updateActivity();
            }
        }
    }["useAuthWithSession.useCallback[handleUserActivity]"], [
        auth.isAuthenticated,
        updateActivity
    ]);
    // Auto-update activity on mouse/keyboard events
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useAuthWithSession.useEffect": ()=>{
            if (auth.isAuthenticated) {
                const events = [
                    'mousedown',
                    'mousemove',
                    'keypress',
                    'scroll',
                    'touchstart'
                ];
                events.forEach({
                    "useAuthWithSession.useEffect": (event)=>{
                        document.addEventListener(event, handleUserActivity, true);
                    }
                }["useAuthWithSession.useEffect"]);
                return ({
                    "useAuthWithSession.useEffect": ()=>{
                        events.forEach({
                            "useAuthWithSession.useEffect": (event)=>{
                                document.removeEventListener(event, handleUserActivity, true);
                            }
                        }["useAuthWithSession.useEffect"]);
                    }
                })["useAuthWithSession.useEffect"];
            }
        }
    }["useAuthWithSession.useEffect"], [
        auth.isAuthenticated,
        handleUserActivity
    ]);
    return {
        ...auth,
        checkSession,
        updateActivity
    };
};
_s20(useAuthWithSession, "AZhya6d4LQ2m9d2CT9VfOC+ZQ+4=", false, function() {
    return [
        useAuth,
        useCheckSession,
        useUpdateActivity
    ];
});
const usePermissions = (requiredRoles)=>{
    _s21();
    const user = useUser();
    const hasPermission = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePermissions.useCallback[hasPermission]": (roles)=>{
            if (!user) return false;
            return roles.includes(user.role);
        }
    }["usePermissions.useCallback[hasPermission]"], [
        user
    ]);
    const hasAnyPermission = hasPermission(requiredRoles);
    return {
        hasPermission: hasAnyPermission,
        userRole: user?.role,
        checkRole: hasPermission
    };
};
_s21(usePermissions, "Lzp/G+M7rv7dScEztGTaLkLHqDw=", false, function() {
    return [
        useUser
    ];
});
const useRouteProtection = (requiredRoles)=>{
    _s22();
    const isAuthenticated = useIsAuthenticated();
    const user = useUser();
    const isLoading = useAuthLoading();
    const hasAccess = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRouteProtection.useCallback[hasAccess]": ()=>{
            if (!isAuthenticated) return false;
            if (!requiredRoles || requiredRoles.length === 0) return true;
            if (!user) return false;
            return requiredRoles.includes(user.role);
        }
    }["useRouteProtection.useCallback[hasAccess]"], [
        isAuthenticated,
        user,
        requiredRoles
    ]);
    return {
        isAuthenticated,
        hasAccess: hasAccess(),
        isLoading,
        user,
        shouldRedirect: !isLoading && !isAuthenticated,
        shouldShowUnauthorized: !isLoading && isAuthenticated && !hasAccess()
    };
};
_s22(useRouteProtection, "zSxlCGxBMi2FdpU3LspmENoF2lA=", false, function() {
    return [
        useIsAuthenticated,
        useUser,
        useAuthLoading
    ];
});
const useAuthDebug = ()=>{
    _s23();
    const state = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])({
        "useAuthDebug.useAuthStore[state]": (state)=>state
    }["useAuthDebug.useAuthStore[state]"]);
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return {
        fullState: state,
        hasHydrated: state._hasHydrated,
        lastActivity: new Date(state.lastActivity).toISOString(),
        sessionTimeout: state.sessionTimeout,
        tokenExpiry: state.tokens ? new Date(state.tokens.expiresAt).toISOString() : null
    };
};
_s23(useAuthDebug, "u52TyCeu374WlgENmxbZ5P3kg3w=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/auth-utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Authentication Utilities
 * Helper functions for authentication operations
 */ __turbopack_esm__({
    "authenticatedApiRequest": (()=>authenticatedApiRequest),
    "authenticatedFetch": (()=>authenticatedFetch),
    "canAccessRoute": (()=>canAccessRoute),
    "canAdmin": (()=>canAdmin),
    "canEdit": (()=>canEdit),
    "canModerate": (()=>canModerate),
    "checkSession": (()=>checkSession),
    "clearAuthError": (()=>clearAuthError),
    "getAccessToken": (()=>getAccessToken),
    "getAuthError": (()=>getAuthError),
    "getAuthHeader": (()=>getAuthHeader),
    "getAuthSessionRemainingTime": (()=>getAuthSessionRemainingTime),
    "getAuthState": (()=>getAuthState),
    "getCurrentUser": (()=>getCurrentUser),
    "getCurrentUserRole": (()=>getCurrentUserRole),
    "getLoginRedirectPath": (()=>getLoginRedirectPath),
    "getPostLoginRedirectPath": (()=>getPostLoginRedirectPath),
    "getRefreshToken": (()=>getRefreshToken),
    "hasAnyRole": (()=>hasAnyRole),
    "hasAuthError": (()=>hasAuthError),
    "hasRole": (()=>hasRole),
    "isAdmin": (()=>isAdmin),
    "isAuthenticated": (()=>isAuthenticated),
    "isEditor": (()=>isEditor),
    "isModerator": (()=>isModerator),
    "isSessionExpiringSoon": (()=>isSessionExpiringSoon),
    "login": (()=>login),
    "logout": (()=>logout),
    "logoutAll": (()=>logoutAll),
    "mockLogin": (()=>mockLogin),
    "refreshTokens": (()=>refreshTokens),
    "resetAuthState": (()=>resetAuthState),
    "updateActivity": (()=>updateActivity),
    "updateProfile": (()=>updateProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
;
const getAccessToken = ()=>{
    const tokens = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState().tokens;
    return tokens?.accessToken || null;
};
const getRefreshToken = ()=>{
    const tokens = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState().tokens;
    return tokens?.refreshToken || null;
};
const getAuthHeader = ()=>{
    const token = getAccessToken();
    return token ? {
        Authorization: `Bearer ${token}`
    } : {};
};
const isAuthenticated = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState().isAuthenticated;
};
const getCurrentUser = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState().user;
};
const getCurrentUserRole = ()=>{
    const user = getCurrentUser();
    return user?.role || null;
};
const hasRole = (role)=>{
    const currentRole = getCurrentUserRole();
    return currentRole === role;
};
const hasAnyRole = (roles)=>{
    const currentRole = getCurrentUserRole();
    return currentRole ? roles.includes(currentRole) : false;
};
const isAdmin = ()=>{
    return hasRole('Admin');
};
const isEditor = ()=>{
    return hasRole('Editor');
};
const isModerator = ()=>{
    return hasRole('Moderator');
};
const canEdit = ()=>{
    return hasAnyRole([
        'Admin',
        'Editor'
    ]);
};
const canAdmin = ()=>{
    return isAdmin();
};
const canModerate = ()=>{
    return hasAnyRole([
        'Admin',
        'Editor',
        'Moderator'
    ]);
};
const checkSession = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState().checkSession();
};
const updateActivity = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState().updateLastActivity();
};
const getAuthSessionRemainingTime = ()=>{
    const { lastActivity, sessionTimeout } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState();
    const now = Date.now();
    const timeoutMs = sessionTimeout * 60 * 1000;
    const elapsed = now - lastActivity;
    const remaining = timeoutMs - elapsed;
    return Math.max(0, Math.floor(remaining / (60 * 1000)));
};
const isSessionExpiringSoon = ()=>{
    return getAuthSessionRemainingTime() <= 5;
};
const login = async (email, password)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState().login(email, password);
};
const logout = async ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState().logout();
};
const logoutAll = async ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState().logoutAll();
};
const updateProfile = async (data)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState().updateProfile(data);
};
const refreshTokens = async ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState().refreshTokens();
};
const getAuthError = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState().error;
};
const clearAuthError = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState().clearError();
};
const hasAuthError = ()=>{
    return !!getAuthError();
};
const authenticatedFetch = async (url, options = {})=>{
    const authHeaders = getAuthHeader();
    const config = {
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...authHeaders,
            ...options.headers
        }
    };
    const response = await fetch(url, config);
    // Handle token expiration
    if (response.status === 401) {
        const isAuth = isAuthenticated();
        if (isAuth) {
            // Try to refresh tokens
            try {
                await refreshTokens();
                // Retry the request with new token
                const newAuthHeaders = getAuthHeader();
                const retryConfig = {
                    ...config,
                    headers: {
                        ...config.headers,
                        ...newAuthHeaders
                    }
                };
                return fetch(url, retryConfig);
            } catch (error) {
                // Refresh failed, logout user
                await logout();
                throw new Error('Authentication expired. Please log in again.');
            }
        }
    }
    return response;
};
const authenticatedApiRequest = async (url, options = {})=>{
    const response = await authenticatedFetch(url, options);
    if (!response.ok) {
        const errorData = await response.json().catch(()=>({}));
        throw new Error(errorData.message || `Request failed with status ${response.status}`);
    }
    return response.json();
};
const canAccessRoute = (requiredRoles)=>{
    if (!isAuthenticated()) return false;
    if (!requiredRoles || requiredRoles.length === 0) return true;
    return hasAnyRole(requiredRoles);
};
const getLoginRedirectPath = (currentPath)=>{
    const loginPath = '/login';
    if (currentPath && currentPath !== '/') {
        return `${loginPath}?redirect=${encodeURIComponent(currentPath)}`;
    }
    return loginPath;
};
const getPostLoginRedirectPath = (searchParams)=>{
    const redirectParam = searchParams?.get('redirect');
    return redirectParam || '/dashboard';
};
const getAuthState = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState();
};
const mockLogin = (user, tokens)=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState().setUser(user);
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState().setTokens(tokens);
};
const resetAuthState = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"].getState().logout();
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/app-store.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Application Store
 * Manages application state, theme, settings, navigation, and UI state
 */ __turbopack_esm__({
    "createAppStore": (()=>createAppStore),
    "useAppStore": (()=>useAppStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
;
;
;
// ============================================================================
// Initial State
// ============================================================================
const initialAppState = {
    // Base store state
    _hasHydrated: false,
    // Configuration
    theme: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_THEME"],
    settings: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_APP_SETTINGS"],
    // Navigation
    navigation: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_NAVIGATION"],
    // UI state
    ui: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_UI_STATE"],
    // System info
    version: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
    buildTime: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_BUILD_TIME || new Date().toISOString(),
    environment: ("TURBOPACK compile-time value", "development") || 'development'
};
// ============================================================================
// Store Implementation
// ============================================================================
/**
 * Application Store Creator
 */ const createAppStore = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createStoreWithMiddleware"])((set, get)=>({
            ...initialAppState,
            // Base store actions
            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createBaseStoreActions"])(set),
            // ========================================================================
            // Theme Management Actions
            // ========================================================================
            /**
         * Set theme configuration
         */ setTheme: (themeUpdate)=>{
                const currentTheme = get().theme;
                const newTheme = {
                    ...currentTheme,
                    ...themeUpdate
                };
                set({
                    theme: newTheme
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_theme', themeUpdate);
            },
            /**
         * Toggle between light and dark mode
         */ toggleTheme: ()=>{
                const currentMode = get().theme.mode;
                const newMode = currentMode === 'light' ? 'dark' : 'light';
                get().setTheme({
                    mode: newMode
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'toggle_theme', {
                    newMode
                });
            },
            // ========================================================================
            // Settings Management Actions
            // ========================================================================
            /**
         * Update application settings
         */ updateSettings: (settingsUpdate)=>{
                const currentSettings = get().settings;
                const newSettings = {
                    ...currentSettings,
                    ...settingsUpdate
                };
                set({
                    settings: newSettings
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'update_settings', settingsUpdate);
            },
            /**
         * Reset settings to default values
         */ resetSettings: ()=>{
                set({
                    settings: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_APP_SETTINGS"]
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'reset_settings');
            },
            // ========================================================================
            // Navigation Actions
            // ========================================================================
            /**
         * Set current path
         */ setCurrentPath: (path)=>{
                const currentNavigation = get().navigation;
                const newNavigation = {
                    ...currentNavigation,
                    currentPath: path
                };
                set({
                    navigation: newNavigation
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_current_path', {
                    path
                });
            },
            /**
         * Set breadcrumbs
         */ setBreadcrumbs: (breadcrumbs)=>{
                const currentNavigation = get().navigation;
                const newNavigation = {
                    ...currentNavigation,
                    breadcrumbs
                };
                set({
                    navigation: newNavigation
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_breadcrumbs', {
                    count: breadcrumbs.length
                });
            },
            /**
         * Toggle sidebar collapsed state
         */ toggleSidebar: ()=>{
                const currentNavigation = get().navigation;
                const newCollapsed = !currentNavigation.sidebarCollapsed;
                const newNavigation = {
                    ...currentNavigation,
                    sidebarCollapsed: newCollapsed
                };
                set({
                    navigation: newNavigation
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'toggle_sidebar', {
                    collapsed: newCollapsed
                });
            },
            /**
         * Set active menu key
         */ setActiveMenu: (key)=>{
                const currentNavigation = get().navigation;
                const newNavigation = {
                    ...currentNavigation,
                    activeMenuKey: key
                };
                set({
                    navigation: newNavigation
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_active_menu', {
                    key
                });
            },
            // ========================================================================
            // UI State Management Actions
            // ========================================================================
            /**
         * Set global loading state
         */ setGlobalLoading: (loading, message)=>{
                const currentUI = get().ui;
                const newUI = {
                    ...currentUI,
                    globalLoading: loading,
                    loadingMessage: message || ''
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_global_loading', {
                    loading,
                    message
                });
            },
            /**
         * Set global error
         */ setGlobalError: (error, details)=>{
                const currentUI = get().ui;
                const newUI = {
                    ...currentUI,
                    globalError: error,
                    errorDetails: details || null
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_global_error', {
                    error,
                    hasDetails: !!details
                });
            },
            /**
         * Clear global error
         */ clearGlobalError: ()=>{
                const currentUI = get().ui;
                const newUI = {
                    ...currentUI,
                    globalError: null,
                    errorDetails: null
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'clear_global_error');
            },
            // ========================================================================
            // Notifications Actions
            // ========================================================================
            /**
         * Add notification
         */ addNotification: (notification)=>{
                const id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateNotificationId"])();
                const timestamp = Date.now();
                const duration = notification.duration || (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDefaultNotificationDuration"])(notification.type);
                const newNotification = {
                    ...notification,
                    id,
                    timestamp,
                    duration
                };
                const currentUI = get().ui;
                const newNotifications = [
                    ...currentUI.notifications,
                    newNotification
                ];
                const newUI = {
                    ...currentUI,
                    notifications: newNotifications
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'add_notification', {
                    type: notification.type,
                    id
                });
                // Auto-remove notification after duration
                if (duration > 0) {
                    setTimeout(()=>{
                        get().removeNotification(id);
                    }, duration);
                }
            },
            /**
         * Remove notification
         */ removeNotification: (id)=>{
                const currentUI = get().ui;
                const newNotifications = currentUI.notifications.filter((n)=>n.id !== id);
                const newUI = {
                    ...currentUI,
                    notifications: newNotifications
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'remove_notification', {
                    id
                });
            },
            /**
         * Clear all notifications
         */ clearNotifications: ()=>{
                const currentUI = get().ui;
                const newUI = {
                    ...currentUI,
                    notifications: []
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'clear_notifications');
            },
            // ========================================================================
            // Modals Actions
            // ========================================================================
            /**
         * Show modal
         */ showModal: (key, data)=>{
                const currentUI = get().ui;
                const newModals = {
                    ...currentUI.modals,
                    [key]: {
                        visible: true,
                        data
                    }
                };
                const newUI = {
                    ...currentUI,
                    modals: newModals
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'show_modal', {
                    key,
                    hasData: !!data
                });
            },
            /**
         * Hide modal
         */ hideModal: (key)=>{
                const currentUI = get().ui;
                const newModals = {
                    ...currentUI.modals,
                    [key]: {
                        visible: false,
                        data: undefined
                    }
                };
                const newUI = {
                    ...currentUI,
                    modals: newModals
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'hide_modal', {
                    key
                });
            },
            /**
         * Hide all modals
         */ hideAllModals: ()=>{
                const currentUI = get().ui;
                const newModals = {};
                // Set all modals to hidden
                Object.keys(currentUI.modals).forEach((key)=>{
                    newModals[key] = {
                        visible: false,
                        data: undefined
                    };
                });
                const newUI = {
                    ...currentUI,
                    modals: newModals
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'hide_all_modals');
            }
        }), {
        persist: {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].APP,
            version: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_VERSIONS"].APP,
            partialize: (state)=>({
                    theme: state.theme,
                    settings: state.settings,
                    navigation: {
                        sidebarCollapsed: state.navigation.sidebarCollapsed,
                        activeMenuKey: state.navigation.activeMenuKey
                    }
                })
        },
        devtools: {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP,
            enabled: ("TURBOPACK compile-time value", "development") === 'development'
        }
    }));
};
const useAppStore = createAppStore();
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/app-hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Application Hooks
 * Custom hooks for easy application state access
 */ __turbopack_esm__({
    "useActiveMenu": (()=>useActiveMenu),
    "useApp": (()=>useApp),
    "useAppSettings": (()=>useAppSettings),
    "useAppVersion": (()=>useAppVersion),
    "useBreadcrumbs": (()=>useBreadcrumbs),
    "useBuildTime": (()=>useBuildTime),
    "useCurrentPath": (()=>useCurrentPath),
    "useEnvironment": (()=>useEnvironment),
    "useGlobalError": (()=>useGlobalError),
    "useGlobalLoading": (()=>useGlobalLoading),
    "useIsDarkMode": (()=>useIsDarkMode),
    "useModal": (()=>useModal),
    "useModalActions": (()=>useModalActions),
    "useModals": (()=>useModals),
    "useNavigation": (()=>useNavigation),
    "useNavigationActions": (()=>useNavigationActions),
    "useNotificationActions": (()=>useNotificationActions),
    "useNotifications": (()=>useNotifications),
    "useNotify": (()=>useNotify),
    "useResponsive": (()=>useResponsive),
    "useSetting": (()=>useSetting),
    "useSettingsActions": (()=>useSettingsActions),
    "useSidebarState": (()=>useSidebarState),
    "useSystemInfo": (()=>useSystemInfo),
    "useTheme": (()=>useTheme),
    "useThemeActions": (()=>useThemeActions),
    "useThemeMode": (()=>useThemeMode),
    "useUIActions": (()=>useUIActions),
    "useUIState": (()=>useUIState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-store.ts [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature(), _s5 = __turbopack_refresh__.signature(), _s6 = __turbopack_refresh__.signature(), _s7 = __turbopack_refresh__.signature(), _s8 = __turbopack_refresh__.signature(), _s9 = __turbopack_refresh__.signature(), _s10 = __turbopack_refresh__.signature(), _s11 = __turbopack_refresh__.signature(), _s12 = __turbopack_refresh__.signature(), _s13 = __turbopack_refresh__.signature(), _s14 = __turbopack_refresh__.signature(), _s15 = __turbopack_refresh__.signature(), _s16 = __turbopack_refresh__.signature(), _s17 = __turbopack_refresh__.signature(), _s18 = __turbopack_refresh__.signature(), _s19 = __turbopack_refresh__.signature(), _s20 = __turbopack_refresh__.signature(), _s21 = __turbopack_refresh__.signature(), _s22 = __turbopack_refresh__.signature(), _s23 = __turbopack_refresh__.signature(), _s24 = __turbopack_refresh__.signature(), _s25 = __turbopack_refresh__.signature(), _s26 = __turbopack_refresh__.signature(), _s27 = __turbopack_refresh__.signature(), _s28 = __turbopack_refresh__.signature();
'use client';
;
;
const useTheme = ()=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useTheme.useAppStore": (state)=>state.theme
    }["useTheme.useAppStore"]);
};
_s(useTheme, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useThemeMode = ()=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useThemeMode.useAppStore": (state)=>state.theme.mode
    }["useThemeMode.useAppStore"]);
};
_s1(useThemeMode, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useIsDarkMode = ()=>{
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useIsDarkMode.useAppStore": (state)=>state.theme.mode === 'dark'
    }["useIsDarkMode.useAppStore"]);
};
_s2(useIsDarkMode, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useThemeActions = ()=>{
    _s3();
    const setTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useThemeActions.useAppStore[setTheme]": (state)=>state.setTheme
    }["useThemeActions.useAppStore[setTheme]"]);
    const toggleTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useThemeActions.useAppStore[toggleTheme]": (state)=>state.toggleTheme
    }["useThemeActions.useAppStore[toggleTheme]"]);
    return {
        setTheme,
        toggleTheme
    };
};
_s3(useThemeActions, "fktaLG1y+yrwp1eeIN6PtDjRE5o=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useAppSettings = ()=>{
    _s4();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useAppSettings.useAppStore": (state)=>state.settings
    }["useAppSettings.useAppStore"]);
};
_s4(useAppSettings, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useSettingsActions = ()=>{
    _s5();
    const updateSettings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useSettingsActions.useAppStore[updateSettings]": (state)=>state.updateSettings
    }["useSettingsActions.useAppStore[updateSettings]"]);
    const resetSettings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useSettingsActions.useAppStore[resetSettings]": (state)=>state.resetSettings
    }["useSettingsActions.useAppStore[resetSettings]"]);
    return {
        updateSettings,
        resetSettings
    };
};
_s5(useSettingsActions, "YRCJBTyYwTrRohP2UON80zn4N1w=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useSetting = (key)=>{
    _s6();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useSetting.useAppStore": (state)=>state.settings[key]
    }["useSetting.useAppStore"]);
};
_s6(useSetting, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useNavigation = ()=>{
    _s7();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNavigation.useAppStore": (state)=>state.navigation
    }["useNavigation.useAppStore"]);
};
_s7(useNavigation, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useCurrentPath = ()=>{
    _s8();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useCurrentPath.useAppStore": (state)=>state.navigation.currentPath
    }["useCurrentPath.useAppStore"]);
};
_s8(useCurrentPath, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useBreadcrumbs = ()=>{
    _s9();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useBreadcrumbs.useAppStore": (state)=>state.navigation.breadcrumbs
    }["useBreadcrumbs.useAppStore"]);
};
_s9(useBreadcrumbs, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useSidebarState = ()=>{
    _s10();
    const collapsed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useSidebarState.useAppStore[collapsed]": (state)=>state.navigation.sidebarCollapsed
    }["useSidebarState.useAppStore[collapsed]"]);
    const toggleSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useSidebarState.useAppStore[toggleSidebar]": (state)=>state.toggleSidebar
    }["useSidebarState.useAppStore[toggleSidebar]"]);
    return {
        collapsed,
        toggleSidebar
    };
};
_s10(useSidebarState, "0LbuncB40cF4m6ds8NIvdt0eHO0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useActiveMenu = ()=>{
    _s11();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useActiveMenu.useAppStore": (state)=>state.navigation.activeMenuKey
    }["useActiveMenu.useAppStore"]);
};
_s11(useActiveMenu, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useNavigationActions = ()=>{
    _s12();
    const setCurrentPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNavigationActions.useAppStore[setCurrentPath]": (state)=>state.setCurrentPath
    }["useNavigationActions.useAppStore[setCurrentPath]"]);
    const setBreadcrumbs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNavigationActions.useAppStore[setBreadcrumbs]": (state)=>state.setBreadcrumbs
    }["useNavigationActions.useAppStore[setBreadcrumbs]"]);
    const toggleSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNavigationActions.useAppStore[toggleSidebar]": (state)=>state.toggleSidebar
    }["useNavigationActions.useAppStore[toggleSidebar]"]);
    const setActiveMenu = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNavigationActions.useAppStore[setActiveMenu]": (state)=>state.setActiveMenu
    }["useNavigationActions.useAppStore[setActiveMenu]"]);
    return {
        setCurrentPath,
        setBreadcrumbs,
        toggleSidebar,
        setActiveMenu
    };
};
_s12(useNavigationActions, "q6iR5zAz0svK3rwVC1m07g+0co4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useUIState = ()=>{
    _s13();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useUIState.useAppStore": (state)=>state.ui
    }["useUIState.useAppStore"]);
};
_s13(useUIState, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useGlobalLoading = ()=>{
    _s14();
    const loading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useGlobalLoading.useAppStore[loading]": (state)=>state.ui.globalLoading
    }["useGlobalLoading.useAppStore[loading]"]);
    const message = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useGlobalLoading.useAppStore[message]": (state)=>state.ui.loadingMessage
    }["useGlobalLoading.useAppStore[message]"]);
    return {
        loading,
        message
    };
};
_s14(useGlobalLoading, "qDIZx7g2h4o/l0FydxK1Za5p7xE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useGlobalError = ()=>{
    _s15();
    const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useGlobalError.useAppStore[error]": (state)=>state.ui.globalError
    }["useGlobalError.useAppStore[error]"]);
    const details = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useGlobalError.useAppStore[details]": (state)=>state.ui.errorDetails
    }["useGlobalError.useAppStore[details]"]);
    return {
        error,
        details
    };
};
_s15(useGlobalError, "JhhG5vdITFJAjWeyDjp+ZkKz8Ww=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useUIActions = ()=>{
    _s16();
    const setGlobalLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useUIActions.useAppStore[setGlobalLoading]": (state)=>state.setGlobalLoading
    }["useUIActions.useAppStore[setGlobalLoading]"]);
    const setGlobalError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useUIActions.useAppStore[setGlobalError]": (state)=>state.setGlobalError
    }["useUIActions.useAppStore[setGlobalError]"]);
    const clearGlobalError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useUIActions.useAppStore[clearGlobalError]": (state)=>state.clearGlobalError
    }["useUIActions.useAppStore[clearGlobalError]"]);
    return {
        setGlobalLoading,
        setGlobalError,
        clearGlobalError
    };
};
_s16(useUIActions, "itumUNF25Atg29v/X4BqO+jDoaE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useNotifications = ()=>{
    _s17();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNotifications.useAppStore": (state)=>state.ui.notifications
    }["useNotifications.useAppStore"]);
};
_s17(useNotifications, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useNotificationActions = ()=>{
    _s18();
    const addNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNotificationActions.useAppStore[addNotification]": (state)=>state.addNotification
    }["useNotificationActions.useAppStore[addNotification]"]);
    const removeNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNotificationActions.useAppStore[removeNotification]": (state)=>state.removeNotification
    }["useNotificationActions.useAppStore[removeNotification]"]);
    const clearNotifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNotificationActions.useAppStore[clearNotifications]": (state)=>state.clearNotifications
    }["useNotificationActions.useAppStore[clearNotifications]"]);
    return {
        addNotification,
        removeNotification,
        clearNotifications
    };
};
_s18(useNotificationActions, "8N02qsN1AIM6T9BNrU2RbFNs0kE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useNotify = ()=>{
    _s19();
    const addNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNotify.useAppStore[addNotification]": (state)=>state.addNotification
    }["useNotify.useAppStore[addNotification]"]);
    const notify = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNotify.useCallback[notify]": ()=>({
                success: ({
                    "useNotify.useCallback[notify]": (message, title)=>{
                        addNotification({
                            type: 'success',
                            title: title || 'Success',
                            message
                        });
                    }
                })["useNotify.useCallback[notify]"],
                error: ({
                    "useNotify.useCallback[notify]": (message, title)=>{
                        addNotification({
                            type: 'error',
                            title: title || 'Error',
                            message
                        });
                    }
                })["useNotify.useCallback[notify]"],
                warning: ({
                    "useNotify.useCallback[notify]": (message, title)=>{
                        addNotification({
                            type: 'warning',
                            title: title || 'Warning',
                            message
                        });
                    }
                })["useNotify.useCallback[notify]"],
                info: ({
                    "useNotify.useCallback[notify]": (message, title)=>{
                        addNotification({
                            type: 'info',
                            title: title || 'Info',
                            message
                        });
                    }
                })["useNotify.useCallback[notify]"]
            })
    }["useNotify.useCallback[notify]"], [
        addNotification
    ]);
    return notify();
};
_s19(useNotify, "fqIrf568d5dpBBISc0jJBqkr4Tw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useModals = ()=>{
    _s20();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useModals.useAppStore": (state)=>state.ui.modals
    }["useModals.useAppStore"]);
};
_s20(useModals, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useModal = (key)=>{
    _s21();
    const modal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useModal.useAppStore[modal]": (state)=>state.ui.modals[key]
    }["useModal.useAppStore[modal]"]);
    const showModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useModal.useAppStore[showModal]": (state)=>state.showModal
    }["useModal.useAppStore[showModal]"]);
    const hideModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useModal.useAppStore[hideModal]": (state)=>state.hideModal
    }["useModal.useAppStore[hideModal]"]);
    const show = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[show]": (data)=>{
            showModal(key, data);
        }
    }["useModal.useCallback[show]"], [
        showModal,
        key
    ]);
    const hide = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[hide]": ()=>{
            hideModal(key);
        }
    }["useModal.useCallback[hide]"], [
        hideModal,
        key
    ]);
    return {
        visible: modal?.visible || false,
        data: modal?.data,
        show,
        hide
    };
};
_s21(useModal, "+xze8a4MsG4hK71Dr9tsD0e+ECY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useModalActions = ()=>{
    _s22();
    const showModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useModalActions.useAppStore[showModal]": (state)=>state.showModal
    }["useModalActions.useAppStore[showModal]"]);
    const hideModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useModalActions.useAppStore[hideModal]": (state)=>state.hideModal
    }["useModalActions.useAppStore[hideModal]"]);
    const hideAllModals = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useModalActions.useAppStore[hideAllModals]": (state)=>state.hideAllModals
    }["useModalActions.useAppStore[hideAllModals]"]);
    return {
        showModal,
        hideModal,
        hideAllModals
    };
};
_s22(useModalActions, "SCo+OhxHAqnk/it92onpXUpbgKc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useAppVersion = ()=>{
    _s23();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useAppVersion.useAppStore": (state)=>state.version
    }["useAppVersion.useAppStore"]);
};
_s23(useAppVersion, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useBuildTime = ()=>{
    _s24();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useBuildTime.useAppStore": (state)=>state.buildTime
    }["useBuildTime.useAppStore"]);
};
_s24(useBuildTime, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useEnvironment = ()=>{
    _s25();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useEnvironment.useAppStore": (state)=>state.environment
    }["useEnvironment.useAppStore"]);
};
_s25(useEnvironment, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useSystemInfo = ()=>{
    _s26();
    const version = useAppVersion();
    const buildTime = useBuildTime();
    const environment = useEnvironment();
    return {
        version,
        buildTime,
        environment,
        isDevelopment: environment === 'development',
        isProduction: environment === 'production'
    };
};
_s26(useSystemInfo, "v8xdnOj/+I5EMQ1Nln1Qw2VHX9A=", false, function() {
    return [
        useAppVersion,
        useBuildTime,
        useEnvironment
    ];
});
const useApp = ()=>{
    _s27();
    const theme = useTheme();
    const settings = useAppSettings();
    const navigation = useNavigation();
    const ui = useUIState();
    const systemInfo = useSystemInfo();
    const themeActions = useThemeActions();
    const settingsActions = useSettingsActions();
    const navigationActions = useNavigationActions();
    const uiActions = useUIActions();
    const notificationActions = useNotificationActions();
    const modalActions = useModalActions();
    return {
        // State
        theme,
        settings,
        navigation,
        ui,
        systemInfo,
        // Actions
        ...themeActions,
        ...settingsActions,
        ...navigationActions,
        ...uiActions,
        ...notificationActions,
        ...modalActions
    };
};
_s27(useApp, "8QCu3HIXL9yiBaXIAYGB5ZqCeKw=", false, function() {
    return [
        useTheme,
        useAppSettings,
        useNavigation,
        useUIState,
        useSystemInfo,
        useThemeActions,
        useSettingsActions,
        useNavigationActions,
        useUIActions,
        useNotificationActions,
        useModalActions
    ];
});
const useResponsive = ()=>{
    _s28();
    const sidebarCollapsed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useResponsive.useAppStore[sidebarCollapsed]": (state)=>state.navigation.sidebarCollapsed
    }["useResponsive.useAppStore[sidebarCollapsed]"]);
    const toggleSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useResponsive.useAppStore[toggleSidebar]": (state)=>state.toggleSidebar
    }["useResponsive.useAppStore[toggleSidebar]"]);
    // Auto-collapse sidebar on mobile
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useResponsive.useEffect": ()=>{
            const handleResize = {
                "useResponsive.useEffect.handleResize": ()=>{
                    const isMobile = window.innerWidth < 768;
                    if (isMobile && !sidebarCollapsed) {
                        toggleSidebar();
                    }
                }
            }["useResponsive.useEffect.handleResize"];
            window.addEventListener('resize', handleResize);
            handleResize(); // Check on mount
            return ({
                "useResponsive.useEffect": ()=>window.removeEventListener('resize', handleResize)
            })["useResponsive.useEffect"];
        }
    }["useResponsive.useEffect"], [
        sidebarCollapsed,
        toggleSidebar
    ]);
    return {
        isMobile: ("TURBOPACK compile-time truthy", 1) ? window.innerWidth < 768 : ("TURBOPACK unreachable", undefined),
        isTablet: ("TURBOPACK compile-time truthy", 1) ? window.innerWidth >= 768 && window.innerWidth < 1024 : ("TURBOPACK unreachable", undefined),
        isDesktop: ("TURBOPACK compile-time truthy", 1) ? window.innerWidth >= 1024 : ("TURBOPACK unreachable", undefined)
    };
};
_s28(useResponsive, "cS1R8GfG/99EpyfAhhG7oTdEmgs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/app-utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Application Utilities
 * Helper functions for application operations
 */ __turbopack_esm__({
    "addNotification": (()=>addNotification),
    "applyThemeToDocument": (()=>applyThemeToDocument),
    "clearGlobalError": (()=>clearGlobalError),
    "clearNotifications": (()=>clearNotifications),
    "getActiveMenuKey": (()=>getActiveMenuKey),
    "getAppState": (()=>getAppState),
    "getAppVersion": (()=>getAppVersion),
    "getBreadcrumbs": (()=>getBreadcrumbs),
    "getBuildTime": (()=>getBuildTime),
    "getCurrentBreakpoint": (()=>getCurrentBreakpoint),
    "getCurrentNavigation": (()=>getCurrentNavigation),
    "getCurrentPath": (()=>getCurrentPath),
    "getCurrentSettings": (()=>getCurrentSettings),
    "getCurrentTheme": (()=>getCurrentTheme),
    "getCurrentThemeMode": (()=>getCurrentThemeMode),
    "getCurrentUIState": (()=>getCurrentUIState),
    "getEnvironment": (()=>getEnvironment),
    "getGlobalError": (()=>getGlobalError),
    "getModalData": (()=>getModalData),
    "getModalsState": (()=>getModalsState),
    "getNotifications": (()=>getNotifications),
    "getSetting": (()=>getSetting),
    "hideAllModals": (()=>hideAllModals),
    "hideModal": (()=>hideModal),
    "isDarkMode": (()=>isDarkMode),
    "isDesktop": (()=>isDesktop),
    "isDevelopment": (()=>isDevelopment),
    "isGlobalLoading": (()=>isGlobalLoading),
    "isMobile": (()=>isMobile),
    "isModalVisible": (()=>isModalVisible),
    "isProduction": (()=>isProduction),
    "isSidebarCollapsed": (()=>isSidebarCollapsed),
    "isTablet": (()=>isTablet),
    "notifyError": (()=>notifyError),
    "notifyInfo": (()=>notifyInfo),
    "notifySuccess": (()=>notifySuccess),
    "notifyWarning": (()=>notifyWarning),
    "removeNotification": (()=>removeNotification),
    "resetAppState": (()=>resetAppState),
    "resetSettings": (()=>resetSettings),
    "setActiveMenu": (()=>setActiveMenu),
    "setBreadcrumbs": (()=>setBreadcrumbs),
    "setCurrentPath": (()=>setCurrentPath),
    "setGlobalError": (()=>setGlobalError),
    "setGlobalLoading": (()=>setGlobalLoading),
    "setThemeMode": (()=>setThemeMode),
    "showModal": (()=>showModal),
    "toggleSidebar": (()=>toggleSidebar),
    "toggleTheme": (()=>toggleTheme),
    "updateSettings": (()=>updateSettings)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
;
const getCurrentTheme = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().theme;
};
const getCurrentThemeMode = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().theme.mode;
};
const isDarkMode = ()=>{
    return getCurrentThemeMode() === 'dark';
};
const toggleTheme = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().toggleTheme();
};
const setThemeMode = (mode)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().setTheme({
        mode
    });
};
const applyThemeToDocument = ()=>{
    if (typeof document === 'undefined') return;
    const theme = getCurrentTheme();
    const { mode, primaryColor, borderRadius } = theme;
    // Apply theme class to body
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${mode}`);
    // Apply CSS custom properties
    const root = document.documentElement;
    root.style.setProperty('--primary-color', primaryColor);
    root.style.setProperty('--border-radius', `${borderRadius}px`);
};
const getCurrentSettings = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().settings;
};
const getSetting = (key)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().settings[key];
};
const updateSettings = (settings)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().updateSettings(settings);
};
const resetSettings = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().resetSettings();
};
const getCurrentNavigation = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().navigation;
};
const getCurrentPath = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().navigation.currentPath;
};
const setCurrentPath = (path)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().setCurrentPath(path);
};
const getBreadcrumbs = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().navigation.breadcrumbs;
};
const setBreadcrumbs = (breadcrumbs)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().setBreadcrumbs(breadcrumbs);
};
const isSidebarCollapsed = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().navigation.sidebarCollapsed;
};
const toggleSidebar = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().toggleSidebar();
};
const getActiveMenuKey = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().navigation.activeMenuKey;
};
const setActiveMenu = (key)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().setActiveMenu(key);
};
const getCurrentUIState = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui;
};
const isGlobalLoading = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.globalLoading;
};
const setGlobalLoading = (loading, message)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().setGlobalLoading(loading, message);
};
const getGlobalError = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.globalError;
};
const setGlobalError = (error, details)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().setGlobalError(error, details);
};
const clearGlobalError = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().clearGlobalError();
};
const getNotifications = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.notifications;
};
const addNotification = (notification)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification(notification);
};
const removeNotification = (id)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().removeNotification(id);
};
const clearNotifications = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().clearNotifications();
};
const notifySuccess = (message, title)=>{
    addNotification({
        type: 'success',
        title: title || 'Success',
        message
    });
};
const notifyError = (message, title)=>{
    addNotification({
        type: 'error',
        title: title || 'Error',
        message
    });
};
const notifyWarning = (message, title)=>{
    addNotification({
        type: 'warning',
        title: title || 'Warning',
        message
    });
};
const notifyInfo = (message, title)=>{
    addNotification({
        type: 'info',
        title: title || 'Info',
        message
    });
};
const getModalsState = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.modals;
};
const isModalVisible = (key)=>{
    const modal = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.modals[key];
    return modal?.visible || false;
};
const getModalData = (key)=>{
    const modal = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.modals[key];
    return modal?.data;
};
const showModal = (key, data)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().showModal(key, data);
};
const hideModal = (key)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().hideModal(key);
};
const hideAllModals = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().hideAllModals();
};
const getAppVersion = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().version;
};
const getBuildTime = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().buildTime;
};
const getEnvironment = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().environment;
};
const isDevelopment = ()=>{
    return getEnvironment() === 'development';
};
const isProduction = ()=>{
    return getEnvironment() === 'production';
};
const isMobile = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return window.innerWidth < 768;
};
const isTablet = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return window.innerWidth >= 768 && window.innerWidth < 1024;
};
const isDesktop = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return window.innerWidth >= 1024;
};
const getCurrentBreakpoint = ()=>{
    if (isMobile()) return 'mobile';
    if (isTablet()) return 'tablet';
    return 'desktop';
};
const getAppState = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState();
};
const resetAppState = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    const store = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState();
    store.resetSettings();
    store.clearNotifications();
    store.hideAllModals();
    store.clearGlobalError();
    store.setGlobalLoading(false);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/store-context.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Context - React context for accessing stores
 * Provides centralized access to all Zustand stores
 */ __turbopack_esm__({
    "StoreContextProvider": (()=>StoreContextProvider),
    "useAppStoreContext": (()=>useAppStoreContext),
    "useAuthStoreContext": (()=>useAuthStoreContext),
    "useIsStoreContextAvailable": (()=>useIsStoreContextAvailable),
    "useStoreContext": (()=>useStoreContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-hooks.ts [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature();
'use client';
;
;
;
/**
 * Store context
 */ const StoreContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
function StoreContextProvider({ children }) {
    _s();
    // Get store instances
    const authStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const appStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApp"])();
    const contextValue = {
        authStore,
        appStore
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(StoreContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/stores/store-context.tsx",
        lineNumber: 48,
        columnNumber: 5
    }, this);
}
_s(StoreContextProvider, "OicTdOWBIqHnugywUtDndIumbeM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApp"]
    ];
});
_c = StoreContextProvider;
function useStoreContext() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(StoreContext);
    if (!context) {
        throw new Error('useStoreContext must be used within a StoreContextProvider');
    }
    return context;
}
_s1(useStoreContext, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function useAuthStoreContext() {
    _s2();
    const { authStore } = useStoreContext();
    return authStore;
}
_s2(useAuthStoreContext, "oXAYGMcwWuCUpHlPw3viwr0fwMY=", false, function() {
    return [
        useStoreContext
    ];
});
function useAppStoreContext() {
    _s3();
    const { appStore } = useStoreContext();
    return appStore;
}
_s3(useAppStoreContext, "ndF6xaHZzaktCbjtePbTt23CuEo=", false, function() {
    return [
        useStoreContext
    ];
});
function useIsStoreContextAvailable() {
    _s4();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(StoreContext);
    return context !== null;
}
_s4(useIsStoreContextAvailable, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_refresh__.register(_c, "StoreContextProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/store-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Provider - Main provider component for all stores
 * Wraps the application with necessary store providers
 */ __turbopack_esm__({
    "StoreProvider": (()=>StoreProvider),
    "StoreProviderUtils": (()=>StoreProviderUtils),
    "withStoreProvider": (()=>withStoreProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/store-context.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/constants.ts [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
'use client';
;
;
;
;
;
/**
 * Store initialization component
 * Handles store initialization and hydration
 */ function StoreInitializer({ children }) {
    _s();
    const authStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const appStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApp"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "StoreInitializer.useEffect": ()=>{
            // Initialize stores on mount
            const initializeStores = {
                "StoreInitializer.useEffect.initializeStores": async ()=>{
                    try {
                        console.log('✅ Stores initialized successfully');
                    } catch (error) {
                        console.error('❌ Failed to initialize stores:', error);
                    }
                }
            }["StoreInitializer.useEffect.initializeStores"];
            initializeStores();
        }
    }["StoreInitializer.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(StoreInitializer, "1EJnJaUr5kHmJgPxnLzBwxNnRWU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApp"]
    ];
});
_c = StoreInitializer;
function StoreProvider({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["StoreContextProvider"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(StoreInitializer, {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/stores/store-provider.tsx",
            lineNumber: 52,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/stores/store-provider.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
}
_c1 = StoreProvider;
function withStoreProvider(Component) {
    const WrappedComponent = (props)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(StoreProvider, {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
                ...props
            }, void 0, false, {
                fileName: "[project]/src/stores/store-provider.tsx",
                lineNumber: 67,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/stores/store-provider.tsx",
            lineNumber: 66,
            columnNumber: 5
        }, this);
    WrappedComponent.displayName = `withStoreProvider(${Component.displayName || Component.name})`;
    return WrappedComponent;
}
const StoreProviderUtils = {
    /**
   * Check if stores are properly initialized
   */ checkStoreInitialization: ()=>{
        try {
            return {
                auth: true,
                app: true,
                all: true
            };
        } catch (error) {
            console.error('Failed to check store initialization:', error);
            return {
                auth: false,
                app: false,
                all: false
            };
        }
    },
    /**
   * Reset all stores to initial state
   */ resetAllStores: ()=>{
        try {
            console.log('✅ All stores reset successfully');
        } catch (error) {
            console.error('❌ Failed to reset stores:', error);
        }
    },
    /**
   * Clear all persisted store data
   */ clearPersistedData: ()=>{
        try {
            Object.values(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORAGE_KEYS"]).forEach((key)=>{
                localStorage.removeItem(key);
            });
            console.log('✅ All persisted store data cleared');
        } catch (error) {
            console.error('❌ Failed to clear persisted data:', error);
        }
    }
};
var _c, _c1;
__turbopack_refresh__.register(_c, "StoreInitializer");
__turbopack_refresh__.register(_c1, "StoreProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/provider-hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Provider Hooks - Custom hooks for using stores with providers
 * Provides convenient access to stores through context
 */ __turbopack_esm__({
    "useAppProvider": (()=>useAppProvider),
    "useAuthProvider": (()=>useAuthProvider),
    "useStoreAvailability": (()=>useStoreAvailability),
    "useStoreDebug": (()=>useStoreDebug),
    "useStores": (()=>useStores)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/store-context.tsx [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature();
'use client';
;
;
function useAuthProvider() {
    _s();
    const authStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStoreContext"])();
    const login = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthProvider.useCallback[login]": async (credentials)=>{
            return authStore.login(credentials);
        }
    }["useAuthProvider.useCallback[login]"], [
        authStore
    ]);
    const logout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthProvider.useCallback[logout]": async ()=>{
            return authStore.logout();
        }
    }["useAuthProvider.useCallback[logout]"], [
        authStore
    ]);
    const logoutAll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthProvider.useCallback[logoutAll]": async ()=>{
            return authStore.logoutAll();
        }
    }["useAuthProvider.useCallback[logoutAll]"], [
        authStore
    ]);
    const refreshToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthProvider.useCallback[refreshToken]": async ()=>{
            return authStore.refreshToken();
        }
    }["useAuthProvider.useCallback[refreshToken]"], [
        authStore
    ]);
    const updateProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthProvider.useCallback[updateProfile]": async (data)=>{
            return authStore.updateProfile(data);
        }
    }["useAuthProvider.useCallback[updateProfile]"], [
        authStore
    ]);
    const changePassword = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthProvider.useCallback[changePassword]": async (data)=>{
            return authStore.changePassword(data);
        }
    }["useAuthProvider.useCallback[changePassword]"], [
        authStore
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useAuthProvider.useMemo": ()=>({
                // State
                user: authStore.user,
                token: authStore.token,
                refreshToken: authStore.refreshToken,
                isAuthenticated: authStore.isAuthenticated,
                isLoading: authStore.isLoading,
                error: authStore.error,
                isInitialized: authStore.isInitialized,
                // Actions
                login,
                logout,
                logoutAll,
                refreshToken: refreshToken,
                updateProfile,
                changePassword,
                clearError: authStore.clearError,
                reset: authStore.reset
            })
    }["useAuthProvider.useMemo"], [
        authStore.user,
        authStore.token,
        authStore.refreshToken,
        authStore.isAuthenticated,
        authStore.isLoading,
        authStore.error,
        authStore.isInitialized,
        login,
        logout,
        logoutAll,
        refreshToken,
        updateProfile,
        changePassword,
        authStore.clearError,
        authStore.reset
    ]);
}
_s(useAuthProvider, "oS85YpiiN5ktu11jWhhZl/tW8I8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStoreContext"]
    ];
});
function useAppProvider() {
    _s1();
    const appStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStoreContext"])();
    const setTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAppProvider.useCallback[setTheme]": (theme)=>{
            appStore.setTheme(theme);
        }
    }["useAppProvider.useCallback[setTheme]"], [
        appStore
    ]);
    const toggleTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAppProvider.useCallback[toggleTheme]": ()=>{
            appStore.toggleTheme();
        }
    }["useAppProvider.useCallback[toggleTheme]"], [
        appStore
    ]);
    const setLanguage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAppProvider.useCallback[setLanguage]": (language)=>{
            appStore.setLanguage(language);
        }
    }["useAppProvider.useCallback[setLanguage]"], [
        appStore
    ]);
    const setSidebarCollapsed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAppProvider.useCallback[setSidebarCollapsed]": (collapsed)=>{
            appStore.setSidebarCollapsed(collapsed);
        }
    }["useAppProvider.useCallback[setSidebarCollapsed]"], [
        appStore
    ]);
    const toggleSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAppProvider.useCallback[toggleSidebar]": ()=>{
            appStore.toggleSidebar();
        }
    }["useAppProvider.useCallback[toggleSidebar]"], [
        appStore
    ]);
    const setLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAppProvider.useCallback[setLoading]": (loading)=>{
            appStore.setLoading(loading);
        }
    }["useAppProvider.useCallback[setLoading]"], [
        appStore
    ]);
    const showNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAppProvider.useCallback[showNotification]": (notification)=>{
            appStore.showNotification(notification);
        }
    }["useAppProvider.useCallback[showNotification]"], [
        appStore
    ]);
    const hideNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAppProvider.useCallback[hideNotification]": ()=>{
            appStore.hideNotification();
        }
    }["useAppProvider.useCallback[hideNotification]"], [
        appStore
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useAppProvider.useMemo": ()=>({
                // State
                theme: appStore.theme,
                language: appStore.language,
                sidebarCollapsed: appStore.sidebarCollapsed,
                isLoading: appStore.isLoading,
                notification: appStore.notification,
                isInitialized: appStore.isInitialized,
                // Actions
                setTheme,
                toggleTheme,
                setLanguage,
                setSidebarCollapsed,
                toggleSidebar,
                setLoading,
                showNotification,
                hideNotification,
                reset: appStore.reset
            })
    }["useAppProvider.useMemo"], [
        appStore.theme,
        appStore.language,
        appStore.sidebarCollapsed,
        appStore.isLoading,
        appStore.notification,
        appStore.isInitialized,
        setTheme,
        toggleTheme,
        setLanguage,
        setSidebarCollapsed,
        toggleSidebar,
        setLoading,
        showNotification,
        hideNotification,
        appStore.reset
    ]);
}
_s1(useAppProvider, "RJH3h6LZsbGfJqGOhGTmHOOoXrU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStoreContext"]
    ];
});
function useStoreAvailability() {
    _s2();
    const isAvailable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useIsStoreContextAvailable"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useStoreAvailability.useMemo": ()=>({
                isAvailable,
                isStoreReady: isAvailable
            })
    }["useStoreAvailability.useMemo"], [
        isAvailable
    ]);
}
_s2(useStoreAvailability, "dOJZ4eMDH9j7B8nYKB/wGZfEsQ0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useIsStoreContextAvailable"]
    ];
});
function useStores() {
    _s3();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useStoreContext"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useStores.useMemo": ()=>({
                authStore: context.authStore,
                appStore: context.appStore
            })
    }["useStores.useMemo"], [
        context.authStore,
        context.appStore
    ]);
}
_s3(useStores, "fx9+AZvIqkZTvgQB7YH+XLZ4Q/k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useStoreContext"]
    ];
});
function useStoreDebug() {
    _s4();
    const { authStore, appStore } = useStores();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useStoreDebug.useMemo": ()=>({
                authState: {
                    user: authStore.user,
                    isAuthenticated: authStore.isAuthenticated,
                    isLoading: authStore.isLoading,
                    error: authStore.error,
                    isInitialized: authStore.isInitialized
                },
                appState: {
                    theme: appStore.theme,
                    language: appStore.language,
                    sidebarCollapsed: appStore.sidebarCollapsed,
                    isLoading: appStore.isLoading,
                    notification: appStore.notification,
                    isInitialized: appStore.isInitialized
                },
                actions: {
                    resetAuth: authStore.reset,
                    resetApp: appStore.reset,
                    clearAuthError: authStore.clearError,
                    hideNotification: appStore.hideNotification
                }
            })
    }["useStoreDebug.useMemo"], [
        authStore,
        appStore
    ]);
}
_s4(useStoreDebug, "QXXhmk12UY1uOXk8cVJ5CTrq4jY=", false, function() {
    return [
        useStores
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Index - Central export for all stores
 * Provides barrel exports for all store modules
 */ // Store types and interfaces
__turbopack_esm__({});
;
;
;
;
;
;
;
;
;
;
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/types.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/store-provider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/store-context.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/provider-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/src/stores/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/lib/query-client.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * TanStack Query Client Configuration
 * Centralized configuration for React Query client
 */ __turbopack_esm__({
    "QUERY_CONFIG": (()=>QUERY_CONFIG),
    "createQueryClient": (()=>createQueryClient),
    "getQueryClient": (()=>getQueryClient),
    "queryKeys": (()=>queryKeys),
    "queryUtils": (()=>queryUtils),
    "setupQueryErrorHandling": (()=>setupQueryErrorHandling)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-client] (ecmascript)");
;
/**
 * Default query options for the application
 */ const defaultQueryOptions = {
    queries: {
        // Stale time - how long data is considered fresh (5 minutes)
        staleTime: 5 * 60 * 1000,
        // Cache time - how long data stays in cache when unused (10 minutes)
        gcTime: 10 * 60 * 1000,
        // Retry configuration
        retry: (failureCount, error)=>{
            // Don't retry on 4xx errors (client errors)
            if (error?.status >= 400 && error?.status < 500) {
                return false;
            }
            // Retry up to 3 times for other errors
            return failureCount < 3;
        },
        // Retry delay with exponential backoff
        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
        // Refetch on window focus (disabled for CMS to avoid unnecessary requests)
        refetchOnWindowFocus: false,
        // Refetch on reconnect
        refetchOnReconnect: true,
        // Refetch on mount if data is stale
        refetchOnMount: true
    },
    mutations: {
        // Retry mutations once on failure
        retry: 1,
        // Retry delay for mutations
        retryDelay: 1000
    }
};
/**
 * Development-specific query options
 */ const developmentQueryOptions = {
    queries: {
        ...defaultQueryOptions.queries,
        // Shorter stale time in development for faster feedback
        staleTime: 1 * 60 * 1000,
        // Shorter cache time in development
        gcTime: 2 * 60 * 1000,
        // Enable refetch on window focus in development
        refetchOnWindowFocus: true
    },
    mutations: {
        ...defaultQueryOptions.mutations
    }
};
/**
 * Production-specific query options
 */ const productionQueryOptions = {
    queries: {
        ...defaultQueryOptions.queries,
        // Longer stale time in production for better performance
        staleTime: 10 * 60 * 1000,
        // Longer cache time in production
        gcTime: 30 * 60 * 1000
    },
    mutations: {
        ...defaultQueryOptions.mutations
    }
};
/**
 * Get query options based on environment
 */ function getQueryOptions() {
    const isDevelopment = ("TURBOPACK compile-time value", "development") === 'development';
    return ("TURBOPACK compile-time truthy", 1) ? developmentQueryOptions : ("TURBOPACK unreachable", undefined);
}
function createQueryClient() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClient"]({
        defaultOptions: getQueryOptions(),
        logger: {
            log: (message)=>{
                if ("TURBOPACK compile-time truthy", 1) {
                    console.log(`[QueryClient] ${message}`);
                }
            },
            warn: (message)=>{
                console.warn(`[QueryClient] ${message}`);
            },
            error: (message)=>{
                console.error(`[QueryClient] ${message}`);
            }
        }
    });
}
/**
 * Singleton QueryClient instance
 */ let queryClient = undefined;
function getQueryClient() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Client-side: create client once and reuse
    if (!queryClient) {
        queryClient = createQueryClient();
    }
    return queryClient;
}
const QUERY_CONFIG = {
    // Cache times
    STALE_TIME: {
        SHORT: 1 * 60 * 1000,
        MEDIUM: 5 * 60 * 1000,
        LONG: 10 * 60 * 1000,
        VERY_LONG: 30 * 60 * 1000
    },
    // Retry configuration
    RETRY: {
        NONE: 0,
        ONCE: 1,
        TWICE: 2,
        DEFAULT: 3
    },
    // Refetch intervals
    REFETCH_INTERVAL: {
        FAST: 30 * 1000,
        MEDIUM: 60 * 1000,
        SLOW: 5 * 60 * 1000
    }
};
const queryKeys = {
    // System authentication
    auth: {
        all: [
            'auth'
        ],
        profile: ()=>[
                ...queryKeys.auth.all,
                'profile'
            ],
        users: ()=>[
                ...queryKeys.auth.all,
                'users'
            ],
        user: (id)=>[
                ...queryKeys.auth.users(),
                id
            ]
    },
    // Football data
    football: {
        all: [
            'football'
        ],
        leagues: ()=>[
                ...queryKeys.football.all,
                'leagues'
            ],
        league: (id)=>[
                ...queryKeys.football.leagues(),
                id
            ],
        teams: ()=>[
                ...queryKeys.football.all,
                'teams'
            ],
        team: (id)=>[
                ...queryKeys.football.teams(),
                id
            ],
        fixtures: ()=>[
                ...queryKeys.football.all,
                'fixtures'
            ],
        fixture: (id)=>[
                ...queryKeys.football.fixtures(),
                id
            ],
        sync: ()=>[
                ...queryKeys.football.all,
                'sync'
            ],
        syncStatus: ()=>[
                ...queryKeys.football.sync(),
                'status'
            ]
    },
    // Broadcast links
    broadcast: {
        all: [
            'broadcast'
        ],
        links: ()=>[
                ...queryKeys.broadcast.all,
                'links'
            ],
        link: (id)=>[
                ...queryKeys.broadcast.links(),
                id
            ],
        fixture: (fixtureId)=>[
                ...queryKeys.broadcast.all,
                'fixture',
                fixtureId
            ]
    },
    // Health checks
    health: {
        all: [
            'health'
        ],
        api: ()=>[
                ...queryKeys.health.all,
                'api'
            ]
    }
};
function setupQueryErrorHandling(queryClient) {
    // This function can be used to setup global error handling
    // For now, it's a placeholder that can be extended later
    console.log('[QueryClient] Error handling setup completed');
}
const queryUtils = {
    /**
   * Invalidate all queries for a specific domain
   */ invalidateAuth: (client)=>{
        return client.invalidateQueries({
            queryKey: queryKeys.auth.all
        });
    },
    invalidateFootball: (client)=>{
        return client.invalidateQueries({
            queryKey: queryKeys.football.all
        });
    },
    invalidateBroadcast: (client)=>{
        return client.invalidateQueries({
            queryKey: queryKeys.broadcast.all
        });
    },
    /**
   * Clear all cached data
   */ clearAll: (client)=>{
        return client.clear();
    },
    /**
   * Remove specific queries from cache
   */ removeQueries: (client, queryKey)=>{
        return client.removeQueries({
            queryKey
        });
    },
    /**
   * Prefetch data
   */ prefetchAuth: (client)=>{
    // Prefetch user profile if authenticated
    // Implementation will be added when auth hooks are ready
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/query-devtools.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Query DevTools Configuration
 * Development tools for TanStack Query debugging
 */ __turbopack_esm__({
    "QueryDevTools": (()=>QueryDevTools),
    "QueryDevToolsWithErrorBoundary": (()=>QueryDevToolsWithErrorBoundary),
    "QueryInspector": (()=>QueryInspector),
    "QueryStats": (()=>QueryStats),
    "queryDevUtils": (()=>queryDevUtils),
    "useQueryDebug": (()=>useQueryDebug)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use client';
;
;
/**
 * Lazy-loaded React Query DevTools
 * Only loads in development mode
 */ const ReactQueryDevtools = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].lazy(()=>__turbopack_require__("[project]/node_modules/@tanstack/react-query-devtools/build/modern/index.js [app-client] (ecmascript, async loader)")(__turbopack_import__).then((module)=>({
            default: module.ReactQueryDevtools
        })));
_c = ReactQueryDevtools;
function QueryDevTools() {
    // Only render in development
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Suspense, {
        fallback: null,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ReactQueryDevtools, {
            initialIsOpen: false,
            position: "bottom-right",
            buttonPosition: "bottom-right",
            panelProps: {
                style: {
                    zIndex: 99999
                }
            }
        }, void 0, false, {
            fileName: "[project]/src/lib/query-devtools.tsx",
            lineNumber: 31,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/lib/query-devtools.tsx",
        lineNumber: 30,
        columnNumber: 5
    }, this);
}
_c1 = QueryDevTools;
function QueryDevToolsWithErrorBoundary() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(QueryDevToolsErrorBoundary, {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(QueryDevTools, {}, void 0, false, {
            fileName: "[project]/src/lib/query-devtools.tsx",
            lineNumber: 51,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/lib/query-devtools.tsx",
        lineNumber: 50,
        columnNumber: 5
    }, this);
}
_c2 = QueryDevToolsWithErrorBoundary;
/**
 * Error boundary for DevTools
 */ class QueryDevToolsErrorBoundary extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Component {
    constructor(props){
        super(props);
        this.state = {
            hasError: false
        };
    }
    static getDerivedStateFromError() {
        return {
            hasError: true
        };
    }
    componentDidCatch(error, errorInfo) {
        console.error('[QueryDevTools Error]', error, errorInfo);
    }
    render() {
        if (this.state.hasError) {
            return null; // Silently fail in production
        }
        return this.props.children;
    }
}
const queryDevUtils = {
    /**
   * Log query information
   */ logQuery: (queryKey, data, status)=>{
        if ("TURBOPACK compile-time truthy", 1) {
            console.group(`[Query] ${queryKey.join(' → ')}`);
            console.log('Status:', status);
            console.log('Data:', data);
            console.log('Key:', queryKey);
            console.groupEnd();
        }
    },
    /**
   * Log mutation information
   */ logMutation: (mutationKey, variables, status)=>{
        if ("TURBOPACK compile-time truthy", 1) {
            console.group(`[Mutation] ${mutationKey?.join(' → ') || 'Unknown'}`);
            console.log('Status:', status);
            console.log('Variables:', variables);
            console.log('Key:', mutationKey);
            console.groupEnd();
        }
    },
    /**
   * Log cache operations
   */ logCacheOperation: (operation, queryKey, data)=>{
        if ("TURBOPACK compile-time truthy", 1) {
            console.log(`[Cache ${operation}]`, {
                key: queryKey,
                data: data ? '✓' : '✗'
            });
        }
    },
    /**
   * Performance monitoring
   */ measureQueryTime: (queryKey, queryFn)=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        const startTime = performance.now();
        const label = `Query: ${queryKey.join(' → ')}`;
        console.time(label);
        return queryFn().then((result)=>{
            const endTime = performance.now();
            console.timeEnd(label);
            console.log(`[Query Performance] ${label}: ${(endTime - startTime).toFixed(2)}ms`);
            return result;
        }).catch((error)=>{
            const endTime = performance.now();
            console.timeEnd(label);
            console.log(`[Query Performance] ${label}: ${(endTime - startTime).toFixed(2)}ms (ERROR)`);
            throw error;
        });
    }
};
const useQueryDebug = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return queryDevUtils;
};
function QueryInspector({ queryKey, data, status, error }) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            position: 'fixed',
            top: 10,
            right: 10,
            background: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '10px',
            borderRadius: '5px',
            fontSize: '12px',
            fontFamily: 'monospace',
            zIndex: 10000,
            maxWidth: '300px',
            maxHeight: '200px',
            overflow: 'auto'
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                        children: "Query Key:"
                    }, void 0, false, {
                        fileName: "[project]/src/lib/query-devtools.tsx",
                        lineNumber: 208,
                        columnNumber: 12
                    }, this),
                    " ",
                    JSON.stringify(queryKey)
                ]
            }, void 0, true, {
                fileName: "[project]/src/lib/query-devtools.tsx",
                lineNumber: 208,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                        children: "Status:"
                    }, void 0, false, {
                        fileName: "[project]/src/lib/query-devtools.tsx",
                        lineNumber: 209,
                        columnNumber: 12
                    }, this),
                    " ",
                    status
                ]
            }, void 0, true, {
                fileName: "[project]/src/lib/query-devtools.tsx",
                lineNumber: 209,
                columnNumber: 7
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                        children: "Error:"
                    }, void 0, false, {
                        fileName: "[project]/src/lib/query-devtools.tsx",
                        lineNumber: 210,
                        columnNumber: 22
                    }, this),
                    " ",
                    String(error)
                ]
            }, void 0, true, {
                fileName: "[project]/src/lib/query-devtools.tsx",
                lineNumber: 210,
                columnNumber: 17
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("details", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("summary", {
                        children: "Data"
                    }, void 0, false, {
                        fileName: "[project]/src/lib/query-devtools.tsx",
                        lineNumber: 212,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                        children: JSON.stringify(data, null, 2)
                    }, void 0, false, {
                        fileName: "[project]/src/lib/query-devtools.tsx",
                        lineNumber: 213,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/lib/query-devtools.tsx",
                lineNumber: 211,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/lib/query-devtools.tsx",
        lineNumber: 193,
        columnNumber: 5
    }, this);
}
_c3 = QueryInspector;
function QueryStats() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // This would integrate with QueryClient to show stats
    // For now, just a placeholder
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            position: 'fixed',
            bottom: 10,
            left: 10,
            background: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '10px',
            borderRadius: '5px',
            fontSize: '12px',
            fontFamily: 'monospace',
            zIndex: 10000
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: "Query Stats (Dev Mode)"
            }, void 0, false, {
                fileName: "[project]/src/lib/query-devtools.tsx",
                lineNumber: 242,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: "Active Queries: -"
            }, void 0, false, {
                fileName: "[project]/src/lib/query-devtools.tsx",
                lineNumber: 243,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: "Cache Size: -"
            }, void 0, false, {
                fileName: "[project]/src/lib/query-devtools.tsx",
                lineNumber: 244,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/lib/query-devtools.tsx",
        lineNumber: 230,
        columnNumber: 5
    }, this);
}
_c4 = QueryStats;
var _c, _c1, _c2, _c3, _c4;
__turbopack_refresh__.register(_c, "ReactQueryDevtools");
__turbopack_refresh__.register(_c1, "QueryDevTools");
__turbopack_refresh__.register(_c2, "QueryDevToolsWithErrorBoundary");
__turbopack_refresh__.register(_c3, "QueryInspector");
__turbopack_refresh__.register(_c4, "QueryStats");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/query-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Query Provider Component
 * Wrapper for QueryClientProvider with Next.js integration
 */ __turbopack_esm__({
    "QueryProvider": (()=>QueryProvider),
    "QueryProviderUtils": (()=>QueryProviderUtils),
    "QueryProviderWithErrorBoundary": (()=>QueryProviderWithErrorBoundary),
    "withQueryProvider": (()=>withQueryProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$devtools$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-devtools.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
'use client';
;
;
;
;
function QueryProvider({ children }) {
    _s();
    // Create query client instance (singleton on client, new on server)
    const [queryClient] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState({
        "QueryProvider.useState": ()=>{
            const client = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getQueryClient"])();
            // Setup error handling
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setupQueryErrorHandling"])(client);
            return client;
        }
    }["QueryProvider.useState"]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
        client: queryClient,
        children: [
            children,
            ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$devtools$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryDevToolsWithErrorBoundary"], {}, void 0, false, {
                fileName: "[project]/src/lib/query-provider.tsx",
                lineNumber: 39,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/lib/query-provider.tsx",
        lineNumber: 36,
        columnNumber: 5
    }, this);
}
_s(QueryProvider, "YoUgFNzUqoycFIP6xHFtWaloT/Q=");
_c = QueryProvider;
function QueryProviderWithErrorBoundary({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(QueryProviderErrorBoundary, {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(QueryProvider, {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/lib/query-provider.tsx",
            lineNumber: 51,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/lib/query-provider.tsx",
        lineNumber: 50,
        columnNumber: 5
    }, this);
}
_c1 = QueryProviderWithErrorBoundary;
/**
 * Error boundary for Query Provider
 */ class QueryProviderErrorBoundary extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Component {
    constructor(props){
        super(props);
        this.state = {
            hasError: false
        };
    }
    static getDerivedStateFromError(error) {
        return {
            hasError: true,
            error
        };
    }
    componentDidCatch(error, errorInfo) {
        console.error('[QueryProvider Error]', error, errorInfo);
        // Log to external service in production
        if (("TURBOPACK compile-time value", "development") === 'production') {
        // TODO: Integrate with error reporting service
        }
    }
    render() {
        if (this.state.hasError) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(QueryProviderErrorFallback, {
                error: this.state.error,
                onRetry: ()=>this.setState({
                        hasError: false,
                        error: undefined
                    })
            }, void 0, false, {
                fileName: "[project]/src/lib/query-provider.tsx",
                lineNumber: 86,
                columnNumber: 9
            }, this);
        }
        return this.props.children;
    }
}
/**
 * Error fallback component for Query Provider
 */ function QueryProviderErrorFallback({ error, onRetry }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '100vh',
            padding: '20px',
            textAlign: 'center',
            fontFamily: 'system-ui, sans-serif'
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                style: {
                    color: '#dc2626',
                    marginBottom: '16px'
                },
                children: "Query Provider Error"
            }, void 0, false, {
                fileName: "[project]/src/lib/query-provider.tsx",
                lineNumber: 118,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                style: {
                    color: '#6b7280',
                    marginBottom: '24px',
                    maxWidth: '500px'
                },
                children: "An error occurred while initializing the query system. This might be due to a network issue or a configuration problem."
            }, void 0, false, {
                fileName: "[project]/src/lib/query-provider.tsx",
                lineNumber: 121,
                columnNumber: 7
            }, this),
            error && ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("details", {
                style: {
                    marginBottom: '24px',
                    padding: '16px',
                    backgroundColor: '#f3f4f6',
                    borderRadius: '8px',
                    textAlign: 'left',
                    maxWidth: '600px',
                    width: '100%'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("summary", {
                        style: {
                            cursor: 'pointer',
                            fontWeight: 'bold'
                        },
                        children: "Error Details (Development)"
                    }, void 0, false, {
                        fileName: "[project]/src/lib/query-provider.tsx",
                        lineNumber: 135,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                        style: {
                            marginTop: '12px',
                            fontSize: '12px',
                            overflow: 'auto',
                            whiteSpace: 'pre-wrap'
                        },
                        children: [
                            error.message,
                            error.stack && `\n\n${error.stack}`
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/lib/query-provider.tsx",
                        lineNumber: 138,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/lib/query-provider.tsx",
                lineNumber: 126,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                onClick: onRetry,
                style: {
                    padding: '12px 24px',
                    backgroundColor: '#3b82f6',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    fontSize: '16px'
                },
                children: "Retry"
            }, void 0, false, {
                fileName: "[project]/src/lib/query-provider.tsx",
                lineNumber: 149,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/lib/query-provider.tsx",
        lineNumber: 108,
        columnNumber: 5
    }, this);
}
_c2 = QueryProviderErrorFallback;
function withQueryProvider(Component) {
    const WrappedComponent = (props)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(QueryProvider, {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
                ...props
            }, void 0, false, {
                fileName: "[project]/src/lib/query-provider.tsx",
                lineNumber: 175,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/lib/query-provider.tsx",
            lineNumber: 174,
            columnNumber: 5
        }, this);
    WrappedComponent.displayName = `withQueryProvider(${Component.displayName || Component.name})`;
    return WrappedComponent;
}
const QueryProviderUtils = {
    /**
   * Check if QueryClient is available
   */ isQueryClientAvailable: ()=>{
        try {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getQueryClient"])();
            return true;
        } catch  {
            return false;
        }
    },
    /**
   * Get current QueryClient instance
   */ getCurrentQueryClient: ()=>{
        try {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getQueryClient"])();
        } catch  {
            return null;
        }
    },
    /**
   * Reset QueryClient (development only)
   */ resetQueryClient: ()=>{
        if ("TURBOPACK compile-time truthy", 1) {
            const client = QueryProviderUtils.getCurrentQueryClient();
            if (client) {
                client.clear();
                console.log('[Dev] QueryClient reset');
            }
        }
    }
};
var _c, _c1, _c2;
__turbopack_refresh__.register(_c, "QueryProvider");
__turbopack_refresh__.register(_c1, "QueryProviderWithErrorBoundary");
__turbopack_refresh__.register(_c2, "QueryProviderErrorFallback");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/theme/config.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Ant Design Theme Configuration
 * Custom theme configuration for APISportsGame CMS
 */ __turbopack_esm__({
    "colors": (()=>colors),
    "darkTheme": (()=>darkTheme),
    "defaultTheme": (()=>defaultTheme),
    "lightTheme": (()=>lightTheme),
    "themeConfigs": (()=>themeConfigs)
});
const colors = {
    // Primary colors (Sports theme - Blue)
    primary: {
        50: '#eff6ff',
        100: '#dbeafe',
        200: '#bfdbfe',
        300: '#93c5fd',
        400: '#60a5fa',
        500: '#3b82f6',
        600: '#2563eb',
        700: '#1d4ed8',
        800: '#1e40af',
        900: '#1e3a8a'
    },
    // Success colors (Green)
    success: {
        50: '#f0fdf4',
        100: '#dcfce7',
        200: '#bbf7d0',
        300: '#86efac',
        400: '#4ade80',
        500: '#22c55e',
        600: '#16a34a',
        700: '#15803d',
        800: '#166534',
        900: '#14532d'
    },
    // Warning colors (Orange)
    warning: {
        50: '#fffbeb',
        100: '#fef3c7',
        200: '#fde68a',
        300: '#fcd34d',
        400: '#fbbf24',
        500: '#f59e0b',
        600: '#d97706',
        700: '#b45309',
        800: '#92400e',
        900: '#78350f'
    },
    // Error colors (Red)
    error: {
        50: '#fef2f2',
        100: '#fee2e2',
        200: '#fecaca',
        300: '#fca5a5',
        400: '#f87171',
        500: '#ef4444',
        600: '#dc2626',
        700: '#b91c1c',
        800: '#991b1b',
        900: '#7f1d1d'
    },
    // Neutral colors (Gray)
    neutral: {
        50: '#f9fafb',
        100: '#f3f4f6',
        200: '#e5e7eb',
        300: '#d1d5db',
        400: '#9ca3af',
        500: '#6b7280',
        600: '#4b5563',
        700: '#374151',
        800: '#1f2937',
        900: '#111827'
    }
};
const lightTheme = {
    token: {
        // Color tokens
        colorPrimary: colors.primary[500],
        colorSuccess: colors.success[500],
        colorWarning: colors.warning[500],
        colorError: colors.error[500],
        colorInfo: colors.primary[500],
        // Background colors
        colorBgContainer: '#ffffff',
        colorBgElevated: '#ffffff',
        colorBgLayout: colors.neutral[50],
        colorBgSpotlight: colors.neutral[100],
        // Text colors
        colorText: colors.neutral[900],
        colorTextSecondary: colors.neutral[600],
        colorTextTertiary: colors.neutral[500],
        colorTextQuaternary: colors.neutral[400],
        // Border colors
        colorBorder: colors.neutral[200],
        colorBorderSecondary: colors.neutral[100],
        // Typography
        fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
        fontSize: 14,
        fontSizeHeading1: 32,
        fontSizeHeading2: 24,
        fontSizeHeading3: 20,
        fontSizeHeading4: 16,
        fontSizeHeading5: 14,
        // Layout
        borderRadius: 8,
        borderRadiusLG: 12,
        borderRadiusSM: 6,
        borderRadiusXS: 4,
        // Spacing
        padding: 16,
        paddingLG: 24,
        paddingSM: 12,
        paddingXS: 8,
        paddingXXS: 4,
        margin: 16,
        marginLG: 24,
        marginSM: 12,
        marginXS: 8,
        marginXXS: 4,
        // Shadows
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        boxShadowSecondary: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        boxShadowTertiary: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        // Motion
        motionDurationFast: '0.1s',
        motionDurationMid: '0.2s',
        motionDurationSlow: '0.3s',
        // Z-index
        zIndexBase: 0,
        zIndexPopupBase: 1000
    },
    components: {
        // Layout components
        Layout: {
            headerBg: '#ffffff',
            headerHeight: 64,
            headerPadding: '0 24px',
            siderBg: '#ffffff',
            triggerBg: colors.neutral[100],
            triggerColor: colors.neutral[600]
        },
        // Menu component
        Menu: {
            itemBg: 'transparent',
            itemSelectedBg: colors.primary[50],
            itemSelectedColor: colors.primary[600],
            itemHoverBg: colors.neutral[50],
            itemHoverColor: colors.neutral[900],
            itemActiveBg: colors.primary[100],
            subMenuItemBg: 'transparent'
        },
        // Button component
        Button: {
            borderRadius: 8,
            controlHeight: 40,
            controlHeightLG: 48,
            controlHeightSM: 32,
            paddingInline: 16,
            paddingInlineLG: 20,
            paddingInlineSM: 12
        },
        // Input component
        Input: {
            borderRadius: 8,
            controlHeight: 40,
            controlHeightLG: 48,
            controlHeightSM: 32,
            paddingInline: 12
        },
        // Table component
        Table: {
            headerBg: colors.neutral[50],
            headerColor: colors.neutral[700],
            rowHoverBg: colors.neutral[25],
            borderColor: colors.neutral[200]
        },
        // Card component
        Card: {
            headerBg: 'transparent',
            borderRadiusLG: 12,
            paddingLG: 24
        },
        // Modal component
        Modal: {
            borderRadiusLG: 12,
            paddingLG: 24
        },
        // Notification component
        Notification: {
            borderRadiusLG: 12,
            paddingLG: 16
        },
        // Message component
        Message: {
            borderRadiusLG: 8,
            paddingLG: 12
        }
    }
};
const darkTheme = {
    token: {
        // Color tokens
        colorPrimary: colors.primary[400],
        colorSuccess: colors.success[400],
        colorWarning: colors.warning[400],
        colorError: colors.error[400],
        colorInfo: colors.primary[400],
        // Background colors
        colorBgContainer: colors.neutral[800],
        colorBgElevated: colors.neutral[700],
        colorBgLayout: colors.neutral[900],
        colorBgSpotlight: colors.neutral[800],
        // Text colors
        colorText: colors.neutral[100],
        colorTextSecondary: colors.neutral[300],
        colorTextTertiary: colors.neutral[400],
        colorTextQuaternary: colors.neutral[500],
        // Border colors
        colorBorder: colors.neutral[600],
        colorBorderSecondary: colors.neutral[700],
        // Typography (inherit from light theme)
        fontFamily: lightTheme.token?.fontFamily,
        fontSize: lightTheme.token?.fontSize,
        fontSizeHeading1: lightTheme.token?.fontSizeHeading1,
        fontSizeHeading2: lightTheme.token?.fontSizeHeading2,
        fontSizeHeading3: lightTheme.token?.fontSizeHeading3,
        fontSizeHeading4: lightTheme.token?.fontSizeHeading4,
        fontSizeHeading5: lightTheme.token?.fontSizeHeading5,
        // Layout (inherit from light theme)
        borderRadius: lightTheme.token?.borderRadius,
        borderRadiusLG: lightTheme.token?.borderRadiusLG,
        borderRadiusSM: lightTheme.token?.borderRadiusSM,
        borderRadiusXS: lightTheme.token?.borderRadiusXS,
        // Spacing (inherit from light theme)
        padding: lightTheme.token?.padding,
        paddingLG: lightTheme.token?.paddingLG,
        paddingSM: lightTheme.token?.paddingSM,
        paddingXS: lightTheme.token?.paddingXS,
        paddingXXS: lightTheme.token?.paddingXXS,
        margin: lightTheme.token?.margin,
        marginLG: lightTheme.token?.marginLG,
        marginSM: lightTheme.token?.marginSM,
        marginXS: lightTheme.token?.marginXS,
        marginXXS: lightTheme.token?.marginXXS,
        // Shadows (darker for dark theme)
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2)',
        boxShadowSecondary: '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)',
        boxShadowTertiary: '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)',
        // Motion (inherit from light theme)
        motionDurationFast: lightTheme.token?.motionDurationFast,
        motionDurationMid: lightTheme.token?.motionDurationMid,
        motionDurationSlow: lightTheme.token?.motionDurationSlow,
        // Z-index (inherit from light theme)
        zIndexBase: lightTheme.token?.zIndexBase,
        zIndexPopupBase: lightTheme.token?.zIndexPopupBase
    },
    components: {
        // Layout components
        Layout: {
            headerBg: colors.neutral[800],
            headerHeight: 64,
            headerPadding: '0 24px',
            siderBg: colors.neutral[800],
            triggerBg: colors.neutral[700],
            triggerColor: colors.neutral[300]
        },
        // Menu component
        Menu: {
            itemBg: 'transparent',
            itemSelectedBg: colors.primary[900],
            itemSelectedColor: colors.primary[300],
            itemHoverBg: colors.neutral[700],
            itemHoverColor: colors.neutral[100],
            itemActiveBg: colors.primary[800],
            subMenuItemBg: 'transparent'
        },
        // Inherit other components from light theme with dark adjustments
        Button: lightTheme.components?.Button,
        Input: lightTheme.components?.Input,
        Table: {
            ...lightTheme.components?.Table,
            headerBg: colors.neutral[700],
            headerColor: colors.neutral[200],
            rowHoverBg: colors.neutral[750],
            borderColor: colors.neutral[600]
        },
        Card: lightTheme.components?.Card,
        Modal: lightTheme.components?.Modal,
        Notification: lightTheme.components?.Notification,
        Message: lightTheme.components?.Message
    }
};
const themeConfigs = {
    light: lightTheme,
    dark: darkTheme
};
const defaultTheme = 'light';
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/theme/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Theme Utilities
 * Helper functions for theme management
 */ __turbopack_esm__({
    "THEME_CONSTANTS": (()=>THEME_CONSTANTS),
    "applyCSSVariables": (()=>applyCSSVariables),
    "applyThemeToDocument": (()=>applyThemeToDocument),
    "createThemeMediaQueryListener": (()=>createThemeMediaQueryListener),
    "generateThemeCSSVariables": (()=>generateThemeCSSVariables),
    "getEffectiveTheme": (()=>getEffectiveTheme),
    "getStoredTheme": (()=>getStoredTheme),
    "getSystemTheme": (()=>getSystemTheme),
    "getThemeColors": (()=>getThemeColors),
    "isDarkTheme": (()=>isDarkTheme),
    "isLightTheme": (()=>isLightTheme),
    "storeTheme": (()=>storeTheme),
    "themeUtils": (()=>themeUtils),
    "toggleTheme": (()=>toggleTheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/config.ts [app-client] (ecmascript)");
;
function applyThemeToDocument(theme) {
    const root = document.documentElement;
    // Set theme attribute
    root.setAttribute('data-theme', theme);
    // Apply theme class
    root.classList.remove('theme-light', 'theme-dark');
    root.classList.add(`theme-${theme}`);
    // Apply meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    const themeConfig = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeConfigs"][theme];
    if (metaThemeColor && themeConfig.token?.colorPrimary) {
        metaThemeColor.setAttribute('content', themeConfig.token.colorPrimary);
    }
}
function getSystemTheme() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    return mediaQuery.matches ? 'dark' : 'light';
}
function getStoredTheme() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    try {
        const stored = localStorage.getItem('apisportsgame_theme');
        if (stored === 'light' || stored === 'dark') {
            return stored;
        }
    } catch (error) {
        console.warn('Failed to get stored theme:', error);
    }
    return null;
}
function storeTheme(theme) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    try {
        localStorage.setItem('apisportsgame_theme', theme);
    } catch (error) {
        console.warn('Failed to store theme:', error);
    }
}
function getEffectiveTheme() {
    const stored = getStoredTheme();
    if (stored) return stored;
    return getSystemTheme();
}
function toggleTheme(currentTheme) {
    return currentTheme === 'light' ? 'dark' : 'light';
}
function isDarkTheme(theme) {
    return theme === 'dark';
}
function isLightTheme(theme) {
    return theme === 'light';
}
function getThemeColors(theme) {
    const config = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeConfigs"][theme];
    if (!config || !config.token) {
        // Fallback to default colors if config is not available
        return {
            primary: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].primary[500],
            success: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].success[500],
            warning: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].warning[500],
            error: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].error[500],
            info: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].primary[500],
            background: {
                container: theme === 'dark' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[800] : '#ffffff',
                layout: theme === 'dark' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[900] : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[50],
                elevated: theme === 'dark' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[700] : '#ffffff'
            },
            text: {
                primary: theme === 'dark' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[100] : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[900],
                secondary: theme === 'dark' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[300] : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[600],
                tertiary: theme === 'dark' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[400] : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[500]
            },
            border: {
                primary: theme === 'dark' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[600] : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[200],
                secondary: theme === 'dark' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[700] : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[100]
            }
        };
    }
    return {
        primary: config.token?.colorPrimary || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].primary[500],
        success: config.token?.colorSuccess || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].success[500],
        warning: config.token?.colorWarning || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].warning[500],
        error: config.token?.colorError || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].error[500],
        info: config.token?.colorInfo || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].primary[500],
        background: {
            container: config.token?.colorBgContainer || '#ffffff',
            layout: config.token?.colorBgLayout || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[50],
            elevated: config.token?.colorBgElevated || '#ffffff'
        },
        text: {
            primary: config.token?.colorText || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[900],
            secondary: config.token?.colorTextSecondary || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[600],
            tertiary: config.token?.colorTextTertiary || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[500]
        },
        border: {
            primary: config.token?.colorBorder || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[200],
            secondary: config.token?.colorBorderSecondary || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colors"].neutral[100]
        }
    };
}
function generateThemeCSSVariables(theme) {
    const themeColors = getThemeColors(theme);
    const config = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeConfigs"][theme];
    // Fallback values if config is not available
    const fallbackConfig = {
        borderRadius: 8,
        borderRadiusLG: 12,
        borderRadiusSM: 6,
        padding: 16,
        paddingLG: 24,
        paddingSM: 12,
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        boxShadowSecondary: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
    };
    return {
        // Color variables
        '--theme-primary': themeColors.primary,
        '--theme-success': themeColors.success,
        '--theme-warning': themeColors.warning,
        '--theme-error': themeColors.error,
        '--theme-info': themeColors.info,
        // Background variables
        '--theme-bg-container': themeColors.background.container,
        '--theme-bg-layout': themeColors.background.layout,
        '--theme-bg-elevated': themeColors.background.elevated,
        // Text variables
        '--theme-text-primary': themeColors.text.primary,
        '--theme-text-secondary': themeColors.text.secondary,
        '--theme-text-tertiary': themeColors.text.tertiary,
        // Border variables
        '--theme-border-primary': themeColors.border.primary,
        '--theme-border-secondary': themeColors.border.secondary,
        // Border radius variables
        '--theme-border-radius': `${config?.token?.borderRadius || fallbackConfig.borderRadius}px`,
        '--theme-border-radius-lg': `${config?.token?.borderRadiusLG || fallbackConfig.borderRadiusLG}px`,
        '--theme-border-radius-sm': `${config?.token?.borderRadiusSM || fallbackConfig.borderRadiusSM}px`,
        // Spacing variables
        '--theme-padding': `${config?.token?.padding || fallbackConfig.padding}px`,
        '--theme-padding-lg': `${config?.token?.paddingLG || fallbackConfig.paddingLG}px`,
        '--theme-padding-sm': `${config?.token?.paddingSM || fallbackConfig.paddingSM}px`,
        // Shadow variables
        '--theme-shadow': config?.token?.boxShadow || fallbackConfig.boxShadow,
        '--theme-shadow-lg': config?.token?.boxShadowSecondary || fallbackConfig.boxShadowSecondary
    };
}
function applyCSSVariables(theme) {
    if (typeof document === 'undefined') return;
    const variables = generateThemeCSSVariables(theme);
    const root = document.documentElement;
    Object.entries(variables).forEach(([property, value])=>{
        root.style.setProperty(property, value);
    });
}
function createThemeMediaQueryListener(callback) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const listener = (event)=>{
        const theme = event.matches ? 'dark' : 'light';
        callback(theme);
    };
    mediaQuery.addEventListener('change', listener);
    // Return cleanup function
    return ()=>{
        mediaQuery.removeEventListener('change', listener);
    };
}
const themeUtils = {
    apply: applyThemeToDocument,
    getSystem: getSystemTheme,
    getStored: getStoredTheme,
    store: storeTheme,
    getEffective: getEffectiveTheme,
    toggle: toggleTheme,
    isDark: isDarkTheme,
    isLight: isLightTheme,
    getColors: getThemeColors,
    generateCSSVariables: generateThemeCSSVariables,
    applyCSSVariables: applyCSSVariables,
    createMediaQueryListener: createThemeMediaQueryListener
};
const THEME_CONSTANTS = {
    STORAGE_KEY: 'apisportsgame_theme',
    ATTRIBUTE_NAME: 'data-theme',
    CLASS_PREFIX: 'theme-',
    MEDIA_QUERY: '(prefers-color-scheme: dark)'
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/theme/theme-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Theme Provider
 * Ant Design theme provider with global state integration
 */ __turbopack_esm__({
    "ThemeProvider": (()=>ThemeProvider),
    "ThemeProviderErrorBoundary": (()=>ThemeProviderErrorBoundary),
    "ThemeProviderWithErrorBoundary": (()=>ThemeProviderWithErrorBoundary),
    "withThemeProvider": (()=>withThemeProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/stores/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/config.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/provider-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__ConfigProvider$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/config-provider/index.js [app-client] (ecmascript) <locals> <export default as ConfigProvider>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$app$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__App$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/app/index.js [app-client] (ecmascript) <export default as App>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature();
'use client';
;
;
;
;
;
function ThemeProvider({ children }) {
    _s();
    const app = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppProvider"])();
    // Extract theme mode from theme config object
    const currentTheme = app.theme?.mode || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["defaultTheme"];
    const themeConfig = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeConfigs"][currentTheme];
    // Apply theme to document when theme changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ThemeProvider.useEffect": ()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applyThemeToDocument"])(currentTheme);
        }
    }["ThemeProvider.useEffect"], [
        currentTheme
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__ConfigProvider$3e$__["ConfigProvider"], {
        theme: themeConfig,
        componentSize: "middle",
        direction: "ltr",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$app$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__App$3e$__["App"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ThemeInitializer, {
                theme: currentTheme,
                children: children
            }, void 0, false, {
                fileName: "[project]/src/theme/theme-provider.tsx",
                lineNumber: 43,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/theme/theme-provider.tsx",
            lineNumber: 42,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/theme/theme-provider.tsx",
        lineNumber: 37,
        columnNumber: 5
    }, this);
}
_s(ThemeProvider, "cGvEuSJqxelE7sK9EkzMo33E+DQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppProvider"]
    ];
});
_c = ThemeProvider;
/**
 * Theme initializer component
 * Handles theme initialization and CSS variables
 */ function ThemeInitializer({ children, theme }) {
    _s1();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ThemeInitializer.useEffect": ()=>{
            // Set theme attribute on document
            document.documentElement.setAttribute('data-theme', theme);
            // Apply CSS variables for the current theme
            const root = document.documentElement;
            const themeConfig = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeConfigs"][theme];
            if (themeConfig.token) {
                // Apply color variables
                if (themeConfig.token.colorPrimary) {
                    root.style.setProperty('--ant-color-primary', themeConfig.token.colorPrimary);
                }
                if (themeConfig.token.colorSuccess) {
                    root.style.setProperty('--ant-color-success', themeConfig.token.colorSuccess);
                }
                if (themeConfig.token.colorWarning) {
                    root.style.setProperty('--ant-color-warning', themeConfig.token.colorWarning);
                }
                if (themeConfig.token.colorError) {
                    root.style.setProperty('--ant-color-error', themeConfig.token.colorError);
                }
                // Apply background variables
                if (themeConfig.token.colorBgContainer) {
                    root.style.setProperty('--ant-color-bg-container', themeConfig.token.colorBgContainer);
                }
                if (themeConfig.token.colorBgLayout) {
                    root.style.setProperty('--ant-color-bg-layout', themeConfig.token.colorBgLayout);
                }
                // Apply text variables
                if (themeConfig.token.colorText) {
                    root.style.setProperty('--ant-color-text', themeConfig.token.colorText);
                }
                if (themeConfig.token.colorTextSecondary) {
                    root.style.setProperty('--ant-color-text-secondary', themeConfig.token.colorTextSecondary);
                }
                // Apply border variables
                if (themeConfig.token.colorBorder) {
                    root.style.setProperty('--ant-color-border', themeConfig.token.colorBorder);
                }
                // Apply border radius variables
                if (themeConfig.token.borderRadius) {
                    root.style.setProperty('--ant-border-radius', `${themeConfig.token.borderRadius}px`);
                }
                if (themeConfig.token.borderRadiusLG) {
                    root.style.setProperty('--ant-border-radius-lg', `${themeConfig.token.borderRadiusLG}px`);
                }
            }
            console.log(`🎨 Theme applied: ${theme}`);
        }
    }["ThemeInitializer.useEffect"], [
        theme
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s1(ThemeInitializer, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c1 = ThemeInitializer;
class ThemeProviderErrorBoundary extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Component {
    constructor(props){
        super(props);
        this.state = {
            hasError: false
        };
    }
    static getDerivedStateFromError(error) {
        return {
            hasError: true,
            error
        };
    }
    componentDidCatch(error, errorInfo) {
        console.error('[ThemeProvider Error]', error, errorInfo);
    }
    render() {
        if (this.state.hasError) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    minHeight: '100vh',
                    padding: '20px',
                    textAlign: 'center',
                    fontFamily: 'system-ui, sans-serif'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        style: {
                            color: '#dc2626',
                            marginBottom: '16px'
                        },
                        children: "Theme Provider Error"
                    }, void 0, false, {
                        fileName: "[project]/src/theme/theme-provider.tsx",
                        lineNumber: 154,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        style: {
                            color: '#6b7280',
                            marginBottom: '24px'
                        },
                        children: "An error occurred while loading the theme system."
                    }, void 0, false, {
                        fileName: "[project]/src/theme/theme-provider.tsx",
                        lineNumber: 157,
                        columnNumber: 11
                    }, this),
                    this.state.error && ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("details", {
                        style: {
                            marginBottom: '24px',
                            padding: '16px',
                            backgroundColor: '#f3f4f6',
                            borderRadius: '8px',
                            textAlign: 'left',
                            maxWidth: '600px',
                            width: '100%'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("summary", {
                                style: {
                                    cursor: 'pointer',
                                    fontWeight: 'bold'
                                },
                                children: "Error Details (Development)"
                            }, void 0, false, {
                                fileName: "[project]/src/theme/theme-provider.tsx",
                                lineNumber: 170,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                                style: {
                                    marginTop: '12px',
                                    fontSize: '12px',
                                    overflow: 'auto',
                                    whiteSpace: 'pre-wrap'
                                },
                                children: [
                                    this.state.error.message,
                                    this.state.error.stack && `\n\n${this.state.error.stack}`
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/theme/theme-provider.tsx",
                                lineNumber: 173,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/theme/theme-provider.tsx",
                        lineNumber: 161,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>window.location.reload(),
                        style: {
                            padding: '12px 24px',
                            backgroundColor: '#3b82f6',
                            color: 'white',
                            border: 'none',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            fontSize: '16px'
                        },
                        children: "Reload Page"
                    }, void 0, false, {
                        fileName: "[project]/src/theme/theme-provider.tsx",
                        lineNumber: 184,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/theme/theme-provider.tsx",
                lineNumber: 144,
                columnNumber: 9
            }, this);
        }
        return this.props.children;
    }
}
function ThemeProviderWithErrorBoundary({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ThemeProviderErrorBoundary, {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ThemeProvider, {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/theme/theme-provider.tsx",
            lineNumber: 212,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/theme/theme-provider.tsx",
        lineNumber: 211,
        columnNumber: 5
    }, this);
}
_c2 = ThemeProviderWithErrorBoundary;
function withThemeProvider(Component) {
    const WrappedComponent = (props)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ThemeProvider, {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
                ...props
            }, void 0, false, {
                fileName: "[project]/src/theme/theme-provider.tsx",
                lineNumber: 227,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/theme/theme-provider.tsx",
            lineNumber: 226,
            columnNumber: 5
        }, this);
    WrappedComponent.displayName = `withThemeProvider(${Component.displayName || Component.name})`;
    return WrappedComponent;
}
var _c, _c1, _c2;
__turbopack_refresh__.register(_c, "ThemeProvider");
__turbopack_refresh__.register(_c1, "ThemeInitializer");
__turbopack_refresh__.register(_c2, "ThemeProviderWithErrorBoundary");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/theme/hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Theme Hooks
 * Custom hooks for theme management
 */ __turbopack_esm__({
    "useSystemTheme": (()=>useSystemTheme),
    "useTheme": (()=>useTheme),
    "useThemeDebug": (()=>useThemeDebug),
    "useThemePersistence": (()=>useThemePersistence),
    "useThemePreload": (()=>useThemePreload),
    "useThemeStyles": (()=>useThemeStyles),
    "useThemeSync": (()=>useThemeSync)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/stores/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/provider-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature(), _s5 = __turbopack_refresh__.signature(), _s6 = __turbopack_refresh__.signature();
'use client';
;
;
;
function useTheme() {
    _s();
    const app = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppProvider"])();
    const currentTheme = app.theme || 'light';
    const setTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTheme.useCallback[setTheme]": (theme)=>{
            app.setTheme(theme);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].store(theme);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].apply(theme);
        }
    }["useTheme.useCallback[setTheme]"], [
        app
    ]);
    const toggleTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTheme.useCallback[toggleTheme]": ()=>{
            const newTheme = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].toggle(currentTheme);
            setTheme(newTheme);
        }
    }["useTheme.useCallback[toggleTheme]"], [
        currentTheme,
        setTheme
    ]);
    const resetToSystem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTheme.useCallback[resetToSystem]": ()=>{
            const systemTheme = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].getSystem();
            setTheme(systemTheme);
        }
    }["useTheme.useCallback[resetToSystem]"], [
        setTheme
    ]);
    return {
        // Current theme state
        theme: currentTheme,
        isDark: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].isDark(currentTheme),
        isLight: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].isLight(currentTheme),
        // Theme colors
        colors: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getThemeColors"])(currentTheme),
        // Theme actions
        setTheme,
        toggleTheme,
        resetToSystem,
        // Theme utilities
        utils: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"]
    };
}
_s(useTheme, "nlwiwLR85ILtpK26ByF8cg6X/As=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppProvider"]
    ];
});
function useSystemTheme() {
    _s1();
    const [systemTheme, setSystemTheme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "useSystemTheme.useState": ()=>{
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].getSystem();
        }
    }["useSystemTheme.useState"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useSystemTheme.useEffect": ()=>{
            const cleanup = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createThemeMediaQueryListener"])({
                "useSystemTheme.useEffect.cleanup": (theme)=>{
                    setSystemTheme(theme);
                }
            }["useSystemTheme.useEffect.cleanup"]);
            return cleanup || undefined;
        }
    }["useSystemTheme.useEffect"], []);
    return {
        systemTheme,
        isSystemDark: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].isDark(systemTheme),
        isSystemLight: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].isLight(systemTheme)
    };
}
_s1(useSystemTheme, "ZiKrst83U/C8rZZZTVzIpwTjQQM=");
function useThemeSync(autoSync = false) {
    _s2();
    const { theme, setTheme } = useTheme();
    const { systemTheme } = useSystemTheme();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useThemeSync.useEffect": ()=>{
            if (autoSync && theme !== systemTheme) {
                setTheme(systemTheme);
            }
        }
    }["useThemeSync.useEffect"], [
        autoSync,
        theme,
        systemTheme,
        setTheme
    ]);
    const syncWithSystem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useThemeSync.useCallback[syncWithSystem]": ()=>{
            setTheme(systemTheme);
        }
    }["useThemeSync.useCallback[syncWithSystem]"], [
        systemTheme,
        setTheme
    ]);
    return {
        theme,
        systemTheme,
        isInSync: theme === systemTheme,
        syncWithSystem
    };
}
_s2(useThemeSync, "qmFLfF2n0Rpn6zgLkxw8O5U0emk=", false, function() {
    return [
        useTheme,
        useSystemTheme
    ];
});
function useThemeStyles() {
    _s3();
    const { theme, colors } = useTheme();
    const getStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useThemeStyles.useCallback[getStyle]": (lightStyle, darkStyle)=>{
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].isDark(theme) ? darkStyle : lightStyle;
        }
    }["useThemeStyles.useCallback[getStyle]"], [
        theme
    ]);
    const getColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useThemeStyles.useCallback[getColor]": (colorKey)=>{
            return colors[colorKey];
        }
    }["useThemeStyles.useCallback[getColor]"], [
        colors
    ]);
    const getBackgroundColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useThemeStyles.useCallback[getBackgroundColor]": (backgroundKey)=>{
            return colors.background[backgroundKey];
        }
    }["useThemeStyles.useCallback[getBackgroundColor]"], [
        colors
    ]);
    const getTextColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useThemeStyles.useCallback[getTextColor]": (textKey)=>{
            return colors.text[textKey];
        }
    }["useThemeStyles.useCallback[getTextColor]"], [
        colors
    ]);
    const getBorderColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useThemeStyles.useCallback[getBorderColor]": (borderKey)=>{
            return colors.border[borderKey];
        }
    }["useThemeStyles.useCallback[getBorderColor]"], [
        colors
    ]);
    return {
        theme,
        colors,
        getStyle,
        getColor,
        getBackgroundColor,
        getTextColor,
        getBorderColor,
        // Common styles
        containerStyle: {
            backgroundColor: colors.background.container,
            color: colors.text.primary
        },
        cardStyle: {
            backgroundColor: colors.background.elevated,
            color: colors.text.primary,
            border: `1px solid ${colors.border.primary}`
        },
        headerStyle: {
            backgroundColor: colors.background.container,
            color: colors.text.primary,
            borderBottom: `1px solid ${colors.border.primary}`
        }
    };
}
_s3(useThemeStyles, "XnKbLLRgdM9Rjwu0sI91j+kg4+k=", false, function() {
    return [
        useTheme
    ];
});
function useThemePersistence() {
    _s4();
    const { theme, setTheme } = useTheme();
    const loadStoredTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useThemePersistence.useCallback[loadStoredTheme]": ()=>{
            const stored = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].getStored();
            if (stored && stored !== theme) {
                setTheme(stored);
            }
        }
    }["useThemePersistence.useCallback[loadStoredTheme]"], [
        theme,
        setTheme
    ]);
    const clearStoredTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useThemePersistence.useCallback[clearStoredTheme]": ()=>{
            if ("TURBOPACK compile-time truthy", 1) {
                localStorage.removeItem('apisportsgame_theme');
            }
        }
    }["useThemePersistence.useCallback[clearStoredTheme]"], []);
    const resetToDefault = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useThemePersistence.useCallback[resetToDefault]": ()=>{
            clearStoredTheme();
            setTheme('light');
        }
    }["useThemePersistence.useCallback[resetToDefault]"], [
        clearStoredTheme,
        setTheme
    ]);
    return {
        theme,
        loadStoredTheme,
        clearStoredTheme,
        resetToDefault,
        hasStoredTheme: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].getStored() !== null
    };
}
_s4(useThemePersistence, "N3HVdYt5H7aS4vC98QFFWOf9R+0=", false, function() {
    return [
        useTheme
    ];
});
function useThemeDebug() {
    _s5();
    const { theme, colors } = useTheme();
    const { systemTheme } = useSystemTheme();
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    const debugInfo = {
        currentTheme: theme,
        systemTheme,
        storedTheme: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].getStored(),
        effectiveTheme: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].getEffective(),
        colors,
        cssVariables: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].generateCSSVariables(theme)
    };
    const logThemeInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useThemeDebug.useCallback[logThemeInfo]": ()=>{
            console.group('🎨 Theme Debug Info');
            console.log('Current Theme:', debugInfo.currentTheme);
            console.log('System Theme:', debugInfo.systemTheme);
            console.log('Stored Theme:', debugInfo.storedTheme);
            console.log('Effective Theme:', debugInfo.effectiveTheme);
            console.log('Colors:', debugInfo.colors);
            console.log('CSS Variables:', debugInfo.cssVariables);
            console.groupEnd();
        }
    }["useThemeDebug.useCallback[logThemeInfo]"], [
        debugInfo
    ]);
    return {
        debugInfo,
        logThemeInfo
    };
}
_s5(useThemeDebug, "pimktZ5flp8hvDHnoru5359ixpk=", false, function() {
    return [
        useTheme,
        useSystemTheme
    ];
});
function useThemePreload() {
    _s6();
    const [isPreloaded, setIsPreloaded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useThemePreload.useEffect": ()=>{
            // Preload theme-related assets
            const preloadAssets = {
                "useThemePreload.useEffect.preloadAssets": async ()=>{
                    try {
                        // Apply initial theme
                        const effectiveTheme = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].getEffective();
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].apply(effectiveTheme);
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeUtils"].applyCSSVariables(effectiveTheme);
                        setIsPreloaded(true);
                        console.log('🎨 Theme assets preloaded');
                    } catch (error) {
                        console.error('Failed to preload theme assets:', error);
                        setIsPreloaded(true); // Set to true anyway to prevent blocking
                    }
                }
            }["useThemePreload.useEffect.preloadAssets"];
            preloadAssets();
        }
    }["useThemePreload.useEffect"], []);
    return {
        isPreloaded
    };
}
_s6(useThemePreload, "WCdd7haIUHwJs3KOKxAhgRyyS/s=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/theme/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Theme Index
 * Central export for all theme functionality
 */ // Theme configuration
__turbopack_esm__({
    "THEME_NAME": (()=>THEME_NAME),
    "THEME_VERSION": (()=>THEME_VERSION),
    "setupTheme": (()=>setupTheme)
});
;
;
;
;
const THEME_VERSION = '1.0.0';
const THEME_NAME = 'APISportsGame Theme System';
function setupTheme() {
    console.log(`${THEME_NAME} v${THEME_VERSION} initialized`);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/theme/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/config.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$theme$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/theme-provider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/src/theme/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/providers/app-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * App Provider - Combined provider for all application contexts
 * Integrates Store Provider and Query Provider
 */ __turbopack_esm__({
    "AppProvider": (()=>AppProvider),
    "AppProviderErrorBoundary": (()=>AppProviderErrorBoundary),
    "withAppProvider": (()=>withAppProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/stores/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-provider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/theme/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/store-provider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$theme$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/theme-provider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
'use client';
;
;
;
;
function AppProvider({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryProviderWithErrorBoundary"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["StoreProvider"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$theme$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ThemeProviderWithErrorBoundary"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AppInitializer, {
                    children: children
                }, void 0, false, {
                    fileName: "[project]/src/providers/app-provider.tsx",
                    lineNumber: 29,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/providers/app-provider.tsx",
                lineNumber: 28,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/providers/app-provider.tsx",
            lineNumber: 27,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/providers/app-provider.tsx",
        lineNumber: 26,
        columnNumber: 5
    }, this);
}
_c = AppProvider;
/**
 * App initializer component
 * Handles app-wide initialization logic
 */ function AppInitializer({ children }) {
    _s();
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "AppInitializer.useEffect": ()=>{
            // Initialize app-wide settings
            initializeApp();
        }
    }["AppInitializer.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(AppInitializer, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c1 = AppInitializer;
/**
 * Initialize application
 */ async function initializeApp() {
    try {
        console.log('🚀 APISportsGame CMS initializing...');
        // Initialize theme
        initializeTheme();
        // Initialize error tracking (production only)
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        // Initialize performance monitoring (development only)
        if ("TURBOPACK compile-time truthy", 1) {
            initializePerformanceMonitoring();
        }
        console.log('✅ APISportsGame CMS initialized successfully');
    } catch (error) {
        console.error('❌ Failed to initialize APISportsGame CMS:', error);
    }
}
/**
 * Initialize theme system
 */ function initializeTheme() {
    try {
        // Get saved theme from localStorage
        const savedTheme = localStorage.getItem('apisportsgame_theme');
        if (savedTheme) {
            // Apply saved theme
            document.documentElement.setAttribute('data-theme', savedTheme);
        } else {
            // Detect system theme preference
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const defaultTheme = prefersDark ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', defaultTheme);
        }
        console.log('🎨 Theme system initialized');
    } catch (error) {
        console.warn('⚠️ Failed to initialize theme system:', error);
    }
}
/**
 * Initialize error tracking (production only)
 */ function initializeErrorTracking() {
    try {
        // Global error handler
        window.addEventListener('error', (event)=>{
            console.error('[Global Error]', event.error);
        // TODO: Send to error tracking service
        });
        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event)=>{
            console.error('[Unhandled Promise Rejection]', event.reason);
        // TODO: Send to error tracking service
        });
        console.log('🔍 Error tracking initialized');
    } catch (error) {
        console.warn('⚠️ Failed to initialize error tracking:', error);
    }
}
/**
 * Initialize performance monitoring (development only)
 */ function initializePerformanceMonitoring() {
    try {
        // Performance observer for navigation timing
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list)=>{
                for (const entry of list.getEntries()){
                    if (entry.entryType === 'navigation') {
                        console.log('[Performance] Navigation timing:', entry);
                    }
                }
            });
            observer.observe({
                entryTypes: [
                    'navigation'
                ]
            });
        }
        // Log initial performance metrics
        setTimeout(()=>{
            const navigation = performance.getEntriesByType('navigation')[0];
            if (navigation) {
                console.log('[Performance] Page load metrics:', {
                    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                    loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                    totalTime: navigation.loadEventEnd - navigation.fetchStart
                });
            }
        }, 1000);
        console.log('📊 Performance monitoring initialized');
    } catch (error) {
        console.warn('⚠️ Failed to initialize performance monitoring:', error);
    }
}
class AppProviderErrorBoundary extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Component {
    constructor(props){
        super(props);
        this.state = {
            hasError: false
        };
    }
    static getDerivedStateFromError(error) {
        return {
            hasError: true,
            error
        };
    }
    componentDidCatch(error, errorInfo) {
        console.error('[AppProvider Error]', error, errorInfo);
    }
    render() {
        if (this.state.hasError) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AppErrorFallback, {
                error: this.state.error,
                onRetry: ()=>this.setState({
                        hasError: false,
                        error: undefined
                    })
            }, void 0, false, {
                fileName: "[project]/src/providers/app-provider.tsx",
                lineNumber: 183,
                columnNumber: 9
            }, this);
        }
        return this.props.children;
    }
}
/**
 * App error fallback component
 */ function AppErrorFallback({ error, onRetry }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '100vh',
            padding: '20px',
            textAlign: 'center',
            fontFamily: 'system-ui, sans-serif',
            backgroundColor: '#f9fafb'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            style: {
                backgroundColor: 'white',
                padding: '40px',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                maxWidth: '600px',
                width: '100%'
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                    style: {
                        color: '#dc2626',
                        marginBottom: '16px',
                        fontSize: '24px',
                        fontWeight: 'bold'
                    },
                    children: "APISportsGame CMS Error"
                }, void 0, false, {
                    fileName: "[project]/src/providers/app-provider.tsx",
                    lineNumber: 224,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    style: {
                        color: '#6b7280',
                        marginBottom: '24px',
                        lineHeight: '1.6'
                    },
                    children: "An unexpected error occurred while loading the application. Please try refreshing the page or contact support if the problem persists."
                }, void 0, false, {
                    fileName: "[project]/src/providers/app-provider.tsx",
                    lineNumber: 232,
                    columnNumber: 9
                }, this),
                error && ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("details", {
                    style: {
                        marginBottom: '24px',
                        padding: '16px',
                        backgroundColor: '#f3f4f6',
                        borderRadius: '8px',
                        textAlign: 'left'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("summary", {
                            style: {
                                cursor: 'pointer',
                                fontWeight: 'bold',
                                marginBottom: '12px'
                            },
                            children: "Error Details (Development)"
                        }, void 0, false, {
                            fileName: "[project]/src/providers/app-provider.tsx",
                            lineNumber: 249,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                            style: {
                                fontSize: '12px',
                                overflow: 'auto',
                                whiteSpace: 'pre-wrap',
                                margin: 0
                            },
                            children: [
                                error.message,
                                error.stack && `\n\n${error.stack}`
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/providers/app-provider.tsx",
                            lineNumber: 256,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/providers/app-provider.tsx",
                    lineNumber: 242,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        display: 'flex',
                        gap: '12px',
                        justifyContent: 'center'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: onRetry,
                            style: {
                                padding: '12px 24px',
                                backgroundColor: '#3b82f6',
                                color: 'white',
                                border: 'none',
                                borderRadius: '6px',
                                cursor: 'pointer',
                                fontSize: '16px',
                                fontWeight: '500'
                            },
                            children: "Try Again"
                        }, void 0, false, {
                            fileName: "[project]/src/providers/app-provider.tsx",
                            lineNumber: 269,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: ()=>window.location.reload(),
                            style: {
                                padding: '12px 24px',
                                backgroundColor: '#6b7280',
                                color: 'white',
                                border: 'none',
                                borderRadius: '6px',
                                cursor: 'pointer',
                                fontSize: '16px',
                                fontWeight: '500'
                            },
                            children: "Refresh Page"
                        }, void 0, false, {
                            fileName: "[project]/src/providers/app-provider.tsx",
                            lineNumber: 284,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/providers/app-provider.tsx",
                    lineNumber: 268,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/providers/app-provider.tsx",
            lineNumber: 216,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/providers/app-provider.tsx",
        lineNumber: 205,
        columnNumber: 5
    }, this);
}
_c2 = AppErrorFallback;
function withAppProvider(Component) {
    const WrappedComponent = (props)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AppProvider, {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
                ...props
            }, void 0, false, {
                fileName: "[project]/src/providers/app-provider.tsx",
                lineNumber: 313,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/providers/app-provider.tsx",
            lineNumber: 312,
            columnNumber: 5
        }, this);
    WrappedComponent.displayName = `withAppProvider(${Component.displayName || Component.name})`;
    return WrappedComponent;
}
var _c, _c1, _c2;
__turbopack_refresh__.register(_c, "AppProvider");
__turbopack_refresh__.register(_c1, "AppInitializer");
__turbopack_refresh__.register(_c2, "AppErrorFallback");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=src_fb9ea2._.js.map