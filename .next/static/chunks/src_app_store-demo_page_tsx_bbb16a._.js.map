{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/store-demo/page.tsx"], "sourcesContent": ["/**\n * Store Demo Page - Test page for Store Provider functionality\n * Demonstrates usage of stores through providers\n */\n\n'use client';\n\nimport React from 'react';\nimport { useAuthProvider, useAppProvider, useStoreAvailability, useStoreDebug } from '@/stores';\n\nexport default function StoreDemoPage() {\n  const auth = useAuthProvider();\n  const app = useAppProvider();\n  const { isAvailable } = useStoreAvailability();\n  const debug = useStoreDebug();\n\n  const handleLogin = async () => {\n    try {\n      await auth.login({\n        username: 'admin',\n        password: 'admin123456'\n      });\n    } catch (error) {\n      console.error('Login failed:', error);\n    }\n  };\n\n  const handleLogout = async () => {\n    try {\n      await auth.logout();\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n\n  const handleThemeToggle = () => {\n    app.toggleTheme();\n  };\n\n  const handleSidebarToggle = () => {\n    app.toggleSidebar();\n  };\n\n  const handleShowNotification = () => {\n    app.showNotification({\n      type: 'success',\n      message: 'Test Notification',\n      description: 'This is a test notification from the store provider demo.'\n    });\n  };\n\n  const handleLanguageChange = () => {\n    const newLanguage = app.language === 'en' ? 'vi' : 'en';\n    app.setLanguage(newLanguage);\n  };\n\n  return (\n    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>\n      <h1>Store Provider Demo</h1>\n      \n      {/* Store Availability */}\n      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>Store Availability</h2>\n        <p>Store Available: <strong>{isAvailable ? 'Yes' : 'No'}</strong></p>\n        <p>Auth Initialized: <strong>{auth.isInitialized ? 'Yes' : 'No'}</strong></p>\n        <p>App Initialized: <strong>{app.isInitialized ? 'Yes' : 'No'}</strong></p>\n      </div>\n\n      {/* Authentication State */}\n      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>Authentication State</h2>\n        <p>Authenticated: <strong>{auth.isAuthenticated ? 'Yes' : 'No'}</strong></p>\n        <p>Loading: <strong>{auth.isLoading ? 'Yes' : 'No'}</strong></p>\n        <p>User: <strong>{auth.user ? `${auth.user.username} (${auth.user.role})` : 'None'}</strong></p>\n        <p>Error: <strong>{auth.error || 'None'}</strong></p>\n        \n        <div style={{ marginTop: '10px' }}>\n          <button \n            onClick={handleLogin} \n            disabled={auth.isLoading || auth.isAuthenticated}\n            style={{ marginRight: '10px', padding: '5px 10px' }}\n          >\n            Login (Demo)\n          </button>\n          <button \n            onClick={handleLogout} \n            disabled={auth.isLoading || !auth.isAuthenticated}\n            style={{ marginRight: '10px', padding: '5px 10px' }}\n          >\n            Logout\n          </button>\n          <button \n            onClick={auth.clearError} \n            disabled={!auth.error}\n            style={{ padding: '5px 10px' }}\n          >\n            Clear Error\n          </button>\n        </div>\n      </div>\n\n      {/* Application State */}\n      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>Application State</h2>\n        <p>Theme: <strong>{app.theme}</strong></p>\n        <p>Language: <strong>{app.language}</strong></p>\n        <p>Sidebar Collapsed: <strong>{app.sidebarCollapsed ? 'Yes' : 'No'}</strong></p>\n        <p>Loading: <strong>{app.isLoading ? 'Yes' : 'No'}</strong></p>\n        <p>Notification: <strong>{app.notification ? `${app.notification.type}: ${app.notification.message}` : 'None'}</strong></p>\n        \n        <div style={{ marginTop: '10px' }}>\n          <button \n            onClick={handleThemeToggle}\n            style={{ marginRight: '10px', padding: '5px 10px' }}\n          >\n            Toggle Theme\n          </button>\n          <button \n            onClick={handleSidebarToggle}\n            style={{ marginRight: '10px', padding: '5px 10px' }}\n          >\n            Toggle Sidebar\n          </button>\n          <button \n            onClick={handleLanguageChange}\n            style={{ marginRight: '10px', padding: '5px 10px' }}\n          >\n            Change Language\n          </button>\n          <button \n            onClick={handleShowNotification}\n            style={{ marginRight: '10px', padding: '5px 10px' }}\n          >\n            Show Notification\n          </button>\n          <button \n            onClick={app.hideNotification}\n            disabled={!app.notification}\n            style={{ padding: '5px 10px' }}\n          >\n            Hide Notification\n          </button>\n        </div>\n      </div>\n\n      {/* Debug Information */}\n      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>Debug Information</h2>\n        <details>\n          <summary>Auth State (Click to expand)</summary>\n          <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>\n            {JSON.stringify(debug.authState, null, 2)}\n          </pre>\n        </details>\n        \n        <details style={{ marginTop: '10px' }}>\n          <summary>App State (Click to expand)</summary>\n          <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>\n            {JSON.stringify(debug.appState, null, 2)}\n          </pre>\n        </details>\n        \n        <div style={{ marginTop: '10px' }}>\n          <button \n            onClick={debug.actions.resetAuth}\n            style={{ marginRight: '10px', padding: '5px 10px' }}\n          >\n            Reset Auth Store\n          </button>\n          <button \n            onClick={debug.actions.resetApp}\n            style={{ padding: '5px 10px' }}\n          >\n            Reset App Store\n          </button>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div style={{ marginTop: '30px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>Navigation</h2>\n        <p>This demo page shows the Store Provider functionality.</p>\n        <a href=\"/\" style={{ color: 'blue', textDecoration: 'underline' }}>\n          ← Back to Home\n        </a>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAKD;AAAA;;;AAHA;;AAKe,SAAS;;IACtB,MAAM,OAAO,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAC3B,MAAM,MAAM,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD;IACzB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,uBAAoB,AAAD;IAC3C,MAAM,QAAQ,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IAE1B,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,KAAK,KAAK,CAAC;gBACf,UAAU;gBACV,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,KAAK,MAAM;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,WAAW;IACjB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,aAAa;IACnB;IAEA,MAAM,yBAAyB;QAC7B,IAAI,gBAAgB,CAAC;YACnB,MAAM;YACN,SAAS;YACT,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB;QAC3B,MAAM,cAAc,IAAI,QAAQ,KAAK,OAAO,OAAO;QACnD,IAAI,WAAW,CAAC;IAClB;IAEA,qBACE,6LAAC;QAAI,OAAO;YAAE,SAAS;YAAQ,YAAY;QAAoB;;0BAC7D,6LAAC;0BAAG;;;;;;0BAGJ,6LAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCACjG,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;;4BAAE;0CAAiB,6LAAC;0CAAQ,cAAc,QAAQ;;;;;;;;;;;;kCACnD,6LAAC;;4BAAE;0CAAkB,6LAAC;0CAAQ,KAAK,aAAa,GAAG,QAAQ;;;;;;;;;;;;kCAC3D,6LAAC;;4BAAE;0CAAiB,6LAAC;0CAAQ,IAAI,aAAa,GAAG,QAAQ;;;;;;;;;;;;;;;;;;0BAI3D,6LAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCACjG,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;;4BAAE;0CAAe,6LAAC;0CAAQ,KAAK,eAAe,GAAG,QAAQ;;;;;;;;;;;;kCAC1D,6LAAC;;4BAAE;0CAAS,6LAAC;0CAAQ,KAAK,SAAS,GAAG,QAAQ;;;;;;;;;;;;kCAC9C,6LAAC;;4BAAE;0CAAM,6LAAC;0CAAQ,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG;;;;;;;;;;;;kCAC5E,6LAAC;;4BAAE;0CAAO,6LAAC;0CAAQ,KAAK,KAAK,IAAI;;;;;;;;;;;;kCAEjC,6LAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAO;;0CAC9B,6LAAC;gCACC,SAAS;gCACT,UAAU,KAAK,SAAS,IAAI,KAAK,eAAe;gCAChD,OAAO;oCAAE,aAAa;oCAAQ,SAAS;gCAAW;0CACnD;;;;;;0CAGD,6LAAC;gCACC,SAAS;gCACT,UAAU,KAAK,SAAS,IAAI,CAAC,KAAK,eAAe;gCACjD,OAAO;oCAAE,aAAa;oCAAQ,SAAS;gCAAW;0CACnD;;;;;;0CAGD,6LAAC;gCACC,SAAS,KAAK,UAAU;gCACxB,UAAU,CAAC,KAAK,KAAK;gCACrB,OAAO;oCAAE,SAAS;gCAAW;0CAC9B;;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCACjG,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;;4BAAE;0CAAO,6LAAC;0CAAQ,IAAI,KAAK;;;;;;;;;;;;kCAC5B,6LAAC;;4BAAE;0CAAU,6LAAC;0CAAQ,IAAI,QAAQ;;;;;;;;;;;;kCAClC,6LAAC;;4BAAE;0CAAmB,6LAAC;0CAAQ,IAAI,gBAAgB,GAAG,QAAQ;;;;;;;;;;;;kCAC9D,6LAAC;;4BAAE;0CAAS,6LAAC;0CAAQ,IAAI,SAAS,GAAG,QAAQ;;;;;;;;;;;;kCAC7C,6LAAC;;4BAAE;0CAAc,6LAAC;0CAAQ,IAAI,YAAY,GAAG,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE,GAAG;;;;;;;;;;;;kCAEvG,6LAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAO;;0CAC9B,6LAAC;gCACC,SAAS;gCACT,OAAO;oCAAE,aAAa;oCAAQ,SAAS;gCAAW;0CACnD;;;;;;0CAGD,6LAAC;gCACC,SAAS;gCACT,OAAO;oCAAE,aAAa;oCAAQ,SAAS;gCAAW;0CACnD;;;;;;0CAGD,6LAAC;gCACC,SAAS;gCACT,OAAO;oCAAE,aAAa;oCAAQ,SAAS;gCAAW;0CACnD;;;;;;0CAGD,6LAAC;gCACC,SAAS;gCACT,OAAO;oCAAE,aAAa;oCAAQ,SAAS;gCAAW;0CACnD;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAI,gBAAgB;gCAC7B,UAAU,CAAC,IAAI,YAAY;gCAC3B,OAAO;oCAAE,SAAS;gCAAW;0CAC9B;;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCACjG,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;;0CACC,6LAAC;0CAAQ;;;;;;0CACT,6LAAC;gCAAI,OAAO;oCAAE,YAAY;oCAAW,SAAS;oCAAQ,UAAU;gCAAO;0CACpE,KAAK,SAAS,CAAC,MAAM,SAAS,EAAE,MAAM;;;;;;;;;;;;kCAI3C,6LAAC;wBAAQ,OAAO;4BAAE,WAAW;wBAAO;;0CAClC,6LAAC;0CAAQ;;;;;;0CACT,6LAAC;gCAAI,OAAO;oCAAE,YAAY;oCAAW,SAAS;oCAAQ,UAAU;gCAAO;0CACpE,KAAK,SAAS,CAAC,MAAM,QAAQ,EAAE,MAAM;;;;;;;;;;;;kCAI1C,6LAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAO;;0CAC9B,6LAAC;gCACC,SAAS,MAAM,OAAO,CAAC,SAAS;gCAChC,OAAO;oCAAE,aAAa;oCAAQ,SAAS;gCAAW;0CACnD;;;;;;0CAGD,6LAAC;gCACC,SAAS,MAAM,OAAO,CAAC,QAAQ;gCAC/B,OAAO;oCAAE,SAAS;gCAAW;0CAC9B;;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,OAAO;oBAAE,WAAW;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCAC9F,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;kCAAE;;;;;;kCACH,6LAAC;wBAAE,MAAK;wBAAI,OAAO;4BAAE,OAAO;4BAAQ,gBAAgB;wBAAY;kCAAG;;;;;;;;;;;;;;;;;;AAM3E;GAlLwB;;QACT,qIAAA,CAAA,kBAAe;QAChB,qIAAA,CAAA,iBAAc;QACF,qIAAA,CAAA,uBAAoB;QAC9B,qIAAA,CAAA,gBAAa;;;KAJL"}}, {"offset": {"line": 621, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}