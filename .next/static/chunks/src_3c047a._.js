(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_3c047a._.js", {

"[project]/src/components/layout/app-footer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * App Footer Component
 * Footer for the APISportsGame CMS
 */ __turbopack_esm__({
    "AppFooter": (()=>AppFooter),
    "SimpleFooter": (()=>SimpleFooter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/theme/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/layout/index.js [app-client] (ecmascript) <export default as Layout>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$typography$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/typography/index.js [app-client] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ApiOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ApiOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/ApiOutlined.js [app-client] (ecmascript) <export default as ApiOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$GithubOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__GithubOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/GithubOutlined.js [app-client] (ecmascript) <export default as GithubOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$TwitterOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TwitterOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/TwitterOutlined.js [app-client] (ecmascript) <export default as TwitterOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LinkedinOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LinkedinOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/LinkedinOutlined.js [app-client] (ecmascript) <export default as LinkedinOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$GlobalOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__GlobalOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/GlobalOutlined.js [app-client] (ecmascript) <export default as GlobalOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$divider$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/divider/index.js [app-client] (ecmascript) <export default as Divider>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$HeartFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__HeartFilled$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/HeartFilled.js [app-client] (ecmascript) <export default as HeartFilled>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/space/index.js [app-client] (ecmascript) <locals> <export default as Space>");
;
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature();
'use client';
;
;
;
const { Footer } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__["Layout"];
const { Text, Link } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$typography$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"];
function AppFooter({ className, style, compact = false }) {
    _s();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    const footerStyle = {
        backgroundColor: themeStyles.getBackgroundColor('container'),
        borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,
        padding: compact ? '12px 24px' : '24px',
        textAlign: 'center',
        ...style
    };
    const currentYear = new Date().getFullYear();
    if (compact) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Footer, {
            className: className,
            style: footerStyle,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                style: {
                    fontSize: '12px',
                    color: themeStyles.getTextColor('tertiary')
                },
                children: [
                    "© ",
                    currentYear,
                    " APISportsGame CMS. Built with",
                    ' ',
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$HeartFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__HeartFilled$3e$__["HeartFilled"], {
                        style: {
                            color: themeStyles.getColor('error')
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/app-footer.tsx",
                        lineNumber: 58,
                        columnNumber: 11
                    }, this),
                    " by Augment Code"
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/app-footer.tsx",
                lineNumber: 51,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/app-footer.tsx",
            lineNumber: 50,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Footer, {
        className: className,
        style: footerStyle,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            style: {
                maxWidth: '1200px',
                margin: '0 auto'
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        display: 'grid',
                        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                        gap: '32px',
                        marginBottom: '24px',
                        textAlign: 'left'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '8px',
                                        marginBottom: '12px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            style: {
                                                width: '24px',
                                                height: '24px',
                                                backgroundColor: themeStyles.getColor('primary'),
                                                borderRadius: '4px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                color: 'white',
                                                fontSize: '14px',
                                                fontWeight: 'bold'
                                            },
                                            children: "⚽"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 87,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                            style: {
                                                fontSize: '16px',
                                                fontWeight: 'bold',
                                                color: themeStyles.getTextColor('primary')
                                            },
                                            children: "APISportsGame"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 103,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 79,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                    style: {
                                        fontSize: '14px',
                                        color: themeStyles.getTextColor('secondary'),
                                        lineHeight: 1.6
                                    },
                                    children: "A comprehensive CMS for managing football data, broadcast links, and user systems. Built with modern technologies for optimal performance."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 113,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/app-footer.tsx",
                            lineNumber: 78,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                    style: {
                                        fontSize: '14px',
                                        fontWeight: 'bold',
                                        color: themeStyles.getTextColor('primary'),
                                        marginBottom: '12px',
                                        display: 'block'
                                    },
                                    children: "Quick Links"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 127,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        display: 'flex',
                                        flexDirection: 'column',
                                        gap: '8px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "/",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: "Dashboard"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 139,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "/football/fixtures",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: "Fixtures"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 148,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "/broadcast/links",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: "Broadcast Links"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 157,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "/system/health",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: "System Health"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 166,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 138,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/app-footer.tsx",
                            lineNumber: 126,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                    style: {
                                        fontSize: '14px',
                                        fontWeight: 'bold',
                                        color: themeStyles.getTextColor('primary'),
                                        marginBottom: '12px',
                                        display: 'block'
                                    },
                                    children: "Resources"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 180,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        display: 'flex',
                                        flexDirection: 'column',
                                        gap: '8px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "/system/api-docs",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ApiOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ApiOutlined$3e$__["ApiOutlined"], {
                                                    style: {
                                                        marginRight: '4px'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                                    lineNumber: 199,
                                                    columnNumber: 17
                                                }, this),
                                                "API Documentation"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 192,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "/components-demo",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: "Component Library"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 202,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "/theme-demo",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: "Theme System"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 211,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "https://github.com/apisportsgame",
                                            target: "_blank",
                                            rel: "noopener noreferrer",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$GithubOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__GithubOutlined$3e$__["GithubOutlined"], {
                                                    style: {
                                                        marginRight: '4px'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                                    lineNumber: 229,
                                                    columnNumber: 17
                                                }, this),
                                                "GitHub Repository"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 220,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 191,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/app-footer.tsx",
                            lineNumber: 179,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                    style: {
                                        fontSize: '14px',
                                        fontWeight: 'bold',
                                        color: themeStyles.getTextColor('primary'),
                                        marginBottom: '12px',
                                        display: 'block'
                                    },
                                    children: "Connect"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 237,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        display: 'flex',
                                        flexDirection: 'column',
                                        gap: '8px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "https://github.com/apisportsgame",
                                            target: "_blank",
                                            rel: "noopener noreferrer",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$GithubOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__GithubOutlined$3e$__["GithubOutlined"], {
                                                    style: {
                                                        marginRight: '4px'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                                    lineNumber: 258,
                                                    columnNumber: 17
                                                }, this),
                                                "GitHub"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 249,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "https://twitter.com/apisportsgame",
                                            target: "_blank",
                                            rel: "noopener noreferrer",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$TwitterOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TwitterOutlined$3e$__["TwitterOutlined"], {
                                                    style: {
                                                        marginRight: '4px'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                                    lineNumber: 270,
                                                    columnNumber: 17
                                                }, this),
                                                "Twitter"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 261,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "https://linkedin.com/company/apisportsgame",
                                            target: "_blank",
                                            rel: "noopener noreferrer",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LinkedinOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LinkedinOutlined$3e$__["LinkedinOutlined"], {
                                                    style: {
                                                        marginRight: '4px'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                                    lineNumber: 282,
                                                    columnNumber: 17
                                                }, this),
                                                "LinkedIn"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 273,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "https://apisportsgame.com",
                                            target: "_blank",
                                            rel: "noopener noreferrer",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$GlobalOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__GlobalOutlined$3e$__["GlobalOutlined"], {
                                                    style: {
                                                        marginRight: '4px'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                                    lineNumber: 294,
                                                    columnNumber: 17
                                                }, this),
                                                "Website"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 285,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 248,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/app-footer.tsx",
                            lineNumber: 236,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/layout/app-footer.tsx",
                    lineNumber: 68,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$divider$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__["Divider"], {
                    style: {
                        margin: '24px 0 16px 0'
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/app-footer.tsx",
                    lineNumber: 301,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        flexWrap: 'wrap',
                        gap: '16px'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                            style: {
                                fontSize: '13px',
                                color: themeStyles.getTextColor('tertiary')
                            },
                            children: [
                                "© ",
                                currentYear,
                                " APISportsGame CMS. All rights reserved. Built with",
                                ' ',
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$HeartFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__HeartFilled$3e$__["HeartFilled"], {
                                    style: {
                                        color: themeStyles.getColor('error')
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 320,
                                    columnNumber: 13
                                }, this),
                                " by Augment Code"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/app-footer.tsx",
                            lineNumber: 313,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__["Space"], {
                            size: "middle",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                    href: "/privacy",
                                    style: {
                                        fontSize: '13px',
                                        color: themeStyles.getTextColor('tertiary')
                                    },
                                    children: "Privacy Policy"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 324,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                    href: "/terms",
                                    style: {
                                        fontSize: '13px',
                                        color: themeStyles.getTextColor('tertiary')
                                    },
                                    children: "Terms of Service"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 333,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                    style: {
                                        fontSize: '13px',
                                        color: themeStyles.getTextColor('tertiary')
                                    },
                                    children: "v1.0.0"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 342,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/app-footer.tsx",
                            lineNumber: 323,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/layout/app-footer.tsx",
                    lineNumber: 304,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/app-footer.tsx",
            lineNumber: 66,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/app-footer.tsx",
        lineNumber: 65,
        columnNumber: 5
    }, this);
}
_s(AppFooter, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c = AppFooter;
function SimpleFooter({ className, style }) {
    _s1();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    const currentYear = new Date().getFullYear();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        style: {
            padding: '16px 24px',
            textAlign: 'center',
            backgroundColor: themeStyles.getBackgroundColor('container'),
            borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,
            ...style
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
            style: {
                fontSize: '12px',
                color: themeStyles.getTextColor('tertiary')
            },
            children: [
                "© ",
                currentYear,
                " APISportsGame CMS. Built with",
                ' ',
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$HeartFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__HeartFilled$3e$__["HeartFilled"], {
                    style: {
                        color: themeStyles.getColor('error')
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/app-footer.tsx",
                    lineNumber: 387,
                    columnNumber: 9
                }, this),
                " by Augment Code"
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/app-footer.tsx",
            lineNumber: 380,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/app-footer.tsx",
        lineNumber: 370,
        columnNumber: 5
    }, this);
}
_s1(SimpleFooter, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c1 = SimpleFooter;
var _c, _c1;
__turbopack_refresh__.register(_c, "AppFooter");
__turbopack_refresh__.register(_c1, "SimpleFooter");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/auth-layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Authentication Layout Component
 * Specialized layout for authentication pages
 */ __turbopack_esm__({
    "AuthCard": (()=>AuthCard),
    "AuthDivider": (()=>AuthDivider),
    "AuthForm": (()=>AuthForm),
    "AuthLayout": (()=>AuthLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/theme/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$footer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-footer.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/layout/index.js [app-client] (ecmascript) <export default as Layout>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$typography$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/typography/index.js [app-client] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$card$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/card/index.js [app-client] (ecmascript) <export default as Card>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/space/index.js [app-client] (ecmascript) <locals> <export default as Space>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$divider$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/divider/index.js [app-client] (ecmascript) <export default as Divider>");
;
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature();
'use client';
;
;
;
const { Content } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__["Layout"];
const { Title, Text, Link } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$typography$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"];
function AuthLayout({ children, title = 'APISportsGame CMS', subtitle = 'System Administration Portal', showFooter = true, className, style }) {
    _s();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    const layoutStyle = {
        minHeight: '100vh',
        backgroundColor: themeStyles.getBackgroundColor('layout'),
        display: 'flex',
        flexDirection: 'column',
        ...style
    };
    const contentStyle = {
        flex: 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '24px',
        backgroundImage: `linear-gradient(135deg, ${themeStyles.getColor('primary')}10 0%, ${themeStyles.getColor('primary')}05 100%)`
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__["Layout"], {
        className: className,
        style: layoutStyle,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Content, {
                style: contentStyle,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        width: '100%',
                        maxWidth: '400px',
                        margin: '0 auto'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                textAlign: 'center',
                                marginBottom: '32px'
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        display: 'flex',
                                        justifyContent: 'center',
                                        marginBottom: '16px'
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            width: '64px',
                                            height: '64px',
                                            backgroundColor: themeStyles.getColor('primary'),
                                            borderRadius: '12px',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            color: 'white',
                                            fontSize: '32px',
                                            fontWeight: 'bold',
                                            boxShadow: `0 8px 24px ${themeStyles.getColor('primary')}30`
                                        },
                                        children: "⚽"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/auth-layout.tsx",
                                        lineNumber: 83,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/auth-layout.tsx",
                                    lineNumber: 76,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Title, {
                                    level: 2,
                                    style: {
                                        color: themeStyles.getTextColor('primary'),
                                        marginBottom: '8px',
                                        fontSize: '28px',
                                        fontWeight: 'bold'
                                    },
                                    children: title
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/auth-layout.tsx",
                                    lineNumber: 103,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                    style: {
                                        color: themeStyles.getTextColor('secondary'),
                                        fontSize: '16px'
                                    },
                                    children: subtitle
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/auth-layout.tsx",
                                    lineNumber: 116,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/auth-layout.tsx",
                            lineNumber: 69,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$card$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
                            style: {
                                backgroundColor: themeStyles.getBackgroundColor('container'),
                                border: `1px solid ${themeStyles.getBorderColor('primary')}`,
                                borderRadius: '12px',
                                boxShadow: `0 8px 32px ${themeStyles.getColor('primary')}08`
                            },
                            bodyStyle: {
                                padding: '32px'
                            },
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/auth-layout.tsx",
                            lineNumber: 127,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                textAlign: 'center',
                                marginTop: '24px'
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__["Space"], {
                                split: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$divider$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__["Divider"], {
                                    type: "vertical"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/auth-layout.tsx",
                                    lineNumber: 148,
                                    columnNumber: 27
                                }, void 0),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                        href: "/help",
                                        style: {
                                            color: themeStyles.getTextColor('secondary'),
                                            fontSize: '14px'
                                        },
                                        children: "Help & Support"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/auth-layout.tsx",
                                        lineNumber: 149,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                        href: "/privacy",
                                        style: {
                                            color: themeStyles.getTextColor('secondary'),
                                            fontSize: '14px'
                                        },
                                        children: "Privacy Policy"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/auth-layout.tsx",
                                        lineNumber: 158,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                        href: "/terms",
                                        style: {
                                            color: themeStyles.getTextColor('secondary'),
                                            fontSize: '14px'
                                        },
                                        children: "Terms of Service"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/auth-layout.tsx",
                                        lineNumber: 167,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/layout/auth-layout.tsx",
                                lineNumber: 148,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/auth-layout.tsx",
                            lineNumber: 142,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/layout/auth-layout.tsx",
                    lineNumber: 61,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/layout/auth-layout.tsx",
                lineNumber: 60,
                columnNumber: 7
            }, this),
            showFooter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$footer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SimpleFooter"], {
                style: {
                    backgroundColor: themeStyles.getBackgroundColor('container'),
                    borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`
                }
            }, void 0, false, {
                fileName: "[project]/src/components/layout/auth-layout.tsx",
                lineNumber: 183,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/auth-layout.tsx",
        lineNumber: 59,
        columnNumber: 5
    }, this);
}
_s(AuthLayout, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c = AuthLayout;
function AuthCard({ children, title, description, className, style }) {
    _s1();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        style: style,
        children: [
            (title || description) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginBottom: '24px',
                    textAlign: 'center'
                },
                children: [
                    title && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Title, {
                        level: 3,
                        style: {
                            color: themeStyles.getTextColor('primary'),
                            marginBottom: description ? '8px' : '0',
                            fontSize: '24px',
                            fontWeight: 'bold'
                        },
                        children: title
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/auth-layout.tsx",
                        lineNumber: 219,
                        columnNumber: 13
                    }, this),
                    description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                        style: {
                            color: themeStyles.getTextColor('secondary'),
                            fontSize: '14px',
                            lineHeight: 1.5
                        },
                        children: description
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/auth-layout.tsx",
                        lineNumber: 232,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/auth-layout.tsx",
                lineNumber: 217,
                columnNumber: 9
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/auth-layout.tsx",
        lineNumber: 215,
        columnNumber: 5
    }, this);
}
_s1(AuthCard, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c1 = AuthCard;
function AuthForm({ children, onSubmit, className, style }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
        className: className,
        style: {
            width: '100%',
            ...style
        },
        onSubmit: onSubmit,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__["Space"], {
            direction: "vertical",
            size: "large",
            style: {
                width: '100%'
            },
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/layout/auth-layout.tsx",
            lineNumber: 274,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/auth-layout.tsx",
        lineNumber: 266,
        columnNumber: 5
    }, this);
}
_c2 = AuthForm;
function AuthDivider({ text = 'OR', className, style }) {
    _s2();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        style: {
            position: 'relative',
            textAlign: 'center',
            margin: '24px 0',
            ...style
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$divider$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__["Divider"], {
                style: {
                    borderColor: themeStyles.getBorderColor('primary')
                }
            }, void 0, false, {
                fileName: "[project]/src/components/layout/auth-layout.tsx",
                lineNumber: 311,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                style: {
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    backgroundColor: themeStyles.getBackgroundColor('container'),
                    padding: '0 16px',
                    color: themeStyles.getTextColor('tertiary'),
                    fontSize: '12px',
                    fontWeight: 'bold'
                },
                children: text
            }, void 0, false, {
                fileName: "[project]/src/components/layout/auth-layout.tsx",
                lineNumber: 316,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/auth-layout.tsx",
        lineNumber: 302,
        columnNumber: 5
    }, this);
}
_s2(AuthDivider, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c3 = AuthDivider;
var _c, _c1, _c2, _c3;
__turbopack_refresh__.register(_c, "AuthLayout");
__turbopack_refresh__.register(_c1, "AuthCard");
__turbopack_refresh__.register(_c2, "AuthForm");
__turbopack_refresh__.register(_c3, "AuthDivider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/query-error-handler.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Query Error Handler
 * Centralized error handling for TanStack Query
 */ __turbopack_esm__({
    "ErrorType": (()=>ErrorType),
    "createApiError": (()=>createApiError),
    "createGlobalErrorHandler": (()=>createGlobalErrorHandler),
    "errorUtils": (()=>errorUtils),
    "getErrorType": (()=>getErrorType),
    "handleApiError": (()=>handleApiError),
    "isApiError": (()=>isApiError),
    "parseErrorResponse": (()=>parseErrorResponse),
    "setupQueryErrorHandling": (()=>setupQueryErrorHandling)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var ErrorType = /*#__PURE__*/ function(ErrorType) {
    ErrorType["NETWORK"] = "NETWORK";
    ErrorType["AUTHENTICATION"] = "AUTHENTICATION";
    ErrorType["AUTHORIZATION"] = "AUTHORIZATION";
    ErrorType["VALIDATION"] = "VALIDATION";
    ErrorType["SERVER"] = "SERVER";
    ErrorType["UNKNOWN"] = "UNKNOWN";
    return ErrorType;
}({});
function getErrorType(status) {
    if (status === 401) return "AUTHENTICATION";
    if (status === 403) return "AUTHORIZATION";
    if (status >= 400 && status < 500) return "VALIDATION";
    if (status >= 500) return "SERVER";
    if (status === 0) return "NETWORK";
    return "UNKNOWN";
}
function createApiError(status, statusText, message, details) {
    return {
        status,
        statusText,
        message,
        details,
        timestamp: new Date().toISOString()
    };
}
async function parseErrorResponse(response) {
    let message = response.statusText || 'An error occurred';
    let details = null;
    try {
        const errorData = await response.json();
        message = errorData.message || errorData.error || message;
        details = errorData.details || errorData;
    } catch  {
    // If response is not JSON, use status text
    }
    return createApiError(response.status, response.statusText, message, details);
}
function createGlobalErrorHandler() {
    return (error)=>{
        console.error('[Query Error]', error);
        // Handle different types of errors
        if (error instanceof Error) {
            // Network errors, parsing errors, etc.
            console.error('Error details:', {
                name: error.name,
                message: error.message,
                stack: error.stack
            });
        }
        // Handle API errors
        if (isApiError(error)) {
            handleApiError(error);
        }
    };
}
function isApiError(error) {
    return typeof error === 'object' && error !== null && 'status' in error && 'message' in error;
}
function handleApiError(error) {
    const errorType = getErrorType(error.status);
    switch(errorType){
        case "AUTHENTICATION":
            handleAuthenticationError(error);
            break;
        case "AUTHORIZATION":
            handleAuthorizationError(error);
            break;
        case "VALIDATION":
            handleValidationError(error);
            break;
        case "SERVER":
            handleServerError(error);
            break;
        case "NETWORK":
            handleNetworkError(error);
            break;
        default:
            handleUnknownError(error);
    }
}
/**
 * Handle authentication errors (401)
 */ function handleAuthenticationError(error) {
    console.warn('[Auth Error]', error.message);
    // In development mode, authentication is disabled
    if ("TURBOPACK compile-time truthy", 1) {
        console.log('[Dev Mode] Authentication error ignored');
        return;
    }
// In production, redirect to login or refresh token
// This will be implemented when auth system is ready
}
/**
 * Handle authorization errors (403)
 */ function handleAuthorizationError(error) {
    console.warn('[Authorization Error]', error.message);
// Show user-friendly message about insufficient permissions
// This will be integrated with notification system
}
/**
 * Handle validation errors (400-499)
 */ function handleValidationError(error) {
    console.warn('[Validation Error]', error.message);
// These are usually handled by individual components
// Global handler just logs for debugging
}
/**
 * Handle server errors (500+)
 */ function handleServerError(error) {
    console.error('[Server Error]', error.message);
// Show generic error message to user
// Log detailed error for debugging
}
/**
 * Handle network errors
 */ function handleNetworkError(error) {
    console.error('[Network Error]', error.message);
// Show network connectivity message
// Suggest retry or check connection
}
/**
 * Handle unknown errors
 */ function handleUnknownError(error) {
    console.error('[Unknown Error]', error);
// Show generic error message
// Log for investigation
}
function setupQueryErrorHandling(queryClient) {
    // Set up global error handler
    queryClient.setDefaultOptions({
        queries: {
            ...queryClient.getDefaultOptions().queries,
            throwOnError: false
        },
        mutations: {
            ...queryClient.getDefaultOptions().mutations,
            throwOnError: false
        }
    });
    // Set up global error handler
    queryClient.setMutationDefaults([
        'mutation'
    ], {
        onError: createGlobalErrorHandler()
    });
}
const errorUtils = {
    /**
   * Check if error should trigger retry
   */ shouldRetry: (error)=>{
        if (isApiError(error)) {
            const errorType = getErrorType(error.status);
            // Don't retry client errors (4xx)
            return errorType !== "VALIDATION" && errorType !== "AUTHENTICATION" && errorType !== "AUTHORIZATION";
        }
        return true; // Retry network and unknown errors
    },
    /**
   * Get user-friendly error message
   */ getUserMessage: (error)=>{
        if (isApiError(error)) {
            const errorType = getErrorType(error.status);
            switch(errorType){
                case "AUTHENTICATION":
                    return 'Please log in to continue';
                case "AUTHORIZATION":
                    return 'You do not have permission to perform this action';
                case "VALIDATION":
                    return error.message || 'Please check your input and try again';
                case "SERVER":
                    return 'Server error occurred. Please try again later';
                case "NETWORK":
                    return 'Network error. Please check your connection';
                default:
                    return 'An unexpected error occurred';
            }
        }
        return 'An unexpected error occurred';
    },
    /**
   * Check if error is retryable
   */ isRetryable: (error)=>{
        if (isApiError(error)) {
            return error.status >= 500 || error.status === 0;
        }
        return true;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/query-utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Query Utilities and Helpers
 * Common utilities for working with TanStack Query
 */ __turbopack_esm__({
    "cacheUtils": (()=>cacheUtils),
    "devUtils": (()=>devUtils),
    "mutationOptionsBuilder": (()=>mutationOptionsBuilder),
    "queryErrorUtils": (()=>queryErrorUtils),
    "queryOptionsBuilder": (()=>queryOptionsBuilder),
    "queryStateUtils": (()=>queryStateUtils),
    "typeGuards": (()=>typeGuards)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-error-handler.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
;
;
const queryOptionsBuilder = {
    /**
   * Build options for real-time data (short cache)
   */ realTime: (options)=>({
            staleTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.SHORT,
            gcTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.MEDIUM,
            refetchInterval: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].REFETCH_INTERVAL.FAST,
            ...options
        }),
    /**
   * Build options for static data (long cache)
   */ static: (options)=>({
            staleTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.VERY_LONG,
            gcTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.VERY_LONG,
            refetchOnWindowFocus: false,
            refetchOnReconnect: false,
            ...options
        }),
    /**
   * Build options for user-specific data
   */ userSpecific: (options)=>({
            staleTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.MEDIUM,
            gcTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.LONG,
            refetchOnWindowFocus: true,
            ...options
        }),
    /**
   * Build options for background sync data
   */ backgroundSync: (options)=>({
            staleTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.LONG,
            gcTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.VERY_LONG,
            refetchInterval: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].REFETCH_INTERVAL.SLOW,
            refetchIntervalInBackground: true,
            ...options
        })
};
const mutationOptionsBuilder = {
    /**
   * Build options for optimistic updates
   */ optimistic: (options)=>({
            retry: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].RETRY.ONCE,
            ...options
        }),
    /**
   * Build options for critical operations
   */ critical: (options)=>({
            retry: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].RETRY.DEFAULT,
            retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
            ...options
        }),
    /**
   * Build options for background operations
   */ background: (options)=>({
            retry: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].RETRY.TWICE,
            ...options
        })
};
const cacheUtils = {
    /**
   * Invalidate queries by pattern
   */ invalidateByPattern: async (queryClient, pattern)=>{
        await queryClient.invalidateQueries({
            queryKey: pattern
        });
    },
    /**
   * Remove queries by pattern
   */ removeByPattern: (queryClient, pattern)=>{
        queryClient.removeQueries({
            queryKey: pattern
        });
    },
    /**
   * Update query data
   */ updateQueryData: (queryClient, queryKey, updater)=>{
        queryClient.setQueryData(queryKey, updater);
    },
    /**
   * Optimistically update list data
   */ optimisticListUpdate: (queryClient, queryKey, item, operation)=>{
        queryClient.setQueryData(queryKey, (oldData)=>{
            if (!oldData) return operation === 'add' ? [
                item
            ] : [];
            switch(operation){
                case 'add':
                    return [
                        ...oldData,
                        item
                    ];
                case 'update':
                    return oldData.map((existing)=>existing.id === item.id ? {
                            ...existing,
                            ...item
                        } : existing);
                case 'remove':
                    return oldData.filter((existing)=>existing.id !== item.id);
                default:
                    return oldData;
            }
        });
    },
    /**
   * Optimistically update paginated data
   */ optimisticPaginatedUpdate: (queryClient, queryKey, item, operation)=>{
        queryClient.setQueryData(queryKey, (oldData)=>{
            if (!oldData) return oldData;
            const updatedData = cacheUtils.optimisticListUpdate(queryClient, [
                'temp'
            ], item, operation);
            return {
                ...oldData,
                data: updatedData || oldData.data
            };
        });
    }
};
const queryStateUtils = {
    /**
   * Check if any queries are loading
   */ isAnyLoading: (queryClient, queryKeys)=>{
        return queryKeys.some((key)=>{
            const query = queryClient.getQueryState(key);
            return query?.fetchStatus === 'fetching';
        });
    },
    /**
   * Check if any queries have errors
   */ hasAnyErrors: (queryClient, queryKeys)=>{
        return queryKeys.some((key)=>{
            const query = queryClient.getQueryState(key);
            return query?.status === 'error';
        });
    },
    /**
   * Get all errors from queries
   */ getAllErrors: (queryClient, queryKeys)=>{
        return queryKeys.map((key)=>{
            const query = queryClient.getQueryState(key);
            return query?.error;
        }).filter((error)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isApiError"])(error));
    },
    /**
   * Check if data is stale
   */ isStale: (queryClient, queryKey)=>{
        const query = queryClient.getQueryState(queryKey);
        return query ? query.isStale : true;
    }
};
const devUtils = {
    /**
   * Log query cache state
   */ logCacheState: (queryClient)=>{
        if ("TURBOPACK compile-time truthy", 1) {
            const cache = queryClient.getQueryCache();
            console.log('[Query Cache]', {
                queries: cache.getAll().length,
                state: cache.getAll().map((query)=>({
                        key: query.queryKey,
                        status: query.state.status,
                        dataUpdatedAt: query.state.dataUpdatedAt,
                        error: query.state.error
                    }))
            });
        }
    },
    /**
   * Clear all cache (development only)
   */ clearAllCache: (queryClient)=>{
        if ("TURBOPACK compile-time truthy", 1) {
            queryClient.clear();
            console.log('[Dev] Query cache cleared');
        }
    },
    /**
   * Force refetch all queries (development only)
   */ refetchAll: async (queryClient)=>{
        if ("TURBOPACK compile-time truthy", 1) {
            await queryClient.refetchQueries();
            console.log('[Dev] All queries refetched');
        }
    }
};
const queryErrorUtils = {
    /**
   * Handle query error with user feedback
   */ handleQueryError: (error, context)=>{
        const message = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["errorUtils"].getUserMessage(error);
        console.error(`[Query Error${context ? ` - ${context}` : ''}]`, error);
        // This will be integrated with notification system
        // For now, just log the user-friendly message
        console.log('[User Message]', message);
        return message;
    },
    /**
   * Handle mutation error with user feedback
   */ handleMutationError: (error, context)=>{
        const message = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["errorUtils"].getUserMessage(error);
        console.error(`[Mutation Error${context ? ` - ${context}` : ''}]`, error);
        // This will be integrated with notification system
        // For now, just log the user-friendly message
        console.log('[User Message]', message);
        return message;
    }
};
const typeGuards = {
    /**
   * Check if response is a valid API response
   */ isApiResponse: (data)=>{
        return typeof data === 'object' && data !== null && 'data' in data && 'success' in data && 'timestamp' in data;
    },
    /**
   * Check if response is a paginated response
   */ isPaginatedResponse: (data)=>{
        return typeGuards.isApiResponse(data) && 'pagination' in data && typeof data.pagination === 'object';
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/api/base-hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Base API Hooks
 * Foundation hooks for API operations with TanStack Query
 */ __turbopack_esm__({
    "useApiHookUtils": (()=>useApiHookUtils),
    "useApiStatus": (()=>useApiStatus),
    "useBackgroundMutation": (()=>useBackgroundMutation),
    "useBackgroundSyncQuery": (()=>useBackgroundSyncQuery),
    "useBaseMutation": (()=>useBaseMutation),
    "useBaseQuery": (()=>useBaseQuery),
    "useCriticalMutation": (()=>useCriticalMutation),
    "useOptimisticMutation": (()=>useOptimisticMutation),
    "usePaginatedQuery": (()=>usePaginatedQuery),
    "useRealTimeQuery": (()=>useRealTimeQuery),
    "useStaticQuery": (()=>useStaticQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-error-handler.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature(), _s5 = __turbopack_refresh__.signature(), _s6 = __turbopack_refresh__.signature(), _s7 = __turbopack_refresh__.signature(), _s8 = __turbopack_refresh__.signature(), _s9 = __turbopack_refresh__.signature(), _s10 = __turbopack_refresh__.signature();
'use client';
;
;
;
function useBaseQuery(queryKey, queryFn, options) {
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        ...options,
        onError: {
            "useBaseQuery.useQuery": (error)=>{
                console.error(`[Query Error] ${queryKey.join(' → ')}:`, error);
                if (options?.onError) {
                    options.onError(error);
                }
            }
        }["useBaseQuery.useQuery"]
    });
}
_s(useBaseQuery, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useBaseMutation(mutationFn, options) {
    _s1();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn,
        ...options,
        onError: {
            "useBaseMutation.useMutation": (error, variables, context)=>{
                console.error('[Mutation Error]:', error);
                if (options?.onError) {
                    options.onError(error, variables, context);
                }
            }
        }["useBaseMutation.useMutation"],
        onSuccess: {
            "useBaseMutation.useMutation": (data, variables, context)=>{
                if (options?.onSuccess) {
                    options.onSuccess(data, variables, context);
                }
            }
        }["useBaseMutation.useMutation"]
    });
}
_s1(useBaseMutation, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function usePaginatedQuery(queryKey, queryFn, options) {
    _s2();
    return useBaseQuery(queryKey, queryFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryOptionsBuilder"].userSpecific(),
        ...options
    });
}
_s2(usePaginatedQuery, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        useBaseQuery
    ];
});
function useRealTimeQuery(queryKey, queryFn, options) {
    _s3();
    return useBaseQuery(queryKey, queryFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryOptionsBuilder"].realTime(),
        ...options
    });
}
_s3(useRealTimeQuery, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        useBaseQuery
    ];
});
function useStaticQuery(queryKey, queryFn, options) {
    _s4();
    return useBaseQuery(queryKey, queryFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryOptionsBuilder"].static(),
        ...options
    });
}
_s4(useStaticQuery, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        useBaseQuery
    ];
});
function useBackgroundSyncQuery(queryKey, queryFn, options) {
    _s5();
    return useBaseQuery(queryKey, queryFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryOptionsBuilder"].backgroundSync(),
        ...options
    });
}
_s5(useBackgroundSyncQuery, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        useBaseQuery
    ];
});
function useOptimisticMutation(mutationFn, options) {
    _s6();
    return useBaseMutation(mutationFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mutationOptionsBuilder"].optimistic(),
        ...options
    });
}
_s6(useOptimisticMutation, "cbsa5fN2Kd00W6TK3HZASQia1Kg=", false, function() {
    return [
        useBaseMutation
    ];
});
function useCriticalMutation(mutationFn, options) {
    _s7();
    return useBaseMutation(mutationFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mutationOptionsBuilder"].critical(),
        ...options
    });
}
_s7(useCriticalMutation, "cbsa5fN2Kd00W6TK3HZASQia1Kg=", false, function() {
    return [
        useBaseMutation
    ];
});
function useBackgroundMutation(mutationFn, options) {
    _s8();
    return useBaseMutation(mutationFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mutationOptionsBuilder"].background(),
        ...options
    });
}
_s8(useBackgroundMutation, "cbsa5fN2Kd00W6TK3HZASQia1Kg=", false, function() {
    return [
        useBaseMutation
    ];
});
const useApiHookUtils = ()=>{
    _s9();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return {
        /**
     * Invalidate queries by pattern
     */ invalidateQueries: (queryKey)=>{
            return queryClient.invalidateQueries({
                queryKey
            });
        },
        /**
     * Remove queries from cache
     */ removeQueries: (queryKey)=>{
            return queryClient.removeQueries({
                queryKey
            });
        },
        /**
     * Update query data optimistically
     */ updateQueryData: (queryKey, updater)=>{
            queryClient.setQueryData(queryKey, updater);
        },
        /**
     * Get cached query data
     */ getQueryData: (queryKey)=>{
            return queryClient.getQueryData(queryKey);
        },
        /**
     * Prefetch query data
     */ prefetchQuery: (queryKey, queryFn)=>{
            return queryClient.prefetchQuery({
                queryKey,
                queryFn
            });
        },
        /**
     * Check if query is loading
     */ isQueryLoading: (queryKey)=>{
            const query = queryClient.getQueryState(queryKey);
            return query?.fetchStatus === 'fetching';
        },
        /**
     * Get query error
     */ getQueryError: (queryKey)=>{
            const query = queryClient.getQueryState(queryKey);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isApiError"])(query?.error) ? query.error : null;
        },
        /**
     * Handle API error with user feedback
     */ handleApiError: (error, context)=>{
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["errorUtils"].getUserMessage(error);
        }
    };
};
_s9(useApiHookUtils, "4R+oYVB2Uc11P7bp1KcuhpkfaTw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"]
    ];
});
const useApiStatus = ()=>{
    _s10();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return {
        /**
     * Get overall API status
     */ getApiStatus: ()=>{
            const queries = queryClient.getQueryCache().getAll();
            const totalQueries = queries.length;
            const loadingQueries = queries.filter((q)=>q.state.fetchStatus === 'fetching').length;
            const errorQueries = queries.filter((q)=>q.state.status === 'error').length;
            const successQueries = queries.filter((q)=>q.state.status === 'success').length;
            return {
                total: totalQueries,
                loading: loadingQueries,
                error: errorQueries,
                success: successQueries,
                isLoading: loadingQueries > 0,
                hasErrors: errorQueries > 0,
                healthScore: totalQueries > 0 ? successQueries / totalQueries * 100 : 100
            };
        },
        /**
     * Get queries by status
     */ getQueriesByStatus: (status)=>{
            const queries = queryClient.getQueryCache().getAll();
            switch(status){
                case 'loading':
                    return queries.filter((q)=>q.state.fetchStatus === 'fetching');
                case 'error':
                    return queries.filter((q)=>q.state.status === 'error');
                case 'success':
                    return queries.filter((q)=>q.state.status === 'success');
                case 'idle':
                    return queries.filter((q)=>q.state.fetchStatus === 'idle');
                default:
                    return [];
            }
        },
        /**
     * Clear all errors
     */ clearAllErrors: ()=>{
            const errorQueries = queryClient.getQueryCache().getAll().filter((q)=>q.state.status === 'error');
            errorQueries.forEach((query)=>{
                queryClient.resetQueries({
                    queryKey: query.queryKey
                });
            });
        }
    };
};
_s10(useApiStatus, "4R+oYVB2Uc11P7bp1KcuhpkfaTw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/api/auth-hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Authentication API Hooks
 * Hooks for system authentication operations
 */ __turbopack_esm__({
    "useAuth": (()=>useAuth),
    "useChangePassword": (()=>useChangePassword),
    "useCreateUser": (()=>useCreateUser),
    "useLogin": (()=>useLogin),
    "useLogout": (()=>useLogout),
    "useLogoutAll": (()=>useLogoutAll),
    "useProfile": (()=>useProfile),
    "useSystemUser": (()=>useSystemUser),
    "useSystemUsers": (()=>useSystemUsers),
    "useUpdateProfile": (()=>useUpdateProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/base-hooks.ts [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature(), _s5 = __turbopack_refresh__.signature(), _s6 = __turbopack_refresh__.signature(), _s7 = __turbopack_refresh__.signature(), _s8 = __turbopack_refresh__.signature(), _s9 = __turbopack_refresh__.signature();
'use client';
;
;
function useLogin() {
    _s();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useLogin.useBaseMutation": async (credentials)=>{
            const response = await fetch('/api/system-auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(credentials)
            });
            if (!response.ok) {
                throw new Error(`Login failed: ${response.statusText}`);
            }
            return response.json();
        }
    }["useLogin.useBaseMutation"], {
        onSuccess: {
            "useLogin.useBaseMutation": (data)=>{
                // Invalidate auth queries on successful login
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].auth.all);
                console.log('✅ Login successful:', data.user.username);
            }
        }["useLogin.useBaseMutation"],
        onError: {
            "useLogin.useBaseMutation": (error)=>{
                console.error('❌ Login failed:', error);
            }
        }["useLogin.useBaseMutation"]
    });
}
_s(useLogin, "u64JDqrckp6d8UXRWRy4SgQqcdg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useLogout() {
    _s1();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useLogout.useBaseMutation": async ()=>{
            const response = await fetch('/api/system-auth/logout', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`Logout failed: ${response.statusText}`);
            }
            return response.json();
        }
    }["useLogout.useBaseMutation"], {
        onSuccess: {
            "useLogout.useBaseMutation": ()=>{
                // Clear all auth-related queries on logout
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].auth.all);
                console.log('✅ Logout successful');
            }
        }["useLogout.useBaseMutation"],
        onError: {
            "useLogout.useBaseMutation": (error)=>{
                console.error('❌ Logout failed:', error);
            }
        }["useLogout.useBaseMutation"]
    });
}
_s1(useLogout, "u64JDqrckp6d8UXRWRy4SgQqcdg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useLogoutAll() {
    _s2();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useLogoutAll.useBaseMutation": async ()=>{
            const response = await fetch('/api/system-auth/logout-all', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`Logout all failed: ${response.statusText}`);
            }
            return response.json();
        }
    }["useLogoutAll.useBaseMutation"], {
        onSuccess: {
            "useLogoutAll.useBaseMutation": ()=>{
                // Clear all auth-related queries on logout all
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].auth.all);
                console.log('✅ Logout all successful');
            }
        }["useLogoutAll.useBaseMutation"],
        onError: {
            "useLogoutAll.useBaseMutation": (error)=>{
                console.error('❌ Logout all failed:', error);
            }
        }["useLogoutAll.useBaseMutation"]
    });
}
_s2(useLogoutAll, "u64JDqrckp6d8UXRWRy4SgQqcdg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useProfile() {
    _s3();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].auth.profile(), {
        "useProfile.useBaseQuery": async ()=>{
            const response = await fetch('/api/system-auth/profile');
            if (!response.ok) {
                throw new Error(`Failed to fetch profile: ${response.statusText}`);
            }
            return response.json();
        }
    }["useProfile.useBaseQuery"], {
        enabled: false,
        staleTime: 5 * 60 * 1000,
        retry: {
            "useProfile.useBaseQuery": (failureCount, error)=>{
                // Don't retry on 401 (unauthorized)
                if (error?.status === 401) return false;
                return failureCount < 2;
            }
        }["useProfile.useBaseQuery"]
    });
}
_s3(useProfile, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useUpdateProfile() {
    _s4();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useUpdateProfile.useBaseMutation": async (data)=>{
            const response = await fetch('/api/system-auth/profile', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            if (!response.ok) {
                throw new Error(`Failed to update profile: ${response.statusText}`);
            }
            return response.json();
        }
    }["useUpdateProfile.useBaseMutation"], {
        onSuccess: {
            "useUpdateProfile.useBaseMutation": ()=>{
                // Invalidate profile query to refetch updated data
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].auth.profile());
                console.log('✅ Profile updated successfully');
            }
        }["useUpdateProfile.useBaseMutation"],
        onError: {
            "useUpdateProfile.useBaseMutation": (error)=>{
                console.error('❌ Profile update failed:', error);
            }
        }["useUpdateProfile.useBaseMutation"]
    });
}
_s4(useUpdateProfile, "u64JDqrckp6d8UXRWRy4SgQqcdg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useChangePassword() {
    _s5();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useChangePassword.useBaseMutation": async (data)=>{
            const response = await fetch('/api/system-auth/change-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            if (!response.ok) {
                throw new Error(`Failed to change password: ${response.statusText}`);
            }
            return response.json();
        }
    }["useChangePassword.useBaseMutation"], {
        onSuccess: {
            "useChangePassword.useBaseMutation": ()=>{
                console.log('✅ Password changed successfully');
            }
        }["useChangePassword.useBaseMutation"],
        onError: {
            "useChangePassword.useBaseMutation": (error)=>{
                console.error('❌ Password change failed:', error);
            }
        }["useChangePassword.useBaseMutation"]
    });
}
_s5(useChangePassword, "cbsa5fN2Kd00W6TK3HZASQia1Kg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useCreateUser() {
    _s6();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useCreateUser.useBaseMutation": async (data)=>{
            const response = await fetch('/api/system-auth/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            if (!response.ok) {
                throw new Error(`Failed to create user: ${response.statusText}`);
            }
            return response.json();
        }
    }["useCreateUser.useBaseMutation"], {
        onSuccess: {
            "useCreateUser.useBaseMutation": ()=>{
                // Invalidate users list to show new user
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].auth.users());
                console.log('✅ User created successfully');
            }
        }["useCreateUser.useBaseMutation"],
        onError: {
            "useCreateUser.useBaseMutation": (error)=>{
                console.error('❌ User creation failed:', error);
            }
        }["useCreateUser.useBaseMutation"]
    });
}
_s6(useCreateUser, "u64JDqrckp6d8UXRWRy4SgQqcdg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useSystemUsers() {
    _s7();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].auth.users(), {
        "useSystemUsers.useBaseQuery": async ()=>{
            const response = await fetch('/api/system-auth/users');
            if (!response.ok) {
                throw new Error(`Failed to fetch users: ${response.statusText}`);
            }
            return response.json();
        }
    }["useSystemUsers.useBaseQuery"], {
        enabled: false,
        staleTime: 2 * 60 * 1000
    });
}
_s7(useSystemUsers, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useSystemUser(userId) {
    _s8();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].auth.user(userId), {
        "useSystemUser.useBaseQuery": async ()=>{
            const response = await fetch(`/api/system-auth/users/${userId}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch user: ${response.statusText}`);
            }
            return response.json();
        }
    }["useSystemUser.useBaseQuery"], {
        enabled: !!userId,
        staleTime: 5 * 60 * 1000
    });
}
_s8(useSystemUser, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useAuth() {
    _s9();
    const login = useLogin();
    const logout = useLogout();
    const logoutAll = useLogoutAll();
    const profile = useProfile();
    const updateProfile = useUpdateProfile();
    const changePassword = useChangePassword();
    const createUser = useCreateUser();
    return {
        // Queries
        profile,
        // Mutations
        login,
        logout,
        logoutAll,
        updateProfile,
        changePassword,
        createUser,
        // Computed state
        isAuthenticated: !!profile.data,
        user: profile.data,
        isLoading: profile.isLoading || login.isPending || logout.isPending,
        error: profile.error || login.error || logout.error,
        // Actions
        loginUser: login.mutate,
        logoutUser: logout.mutate,
        logoutAllDevices: logoutAll.mutate,
        updateUserProfile: updateProfile.mutate,
        changeUserPassword: changePassword.mutate,
        createNewUser: createUser.mutate
    };
}
_s9(useAuth, "Vh8Wc9jVPdrmNcsWGzFAy7ZTC2E=", false, function() {
    return [
        useLogin,
        useLogout,
        useLogoutAll,
        useProfile,
        useUpdateProfile,
        useChangePassword,
        useCreateUser
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/api/football-hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Football Data API Hooks
 * Hooks for football leagues, teams, and fixtures operations
 */ __turbopack_esm__({
    "useDailySync": (()=>useDailySync),
    "useFixture": (()=>useFixture),
    "useFixtures": (()=>useFixtures),
    "useFootball": (()=>useFootball),
    "useLeague": (()=>useLeague),
    "useLeagues": (()=>useLeagues),
    "useLiveFixtures": (()=>useLiveFixtures),
    "useSyncFixtures": (()=>useSyncFixtures),
    "useSyncStatus": (()=>useSyncStatus),
    "useTeam": (()=>useTeam),
    "useTeams": (()=>useTeams),
    "useTodayFixtures": (()=>useTodayFixtures),
    "useUpcomingFixtures": (()=>useUpcomingFixtures)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/base-hooks.ts [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature(), _s5 = __turbopack_refresh__.signature(), _s6 = __turbopack_refresh__.signature(), _s7 = __turbopack_refresh__.signature(), _s8 = __turbopack_refresh__.signature(), _s9 = __turbopack_refresh__.signature(), _s10 = __turbopack_refresh__.signature(), _s11 = __turbopack_refresh__.signature(), _s12 = __turbopack_refresh__.signature();
'use client';
;
;
function useLeagues(params) {
    _s();
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());
    if (params?.country) queryParams.set('country', params.country);
    if (params?.isActive !== undefined) queryParams.set('isActive', params.isActive.toString());
    if (params?.query) queryParams.set('query', params.query);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaginatedQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.leagues(),
        params
    ], {
        "useLeagues.usePaginatedQuery": async ()=>{
            const response = await fetch(`/api/football/leagues?${queryParams.toString()}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch leagues: ${response.statusText}`);
            }
            return response.json();
        }
    }["useLeagues.usePaginatedQuery"], {
        staleTime: 10 * 60 * 1000
    });
}
_s(useLeagues, "8dRrW1kK8lRPrshcMFzQabx/L7w=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaginatedQuery"]
    ];
});
function useLeague(leagueId) {
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.league(leagueId), {
        "useLeague.useBaseQuery": async ()=>{
            const response = await fetch(`/api/football/leagues/${leagueId}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch league: ${response.statusText}`);
            }
            return response.json();
        }
    }["useLeague.useBaseQuery"], {
        enabled: !!leagueId,
        staleTime: 10 * 60 * 1000
    });
}
_s1(useLeague, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useTeams(params) {
    _s2();
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());
    if (params?.leagueId) queryParams.set('leagueId', params.leagueId);
    if (params?.country) queryParams.set('country', params.country);
    if (params?.query) queryParams.set('query', params.query);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaginatedQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.teams(),
        params
    ], {
        "useTeams.usePaginatedQuery": async ()=>{
            const response = await fetch(`/api/football/teams?${queryParams.toString()}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch teams: ${response.statusText}`);
            }
            return response.json();
        }
    }["useTeams.usePaginatedQuery"], {
        staleTime: 5 * 60 * 1000
    });
}
_s2(useTeams, "8dRrW1kK8lRPrshcMFzQabx/L7w=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaginatedQuery"]
    ];
});
function useTeam(teamId) {
    _s3();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.team(teamId), {
        "useTeam.useBaseQuery": async ()=>{
            const response = await fetch(`/api/football/teams/${teamId}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch team: ${response.statusText}`);
            }
            return response.json();
        }
    }["useTeam.useBaseQuery"], {
        enabled: !!teamId,
        staleTime: 5 * 60 * 1000
    });
}
_s3(useTeam, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useFixtures(params) {
    _s4();
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());
    if (params?.leagueId) queryParams.set('leagueId', params.leagueId);
    if (params?.teamId) queryParams.set('teamId', params.teamId);
    if (params?.status) queryParams.set('status', params.status);
    if (params?.dateFrom) queryParams.set('dateFrom', params.dateFrom);
    if (params?.dateTo) queryParams.set('dateTo', params.dateTo);
    if (params?.query) queryParams.set('query', params.query);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaginatedQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.fixtures(),
        params
    ], {
        "useFixtures.usePaginatedQuery": async ()=>{
            const response = await fetch(`/api/football/fixtures?${queryParams.toString()}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch fixtures: ${response.statusText}`);
            }
            return response.json();
        }
    }["useFixtures.usePaginatedQuery"], {
        staleTime: 1 * 60 * 1000
    });
}
_s4(useFixtures, "8dRrW1kK8lRPrshcMFzQabx/L7w=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaginatedQuery"]
    ];
});
function useFixture(fixtureId) {
    _s5();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.fixture(fixtureId), {
        "useFixture.useBaseQuery": async ()=>{
            const response = await fetch(`/api/football/fixtures/${fixtureId}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch fixture: ${response.statusText}`);
            }
            return response.json();
        }
    }["useFixture.useBaseQuery"], {
        enabled: !!fixtureId,
        staleTime: 30 * 1000
    });
}
_s5(useFixture, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useSyncStatus() {
    _s6();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBackgroundSyncQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.syncStatus(), {
        "useSyncStatus.useBackgroundSyncQuery": async ()=>{
            const response = await fetch('/api/football/fixtures/sync/status');
            if (!response.ok) {
                throw new Error(`Failed to fetch sync status: ${response.statusText}`);
            }
            return response.json();
        }
    }["useSyncStatus.useBackgroundSyncQuery"], {
        staleTime: 30 * 1000,
        refetchInterval: 60 * 1000
    });
}
_s6(useSyncStatus, "Xisi00twd9E8WbSd/RCl4O/WVzY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBackgroundSyncQuery"]
    ];
});
function useSyncFixtures() {
    _s7();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useSyncFixtures.useBaseMutation": async ()=>{
            const response = await fetch('/api/football/fixtures/sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`Failed to start sync: ${response.statusText}`);
            }
            return response.json();
        }
    }["useSyncFixtures.useBaseMutation"], {
        onSuccess: {
            "useSyncFixtures.useBaseMutation": ()=>{
                // Invalidate sync status and fixtures to show updated data
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.syncStatus());
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.fixtures());
                console.log('✅ Fixtures sync started');
            }
        }["useSyncFixtures.useBaseMutation"],
        onError: {
            "useSyncFixtures.useBaseMutation": (error)=>{
                console.error('❌ Fixtures sync failed:', error);
            }
        }["useSyncFixtures.useBaseMutation"]
    });
}
_s7(useSyncFixtures, "u64JDqrckp6d8UXRWRy4SgQqcdg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useDailySync() {
    _s8();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useDailySync.useBaseMutation": async ()=>{
            const response = await fetch('/api/football/fixtures/sync/daily', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`Failed to start daily sync: ${response.statusText}`);
            }
            return response.json();
        }
    }["useDailySync.useBaseMutation"], {
        onSuccess: {
            "useDailySync.useBaseMutation": ()=>{
                // Invalidate sync status and fixtures
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.syncStatus());
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.fixtures());
                console.log('✅ Daily sync started');
            }
        }["useDailySync.useBaseMutation"],
        onError: {
            "useDailySync.useBaseMutation": (error)=>{
                console.error('❌ Daily sync failed:', error);
            }
        }["useDailySync.useBaseMutation"]
    });
}
_s8(useDailySync, "u64JDqrckp6d8UXRWRy4SgQqcdg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useFootball() {
    _s9();
    const syncFixtures = useSyncFixtures();
    const dailySync = useDailySync();
    const syncStatus = useSyncStatus();
    return {
        // Sync operations
        syncFixtures,
        dailySync,
        syncStatus,
        // Sync actions
        startSync: syncFixtures.mutate,
        startDailySync: dailySync.mutate,
        // Sync state
        isSyncing: syncFixtures.isPending || dailySync.isPending,
        syncError: syncFixtures.error || dailySync.error,
        lastSyncStatus: syncStatus.data
    };
}
_s9(useFootball, "0psCbtzM30SpLKt5GSKOpeeHq2k=", false, function() {
    return [
        useSyncFixtures,
        useDailySync,
        useSyncStatus
    ];
});
function useLiveFixtures() {
    _s10();
    return useFixtures({
        status: 'live',
        limit: 50
    });
}
_s10(useLiveFixtures, "kNJmKRNnT0VZ7/QeoHQabLuDNrM=", false, function() {
    return [
        useFixtures
    ];
});
function useTodayFixtures() {
    _s11();
    const today = new Date().toISOString().split('T')[0];
    return useFixtures({
        dateFrom: today,
        dateTo: today,
        limit: 100
    });
}
_s11(useTodayFixtures, "kNJmKRNnT0VZ7/QeoHQabLuDNrM=", false, function() {
    return [
        useFixtures
    ];
});
function useUpcomingFixtures(days = 7) {
    _s12();
    const today = new Date();
    const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);
    return useFixtures({
        dateFrom: today.toISOString().split('T')[0],
        dateTo: futureDate.toISOString().split('T')[0],
        status: 'scheduled',
        limit: 100
    });
}
_s12(useUpcomingFixtures, "kNJmKRNnT0VZ7/QeoHQabLuDNrM=", false, function() {
    return [
        useFixtures
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/api/broadcast-hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Broadcast Links API Hooks
 * Hooks for broadcast links management operations
 */ __turbopack_esm__({
    "useActiveBroadcastLinks": (()=>useActiveBroadcastLinks),
    "useBroadcastLink": (()=>useBroadcastLink),
    "useBroadcastLinks": (()=>useBroadcastLinks),
    "useBroadcastLinksByLanguage": (()=>useBroadcastLinksByLanguage),
    "useBroadcastLinksByQuality": (()=>useBroadcastLinksByQuality),
    "useBroadcastLinksManager": (()=>useBroadcastLinksManager),
    "useCreateBroadcastLink": (()=>useCreateBroadcastLink),
    "useDeleteBroadcastLink": (()=>useDeleteBroadcastLink),
    "useFixtureBroadcastLinks": (()=>useFixtureBroadcastLinks),
    "useToggleBroadcastLinkStatus": (()=>useToggleBroadcastLinkStatus),
    "useUpdateBroadcastLink": (()=>useUpdateBroadcastLink)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/base-hooks.ts [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature(), _s5 = __turbopack_refresh__.signature(), _s6 = __turbopack_refresh__.signature(), _s7 = __turbopack_refresh__.signature(), _s8 = __turbopack_refresh__.signature(), _s9 = __turbopack_refresh__.signature(), _s10 = __turbopack_refresh__.signature();
'use client';
;
;
function useBroadcastLinks(params) {
    _s();
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());
    if (params?.fixtureId) queryParams.set('fixtureId', params.fixtureId);
    if (params?.quality) queryParams.set('quality', params.quality);
    if (params?.language) queryParams.set('language', params.language);
    if (params?.isActive !== undefined) queryParams.set('isActive', params.isActive.toString());
    if (params?.query) queryParams.set('query', params.query);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaginatedQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.links(),
        params
    ], {
        "useBroadcastLinks.usePaginatedQuery": async ()=>{
            const response = await fetch(`/api/broadcast-links?${queryParams.toString()}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch broadcast links: ${response.statusText}`);
            }
            return response.json();
        }
    }["useBroadcastLinks.usePaginatedQuery"], {
        staleTime: 2 * 60 * 1000
    });
}
_s(useBroadcastLinks, "8dRrW1kK8lRPrshcMFzQabx/L7w=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaginatedQuery"]
    ];
});
function useBroadcastLink(linkId) {
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.link(linkId), {
        "useBroadcastLink.useBaseQuery": async ()=>{
            const response = await fetch(`/api/broadcast-links/${linkId}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch broadcast link: ${response.statusText}`);
            }
            return response.json();
        }
    }["useBroadcastLink.useBaseQuery"], {
        enabled: !!linkId,
        staleTime: 2 * 60 * 1000
    });
}
_s1(useBroadcastLink, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useFixtureBroadcastLinks(fixtureId) {
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.fixture(fixtureId), {
        "useFixtureBroadcastLinks.useBaseQuery": async ()=>{
            const response = await fetch(`/api/broadcast-links/fixture/${fixtureId}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch fixture broadcast links: ${response.statusText}`);
            }
            return response.json();
        }
    }["useFixtureBroadcastLinks.useBaseQuery"], {
        enabled: !!fixtureId,
        staleTime: 1 * 60 * 1000
    });
}
_s2(useFixtureBroadcastLinks, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useCreateBroadcastLink() {
    _s3();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOptimisticMutation"])({
        "useCreateBroadcastLink.useOptimisticMutation": async (data)=>{
            const response = await fetch('/api/broadcast-links', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            if (!response.ok) {
                throw new Error(`Failed to create broadcast link: ${response.statusText}`);
            }
            return response.json();
        }
    }["useCreateBroadcastLink.useOptimisticMutation"], {
        onSuccess: {
            "useCreateBroadcastLink.useOptimisticMutation": (data)=>{
                // Invalidate broadcast links queries
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.links());
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.fixture(data.fixtureId));
                console.log('✅ Broadcast link created successfully');
            }
        }["useCreateBroadcastLink.useOptimisticMutation"],
        onError: {
            "useCreateBroadcastLink.useOptimisticMutation": (error)=>{
                console.error('❌ Failed to create broadcast link:', error);
            }
        }["useCreateBroadcastLink.useOptimisticMutation"]
    });
}
_s3(useCreateBroadcastLink, "wAIvSt9W/+5IvmTsbat1EBSQXss=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOptimisticMutation"]
    ];
});
function useUpdateBroadcastLink() {
    _s4();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOptimisticMutation"])({
        "useUpdateBroadcastLink.useOptimisticMutation": async ({ id, data })=>{
            const response = await fetch(`/api/broadcast-links/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            if (!response.ok) {
                throw new Error(`Failed to update broadcast link: ${response.statusText}`);
            }
            return response.json();
        }
    }["useUpdateBroadcastLink.useOptimisticMutation"], {
        onSuccess: {
            "useUpdateBroadcastLink.useOptimisticMutation": (data)=>{
                // Invalidate specific link and related queries
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.link(data.id));
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.links());
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.fixture(data.fixtureId));
                console.log('✅ Broadcast link updated successfully');
            }
        }["useUpdateBroadcastLink.useOptimisticMutation"],
        onError: {
            "useUpdateBroadcastLink.useOptimisticMutation": (error)=>{
                console.error('❌ Failed to update broadcast link:', error);
            }
        }["useUpdateBroadcastLink.useOptimisticMutation"]
    });
}
_s4(useUpdateBroadcastLink, "wAIvSt9W/+5IvmTsbat1EBSQXss=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOptimisticMutation"]
    ];
});
function useDeleteBroadcastLink() {
    _s5();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useDeleteBroadcastLink.useBaseMutation": async (linkId)=>{
            const response = await fetch(`/api/broadcast-links/${linkId}`, {
                method: 'DELETE'
            });
            if (!response.ok) {
                throw new Error(`Failed to delete broadcast link: ${response.statusText}`);
            }
            return response.json();
        }
    }["useDeleteBroadcastLink.useBaseMutation"], {
        onSuccess: {
            "useDeleteBroadcastLink.useBaseMutation": (_, linkId)=>{
                // Invalidate broadcast links queries
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.links());
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.link(linkId));
                console.log('✅ Broadcast link deleted successfully');
            }
        }["useDeleteBroadcastLink.useBaseMutation"],
        onError: {
            "useDeleteBroadcastLink.useBaseMutation": (error)=>{
                console.error('❌ Failed to delete broadcast link:', error);
            }
        }["useDeleteBroadcastLink.useBaseMutation"]
    });
}
_s5(useDeleteBroadcastLink, "u64JDqrckp6d8UXRWRy4SgQqcdg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useToggleBroadcastLinkStatus() {
    _s6();
    const { invalidateQueries, updateQueryData } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOptimisticMutation"])({
        "useToggleBroadcastLinkStatus.useOptimisticMutation": async ({ id, isActive })=>{
            const response = await fetch(`/api/broadcast-links/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    isActive
                })
            });
            if (!response.ok) {
                throw new Error(`Failed to toggle broadcast link status: ${response.statusText}`);
            }
            return response.json();
        }
    }["useToggleBroadcastLinkStatus.useOptimisticMutation"], {
        onMutate: {
            "useToggleBroadcastLinkStatus.useOptimisticMutation": async ({ id, isActive })=>{
                // Optimistically update the link status
                const linkQueryKey = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.link(id);
                const previousLink = updateQueryData(linkQueryKey, {
                    "useToggleBroadcastLinkStatus.useOptimisticMutation.previousLink": (old)=>old ? {
                            ...old,
                            isActive
                        } : old
                }["useToggleBroadcastLinkStatus.useOptimisticMutation.previousLink"]);
                return {
                    previousLink,
                    linkQueryKey
                };
            }
        }["useToggleBroadcastLinkStatus.useOptimisticMutation"],
        onError: {
            "useToggleBroadcastLinkStatus.useOptimisticMutation": (error, variables, context)=>{
                // Revert optimistic update on error
                if (context?.previousLink && context?.linkQueryKey) {
                    updateQueryData(context.linkQueryKey, {
                        "useToggleBroadcastLinkStatus.useOptimisticMutation": ()=>context.previousLink
                    }["useToggleBroadcastLinkStatus.useOptimisticMutation"]);
                }
                console.error('❌ Failed to toggle broadcast link status:', error);
            }
        }["useToggleBroadcastLinkStatus.useOptimisticMutation"],
        onSuccess: {
            "useToggleBroadcastLinkStatus.useOptimisticMutation": (data)=>{
                // Invalidate related queries
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.links());
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.fixture(data.fixtureId));
                console.log('✅ Broadcast link status toggled successfully');
            }
        }["useToggleBroadcastLinkStatus.useOptimisticMutation"]
    });
}
_s6(useToggleBroadcastLinkStatus, "rW7iOrorG+isk5GV43B/XjTPOuY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOptimisticMutation"]
    ];
});
function useBroadcastLinksManager() {
    _s7();
    const createLink = useCreateBroadcastLink();
    const updateLink = useUpdateBroadcastLink();
    const deleteLink = useDeleteBroadcastLink();
    const toggleStatus = useToggleBroadcastLinkStatus();
    return {
        // Mutations
        createLink,
        updateLink,
        deleteLink,
        toggleStatus,
        // Actions
        createBroadcastLink: createLink.mutate,
        updateBroadcastLink: updateLink.mutate,
        deleteBroadcastLink: deleteLink.mutate,
        toggleLinkStatus: toggleStatus.mutate,
        // State
        isCreating: createLink.isPending,
        isUpdating: updateLink.isPending,
        isDeleting: deleteLink.isPending,
        isToggling: toggleStatus.isPending,
        isLoading: createLink.isPending || updateLink.isPending || deleteLink.isPending || toggleStatus.isPending,
        // Errors
        createError: createLink.error,
        updateError: updateLink.error,
        deleteError: deleteLink.error,
        toggleError: toggleStatus.error
    };
}
_s7(useBroadcastLinksManager, "XbY0YNeA/qFF9uR9FgYYUbUwpQw=", false, function() {
    return [
        useCreateBroadcastLink,
        useUpdateBroadcastLink,
        useDeleteBroadcastLink,
        useToggleBroadcastLinkStatus
    ];
});
function useBroadcastLinksByQuality(quality) {
    _s8();
    return useBroadcastLinks({
        quality,
        isActive: true
    });
}
_s8(useBroadcastLinksByQuality, "M+exTM7lv8qALVRH2RnjVdrHrnw=", false, function() {
    return [
        useBroadcastLinks
    ];
});
function useBroadcastLinksByLanguage(language) {
    _s9();
    return useBroadcastLinks({
        language,
        isActive: true
    });
}
_s9(useBroadcastLinksByLanguage, "M+exTM7lv8qALVRH2RnjVdrHrnw=", false, function() {
    return [
        useBroadcastLinks
    ];
});
function useActiveBroadcastLinks() {
    _s10();
    return useBroadcastLinks({
        isActive: true
    });
}
_s10(useActiveBroadcastLinks, "M+exTM7lv8qALVRH2RnjVdrHrnw=", false, function() {
    return [
        useBroadcastLinks
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/api/health-hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Health Check API Hooks
 * Hooks for API health monitoring operations
 */ __turbopack_esm__({
    "useApiHealth": (()=>useApiHealth),
    "useApiPerformance": (()=>useApiPerformance),
    "useDatabaseHealth": (()=>useDatabaseHealth),
    "useExternalApiHealth": (()=>useExternalApiHealth),
    "useHealthDashboard": (()=>useHealthDashboard),
    "useSystemHealth": (()=>useSystemHealth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/base-hooks.ts [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature(), _s5 = __turbopack_refresh__.signature();
'use client';
;
;
function useApiHealth() {
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBackgroundSyncQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].health.api(), {
        "useApiHealth.useBackgroundSyncQuery": async ()=>{
            const response = await fetch('/api/health');
            if (!response.ok) {
                throw new Error(`Health check failed: ${response.statusText}`);
            }
            return response.json();
        }
    }["useApiHealth.useBackgroundSyncQuery"], {
        staleTime: 30 * 1000,
        refetchInterval: 60 * 1000,
        retry: 3,
        retryDelay: {
            "useApiHealth.useBackgroundSyncQuery": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 10000)
        }["useApiHealth.useBackgroundSyncQuery"]
    });
}
_s(useApiHealth, "Xisi00twd9E8WbSd/RCl4O/WVzY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBackgroundSyncQuery"]
    ];
});
function useDatabaseHealth() {
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].health.all,
        'database'
    ], {
        "useDatabaseHealth.useBaseQuery": async ()=>{
            const startTime = performance.now();
            const response = await fetch('/api/health/database');
            const endTime = performance.now();
            if (!response.ok) {
                throw new Error(`Database health check failed: ${response.statusText}`);
            }
            const data = await response.json();
            return {
                ...data,
                responseTime: endTime - startTime
            };
        }
    }["useDatabaseHealth.useBaseQuery"], {
        staleTime: 30 * 1000,
        retry: 2
    });
}
_s1(useDatabaseHealth, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useExternalApiHealth() {
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].health.all,
        'external-api'
    ], {
        "useExternalApiHealth.useBaseQuery": async ()=>{
            const startTime = performance.now();
            const response = await fetch('/api/health/external-api');
            const endTime = performance.now();
            if (!response.ok) {
                throw new Error(`External API health check failed: ${response.statusText}`);
            }
            const data = await response.json();
            return {
                ...data,
                responseTime: endTime - startTime
            };
        }
    }["useExternalApiHealth.useBaseQuery"], {
        staleTime: 60 * 1000,
        retry: 2
    });
}
_s2(useExternalApiHealth, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useSystemHealth() {
    _s3();
    const apiHealth = useApiHealth();
    const dbHealth = useDatabaseHealth();
    const externalApiHealth = useExternalApiHealth();
    const isLoading = apiHealth.isLoading || dbHealth.isLoading || externalApiHealth.isLoading;
    const hasErrors = apiHealth.isError || dbHealth.isError || externalApiHealth.isError;
    // Calculate overall health status
    const getOverallStatus = ()=>{
        if (hasErrors) return 'unhealthy';
        const apiStatus = apiHealth.data?.status;
        const dbStatus = dbHealth.data?.status;
        const externalStatus = externalApiHealth.data?.status;
        if (apiStatus === 'healthy' && dbStatus === 'up' && externalStatus === 'up') {
            return 'healthy';
        }
        if (apiStatus === 'unhealthy' || dbStatus === 'down') {
            return 'unhealthy';
        }
        return 'degraded';
    };
    // Calculate average response time
    const getAverageResponseTime = ()=>{
        const times = [
            dbHealth.data?.responseTime,
            externalApiHealth.data?.responseTime
        ].filter((time)=>typeof time === 'number');
        if (times.length === 0) return 0;
        return times.reduce((sum, time)=>sum + time, 0) / times.length;
    };
    return {
        // Individual health checks
        api: apiHealth,
        database: dbHealth,
        externalApi: externalApiHealth,
        // Overall status
        isLoading,
        hasErrors,
        overallStatus: getOverallStatus(),
        averageResponseTime: getAverageResponseTime(),
        // Health data
        healthData: {
            api: apiHealth.data,
            database: dbHealth.data,
            externalApi: externalApiHealth.data
        },
        // Error information
        errors: {
            api: apiHealth.error,
            database: dbHealth.error,
            externalApi: externalApiHealth.error
        },
        // Refetch functions
        refetchAll: ()=>{
            apiHealth.refetch();
            dbHealth.refetch();
            externalApiHealth.refetch();
        }
    };
}
_s3(useSystemHealth, "tg4dhD4iEHLtytmDjXCAthYjQ+k=", false, function() {
    return [
        useApiHealth,
        useDatabaseHealth,
        useExternalApiHealth
    ];
});
function useApiPerformance() {
    _s4();
    const systemHealth = useSystemHealth();
    const getPerformanceMetrics = ()=>{
        const { healthData } = systemHealth;
        return {
            uptime: healthData.api?.uptime || 0,
            responseTime: systemHealth.averageResponseTime,
            status: systemHealth.overallStatus,
            services: {
                database: healthData.database?.status || 'unknown',
                externalApi: healthData.externalApi?.status || 'unknown'
            },
            lastCheck: new Date().toISOString()
        };
    };
    return {
        ...systemHealth,
        performanceMetrics: getPerformanceMetrics(),
        // Performance indicators
        isPerformanceGood: systemHealth.averageResponseTime < 1000,
        isPerformanceFair: systemHealth.averageResponseTime < 3000,
        isPerformancePoor: systemHealth.averageResponseTime >= 3000
    };
}
_s4(useApiPerformance, "2d31NriCCprLf+vAmUKMsUXssds=", false, function() {
    return [
        useSystemHealth
    ];
});
function useHealthDashboard() {
    _s5();
    const performance1 = useApiPerformance();
    const getDashboardData = ()=>{
        const { healthData, overallStatus, averageResponseTime } = performance1;
        return {
            status: overallStatus,
            uptime: healthData.api?.uptime || 0,
            version: healthData.api?.version || 'unknown',
            responseTime: averageResponseTime,
            services: [
                {
                    name: 'Database',
                    status: healthData.database?.status || 'unknown',
                    responseTime: healthData.database?.responseTime || 0
                },
                {
                    name: 'External API',
                    status: healthData.externalApi?.status || 'unknown',
                    responseTime: healthData.externalApi?.responseTime || 0
                }
            ],
            lastUpdated: new Date().toISOString()
        };
    };
    return {
        ...performance1,
        dashboardData: getDashboardData(),
        // Dashboard actions
        refreshDashboard: performance1.refetchAll,
        // Status indicators
        statusColor: ({
            healthy: '#10b981',
            degraded: '#f59e0b',
            unhealthy: '#ef4444'
        })[performance1.overallStatus],
        statusIcon: ({
            healthy: '✅',
            degraded: '⚠️',
            unhealthy: '❌'
        })[performance1.overallStatus]
    };
}
_s5(useHealthDashboard, "LCN2gN/M9dZLNWkhxZDpRHCM8Uc=", false, function() {
    return [
        useApiPerformance
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/api/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * API Hooks Index
 * Central export for all API hooks
 */ // Base hooks and utilities
__turbopack_esm__({
    "API_HOOKS_NAME": (()=>API_HOOKS_NAME),
    "API_HOOKS_VERSION": (()=>API_HOOKS_VERSION),
    "setupApiHooks": (()=>setupApiHooks)
});
;
;
;
;
;
;
const API_HOOKS_VERSION = '1.0.0';
const API_HOOKS_NAME = 'APISportsGame API Hooks';
function setupApiHooks() {
    console.log(`${API_HOOKS_NAME} v${API_HOOKS_VERSION} initialized`);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/api/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/base-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/auth-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$football$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/football-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$broadcast$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/broadcast-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$health$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/health-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/src/hooks/api/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/app/login/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Login Page - SystemUser Authentication
 * Login page for system administrators, editors, and moderators
 */ __turbopack_esm__({
    "default": (()=>LoginPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$auth$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/auth-layout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/hooks/api/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/theme/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$typography$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/typography/index.js [app-client] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/auth-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/form/index.js [app-client] (ecmascript) <export default as Form>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$alert$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Alert$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/alert/index.js [app-client] (ecmascript) <export default as Alert>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Input$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/input/index.js [app-client] (ecmascript) <export default as Input>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$UserOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__UserOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/UserOutlined.js [app-client] (ecmascript) <export default as UserOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LockOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LockOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/LockOutlined.js [app-client] (ecmascript) <export default as LockOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$EyeInvisibleOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__EyeInvisibleOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/EyeInvisibleOutlined.js [app-client] (ecmascript) <export default as EyeInvisibleOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$EyeTwoTone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__EyeTwoTone$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/EyeTwoTone.js [app-client] (ecmascript) <export default as EyeTwoTone>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Checkbox$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/checkbox/index.js [app-client] (ecmascript) <export default as Checkbox>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/button/index.js [app-client] (ecmascript) <locals> <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
'use client';
;
;
;
;
;
;
;
const { Text, Link } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$typography$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"];
function LoginPage() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const auth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    const [form] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].useForm();
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Get redirect URL from query params
    const redirectTo = searchParams.get('redirect') || '/';
    // Check if user is already logged in
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LoginPage.useEffect": ()=>{
            if (auth.isAuthenticated) {
                router.push(redirectTo);
            }
        }
    }["LoginPage.useEffect"], [
        auth.isAuthenticated,
        router,
        redirectTo
    ]);
    // Handle form submission
    const handleSubmit = async (values)=>{
        setLoading(true);
        setError(null);
        try {
            const result = await auth.loginUser({
                username: values.username,
                password: values.password
            });
            if (result.success) {
                // Store remember me preference
                if (values.remember) {
                    localStorage.setItem('rememberMe', 'true');
                    localStorage.setItem('lastUsername', values.username);
                } else {
                    localStorage.removeItem('rememberMe');
                    localStorage.removeItem('lastUsername');
                }
                // Redirect to intended page
                router.push(redirectTo);
            } else {
                setError(result.error || 'Login failed. Please try again.');
            }
        } catch (err) {
            console.error('Login error:', err);
            setError('An unexpected error occurred. Please try again.');
        } finally{
            setLoading(false);
        }
    };
    // Handle form validation failure
    const handleSubmitFailed = (errorInfo)=>{
        console.log('Form validation failed:', errorInfo);
        setError('Please check your input and try again.');
    };
    // Load remembered username
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LoginPage.useEffect": ()=>{
            const rememberMe = localStorage.getItem('rememberMe');
            const lastUsername = localStorage.getItem('lastUsername');
            if (rememberMe === 'true' && lastUsername) {
                form.setFieldsValue({
                    username: lastUsername,
                    remember: true
                });
            }
        }
    }["LoginPage.useEffect"], [
        form
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$auth$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthLayout"], {
        title: "Welcome Back",
        subtitle: "Sign in to your administrator account",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$auth$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthCard"], {
            title: "System Login",
            description: "Enter your credentials to access the CMS dashboard",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$auth$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthForm"], {
                children: [
                    error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$alert$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Alert$3e$__["Alert"], {
                        message: "Login Failed",
                        description: error,
                        type: "error",
                        showIcon: true,
                        closable: true,
                        onClose: ()=>setError(null),
                        style: {
                            marginBottom: '16px'
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/app/login/page.tsx",
                        lineNumber: 115,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"], {
                        form: form,
                        name: "login",
                        onFinish: handleSubmit,
                        onFinishFailed: handleSubmitFailed,
                        autoComplete: "off",
                        layout: "vertical",
                        requiredMark: false,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Item, {
                                name: "username",
                                label: "Username",
                                rules: [
                                    {
                                        required: true,
                                        message: 'Please enter your username'
                                    },
                                    {
                                        min: 3,
                                        message: 'Username must be at least 3 characters'
                                    },
                                    {
                                        max: 50,
                                        message: 'Username must not exceed 50 characters'
                                    }
                                ],
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Input$3e$__["Input"], {
                                    prefix: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$UserOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__UserOutlined$3e$__["UserOutlined"], {
                                        style: {
                                            color: themeStyles.getTextColor('tertiary')
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/login/page.tsx",
                                        lineNumber: 147,
                                        columnNumber: 25
                                    }, void 0),
                                    placeholder: "Enter your username",
                                    size: "large",
                                    autoComplete: "username",
                                    style: {
                                        borderRadius: '8px'
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/app/login/page.tsx",
                                    lineNumber: 146,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/login/page.tsx",
                                lineNumber: 137,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Item, {
                                name: "password",
                                label: "Password",
                                rules: [
                                    {
                                        required: true,
                                        message: 'Please enter your password'
                                    },
                                    {
                                        min: 6,
                                        message: 'Password must be at least 6 characters'
                                    }
                                ],
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Input$3e$__["Input"].Password, {
                                    prefix: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LockOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LockOutlined$3e$__["LockOutlined"], {
                                        style: {
                                            color: themeStyles.getTextColor('tertiary')
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/login/page.tsx",
                                        lineNumber: 167,
                                        columnNumber: 25
                                    }, void 0),
                                    placeholder: "Enter your password",
                                    size: "large",
                                    autoComplete: "current-password",
                                    iconRender: (visible)=>visible ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$EyeTwoTone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__EyeTwoTone$3e$__["EyeTwoTone"], {}, void 0, false, {
                                            fileName: "[project]/src/app/login/page.tsx",
                                            lineNumber: 171,
                                            columnNumber: 53
                                        }, void 0) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$EyeInvisibleOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__EyeInvisibleOutlined$3e$__["EyeInvisibleOutlined"], {}, void 0, false, {
                                            fileName: "[project]/src/app/login/page.tsx",
                                            lineNumber: 171,
                                            columnNumber: 70
                                        }, void 0),
                                    style: {
                                        borderRadius: '8px'
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/app/login/page.tsx",
                                    lineNumber: 166,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/login/page.tsx",
                                lineNumber: 158,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    marginBottom: '24px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Item, {
                                        name: "remember",
                                        valuePropName: "checked",
                                        style: {
                                            margin: 0
                                        },
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Checkbox$3e$__["Checkbox"], {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                                style: {
                                                    color: themeStyles.getTextColor('secondary')
                                                },
                                                children: "Remember me"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/login/page.tsx",
                                                lineNumber: 193,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/login/page.tsx",
                                            lineNumber: 192,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/login/page.tsx",
                                        lineNumber: 187,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                        href: "/forgot-password",
                                        style: {
                                            color: themeStyles.getColor('primary'),
                                            fontSize: '14px'
                                        },
                                        children: "Forgot password?"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/login/page.tsx",
                                        lineNumber: 199,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/login/page.tsx",
                                lineNumber: 179,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Item, {
                                style: {
                                    margin: 0
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], {
                                    type: "primary",
                                    htmlType: "submit",
                                    loading: loading,
                                    size: "large",
                                    block: true,
                                    style: {
                                        borderRadius: '8px',
                                        height: '48px',
                                        fontSize: '16px',
                                        fontWeight: 'bold'
                                    },
                                    children: loading ? 'Signing In...' : 'Sign In'
                                }, void 0, false, {
                                    fileName: "[project]/src/app/login/page.tsx",
                                    lineNumber: 212,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/login/page.tsx",
                                lineNumber: 211,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/login/page.tsx",
                        lineNumber: 127,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            marginTop: '24px',
                            padding: '16px',
                            backgroundColor: themeStyles.getBackgroundColor('elevated'),
                            borderRadius: '8px',
                            border: `1px solid ${themeStyles.getBorderColor('primary')}`
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                            style: {
                                color: themeStyles.getTextColor('secondary'),
                                fontSize: '13px',
                                lineHeight: 1.5
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                    children: "System Access:"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/login/page.tsx",
                                    lineNumber: 247,
                                    columnNumber: 15
                                }, this),
                                " This login is for system administrators, editors, and moderators only. If you're looking for the public portal, please visit our main website."
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/login/page.tsx",
                            lineNumber: 240,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/login/page.tsx",
                        lineNumber: 231,
                        columnNumber: 11
                    }, this),
                    ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$alert$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Alert$3e$__["Alert"], {
                        message: "Development Mode",
                        description: "Authentication is disabled during development. You can access the system without logging in.",
                        type: "info",
                        showIcon: true,
                        style: {
                            marginTop: '16px'
                        },
                        action: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], {
                            size: "small",
                            type: "link",
                            onClick: ()=>router.push('/'),
                            children: "Go to Dashboard"
                        }, void 0, false, {
                            fileName: "[project]/src/app/login/page.tsx",
                            lineNumber: 262,
                            columnNumber: 17
                        }, void 0)
                    }, void 0, false, {
                        fileName: "[project]/src/app/login/page.tsx",
                        lineNumber: 255,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/login/page.tsx",
                lineNumber: 112,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/login/page.tsx",
            lineNumber: 108,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/login/page.tsx",
        lineNumber: 104,
        columnNumber: 5
    }, this);
} // Note: Metadata should be defined in layout.tsx or a server component
 // For client components, we handle title via document.title in useEffect
_s(LoginPage, "7vCxilFFCbtkpTFY8JRfnBUfUlQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].useForm
    ];
});
_c = LoginPage;
var _c;
__turbopack_refresh__.register(_c, "LoginPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/login/page.tsx [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=src_3c047a._.js.map