{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/types.ts"], "sourcesContent": ["/**\n * Store Types and Interfaces\n * Defines TypeScript types for all store modules\n */\n\n// ============================================================================\n// Base Store Types\n// ============================================================================\n\n/**\n * Base store state interface\n * All stores should extend this interface\n */\nexport interface BaseStoreState {\n  // Hydration state for SSR compatibility\n  _hasHydrated: boolean;\n  setHasHydrated: (hasHydrated: boolean) => void;\n}\n\n/**\n * Store action interface\n * Defines the structure for store actions\n */\nexport interface StoreAction<T = any> {\n  type: string;\n  payload?: T;\n}\n\n/**\n * Store slice interface\n * For modular store composition\n */\nexport interface StoreSlice<T> {\n  (...args: any[]): T;\n}\n\n// ============================================================================\n// User and Authentication Types\n// ============================================================================\n\n/**\n * System user roles\n */\nexport type SystemUserRole = 'Admin' | 'Editor' | 'Moderator';\n\n/**\n * System user interface\n */\nexport interface SystemUser {\n  id: string;\n  email: string;\n  name: string;\n  role: SystemUserRole;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n  lastLoginAt?: string;\n}\n\n/**\n * Authentication tokens\n */\nexport interface AuthTokens {\n  accessToken: string;\n  refreshToken: string;\n  expiresAt: number; // Unix timestamp\n}\n\n/**\n * Authentication state\n */\nexport interface AuthState {\n  // User data\n  user: SystemUser | null;\n  tokens: AuthTokens | null;\n  \n  // Authentication status\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  \n  // Error handling\n  error: string | null;\n  \n  // Session management\n  lastActivity: number; // Unix timestamp\n  sessionTimeout: number; // Minutes\n}\n\n/**\n * Authentication actions\n */\nexport interface AuthActions {\n  // Login/logout\n  login: (email: string, password: string) => Promise<void>;\n  logout: () => Promise<void>;\n  logoutAll: () => Promise<void>;\n  \n  // User management\n  updateProfile: (data: Partial<SystemUser>) => Promise<void>;\n  refreshTokens: () => Promise<void>;\n  \n  // State management\n  setUser: (user: SystemUser | null) => void;\n  setTokens: (tokens: AuthTokens | null) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  clearError: () => void;\n  \n  // Session management\n  updateLastActivity: () => void;\n  checkSession: () => boolean;\n  \n  // Hydration\n  hydrate: () => void;\n}\n\n// ============================================================================\n// Application State Types\n// ============================================================================\n\n/**\n * Theme configuration\n */\nexport interface ThemeConfig {\n  mode: 'light' | 'dark';\n  primaryColor: string;\n  borderRadius: number;\n  compactMode: boolean;\n}\n\n/**\n * Navigation state\n */\nexport interface NavigationState {\n  currentPath: string;\n  breadcrumbs: Array<{\n    title: string;\n    path: string;\n  }>;\n  sidebarCollapsed: boolean;\n  activeMenuKey: string;\n}\n\n/**\n * Global UI state\n */\nexport interface UIState {\n  // Loading states\n  globalLoading: boolean;\n  loadingMessage: string;\n  \n  // Error states\n  globalError: string | null;\n  errorDetails: any;\n  \n  // Notification state\n  notifications: Array<{\n    id: string;\n    type: 'success' | 'error' | 'warning' | 'info';\n    title: string;\n    message: string;\n    duration?: number;\n    timestamp: number;\n  }>;\n  \n  // Modal state\n  modals: Record<string, {\n    visible: boolean;\n    data?: any;\n  }>;\n}\n\n/**\n * Application settings\n */\nexport interface AppSettings {\n  // Language and localization\n  language: 'en' | 'vi';\n  timezone: string;\n  dateFormat: string;\n  \n  // Data preferences\n  pageSize: number;\n  autoRefresh: boolean;\n  refreshInterval: number; // Seconds\n  \n  // Feature flags\n  features: {\n    darkMode: boolean;\n    notifications: boolean;\n    autoSave: boolean;\n    advancedFilters: boolean;\n  };\n}\n\n/**\n * Application state\n */\nexport interface AppState {\n  // Configuration\n  theme: ThemeConfig;\n  settings: AppSettings;\n  \n  // Navigation\n  navigation: NavigationState;\n  \n  // UI state\n  ui: UIState;\n  \n  // System info\n  version: string;\n  buildTime: string;\n  environment: 'development' | 'staging' | 'production';\n}\n\n/**\n * Application actions\n */\nexport interface AppActions {\n  // Theme management\n  setTheme: (theme: Partial<ThemeConfig>) => void;\n  toggleTheme: () => void;\n  \n  // Settings management\n  updateSettings: (settings: Partial<AppSettings>) => void;\n  resetSettings: () => void;\n  \n  // Navigation\n  setCurrentPath: (path: string) => void;\n  setBreadcrumbs: (breadcrumbs: NavigationState['breadcrumbs']) => void;\n  toggleSidebar: () => void;\n  setActiveMenu: (key: string) => void;\n  \n  // UI state management\n  setGlobalLoading: (loading: boolean, message?: string) => void;\n  setGlobalError: (error: string | null, details?: any) => void;\n  clearGlobalError: () => void;\n  \n  // Notifications\n  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => void;\n  removeNotification: (id: string) => void;\n  clearNotifications: () => void;\n  \n  // Modals\n  showModal: (key: string, data?: any) => void;\n  hideModal: (key: string) => void;\n  hideAllModals: () => void;\n}\n\n// ============================================================================\n// Combined Store Types\n// ============================================================================\n\n/**\n * Complete authentication store\n */\nexport interface AuthStore extends BaseStoreState, AuthState, AuthActions {}\n\n/**\n * Complete application store\n */\nexport interface AppStore extends BaseStoreState, AppState, AppActions {}\n\n// ============================================================================\n// Store Configuration Types\n// ============================================================================\n\n/**\n * Store persistence configuration\n */\nexport interface StorePersistConfig {\n  name: string;\n  version: number;\n  partialize?: (state: any) => any;\n  skipHydration?: boolean;\n}\n\n/**\n * Store devtools configuration\n */\nexport interface StoreDevtoolsConfig {\n  name: string;\n  enabled: boolean;\n}\n\n/**\n * Store configuration\n */\nexport interface StoreConfig {\n  persist?: StorePersistConfig;\n  devtools?: StoreDevtoolsConfig;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,+EAA+E;AAC/E,mBAAmB;AACnB,+EAA+E;AAE/E;;;CAGC"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/utils.ts"], "sourcesContent": ["/**\n * Store Utilities\n * Helper functions and utilities for store management\n */\n\nimport { StateCreator } from 'zustand';\nimport { persist, devtools, createJSONStorage } from 'zustand/middleware';\nimport type {\n  BaseStoreState,\n  StoreConfig,\n  StorePersistConfig,\n  StoreDevtoolsConfig\n} from './types';\n\n// ============================================================================\n// Store Creation Utilities\n// ============================================================================\n\n/**\n * Create a store with middleware support\n * Provides a consistent way to create stores with persistence and devtools\n */\nexport function createStoreWithMiddleware<T extends BaseStoreState>(\n  storeCreator: StateCreator<T>,\n  config: StoreConfig\n): StateCreator<T> {\n  let store: any = storeCreator;\n\n  // Apply persistence middleware if configured\n  if (config.persist) {\n    store = persist(\n      store,\n      {\n        name: config.persist.name,\n        version: config.persist.version,\n        storage: createJSONStorage(() => localStorage),\n        partialize: config.persist.partialize || ((state) => state),\n        skipHydration: config.persist.skipHydration || false,\n        onRehydrateStorage: () => (state: any) => {\n          if (state) {\n            state.setHasHydrated(true);\n          }\n        },\n      }\n    );\n  }\n\n  // Apply devtools middleware if configured\n  if (config.devtools) {\n    store = devtools(\n      store,\n      {\n        name: config.devtools.name,\n        enabled: config.devtools.enabled && process.env.NODE_ENV === 'development',\n      }\n    );\n  }\n\n  return store as StateCreator<T>;\n}\n\n// ============================================================================\n// Base Store State Utilities\n// ============================================================================\n\n/**\n * Create base store state\n * Provides common state properties for all stores\n */\nexport function createBaseStoreState(): BaseStoreState {\n  return {\n    _hasHydrated: false,\n    setHasHydrated: (hasHydrated: boolean) => {\n      // This will be implemented by the actual store\n    },\n  };\n}\n\n/**\n * Create base store actions\n * Provides common actions for all stores\n */\nexport function createBaseStoreActions<T extends BaseStoreState>(\n  set: (partial: Partial<T>) => void\n): Pick<BaseStoreState, 'setHasHydrated'> {\n  return {\n    setHasHydrated: (hasHydrated: boolean) => {\n      set({ _hasHydrated: hasHydrated } as Partial<T>);\n    },\n  };\n}\n\n// ============================================================================\n// Token Management Utilities\n// ============================================================================\n\n/**\n * Check if token is expired\n */\nexport function isTokenExpired(expiresAt: number): boolean {\n  return Date.now() >= expiresAt;\n}\n\n/**\n * Get token expiration time\n * Calculates expiration time from JWT token or sets default\n */\nexport function getTokenExpiration(token: string, defaultMinutes: number = 60): number {\n  try {\n    // Try to decode JWT token to get expiration\n    const payload = JSON.parse(atob(token.split('.')[1]));\n    if (payload.exp) {\n      return payload.exp * 1000; // Convert to milliseconds\n    }\n  } catch (error) {\n    // If JWT parsing fails, use default expiration\n  }\n\n  // Default expiration: current time + defaultMinutes\n  return Date.now() + (defaultMinutes * 60 * 1000);\n}\n\n/**\n * Clear all stored tokens\n */\nexport function clearStoredTokens(): void {\n  if (typeof window !== 'undefined') {\n    localStorage.removeItem('auth-storage');\n    sessionStorage.removeItem('auth-storage');\n  }\n}\n\n// ============================================================================\n// Session Management Utilities\n// ============================================================================\n\n/**\n * Check if session is valid\n */\nexport function isSessionValid(\n  lastActivity: number,\n  sessionTimeout: number\n): boolean {\n  const now = Date.now();\n  const timeoutMs = sessionTimeout * 60 * 1000; // Convert minutes to milliseconds\n  return (now - lastActivity) < timeoutMs;\n}\n\n/**\n * Get session remaining time in minutes\n */\nexport function getSessionRemainingTime(\n  lastActivity: number,\n  sessionTimeout: number\n): number {\n  const now = Date.now();\n  const timeoutMs = sessionTimeout * 60 * 1000;\n  const elapsed = now - lastActivity;\n  const remaining = timeoutMs - elapsed;\n  return Math.max(0, Math.floor(remaining / (60 * 1000)));\n}\n\n// ============================================================================\n// Error Handling Utilities\n// ============================================================================\n\n/**\n * Extract error message from various error types\n */\nexport function extractErrorMessage(error: any): string {\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error?.response?.data?.message) {\n    return error.response.data.message;\n  }\n\n  if (error?.message) {\n    return error.message;\n  }\n\n  if (error?.error) {\n    return error.error;\n  }\n\n  return 'An unexpected error occurred';\n}\n\n/**\n * Create error object with details\n */\nexport function createErrorObject(\n  message: string,\n  details?: any\n): { message: string; details: any } {\n  return {\n    message,\n    details: details || null,\n  };\n}\n\n// ============================================================================\n// Notification Utilities\n// ============================================================================\n\n/**\n * Generate unique notification ID\n */\nexport function generateNotificationId(): string {\n  return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n}\n\n/**\n * Get default notification duration based on type\n */\nexport function getDefaultNotificationDuration(\n  type: 'success' | 'error' | 'warning' | 'info'\n): number {\n  switch (type) {\n    case 'success':\n      return 3000; // 3 seconds\n    case 'error':\n      return 5000; // 5 seconds\n    case 'warning':\n      return 4000; // 4 seconds\n    case 'info':\n      return 3000; // 3 seconds\n    default:\n      return 3000;\n  }\n}\n\n// ============================================================================\n// Local Storage Utilities\n// ============================================================================\n\n/**\n * Safe localStorage operations\n */\nexport const storage = {\n  get: (key: string): any => {\n    if (typeof window === 'undefined') return null;\n\n    try {\n      const item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : null;\n    } catch (error) {\n      console.warn(`Error reading from localStorage key \"${key}\":`, error);\n      return null;\n    }\n  },\n\n  set: (key: string, value: any): void => {\n    if (typeof window === 'undefined') return;\n\n    try {\n      localStorage.setItem(key, JSON.stringify(value));\n    } catch (error) {\n      console.warn(`Error writing to localStorage key \"${key}\":`, error);\n    }\n  },\n\n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return;\n\n    try {\n      localStorage.removeItem(key);\n    } catch (error) {\n      console.warn(`Error removing localStorage key \"${key}\":`, error);\n    }\n  },\n\n  clear: (): void => {\n    if (typeof window === 'undefined') return;\n\n    try {\n      localStorage.clear();\n    } catch (error) {\n      console.warn('Error clearing localStorage:', error);\n    }\n  },\n};\n\n// ============================================================================\n// Development Utilities\n// ============================================================================\n\n/**\n * Log store action for debugging\n */\nexport function logStoreAction(\n  storeName: string,\n  actionName: string,\n  payload?: any\n): void {\n  if (process.env.NODE_ENV === 'development') {\n    console.group(`🐻 [${storeName}] ${actionName}`);\n    if (payload !== undefined) {\n      console.log('Payload:', payload);\n    }\n    console.log('Timestamp:', new Date().toISOString());\n    console.groupEnd();\n  }\n}\n\n/**\n * Validate store state structure\n */\nexport function validateStoreState<T extends BaseStoreState>(\n  state: T,\n  requiredKeys: (keyof T)[]\n): boolean {\n  for (const key of requiredKeys) {\n    if (!(key in state)) {\n      console.error(`Missing required store state key: ${String(key)}`);\n      return false;\n    }\n  }\n  return true;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;AAGD;AA+C4C;;AA/BrC,SAAS,0BACd,YAA6B,EAC7B,MAAmB;IAEnB,IAAI,QAAa;IAEjB,6CAA6C;IAC7C,IAAI,OAAO,OAAO,EAAE;QAClB,QAAQ,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACZ,OACA;YACE,MAAM,OAAO,OAAO,CAAC,IAAI;YACzB,SAAS,OAAO,OAAO,CAAC,OAAO;YAC/B,SAAS,CAAA,GAAA,gJAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;YACjC,YAAY,OAAO,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC,QAAU,KAAK;YAC1D,eAAe,OAAO,OAAO,CAAC,aAAa,IAAI;YAC/C,oBAAoB,IAAM,CAAC;oBACzB,IAAI,OAAO;wBACT,MAAM,cAAc,CAAC;oBACvB;gBACF;QACF;IAEJ;IAEA,0CAA0C;IAC1C,IAAI,OAAO,QAAQ,EAAE;QACnB,QAAQ,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACb,OACA;YACE,MAAM,OAAO,QAAQ,CAAC,IAAI;YAC1B,SAAS,OAAO,QAAQ,CAAC,OAAO,IAAI,oDAAyB;QAC/D;IAEJ;IAEA,OAAO;AACT;AAUO,SAAS;IACd,OAAO;QACL,cAAc;QACd,gBAAgB,CAAC;QACf,+CAA+C;QACjD;IACF;AACF;AAMO,SAAS,uBACd,GAAkC;IAElC,OAAO;QACL,gBAAgB,CAAC;YACf,IAAI;gBAAE,cAAc;YAAY;QAClC;IACF;AACF;AASO,SAAS,eAAe,SAAiB;IAC9C,OAAO,KAAK,GAAG,MAAM;AACvB;AAMO,SAAS,mBAAmB,KAAa,EAAE,iBAAyB,EAAE;IAC3E,IAAI;QACF,4CAA4C;QAC5C,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;QACnD,IAAI,QAAQ,GAAG,EAAE;YACf,OAAO,QAAQ,GAAG,GAAG,MAAM,0BAA0B;QACvD;IACF,EAAE,OAAO,OAAO;IACd,+CAA+C;IACjD;IAEA,oDAAoD;IACpD,OAAO,KAAK,GAAG,KAAM,iBAAiB,KAAK;AAC7C;AAKO,SAAS;IACd,wCAAmC;QACjC,aAAa,UAAU,CAAC;QACxB,eAAe,UAAU,CAAC;IAC5B;AACF;AASO,SAAS,eACd,YAAoB,EACpB,cAAsB;IAEtB,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,iBAAiB,KAAK,MAAM,kCAAkC;IAChF,OAAO,AAAC,MAAM,eAAgB;AAChC;AAKO,SAAS,wBACd,YAAoB,EACpB,cAAsB;IAEtB,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,iBAAiB,KAAK;IACxC,MAAM,UAAU,MAAM;IACtB,MAAM,YAAY,YAAY;IAC9B,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI;AACtD;AASO,SAAS,oBAAoB,KAAU;IAC5C,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IAEA,IAAI,OAAO,UAAU,MAAM,SAAS;QAClC,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;IACpC;IAEA,IAAI,OAAO,SAAS;QAClB,OAAO,MAAM,OAAO;IACtB;IAEA,IAAI,OAAO,OAAO;QAChB,OAAO,MAAM,KAAK;IACpB;IAEA,OAAO;AACT;AAKO,SAAS,kBACd,OAAe,EACf,OAAa;IAEb,OAAO;QACL;QACA,SAAS,WAAW;IACtB;AACF;AASO,SAAS;IACd,OAAO,CAAC,aAAa,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAChF;AAKO,SAAS,+BACd,IAA8C;IAE9C,OAAQ;QACN,KAAK;YACH,OAAO,MAAM,YAAY;QAC3B,KAAK;YACH,OAAO,MAAM,YAAY;QAC3B,KAAK;YACH,OAAO,MAAM,YAAY;QAC3B,KAAK;YACH,OAAO,MAAM,YAAY;QAC3B;YACE,OAAO;IACX;AACF;AASO,MAAM,UAAU;IACrB,KAAK,CAAC;QACJ,uCAAmC;;QAAW;QAE9C,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,qCAAqC,EAAE,IAAI,EAAE,CAAC,EAAE;YAC9D,OAAO;QACT;IACF;IAEA,KAAK,CAAC,KAAa;QACjB,uCAAmC;;QAAM;QAEzC,IAAI;YACF,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,mCAAmC,EAAE,IAAI,EAAE,CAAC,EAAE;QAC9D;IACF;IAEA,QAAQ,CAAC;QACP,uCAAmC;;QAAM;QAEzC,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,iCAAiC,EAAE,IAAI,EAAE,CAAC,EAAE;QAC5D;IACF;IAEA,OAAO;QACL,uCAAmC;;QAAM;QAEzC,IAAI;YACF,aAAa,KAAK;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,gCAAgC;QAC/C;IACF;AACF;AASO,SAAS,eACd,SAAiB,EACjB,UAAkB,EAClB,OAAa;IAEb,wCAA4C;QAC1C,QAAQ,KAAK,CAAC,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,YAAY;QAC/C,IAAI,YAAY,WAAW;YACzB,QAAQ,GAAG,CAAC,YAAY;QAC1B;QACA,QAAQ,GAAG,CAAC,cAAc,IAAI,OAAO,WAAW;QAChD,QAAQ,QAAQ;IAClB;AACF;AAKO,SAAS,mBACd,KAAQ,EACR,YAAyB;IAEzB,KAAK,MAAM,OAAO,aAAc;QAC9B,IAAI,CAAC,CAAC,OAAO,KAAK,GAAG;YACnB,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,OAAO,MAAM;YAChE,OAAO;QACT;IACF;IACA,OAAO;AACT"}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/constants.ts"], "sourcesContent": ["/**\n * Store Constants\n * Defines constants used across all stores\n */\n\n// ============================================================================\n// Store Names\n// ============================================================================\n\nexport const STORE_NAMES = {\n  AUTH: 'auth-store',\n  APP: 'app-store',\n} as const;\n\n// ============================================================================\n// Storage Keys\n// ============================================================================\n\nexport const STORAGE_KEYS = {\n  AUTH: 'auth-storage',\n  APP: 'app-storage',\n  THEME: 'theme-storage',\n  SETTINGS: 'settings-storage',\n} as const;\n\n// ============================================================================\n// Default Values\n// ============================================================================\n\n/**\n * Default theme configuration\n */\nexport const DEFAULT_THEME = {\n  mode: 'light' as const,\n  primaryColor: '#1890ff',\n  borderRadius: 6,\n  compactMode: false,\n};\n\n/**\n * Default application settings\n */\nexport const DEFAULT_APP_SETTINGS = {\n  language: 'en' as const,\n  timezone: 'UTC',\n  dateFormat: 'YYYY-MM-DD',\n  pageSize: 20,\n  autoRefresh: true,\n  refreshInterval: 30, // seconds\n  features: {\n    darkMode: true,\n    notifications: true,\n    autoSave: true,\n    advancedFilters: true,\n  },\n};\n\n/**\n * Default navigation state\n */\nexport const DEFAULT_NAVIGATION = {\n  currentPath: '/',\n  breadcrumbs: [],\n  sidebarCollapsed: false,\n  activeMenuKey: 'dashboard',\n};\n\n/**\n * Default UI state\n */\nexport const DEFAULT_UI_STATE = {\n  globalLoading: false,\n  loadingMessage: '',\n  globalError: null,\n  errorDetails: null,\n  notifications: [],\n  modals: {},\n};\n\n// ============================================================================\n// Session Configuration\n// ============================================================================\n\n/**\n * Session timeout in minutes\n */\nexport const SESSION_TIMEOUT = 60; // 1 hour\n\n/**\n * Token refresh threshold in minutes\n * Refresh token when it expires in less than this time\n */\nexport const TOKEN_REFRESH_THRESHOLD = 5; // 5 minutes\n\n/**\n * Activity tracking interval in milliseconds\n */\nexport const ACTIVITY_TRACKING_INTERVAL = 60000; // 1 minute\n\n// ============================================================================\n// API Configuration\n// ============================================================================\n\n/**\n * API endpoints for store operations\n */\nexport const STORE_API_ENDPOINTS = {\n  AUTH: {\n    LOGIN: '/api/system-auth/login',\n    LOGOUT: '/api/system-auth/logout',\n    LOGOUT_ALL: '/api/system-auth/logout-all',\n    PROFILE: '/api/system-auth/profile',\n    REFRESH: '/api/system-auth/refresh',\n  },\n} as const;\n\n// ============================================================================\n// Error Messages\n// ============================================================================\n\nexport const ERROR_MESSAGES = {\n  AUTH: {\n    LOGIN_FAILED: 'Login failed. Please check your credentials.',\n    LOGOUT_FAILED: 'Logout failed. Please try again.',\n    SESSION_EXPIRED: 'Your session has expired. Please log in again.',\n    TOKEN_REFRESH_FAILED: 'Failed to refresh authentication token.',\n    PROFILE_UPDATE_FAILED: 'Failed to update profile. Please try again.',\n    UNAUTHORIZED: 'You are not authorized to perform this action.',\n  },\n  APP: {\n    SETTINGS_SAVE_FAILED: 'Failed to save settings. Please try again.',\n    THEME_LOAD_FAILED: 'Failed to load theme configuration.',\n    NETWORK_ERROR: 'Network error. Please check your connection.',\n    UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',\n  },\n} as const;\n\n// ============================================================================\n// Success Messages\n// ============================================================================\n\nexport const SUCCESS_MESSAGES = {\n  AUTH: {\n    LOGIN_SUCCESS: 'Successfully logged in.',\n    LOGOUT_SUCCESS: 'Successfully logged out.',\n    PROFILE_UPDATED: 'Profile updated successfully.',\n  },\n  APP: {\n    SETTINGS_SAVED: 'Settings saved successfully.',\n    THEME_UPDATED: 'Theme updated successfully.',\n  },\n} as const;\n\n// ============================================================================\n// Store Versions\n// ============================================================================\n\n/**\n * Store versions for migration support\n */\nexport const STORE_VERSIONS = {\n  AUTH: 1,\n  APP: 1,\n} as const;\n\n// ============================================================================\n// Development Configuration\n// ============================================================================\n\n/**\n * Development mode configuration\n */\nexport const DEV_CONFIG = {\n  ENABLE_DEVTOOLS: process.env.NODE_ENV === 'development',\n  ENABLE_LOGGING: process.env.NODE_ENV === 'development',\n  MOCK_API_DELAY: 1000, // milliseconds\n} as const;\n\n// ============================================================================\n// Feature Flags\n// ============================================================================\n\n/**\n * Feature flags for conditional functionality\n */\nexport const FEATURE_FLAGS = {\n  ENABLE_DARK_MODE: true,\n  ENABLE_NOTIFICATIONS: true,\n  ENABLE_AUTO_SAVE: true,\n  ENABLE_ADVANCED_FILTERS: true,\n  ENABLE_REAL_TIME_UPDATES: false, // Future feature\n  ENABLE_OFFLINE_MODE: false, // Future feature\n} as const;\n\n// ============================================================================\n// Validation Rules\n// ============================================================================\n\n/**\n * Validation rules for store data\n */\nexport const VALIDATION_RULES = {\n  EMAIL: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  PASSWORD_MIN_LENGTH: 8,\n  NAME_MIN_LENGTH: 2,\n  NAME_MAX_LENGTH: 50,\n  PAGE_SIZE_MIN: 5,\n  PAGE_SIZE_MAX: 100,\n  REFRESH_INTERVAL_MIN: 10, // seconds\n  REFRESH_INTERVAL_MAX: 300, // seconds\n} as const;\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,+EAA+E;AAC/E,cAAc;AACd,+EAA+E;;;;;;;;;;;;;;;;;;;AAsK5D;AApKZ,MAAM,cAAc;IACzB,MAAM;IACN,KAAK;AACP;AAMO,MAAM,eAAe;IAC1B,MAAM;IACN,KAAK;IACL,OAAO;IACP,UAAU;AACZ;AASO,MAAM,gBAAgB;IAC3B,MAAM;IACN,cAAc;IACd,cAAc;IACd,aAAa;AACf;AAKO,MAAM,uBAAuB;IAClC,UAAU;IACV,UAAU;IACV,YAAY;IACZ,UAAU;IACV,aAAa;IACb,iBAAiB;IACjB,UAAU;QACR,UAAU;QACV,eAAe;QACf,UAAU;QACV,iBAAiB;IACnB;AACF;AAKO,MAAM,qBAAqB;IAChC,aAAa;IACb,aAAa,EAAE;IACf,kBAAkB;IAClB,eAAe;AACjB;AAKO,MAAM,mBAAmB;IAC9B,eAAe;IACf,gBAAgB;IAChB,aAAa;IACb,cAAc;IACd,eAAe,EAAE;IACjB,QAAQ,CAAC;AACX;AASO,MAAM,kBAAkB,IAAI,SAAS;AAMrC,MAAM,0BAA0B,GAAG,YAAY;AAK/C,MAAM,6BAA6B,OAAO,WAAW;AASrD,MAAM,sBAAsB;IACjC,MAAM;QACJ,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,SAAS;QACT,SAAS;IACX;AACF;AAMO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,cAAc;QACd,eAAe;QACf,iBAAiB;QACjB,sBAAsB;QACtB,uBAAuB;QACvB,cAAc;IAChB;IACA,KAAK;QACH,sBAAsB;QACtB,mBAAmB;QACnB,eAAe;QACf,eAAe;IACjB;AACF;AAMO,MAAM,mBAAmB;IAC9B,MAAM;QACJ,eAAe;QACf,gBAAgB;QAChB,iBAAiB;IACnB;IACA,KAAK;QACH,gBAAgB;QAChB,eAAe;IACjB;AACF;AASO,MAAM,iBAAiB;IAC5B,MAAM;IACN,KAAK;AACP;AASO,MAAM,aAAa;IACxB,iBAAiB,oDAAyB;IAC1C,gBAAgB,oDAAyB;IACzC,gBAAgB;AAClB;AASO,MAAM,gBAAgB;IAC3B,kBAAkB;IAClB,sBAAsB;IACtB,kBAAkB;IAClB,yBAAyB;IACzB,0BAA0B;IAC1B,qBAAqB;AACvB;AASO,MAAM,mBAAmB;IAC9B,OAAO;IACP,qBAAqB;IACrB,iBAAiB;IACjB,iBAAiB;IACjB,eAAe;IACf,eAAe;IACf,sBAAsB;IACtB,sBAAsB;AACxB"}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-store.ts"], "sourcesContent": ["/**\n * Authentication Store\n * Manages user authentication state, tokens, and session\n */\n\nimport { create } from 'zustand';\nimport {\n  AuthStore,\n  SystemUser,\n  AuthTokens,\n  SystemUserRole\n} from './types';\nimport {\n  createStoreWithMiddleware,\n  createBaseStoreActions,\n  extractErrorMessage,\n  isTokenExpired,\n  getTokenExpiration,\n  clearStoredTokens,\n  isSessionValid,\n  logStoreAction\n} from './utils';\nimport {\n  STORE_NAMES,\n  STORAGE_KEYS,\n  SESSION_TIMEOUT,\n  TOKEN_REFRESH_THRESHOLD,\n  STORE_API_ENDPOINTS,\n  ERROR_MESSAGES,\n  SUCCESS_MESSAGES,\n  STORE_VERSIONS\n} from './constants';\n\n// ============================================================================\n// Initial State\n// ============================================================================\n\nconst initialAuthState = {\n  // Base store state\n  _hasHydrated: false,\n\n  // User data\n  user: null,\n  tokens: null,\n\n  // Authentication status\n  isAuthenticated: false,\n  isLoading: false,\n\n  // Error handling\n  error: null,\n\n  // Session management\n  lastActivity: Date.now(),\n  sessionTimeout: SESSION_TIMEOUT,\n};\n\n// ============================================================================\n// Store Implementation\n// ============================================================================\n\n/**\n * Authentication Store Creator\n */\nconst createAuthStore = () => {\n  return create<AuthStore>()(\n    createStoreWithMiddleware<AuthStore>(\n      (set, get) => ({\n        ...initialAuthState,\n\n        // Base store actions\n        ...createBaseStoreActions<AuthStore>(set),\n\n        // ========================================================================\n        // Authentication Actions\n        // ========================================================================\n\n        /**\n         * Login user with email and password\n         */\n        login: async (email: string, password: string) => {\n          logStoreAction(STORE_NAMES.AUTH, 'login', { email });\n\n          set({ isLoading: true, error: null });\n\n          try {\n            const response = await fetch(STORE_API_ENDPOINTS.AUTH.LOGIN, {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json',\n              },\n              body: JSON.stringify({ email, password }),\n            });\n\n            if (!response.ok) {\n              const errorData = await response.json().catch(() => ({}));\n              throw new Error(errorData.message || ERROR_MESSAGES.AUTH.LOGIN_FAILED);\n            }\n\n            const data = await response.json();\n\n            if (data.success && data.data) {\n              const { user, accessToken, refreshToken } = data.data;\n\n              // Create tokens object\n              const tokens: AuthTokens = {\n                accessToken,\n                refreshToken,\n                expiresAt: getTokenExpiration(accessToken),\n              };\n\n              // Update store state\n              set({\n                user,\n                tokens,\n                isAuthenticated: true,\n                isLoading: false,\n                error: null,\n                lastActivity: Date.now(),\n              });\n\n              logStoreAction(STORE_NAMES.AUTH, 'login_success', { userId: user.id });\n            } else {\n              throw new Error(data.message || ERROR_MESSAGES.AUTH.LOGIN_FAILED);\n            }\n          } catch (error) {\n            const errorMessage = extractErrorMessage(error);\n            logStoreAction(STORE_NAMES.AUTH, 'login_error', { error: errorMessage });\n\n            set({\n              isLoading: false,\n              error: errorMessage,\n              isAuthenticated: false,\n              user: null,\n              tokens: null,\n            });\n\n            throw error;\n          }\n        },\n\n        /**\n         * Logout user from current session\n         */\n        logout: async () => {\n          logStoreAction(STORE_NAMES.AUTH, 'logout');\n\n          const { tokens } = get();\n\n          try {\n            // Call logout API if we have tokens\n            if (tokens?.accessToken) {\n              await fetch(STORE_API_ENDPOINTS.AUTH.LOGOUT, {\n                method: 'POST',\n                headers: {\n                  'Content-Type': 'application/json',\n                  'Authorization': `Bearer ${tokens.accessToken}`,\n                },\n              });\n            }\n          } catch (error) {\n            // Log error but don't prevent logout\n            logStoreAction(STORE_NAMES.AUTH, 'logout_api_error', { error: extractErrorMessage(error) });\n          }\n\n          // Clear local state regardless of API call result\n          clearStoredTokens();\n          set({\n            user: null,\n            tokens: null,\n            isAuthenticated: false,\n            error: null,\n            lastActivity: Date.now(),\n          });\n\n          logStoreAction(STORE_NAMES.AUTH, 'logout_success');\n        },\n\n        /**\n         * Logout user from all devices\n         */\n        logoutAll: async () => {\n          logStoreAction(STORE_NAMES.AUTH, 'logout_all');\n\n          const { tokens } = get();\n\n          try {\n            if (tokens?.accessToken) {\n              await fetch(STORE_API_ENDPOINTS.AUTH.LOGOUT_ALL, {\n                method: 'POST',\n                headers: {\n                  'Content-Type': 'application/json',\n                  'Authorization': `Bearer ${tokens.accessToken}`,\n                },\n              });\n            }\n          } catch (error) {\n            logStoreAction(STORE_NAMES.AUTH, 'logout_all_api_error', { error: extractErrorMessage(error) });\n          }\n\n          // Clear local state\n          clearStoredTokens();\n          set({\n            user: null,\n            tokens: null,\n            isAuthenticated: false,\n            error: null,\n            lastActivity: Date.now(),\n          });\n\n          logStoreAction(STORE_NAMES.AUTH, 'logout_all_success');\n        },\n\n        // ========================================================================\n        // User Profile Actions\n        // ========================================================================\n\n        /**\n         * Update user profile\n         */\n        updateProfile: async (data: Partial<SystemUser>) => {\n          logStoreAction(STORE_NAMES.AUTH, 'update_profile', data);\n\n          const { tokens, user } = get();\n\n          if (!tokens?.accessToken || !user) {\n            throw new Error(ERROR_MESSAGES.AUTH.UNAUTHORIZED);\n          }\n\n          set({ isLoading: true, error: null });\n\n          try {\n            const response = await fetch(STORE_API_ENDPOINTS.AUTH.PROFILE, {\n              method: 'PUT',\n              headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${tokens.accessToken}`,\n              },\n              body: JSON.stringify(data),\n            });\n\n            if (!response.ok) {\n              const errorData = await response.json().catch(() => ({}));\n              throw new Error(errorData.message || ERROR_MESSAGES.AUTH.PROFILE_UPDATE_FAILED);\n            }\n\n            const responseData = await response.json();\n\n            if (responseData.success && responseData.data) {\n              set({\n                user: { ...user, ...responseData.data },\n                isLoading: false,\n                error: null,\n                lastActivity: Date.now(),\n              });\n\n              logStoreAction(STORE_NAMES.AUTH, 'update_profile_success');\n            } else {\n              throw new Error(responseData.message || ERROR_MESSAGES.AUTH.PROFILE_UPDATE_FAILED);\n            }\n          } catch (error) {\n            const errorMessage = extractErrorMessage(error);\n            logStoreAction(STORE_NAMES.AUTH, 'update_profile_error', { error: errorMessage });\n\n            set({\n              isLoading: false,\n              error: errorMessage,\n            });\n\n            throw error;\n          }\n        },\n\n        /**\n         * Refresh authentication tokens\n         */\n        refreshTokens: async () => {\n          logStoreAction(STORE_NAMES.AUTH, 'refresh_tokens');\n\n          const { tokens } = get();\n\n          if (!tokens?.refreshToken) {\n            throw new Error(ERROR_MESSAGES.AUTH.TOKEN_REFRESH_FAILED);\n          }\n\n          try {\n            const response = await fetch(STORE_API_ENDPOINTS.AUTH.REFRESH, {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json',\n              },\n              body: JSON.stringify({ refreshToken: tokens.refreshToken }),\n            });\n\n            if (!response.ok) {\n              throw new Error(ERROR_MESSAGES.AUTH.TOKEN_REFRESH_FAILED);\n            }\n\n            const data = await response.json();\n\n            if (data.success && data.data) {\n              const { accessToken, refreshToken } = data.data;\n\n              const newTokens: AuthTokens = {\n                accessToken,\n                refreshToken,\n                expiresAt: getTokenExpiration(accessToken),\n              };\n\n              set({\n                tokens: newTokens,\n                lastActivity: Date.now(),\n              });\n\n              logStoreAction(STORE_NAMES.AUTH, 'refresh_tokens_success');\n            } else {\n              throw new Error(data.message || ERROR_MESSAGES.AUTH.TOKEN_REFRESH_FAILED);\n            }\n          } catch (error) {\n            const errorMessage = extractErrorMessage(error);\n            logStoreAction(STORE_NAMES.AUTH, 'refresh_tokens_error', { error: errorMessage });\n\n            // If refresh fails, logout user\n            get().logout();\n            throw error;\n          }\n        },\n\n        // ========================================================================\n        // State Management Actions\n        // ========================================================================\n\n        /**\n         * Set user data\n         */\n        setUser: (user: SystemUser | null) => {\n          set({ user, isAuthenticated: !!user });\n          logStoreAction(STORE_NAMES.AUTH, 'set_user', { userId: user?.id });\n        },\n\n        /**\n         * Set authentication tokens\n         */\n        setTokens: (tokens: AuthTokens | null) => {\n          set({ tokens });\n          logStoreAction(STORE_NAMES.AUTH, 'set_tokens', { hasTokens: !!tokens });\n        },\n\n        /**\n         * Set loading state\n         */\n        setLoading: (loading: boolean) => {\n          set({ isLoading: loading });\n        },\n\n        /**\n         * Set error message\n         */\n        setError: (error: string | null) => {\n          set({ error });\n          if (error) {\n            logStoreAction(STORE_NAMES.AUTH, 'set_error', { error });\n          }\n        },\n\n        /**\n         * Clear error message\n         */\n        clearError: () => {\n          set({ error: null });\n        },\n\n        // ========================================================================\n        // Session Management Actions\n        // ========================================================================\n\n        /**\n         * Update last activity timestamp\n         */\n        updateLastActivity: () => {\n          set({ lastActivity: Date.now() });\n        },\n\n        /**\n         * Check if current session is valid\n         */\n        checkSession: () => {\n          const { lastActivity, sessionTimeout, tokens } = get();\n\n          // Check session timeout\n          if (!isSessionValid(lastActivity, sessionTimeout)) {\n            logStoreAction(STORE_NAMES.AUTH, 'session_expired');\n            get().logout();\n            return false;\n          }\n\n          // Check token expiration\n          if (tokens && isTokenExpired(tokens.expiresAt)) {\n            logStoreAction(STORE_NAMES.AUTH, 'token_expired');\n\n            // Try to refresh tokens\n            get().refreshTokens().catch(() => {\n              // If refresh fails, logout will be called automatically\n            });\n\n            return false;\n          }\n\n          return true;\n        },\n\n        /**\n         * Hydrate store from persisted state\n         */\n        hydrate: () => {\n          const state = get();\n\n          // Validate persisted session\n          if (state.isAuthenticated && state.user && state.tokens) {\n            const isValid = state.checkSession();\n            if (!isValid) {\n              // Session is invalid, clear state\n              set({\n                user: null,\n                tokens: null,\n                isAuthenticated: false,\n                error: null,\n              });\n            }\n          }\n\n          set({ _hasHydrated: true });\n          logStoreAction(STORE_NAMES.AUTH, 'hydrated');\n        },\n      }),\n      {\n        persist: {\n          name: STORAGE_KEYS.AUTH,\n          version: STORE_VERSIONS.AUTH,\n          partialize: (state) => ({\n            user: state.user,\n            tokens: state.tokens,\n            isAuthenticated: state.isAuthenticated,\n            lastActivity: state.lastActivity,\n            sessionTimeout: state.sessionTimeout,\n          }),\n        },\n        devtools: {\n          name: STORE_NAMES.AUTH,\n          enabled: process.env.NODE_ENV === 'development',\n        },\n      }\n    )\n  );\n};\n\n// ============================================================================\n// Export Store\n// ============================================================================\n\nexport const useAuthStore = createAuthStore();\n\n// Export store for testing and advanced usage\nexport { createAuthStore };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AASD;AAUA;AAjBA;AA4bmB;;;;AAhanB,+EAA+E;AAC/E,gBAAgB;AAChB,+EAA+E;AAE/E,MAAM,mBAAmB;IACvB,mBAAmB;IACnB,cAAc;IAEd,YAAY;IACZ,MAAM;IACN,QAAQ;IAER,wBAAwB;IACxB,iBAAiB;IACjB,WAAW;IAEX,iBAAiB;IACjB,OAAO;IAEP,qBAAqB;IACrB,cAAc,KAAK,GAAG;IACtB,gBAAgB,6HAAA,CAAA,kBAAe;AACjC;AAEA,+EAA+E;AAC/E,uBAAuB;AACvB,+EAA+E;AAE/E;;CAEC,GACD,MAAM,kBAAkB;IACtB,OAAO,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IACV,CAAA,GAAA,yHAAA,CAAA,4BAAyB,AAAD,EACtB,CAAC,KAAK,MAAQ,CAAC;YACb,GAAG,gBAAgB;YAEnB,qBAAqB;YACrB,GAAG,CAAA,GAAA,yHAAA,CAAA,yBAAsB,AAAD,EAAa,IAAI;YAEzC,2EAA2E;YAC3E,yBAAyB;YACzB,2EAA2E;YAE3E;;SAEC,GACD,OAAO,OAAO,OAAe;gBAC3B,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,SAAS;oBAAE;gBAAM;gBAElD,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBAEnC,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,6HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,KAAK,EAAE;wBAC3D,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BAAE;4BAAO;wBAAS;oBACzC;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;wBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,6HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,YAAY;oBACvE;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;wBAC7B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,KAAK,IAAI;wBAErD,uBAAuB;wBACvB,MAAM,SAAqB;4BACzB;4BACA;4BACA,WAAW,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE;wBAChC;wBAEA,qBAAqB;wBACrB,IAAI;4BACF;4BACA;4BACA,iBAAiB;4BACjB,WAAW;4BACX,OAAO;4BACP,cAAc,KAAK,GAAG;wBACxB;wBAEA,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,iBAAiB;4BAAE,QAAQ,KAAK,EAAE;wBAAC;oBACtE,OAAO;wBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI,6HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,YAAY;oBAClE;gBACF,EAAE,OAAO,OAAO;oBACd,MAAM,eAAe,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;oBACzC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,eAAe;wBAAE,OAAO;oBAAa;oBAEtE,IAAI;wBACF,WAAW;wBACX,OAAO;wBACP,iBAAiB;wBACjB,MAAM;wBACN,QAAQ;oBACV;oBAEA,MAAM;gBACR;YACF;YAEA;;SAEC,GACD,QAAQ;gBACN,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;gBAEjC,MAAM,EAAE,MAAM,EAAE,GAAG;gBAEnB,IAAI;oBACF,oCAAoC;oBACpC,IAAI,QAAQ,aAAa;wBACvB,MAAM,MAAM,6HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE;4BAC3C,QAAQ;4BACR,SAAS;gCACP,gBAAgB;gCAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,WAAW,EAAE;4BACjD;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,qCAAqC;oBACrC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,oBAAoB;wBAAE,OAAO,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;oBAAO;gBAC3F;gBAEA,kDAAkD;gBAClD,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD;gBAChB,IAAI;oBACF,MAAM;oBACN,QAAQ;oBACR,iBAAiB;oBACjB,OAAO;oBACP,cAAc,KAAK,GAAG;gBACxB;gBAEA,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;YACnC;YAEA;;SAEC,GACD,WAAW;gBACT,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;gBAEjC,MAAM,EAAE,MAAM,EAAE,GAAG;gBAEnB,IAAI;oBACF,IAAI,QAAQ,aAAa;wBACvB,MAAM,MAAM,6HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,UAAU,EAAE;4BAC/C,QAAQ;4BACR,SAAS;gCACP,gBAAgB;gCAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,WAAW,EAAE;4BACjD;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,wBAAwB;wBAAE,OAAO,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;oBAAO;gBAC/F;gBAEA,oBAAoB;gBACpB,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD;gBAChB,IAAI;oBACF,MAAM;oBACN,QAAQ;oBACR,iBAAiB;oBACjB,OAAO;oBACP,cAAc,KAAK,GAAG;gBACxB;gBAEA,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;YACnC;YAEA,2EAA2E;YAC3E,uBAAuB;YACvB,2EAA2E;YAE3E;;SAEC,GACD,eAAe,OAAO;gBACpB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,kBAAkB;gBAEnD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;gBAEzB,IAAI,CAAC,QAAQ,eAAe,CAAC,MAAM;oBACjC,MAAM,IAAI,MAAM,6HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,YAAY;gBAClD;gBAEA,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBAEnC,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,6HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,OAAO,EAAE;wBAC7D,QAAQ;wBACR,SAAS;4BACP,gBAAgB;4BAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,WAAW,EAAE;wBACjD;wBACA,MAAM,KAAK,SAAS,CAAC;oBACvB;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;wBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,6HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,qBAAqB;oBAChF;oBAEA,MAAM,eAAe,MAAM,SAAS,IAAI;oBAExC,IAAI,aAAa,OAAO,IAAI,aAAa,IAAI,EAAE;wBAC7C,IAAI;4BACF,MAAM;gCAAE,GAAG,IAAI;gCAAE,GAAG,aAAa,IAAI;4BAAC;4BACtC,WAAW;4BACX,OAAO;4BACP,cAAc,KAAK,GAAG;wBACxB;wBAEA,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;oBACnC,OAAO;wBACL,MAAM,IAAI,MAAM,aAAa,OAAO,IAAI,6HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,qBAAqB;oBACnF;gBACF,EAAE,OAAO,OAAO;oBACd,MAAM,eAAe,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;oBACzC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,wBAAwB;wBAAE,OAAO;oBAAa;oBAE/E,IAAI;wBACF,WAAW;wBACX,OAAO;oBACT;oBAEA,MAAM;gBACR;YACF;YAEA;;SAEC,GACD,eAAe;gBACb,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;gBAEjC,MAAM,EAAE,MAAM,EAAE,GAAG;gBAEnB,IAAI,CAAC,QAAQ,cAAc;oBACzB,MAAM,IAAI,MAAM,6HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,oBAAoB;gBAC1D;gBAEA,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,6HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,OAAO,EAAE;wBAC7D,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BAAE,cAAc,OAAO,YAAY;wBAAC;oBAC3D;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,IAAI,MAAM,6HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,oBAAoB;oBAC1D;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;wBAC7B,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,KAAK,IAAI;wBAE/C,MAAM,YAAwB;4BAC5B;4BACA;4BACA,WAAW,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE;wBAChC;wBAEA,IAAI;4BACF,QAAQ;4BACR,cAAc,KAAK,GAAG;wBACxB;wBAEA,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;oBACnC,OAAO;wBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI,6HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,oBAAoB;oBAC1E;gBACF,EAAE,OAAO,OAAO;oBACd,MAAM,eAAe,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;oBACzC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,wBAAwB;wBAAE,OAAO;oBAAa;oBAE/E,gCAAgC;oBAChC,MAAM,MAAM;oBACZ,MAAM;gBACR;YACF;YAEA,2EAA2E;YAC3E,2BAA2B;YAC3B,2EAA2E;YAE3E;;SAEC,GACD,SAAS,CAAC;gBACR,IAAI;oBAAE;oBAAM,iBAAiB,CAAC,CAAC;gBAAK;gBACpC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,YAAY;oBAAE,QAAQ,MAAM;gBAAG;YAClE;YAEA;;SAEC,GACD,WAAW,CAAC;gBACV,IAAI;oBAAE;gBAAO;gBACb,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,cAAc;oBAAE,WAAW,CAAC,CAAC;gBAAO;YACvE;YAEA;;SAEC,GACD,YAAY,CAAC;gBACX,IAAI;oBAAE,WAAW;gBAAQ;YAC3B;YAEA;;SAEC,GACD,UAAU,CAAC;gBACT,IAAI;oBAAE;gBAAM;gBACZ,IAAI,OAAO;oBACT,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,aAAa;wBAAE;oBAAM;gBACxD;YACF;YAEA;;SAEC,GACD,YAAY;gBACV,IAAI;oBAAE,OAAO;gBAAK;YACpB;YAEA,2EAA2E;YAC3E,6BAA6B;YAC7B,2EAA2E;YAE3E;;SAEC,GACD,oBAAoB;gBAClB,IAAI;oBAAE,cAAc,KAAK,GAAG;gBAAG;YACjC;YAEA;;SAEC,GACD,cAAc;gBACZ,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,MAAM,EAAE,GAAG;gBAEjD,wBAAwB;gBACxB,IAAI,CAAC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,iBAAiB;oBACjD,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;oBACjC,MAAM,MAAM;oBACZ,OAAO;gBACT;gBAEA,yBAAyB;gBACzB,IAAI,UAAU,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,SAAS,GAAG;oBAC9C,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;oBAEjC,wBAAwB;oBACxB,MAAM,aAAa,GAAG,KAAK,CAAC;oBAC1B,wDAAwD;oBAC1D;oBAEA,OAAO;gBACT;gBAEA,OAAO;YACT;YAEA;;SAEC,GACD,SAAS;gBACP,MAAM,QAAQ;gBAEd,6BAA6B;gBAC7B,IAAI,MAAM,eAAe,IAAI,MAAM,IAAI,IAAI,MAAM,MAAM,EAAE;oBACvD,MAAM,UAAU,MAAM,YAAY;oBAClC,IAAI,CAAC,SAAS;wBACZ,kCAAkC;wBAClC,IAAI;4BACF,MAAM;4BACN,QAAQ;4BACR,iBAAiB;4BACjB,OAAO;wBACT;oBACF;gBACF;gBAEA,IAAI;oBAAE,cAAc;gBAAK;gBACzB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;YACnC;QACF,CAAC,GACD;QACE,SAAS;YACP,MAAM,6HAAA,CAAA,eAAY,CAAC,IAAI;YACvB,SAAS,6HAAA,CAAA,iBAAc,CAAC,IAAI;YAC5B,YAAY,CAAC,QAAU,CAAC;oBACtB,MAAM,MAAM,IAAI;oBAChB,QAAQ,MAAM,MAAM;oBACpB,iBAAiB,MAAM,eAAe;oBACtC,cAAc,MAAM,YAAY;oBAChC,gBAAgB,MAAM,cAAc;gBACtC,CAAC;QACH;QACA,UAAU;YACR,MAAM,6HAAA,CAAA,cAAW,CAAC,IAAI;YACtB,SAAS,oDAAyB;QACpC;IACF;AAGN;AAMO,MAAM,eAAe"}}, {"offset": {"line": 782, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts"], "sourcesContent": ["/**\n * Authentication Hooks\n * Custom hooks for easy authentication state access\n */\n\n'use client';\n\nimport { useCallback, useEffect } from 'react';\nimport { useAuthStore } from './auth-store';\nimport type { SystemUser, SystemUserRole } from './types';\nimport { ACTIVITY_TRACKING_INTERVAL } from './constants';\n\n// ============================================================================\n// Basic Authentication Hooks\n// ============================================================================\n\n/**\n * Hook to get current user\n */\nexport const useUser = () => {\n  return useAuthStore((state) => state.user);\n};\n\n/**\n * Hook to get authentication status\n */\nexport const useIsAuthenticated = () => {\n  return useAuthStore((state) => state.isAuthenticated);\n};\n\n/**\n * Hook to get authentication loading state\n */\nexport const useAuthLoading = () => {\n  return useAuthStore((state) => state.isLoading);\n};\n\n/**\n * Hook to get authentication error\n */\nexport const useAuthError = () => {\n  return useAuthStore((state) => state.error);\n};\n\n/**\n * Hook to get authentication tokens\n */\nexport const useAuthTokens = () => {\n  return useAuthStore((state) => state.tokens);\n};\n\n// ============================================================================\n// Authentication Action Hooks\n// ============================================================================\n\n/**\n * Hook to get login function\n */\nexport const useLogin = () => {\n  return useAuthStore((state) => state.login);\n};\n\n/**\n * Hook to get logout function\n */\nexport const useLogout = () => {\n  return useAuthStore((state) => state.logout);\n};\n\n/**\n * Hook to get logout all function\n */\nexport const useLogoutAll = () => {\n  return useAuthStore((state) => state.logoutAll);\n};\n\n/**\n * Hook to get update profile function\n */\nexport const useUpdateProfile = () => {\n  return useAuthStore((state) => state.updateProfile);\n};\n\n/**\n * Hook to get refresh tokens function\n */\nexport const useRefreshTokens = () => {\n  return useAuthStore((state) => state.refreshTokens);\n};\n\n// ============================================================================\n// Utility Hooks\n// ============================================================================\n\n/**\n * Hook to clear authentication error\n */\nexport const useClearAuthError = () => {\n  return useAuthStore((state) => state.clearError);\n};\n\n/**\n * Hook to check session validity\n */\nexport const useCheckSession = () => {\n  return useAuthStore((state) => state.checkSession);\n};\n\n/**\n * Hook to update last activity\n */\nexport const useUpdateActivity = () => {\n  return useAuthStore((state) => state.updateLastActivity);\n};\n\n// ============================================================================\n// Role-based Hooks\n// ============================================================================\n\n/**\n * Hook to check if user has specific role\n */\nexport const useHasRole = (role: SystemUserRole) => {\n  const user = useUser();\n  return user?.role === role;\n};\n\n/**\n * Hook to check if user is admin\n */\nexport const useIsAdmin = () => {\n  return useHasRole('Admin');\n};\n\n/**\n * Hook to check if user is editor\n */\nexport const useIsEditor = () => {\n  return useHasRole('Editor');\n};\n\n/**\n * Hook to check if user is moderator\n */\nexport const useIsModerator = () => {\n  return useHasRole('Moderator');\n};\n\n/**\n * Hook to check if user has admin or editor role\n */\nexport const useCanEdit = () => {\n  const user = useUser();\n  return user?.role === 'Admin' || user?.role === 'Editor';\n};\n\n/**\n * Hook to check if user can perform admin actions\n */\nexport const useCanAdmin = () => {\n  return useIsAdmin();\n};\n\n// ============================================================================\n// Composite Hooks\n// ============================================================================\n\n/**\n * Hook to get complete authentication state\n */\nexport const useAuth = () => {\n  const user = useUser();\n  const isAuthenticated = useIsAuthenticated();\n  const isLoading = useAuthLoading();\n  const error = useAuthError();\n  const tokens = useAuthTokens();\n\n  const login = useLogin();\n  const logout = useLogout();\n  const logoutAll = useLogoutAll();\n  const updateProfile = useUpdateProfile();\n  const clearError = useClearAuthError();\n\n  return {\n    // State\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    tokens,\n\n    // Actions\n    login,\n    logout,\n    logoutAll,\n    updateProfile,\n    clearError,\n\n    // Role checks\n    isAdmin: useIsAdmin(),\n    isEditor: useIsEditor(),\n    isModerator: useIsModerator(),\n    canEdit: useCanEdit(),\n    canAdmin: useCanAdmin(),\n  };\n};\n\n/**\n * Hook for authentication with automatic session management\n */\nexport const useAuthWithSession = () => {\n  const auth = useAuth();\n  const checkSession = useCheckSession();\n  const updateActivity = useUpdateActivity();\n\n  // Auto-check session validity\n  useEffect(() => {\n    if (auth.isAuthenticated) {\n      const interval = setInterval(() => {\n        checkSession();\n      }, ACTIVITY_TRACKING_INTERVAL);\n\n      return () => clearInterval(interval);\n    }\n  }, [auth.isAuthenticated, checkSession]);\n\n  // Update activity on user interaction\n  const handleUserActivity = useCallback(() => {\n    if (auth.isAuthenticated) {\n      updateActivity();\n    }\n  }, [auth.isAuthenticated, updateActivity]);\n\n  // Auto-update activity on mouse/keyboard events\n  useEffect(() => {\n    if (auth.isAuthenticated) {\n      const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];\n\n      events.forEach(event => {\n        document.addEventListener(event, handleUserActivity, true);\n      });\n\n      return () => {\n        events.forEach(event => {\n          document.removeEventListener(event, handleUserActivity, true);\n        });\n      };\n    }\n  }, [auth.isAuthenticated, handleUserActivity]);\n\n  return {\n    ...auth,\n    checkSession,\n    updateActivity,\n  };\n};\n\n// ============================================================================\n// Permission Hooks\n// ============================================================================\n\n/**\n * Hook to check multiple permissions\n */\nexport const usePermissions = (requiredRoles: SystemUserRole[]) => {\n  const user = useUser();\n\n  const hasPermission = useCallback((roles: SystemUserRole[]) => {\n    if (!user) return false;\n    return roles.includes(user.role);\n  }, [user]);\n\n  const hasAnyPermission = hasPermission(requiredRoles);\n\n  return {\n    hasPermission: hasAnyPermission,\n    userRole: user?.role,\n    checkRole: hasPermission,\n  };\n};\n\n/**\n * Hook for route protection\n */\nexport const useRouteProtection = (requiredRoles?: SystemUserRole[]) => {\n  const isAuthenticated = useIsAuthenticated();\n  const user = useUser();\n  const isLoading = useAuthLoading();\n\n  const hasAccess = useCallback(() => {\n    if (!isAuthenticated) return false;\n    if (!requiredRoles || requiredRoles.length === 0) return true;\n    if (!user) return false;\n\n    return requiredRoles.includes(user.role);\n  }, [isAuthenticated, user, requiredRoles]);\n\n  return {\n    isAuthenticated,\n    hasAccess: hasAccess(),\n    isLoading,\n    user,\n    shouldRedirect: !isLoading && !isAuthenticated,\n    shouldShowUnauthorized: !isLoading && isAuthenticated && !hasAccess(),\n  };\n};\n\n// ============================================================================\n// Development Hooks\n// ============================================================================\n\n/**\n * Hook for development/debugging authentication state\n */\nexport const useAuthDebug = () => {\n  const state = useAuthStore((state) => state);\n\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  return {\n    fullState: state,\n    hasHydrated: state._hasHydrated,\n    lastActivity: new Date(state.lastActivity).toISOString(),\n    sessionTimeout: state.sessionTimeout,\n    tokenExpiry: state.tokens ? new Date(state.tokens.expiresAt).toISOString() : null,\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;AAID;AACA;AAEA;AAmTM;;AAxTN;;;;AAcO,MAAM,UAAU;;IACrB,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;gCAAE,CAAC,QAAU,MAAM,IAAI;;AAC3C;GAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,qBAAqB;;IAChC,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;2CAAE,CAAC,QAAU,MAAM,eAAe;;AACtD;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,iBAAiB;;IAC5B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;uCAAE,CAAC,QAAU,MAAM,SAAS;;AAChD;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,eAAe;;IAC1B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;qCAAE,CAAC,QAAU,MAAM,KAAK;;AAC5C;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,gBAAgB;;IAC3B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;sCAAE,CAAC,QAAU,MAAM,MAAM;;AAC7C;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAUd,MAAM,WAAW;;IACtB,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;iCAAE,CAAC,QAAU,MAAM,KAAK;;AAC5C;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,YAAY;;IACvB,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;kCAAE,CAAC,QAAU,MAAM,MAAM;;AAC7C;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,eAAe;;IAC1B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;qCAAE,CAAC,QAAU,MAAM,SAAS;;AAChD;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,mBAAmB;;IAC9B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;yCAAE,CAAC,QAAU,MAAM,aAAa;;AACpD;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,mBAAmB;;IAC9B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;yCAAE,CAAC,QAAU,MAAM,aAAa;;AACpD;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAUd,MAAM,oBAAoB;;IAC/B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;0CAAE,CAAC,QAAU,MAAM,UAAU;;AACjD;KAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,kBAAkB;;IAC7B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;wCAAE,CAAC,QAAU,MAAM,YAAY;;AACnD;KAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,oBAAoB;;IAC/B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;0CAAE,CAAC,QAAU,MAAM,kBAAkB;;AACzD;KAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAUd,MAAM,aAAa,CAAC;;IACzB,MAAM,OAAO;IACb,OAAO,MAAM,SAAS;AACxB;KAHa;;QACE;;;AAOR,MAAM,aAAa;;IACxB,OAAO,WAAW;AACpB;KAFa;;QACJ;;;AAMF,MAAM,cAAc;;IACzB,OAAO,WAAW;AACpB;KAFa;;QACJ;;;AAMF,MAAM,iBAAiB;;IAC5B,OAAO,WAAW;AACpB;KAFa;;QACJ;;;AAMF,MAAM,aAAa;;IACxB,MAAM,OAAO;IACb,OAAO,MAAM,SAAS,WAAW,MAAM,SAAS;AAClD;KAHa;;QACE;;;AAOR,MAAM,cAAc;;IACzB,OAAO;AACT;KAFa;;QACJ;;;AAUF,MAAM,UAAU;;IACrB,MAAM,OAAO;IACb,MAAM,kBAAkB;IACxB,MAAM,YAAY;IAClB,MAAM,QAAQ;IACd,MAAM,SAAS;IAEf,MAAM,QAAQ;IACd,MAAM,SAAS;IACf,MAAM,YAAY;IAClB,MAAM,gBAAgB;IACtB,MAAM,aAAa;IAEnB,OAAO;QACL,QAAQ;QACR;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA;QAEA,cAAc;QACd,SAAS;QACT,UAAU;QACV,aAAa;QACb,SAAS;QACT,UAAU;IACZ;AACF;KAnCa;;QACE;QACW;QACN;QACJ;QACC;QAED;QACC;QACG;QACI;QACH;QAkBR;QACC;QACG;QACJ;QACC;;;AAOP,MAAM,qBAAqB;;IAChC,MAAM,OAAO;IACb,MAAM,eAAe;IACrB,MAAM,iBAAiB;IAEvB,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,KAAK,eAAe,EAAE;gBACxB,MAAM,WAAW;6DAAY;wBAC3B;oBACF;4DAAG,6HAAA,CAAA,6BAA0B;gBAE7B;oDAAO,IAAM,cAAc;;YAC7B;QACF;uCAAG;QAAC,KAAK,eAAe;QAAE;KAAa;IAEvC,sCAAsC;IACtC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACrC,IAAI,KAAK,eAAe,EAAE;gBACxB;YACF;QACF;6DAAG;QAAC,KAAK,eAAe;QAAE;KAAe;IAEzC,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,KAAK,eAAe,EAAE;gBACxB,MAAM,SAAS;oBAAC;oBAAa;oBAAa;oBAAY;oBAAU;iBAAa;gBAE7E,OAAO,OAAO;oDAAC,CAAA;wBACb,SAAS,gBAAgB,CAAC,OAAO,oBAAoB;oBACvD;;gBAEA;oDAAO;wBACL,OAAO,OAAO;4DAAC,CAAA;gCACb,SAAS,mBAAmB,CAAC,OAAO,oBAAoB;4BAC1D;;oBACF;;YACF;QACF;uCAAG;QAAC,KAAK,eAAe;QAAE;KAAmB;IAE7C,OAAO;QACL,GAAG,IAAI;QACP;QACA;IACF;AACF;KA7Ca;;QACE;QACQ;QACE;;;AAmDlB,MAAM,iBAAiB,CAAC;;IAC7B,MAAM,OAAO;IAEb,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACjC,IAAI,CAAC,MAAM,OAAO;YAClB,OAAO,MAAM,QAAQ,CAAC,KAAK,IAAI;QACjC;oDAAG;QAAC;KAAK;IAET,MAAM,mBAAmB,cAAc;IAEvC,OAAO;QACL,eAAe;QACf,UAAU,MAAM;QAChB,WAAW;IACb;AACF;KAfa;;QACE;;;AAmBR,MAAM,qBAAqB,CAAC;;IACjC,MAAM,kBAAkB;IACxB,MAAM,OAAO;IACb,MAAM,YAAY;IAElB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC5B,IAAI,CAAC,iBAAiB,OAAO;YAC7B,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG,OAAO;YACzD,IAAI,CAAC,MAAM,OAAO;YAElB,OAAO,cAAc,QAAQ,CAAC,KAAK,IAAI;QACzC;oDAAG;QAAC;QAAiB;QAAM;KAAc;IAEzC,OAAO;QACL;QACA,WAAW;QACX;QACA;QACA,gBAAgB,CAAC,aAAa,CAAC;QAC/B,wBAAwB,CAAC,aAAa,mBAAmB,CAAC;IAC5D;AACF;KArBa;;QACa;QACX;QACK;;;AA2Bb,MAAM,eAAe;;IAC1B,MAAM,QAAQ,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;4CAAE,CAAC,QAAU;;IAEtC,uCAA4C;;IAE5C;IAEA,OAAO;QACL,WAAW;QACX,aAAa,MAAM,YAAY;QAC/B,cAAc,IAAI,KAAK,MAAM,YAAY,EAAE,WAAW;QACtD,gBAAgB,MAAM,cAAc;QACpC,aAAa,MAAM,MAAM,GAAG,IAAI,KAAK,MAAM,MAAM,CAAC,SAAS,EAAE,WAAW,KAAK;IAC/E;AACF;KAda;;QACG,iIAAA,CAAA,eAAY"}}, {"offset": {"line": 1235, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1241, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-utils.ts"], "sourcesContent": ["/**\n * Authentication Utilities\n * Helper functions for authentication operations\n */\n\nimport type { SystemUser, SystemUserRole, AuthTokens } from './types';\nimport { useAuthStore } from './auth-store';\n\n// ============================================================================\n// Token Utilities\n// ============================================================================\n\n/**\n * Get current access token\n */\nexport const getAccessToken = (): string | null => {\n  const tokens = useAuthStore.getState().tokens;\n  return tokens?.accessToken || null;\n};\n\n/**\n * Get current refresh token\n */\nexport const getRefreshToken = (): string | null => {\n  const tokens = useAuthStore.getState().tokens;\n  return tokens?.refreshToken || null;\n};\n\n/**\n * Get authorization header for API requests\n */\nexport const getAuthHeader = (): Record<string, string> => {\n  const token = getAccessToken();\n  return token ? { Authorization: `Bearer ${token}` } : {};\n};\n\n/**\n * Check if user is currently authenticated\n */\nexport const isAuthenticated = (): boolean => {\n  return useAuthStore.getState().isAuthenticated;\n};\n\n/**\n * Get current user\n */\nexport const getCurrentUser = (): SystemUser | null => {\n  return useAuthStore.getState().user;\n};\n\n/**\n * Get current user role\n */\nexport const getCurrentUserRole = (): SystemUserRole | null => {\n  const user = getCurrentUser();\n  return user?.role || null;\n};\n\n// ============================================================================\n// Permission Utilities\n// ============================================================================\n\n/**\n * Check if current user has specific role\n */\nexport const hasRole = (role: SystemUserRole): boolean => {\n  const currentRole = getCurrentUserRole();\n  return currentRole === role;\n};\n\n/**\n * Check if current user has any of the specified roles\n */\nexport const hasAnyRole = (roles: SystemUserRole[]): boolean => {\n  const currentRole = getCurrentUserRole();\n  return currentRole ? roles.includes(currentRole) : false;\n};\n\n/**\n * Check if current user is admin\n */\nexport const isAdmin = (): boolean => {\n  return hasRole('Admin');\n};\n\n/**\n * Check if current user is editor\n */\nexport const isEditor = (): boolean => {\n  return hasRole('Editor');\n};\n\n/**\n * Check if current user is moderator\n */\nexport const isModerator = (): boolean => {\n  return hasRole('Moderator');\n};\n\n/**\n * Check if current user can edit content\n */\nexport const canEdit = (): boolean => {\n  return hasAnyRole(['Admin', 'Editor']);\n};\n\n/**\n * Check if current user can perform admin actions\n */\nexport const canAdmin = (): boolean => {\n  return isAdmin();\n};\n\n/**\n * Check if current user can moderate content\n */\nexport const canModerate = (): boolean => {\n  return hasAnyRole(['Admin', 'Editor', 'Moderator']);\n};\n\n// ============================================================================\n// Session Utilities\n// ============================================================================\n\n/**\n * Force session check\n */\nexport const checkSession = (): boolean => {\n  return useAuthStore.getState().checkSession();\n};\n\n/**\n * Update user activity timestamp\n */\nexport const updateActivity = (): void => {\n  useAuthStore.getState().updateLastActivity();\n};\n\n/**\n * Get session remaining time in minutes\n */\nexport const getAuthSessionRemainingTime = (): number => {\n  const { lastActivity, sessionTimeout } = useAuthStore.getState();\n  const now = Date.now();\n  const timeoutMs = sessionTimeout * 60 * 1000;\n  const elapsed = now - lastActivity;\n  const remaining = timeoutMs - elapsed;\n  return Math.max(0, Math.floor(remaining / (60 * 1000)));\n};\n\n/**\n * Check if session will expire soon (within 5 minutes)\n */\nexport const isSessionExpiringSoon = (): boolean => {\n  return getAuthSessionRemainingTime() <= 5;\n};\n\n// ============================================================================\n// Authentication Actions\n// ============================================================================\n\n/**\n * Login with credentials\n */\nexport const login = async (email: string, password: string): Promise<void> => {\n  return useAuthStore.getState().login(email, password);\n};\n\n/**\n * Logout current user\n */\nexport const logout = async (): Promise<void> => {\n  return useAuthStore.getState().logout();\n};\n\n/**\n * Logout from all devices\n */\nexport const logoutAll = async (): Promise<void> => {\n  return useAuthStore.getState().logoutAll();\n};\n\n/**\n * Update user profile\n */\nexport const updateProfile = async (data: Partial<SystemUser>): Promise<void> => {\n  return useAuthStore.getState().updateProfile(data);\n};\n\n/**\n * Refresh authentication tokens\n */\nexport const refreshTokens = async (): Promise<void> => {\n  return useAuthStore.getState().refreshTokens();\n};\n\n// ============================================================================\n// Error Handling Utilities\n// ============================================================================\n\n/**\n * Get current authentication error\n */\nexport const getAuthError = (): string | null => {\n  return useAuthStore.getState().error;\n};\n\n/**\n * Clear authentication error\n */\nexport const clearAuthError = (): void => {\n  useAuthStore.getState().clearError();\n};\n\n/**\n * Check if there's an authentication error\n */\nexport const hasAuthError = (): boolean => {\n  return !!getAuthError();\n};\n\n// ============================================================================\n// API Request Utilities\n// ============================================================================\n\n/**\n * Create authenticated fetch request\n */\nexport const authenticatedFetch = async (\n  url: string,\n  options: RequestInit = {}\n): Promise<Response> => {\n  const authHeaders = getAuthHeader();\n\n  const config: RequestInit = {\n    ...options,\n    headers: {\n      'Content-Type': 'application/json',\n      ...authHeaders,\n      ...options.headers,\n    },\n  };\n\n  const response = await fetch(url, config);\n\n  // Handle token expiration\n  if (response.status === 401) {\n    const isAuth = isAuthenticated();\n    if (isAuth) {\n      // Try to refresh tokens\n      try {\n        await refreshTokens();\n        // Retry the request with new token\n        const newAuthHeaders = getAuthHeader();\n        const retryConfig: RequestInit = {\n          ...config,\n          headers: {\n            ...config.headers,\n            ...newAuthHeaders,\n          },\n        };\n        return fetch(url, retryConfig);\n      } catch (error) {\n        // Refresh failed, logout user\n        await logout();\n        throw new Error('Authentication expired. Please log in again.');\n      }\n    }\n  }\n\n  return response;\n};\n\n/**\n * Create authenticated API request with JSON response\n */\nexport const authenticatedApiRequest = async <T = any>(\n  url: string,\n  options: RequestInit = {}\n): Promise<T> => {\n  const response = await authenticatedFetch(url, options);\n\n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}));\n    throw new Error(errorData.message || `Request failed with status ${response.status}`);\n  }\n\n  return response.json();\n};\n\n// ============================================================================\n// Route Protection Utilities\n// ============================================================================\n\n/**\n * Check if user can access route with required roles\n */\nexport const canAccessRoute = (requiredRoles?: SystemUserRole[]): boolean => {\n  if (!isAuthenticated()) return false;\n  if (!requiredRoles || requiredRoles.length === 0) return true;\n\n  return hasAnyRole(requiredRoles);\n};\n\n/**\n * Get redirect path for unauthenticated users\n */\nexport const getLoginRedirectPath = (currentPath?: string): string => {\n  const loginPath = '/login';\n  if (currentPath && currentPath !== '/') {\n    return `${loginPath}?redirect=${encodeURIComponent(currentPath)}`;\n  }\n  return loginPath;\n};\n\n/**\n * Get redirect path after successful login\n */\nexport const getPostLoginRedirectPath = (searchParams?: URLSearchParams): string => {\n  const redirectParam = searchParams?.get('redirect');\n  return redirectParam || '/dashboard';\n};\n\n// ============================================================================\n// Development Utilities\n// ============================================================================\n\n/**\n * Get full authentication state (development only)\n */\nexport const getAuthState = () => {\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  return useAuthStore.getState();\n};\n\n/**\n * Mock login for development/testing\n */\nexport const mockLogin = (user: SystemUser, tokens: AuthTokens): void => {\n  if (process.env.NODE_ENV !== 'development') {\n    return;\n  }\n\n  useAuthStore.getState().setUser(user);\n  useAuthStore.getState().setTokens(tokens);\n};\n\n/**\n * Reset authentication state (development/testing only)\n */\nexport const resetAuthState = (): void => {\n  if (process.env.NODE_ENV !== 'development') {\n    return;\n  }\n\n  useAuthStore.getState().logout();\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGD;AAqUM;;AA5TC,MAAM,iBAAiB;IAC5B,MAAM,SAAS,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;IAC7C,OAAO,QAAQ,eAAe;AAChC;AAKO,MAAM,kBAAkB;IAC7B,MAAM,SAAS,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;IAC7C,OAAO,QAAQ,gBAAgB;AACjC;AAKO,MAAM,gBAAgB;IAC3B,MAAM,QAAQ;IACd,OAAO,QAAQ;QAAE,eAAe,CAAC,OAAO,EAAE,OAAO;IAAC,IAAI,CAAC;AACzD;AAKO,MAAM,kBAAkB;IAC7B,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,eAAe;AAChD;AAKO,MAAM,iBAAiB;IAC5B,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,IAAI;AACrC;AAKO,MAAM,qBAAqB;IAChC,MAAM,OAAO;IACb,OAAO,MAAM,QAAQ;AACvB;AASO,MAAM,UAAU,CAAC;IACtB,MAAM,cAAc;IACpB,OAAO,gBAAgB;AACzB;AAKO,MAAM,aAAa,CAAC;IACzB,MAAM,cAAc;IACpB,OAAO,cAAc,MAAM,QAAQ,CAAC,eAAe;AACrD;AAKO,MAAM,UAAU;IACrB,OAAO,QAAQ;AACjB;AAKO,MAAM,WAAW;IACtB,OAAO,QAAQ;AACjB;AAKO,MAAM,cAAc;IACzB,OAAO,QAAQ;AACjB;AAKO,MAAM,UAAU;IACrB,OAAO,WAAW;QAAC;QAAS;KAAS;AACvC;AAKO,MAAM,WAAW;IACtB,OAAO;AACT;AAKO,MAAM,cAAc;IACzB,OAAO,WAAW;QAAC;QAAS;QAAU;KAAY;AACpD;AASO,MAAM,eAAe;IAC1B,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,YAAY;AAC7C;AAKO,MAAM,iBAAiB;IAC5B,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,kBAAkB;AAC5C;AAKO,MAAM,8BAA8B;IACzC,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG,iIAAA,CAAA,eAAY,CAAC,QAAQ;IAC9D,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,iBAAiB,KAAK;IACxC,MAAM,UAAU,MAAM;IACtB,MAAM,YAAY,YAAY;IAC9B,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI;AACtD;AAKO,MAAM,wBAAwB;IACnC,OAAO,iCAAiC;AAC1C;AASO,MAAM,QAAQ,OAAO,OAAe;IACzC,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO;AAC9C;AAKO,MAAM,SAAS;IACpB,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;AACvC;AAKO,MAAM,YAAY;IACvB,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,SAAS;AAC1C;AAKO,MAAM,gBAAgB,OAAO;IAClC,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,aAAa,CAAC;AAC/C;AAKO,MAAM,gBAAgB;IAC3B,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,aAAa;AAC9C;AASO,MAAM,eAAe;IAC1B,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,KAAK;AACtC;AAKO,MAAM,iBAAiB;IAC5B,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,UAAU;AACpC;AAKO,MAAM,eAAe;IAC1B,OAAO,CAAC,CAAC;AACX;AASO,MAAM,qBAAqB,OAChC,KACA,UAAuB,CAAC,CAAC;IAEzB,MAAM,cAAc;IAEpB,MAAM,SAAsB;QAC1B,GAAG,OAAO;QACV,SAAS;YACP,gBAAgB;YAChB,GAAG,WAAW;YACd,GAAG,QAAQ,OAAO;QACpB;IACF;IAEA,MAAM,WAAW,MAAM,MAAM,KAAK;IAElC,0BAA0B;IAC1B,IAAI,SAAS,MAAM,KAAK,KAAK;QAC3B,MAAM,SAAS;QACf,IAAI,QAAQ;YACV,wBAAwB;YACxB,IAAI;gBACF,MAAM;gBACN,mCAAmC;gBACnC,MAAM,iBAAiB;gBACvB,MAAM,cAA2B;oBAC/B,GAAG,MAAM;oBACT,SAAS;wBACP,GAAG,OAAO,OAAO;wBACjB,GAAG,cAAc;oBACnB;gBACF;gBACA,OAAO,MAAM,KAAK;YACpB,EAAE,OAAO,OAAO;gBACd,8BAA8B;gBAC9B,MAAM;gBACN,MAAM,IAAI,MAAM;YAClB;QACF;IACF;IAEA,OAAO;AACT;AAKO,MAAM,0BAA0B,OACrC,KACA,UAAuB,CAAC,CAAC;IAEzB,MAAM,WAAW,MAAM,mBAAmB,KAAK;IAE/C,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,2BAA2B,EAAE,SAAS,MAAM,EAAE;IACtF;IAEA,OAAO,SAAS,IAAI;AACtB;AASO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,mBAAmB,OAAO;IAC/B,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG,OAAO;IAEzD,OAAO,WAAW;AACpB;AAKO,MAAM,uBAAuB,CAAC;IACnC,MAAM,YAAY;IAClB,IAAI,eAAe,gBAAgB,KAAK;QACtC,OAAO,GAAG,UAAU,UAAU,EAAE,mBAAmB,cAAc;IACnE;IACA,OAAO;AACT;AAKO,MAAM,2BAA2B,CAAC;IACvC,MAAM,gBAAgB,cAAc,IAAI;IACxC,OAAO,iBAAiB;AAC1B;AASO,MAAM,eAAe;IAC1B,uCAA4C;;IAE5C;IAEA,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ;AAC9B;AAKO,MAAM,YAAY,CAAC,MAAkB;IAC1C,uCAA4C;;IAE5C;IAEA,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,OAAO,CAAC;IAChC,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,SAAS,CAAC;AACpC;AAKO,MAAM,iBAAiB;IAC5B,uCAA4C;;IAE5C;IAEA,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;AAChC"}}, {"offset": {"line": 1464, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1470, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/app-store.ts"], "sourcesContent": ["/**\n * Application Store\n * Manages application state, theme, settings, navigation, and UI state\n */\n\nimport { create } from 'zustand';\nimport { \n  AppStore, \n  ThemeConfig,\n  AppSettings,\n  NavigationState,\n  UIState\n} from './types';\nimport { \n  createStoreWithMiddleware,\n  createBaseStoreActions,\n  generateNotificationId,\n  getDefaultNotificationDuration,\n  logStoreAction\n} from './utils';\nimport { \n  STORE_NAMES,\n  STORAGE_KEYS,\n  DEFAULT_THEME,\n  DEFAULT_APP_SETTINGS,\n  DEFAULT_NAVIGATION,\n  DEFAULT_UI_STATE,\n  SUCCESS_MESSAGES,\n  ERROR_MESSAGES,\n  STORE_VERSIONS\n} from './constants';\n\n// ============================================================================\n// Initial State\n// ============================================================================\n\nconst initialAppState = {\n  // Base store state\n  _hasHydrated: false,\n  \n  // Configuration\n  theme: DEFAULT_THEME,\n  settings: DEFAULT_APP_SETTINGS,\n  \n  // Navigation\n  navigation: DEFAULT_NAVIGATION,\n  \n  // UI state\n  ui: DEFAULT_UI_STATE,\n  \n  // System info\n  version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',\n  buildTime: process.env.NEXT_PUBLIC_BUILD_TIME || new Date().toISOString(),\n  environment: (process.env.NODE_ENV as 'development' | 'staging' | 'production') || 'development',\n};\n\n// ============================================================================\n// Store Implementation\n// ============================================================================\n\n/**\n * Application Store Creator\n */\nconst createAppStore = () => {\n  return create<AppStore>()(\n    createStoreWithMiddleware<AppStore>(\n      (set, get) => ({\n        ...initialAppState,\n        \n        // Base store actions\n        ...createBaseStoreActions<AppStore>(set),\n        \n        // ========================================================================\n        // Theme Management Actions\n        // ========================================================================\n        \n        /**\n         * Set theme configuration\n         */\n        setTheme: (themeUpdate: Partial<ThemeConfig>) => {\n          const currentTheme = get().theme;\n          const newTheme = { ...currentTheme, ...themeUpdate };\n          \n          set({ theme: newTheme });\n          logStoreAction(STORE_NAMES.APP, 'set_theme', themeUpdate);\n        },\n        \n        /**\n         * Toggle between light and dark mode\n         */\n        toggleTheme: () => {\n          const currentMode = get().theme.mode;\n          const newMode = currentMode === 'light' ? 'dark' : 'light';\n          \n          get().setTheme({ mode: newMode });\n          logStoreAction(STORE_NAMES.APP, 'toggle_theme', { newMode });\n        },\n        \n        // ========================================================================\n        // Settings Management Actions\n        // ========================================================================\n        \n        /**\n         * Update application settings\n         */\n        updateSettings: (settingsUpdate: Partial<AppSettings>) => {\n          const currentSettings = get().settings;\n          const newSettings = { ...currentSettings, ...settingsUpdate };\n          \n          set({ settings: newSettings });\n          logStoreAction(STORE_NAMES.APP, 'update_settings', settingsUpdate);\n        },\n        \n        /**\n         * Reset settings to default values\n         */\n        resetSettings: () => {\n          set({ settings: DEFAULT_APP_SETTINGS });\n          logStoreAction(STORE_NAMES.APP, 'reset_settings');\n        },\n        \n        // ========================================================================\n        // Navigation Actions\n        // ========================================================================\n        \n        /**\n         * Set current path\n         */\n        setCurrentPath: (path: string) => {\n          const currentNavigation = get().navigation;\n          const newNavigation = { ...currentNavigation, currentPath: path };\n          \n          set({ navigation: newNavigation });\n          logStoreAction(STORE_NAMES.APP, 'set_current_path', { path });\n        },\n        \n        /**\n         * Set breadcrumbs\n         */\n        setBreadcrumbs: (breadcrumbs: NavigationState['breadcrumbs']) => {\n          const currentNavigation = get().navigation;\n          const newNavigation = { ...currentNavigation, breadcrumbs };\n          \n          set({ navigation: newNavigation });\n          logStoreAction(STORE_NAMES.APP, 'set_breadcrumbs', { count: breadcrumbs.length });\n        },\n        \n        /**\n         * Toggle sidebar collapsed state\n         */\n        toggleSidebar: () => {\n          const currentNavigation = get().navigation;\n          const newCollapsed = !currentNavigation.sidebarCollapsed;\n          const newNavigation = { ...currentNavigation, sidebarCollapsed: newCollapsed };\n          \n          set({ navigation: newNavigation });\n          logStoreAction(STORE_NAMES.APP, 'toggle_sidebar', { collapsed: newCollapsed });\n        },\n        \n        /**\n         * Set active menu key\n         */\n        setActiveMenu: (key: string) => {\n          const currentNavigation = get().navigation;\n          const newNavigation = { ...currentNavigation, activeMenuKey: key };\n          \n          set({ navigation: newNavigation });\n          logStoreAction(STORE_NAMES.APP, 'set_active_menu', { key });\n        },\n        \n        // ========================================================================\n        // UI State Management Actions\n        // ========================================================================\n        \n        /**\n         * Set global loading state\n         */\n        setGlobalLoading: (loading: boolean, message?: string) => {\n          const currentUI = get().ui;\n          const newUI = { \n            ...currentUI, \n            globalLoading: loading,\n            loadingMessage: message || ''\n          };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'set_global_loading', { loading, message });\n        },\n        \n        /**\n         * Set global error\n         */\n        setGlobalError: (error: string | null, details?: any) => {\n          const currentUI = get().ui;\n          const newUI = { \n            ...currentUI, \n            globalError: error,\n            errorDetails: details || null\n          };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'set_global_error', { error, hasDetails: !!details });\n        },\n        \n        /**\n         * Clear global error\n         */\n        clearGlobalError: () => {\n          const currentUI = get().ui;\n          const newUI = { \n            ...currentUI, \n            globalError: null,\n            errorDetails: null\n          };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'clear_global_error');\n        },\n        \n        // ========================================================================\n        // Notifications Actions\n        // ========================================================================\n        \n        /**\n         * Add notification\n         */\n        addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => {\n          const id = generateNotificationId();\n          const timestamp = Date.now();\n          const duration = notification.duration || getDefaultNotificationDuration(notification.type);\n          \n          const newNotification = {\n            ...notification,\n            id,\n            timestamp,\n            duration,\n          };\n          \n          const currentUI = get().ui;\n          const newNotifications = [...currentUI.notifications, newNotification];\n          const newUI = { ...currentUI, notifications: newNotifications };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'add_notification', { type: notification.type, id });\n          \n          // Auto-remove notification after duration\n          if (duration > 0) {\n            setTimeout(() => {\n              get().removeNotification(id);\n            }, duration);\n          }\n        },\n        \n        /**\n         * Remove notification\n         */\n        removeNotification: (id: string) => {\n          const currentUI = get().ui;\n          const newNotifications = currentUI.notifications.filter(n => n.id !== id);\n          const newUI = { ...currentUI, notifications: newNotifications };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'remove_notification', { id });\n        },\n        \n        /**\n         * Clear all notifications\n         */\n        clearNotifications: () => {\n          const currentUI = get().ui;\n          const newUI = { ...currentUI, notifications: [] };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'clear_notifications');\n        },\n        \n        // ========================================================================\n        // Modals Actions\n        // ========================================================================\n        \n        /**\n         * Show modal\n         */\n        showModal: (key: string, data?: any) => {\n          const currentUI = get().ui;\n          const newModals = { \n            ...currentUI.modals, \n            [key]: { visible: true, data } \n          };\n          const newUI = { ...currentUI, modals: newModals };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'show_modal', { key, hasData: !!data });\n        },\n        \n        /**\n         * Hide modal\n         */\n        hideModal: (key: string) => {\n          const currentUI = get().ui;\n          const newModals = { \n            ...currentUI.modals, \n            [key]: { visible: false, data: undefined } \n          };\n          const newUI = { ...currentUI, modals: newModals };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'hide_modal', { key });\n        },\n        \n        /**\n         * Hide all modals\n         */\n        hideAllModals: () => {\n          const currentUI = get().ui;\n          const newModals: Record<string, { visible: boolean; data?: any }> = {};\n          \n          // Set all modals to hidden\n          Object.keys(currentUI.modals).forEach(key => {\n            newModals[key] = { visible: false, data: undefined };\n          });\n          \n          const newUI = { ...currentUI, modals: newModals };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'hide_all_modals');\n        },\n      }),\n      {\n        persist: {\n          name: STORAGE_KEYS.APP,\n          version: STORE_VERSIONS.APP,\n          partialize: (state) => ({\n            theme: state.theme,\n            settings: state.settings,\n            navigation: {\n              sidebarCollapsed: state.navigation.sidebarCollapsed,\n              activeMenuKey: state.navigation.activeMenuKey,\n            },\n          }),\n        },\n        devtools: {\n          name: STORE_NAMES.APP,\n          enabled: process.env.NODE_ENV === 'development',\n        },\n      }\n    )\n  );\n};\n\n// ============================================================================\n// Export Store\n// ============================================================================\n\nexport const useAppStore = createAppStore();\n\n// Export store for testing and advanced usage\nexport { createAppStore };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAUD;AAOA;AA+BW;AA9CX;;;;AA2BA,+EAA+E;AAC/E,gBAAgB;AAChB,+EAA+E;AAE/E,MAAM,kBAAkB;IACtB,mBAAmB;IACnB,cAAc;IAEd,gBAAgB;IAChB,OAAO,6HAAA,CAAA,gBAAa;IACpB,UAAU,6HAAA,CAAA,uBAAoB;IAE9B,aAAa;IACb,YAAY,6HAAA,CAAA,qBAAkB;IAE9B,WAAW;IACX,IAAI,6HAAA,CAAA,mBAAgB;IAEpB,cAAc;IACd,SAAS,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI;IAChD,WAAW,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,IAAI,OAAO,WAAW;IACvE,aAAa,mDAAsE;AACrF;AAEA,+EAA+E;AAC/E,uBAAuB;AACvB,+EAA+E;AAE/E;;CAEC,GACD,MAAM,iBAAiB;IACrB,OAAO,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IACV,CAAA,GAAA,yHAAA,CAAA,4BAAyB,AAAD,EACtB,CAAC,KAAK,MAAQ,CAAC;YACb,GAAG,eAAe;YAElB,qBAAqB;YACrB,GAAG,CAAA,GAAA,yHAAA,CAAA,yBAAsB,AAAD,EAAY,IAAI;YAExC,2EAA2E;YAC3E,2BAA2B;YAC3B,2EAA2E;YAE3E;;SAEC,GACD,UAAU,CAAC;gBACT,MAAM,eAAe,MAAM,KAAK;gBAChC,MAAM,WAAW;oBAAE,GAAG,YAAY;oBAAE,GAAG,WAAW;gBAAC;gBAEnD,IAAI;oBAAE,OAAO;gBAAS;gBACtB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,aAAa;YAC/C;YAEA;;SAEC,GACD,aAAa;gBACX,MAAM,cAAc,MAAM,KAAK,CAAC,IAAI;gBACpC,MAAM,UAAU,gBAAgB,UAAU,SAAS;gBAEnD,MAAM,QAAQ,CAAC;oBAAE,MAAM;gBAAQ;gBAC/B,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,gBAAgB;oBAAE;gBAAQ;YAC5D;YAEA,2EAA2E;YAC3E,8BAA8B;YAC9B,2EAA2E;YAE3E;;SAEC,GACD,gBAAgB,CAAC;gBACf,MAAM,kBAAkB,MAAM,QAAQ;gBACtC,MAAM,cAAc;oBAAE,GAAG,eAAe;oBAAE,GAAG,cAAc;gBAAC;gBAE5D,IAAI;oBAAE,UAAU;gBAAY;gBAC5B,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,mBAAmB;YACrD;YAEA;;SAEC,GACD,eAAe;gBACb,IAAI;oBAAE,UAAU,6HAAA,CAAA,uBAAoB;gBAAC;gBACrC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE;YAClC;YAEA,2EAA2E;YAC3E,qBAAqB;YACrB,2EAA2E;YAE3E;;SAEC,GACD,gBAAgB,CAAC;gBACf,MAAM,oBAAoB,MAAM,UAAU;gBAC1C,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;oBAAE,aAAa;gBAAK;gBAEhE,IAAI;oBAAE,YAAY;gBAAc;gBAChC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,oBAAoB;oBAAE;gBAAK;YAC7D;YAEA;;SAEC,GACD,gBAAgB,CAAC;gBACf,MAAM,oBAAoB,MAAM,UAAU;gBAC1C,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;oBAAE;gBAAY;gBAE1D,IAAI;oBAAE,YAAY;gBAAc;gBAChC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,mBAAmB;oBAAE,OAAO,YAAY,MAAM;gBAAC;YACjF;YAEA;;SAEC,GACD,eAAe;gBACb,MAAM,oBAAoB,MAAM,UAAU;gBAC1C,MAAM,eAAe,CAAC,kBAAkB,gBAAgB;gBACxD,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;oBAAE,kBAAkB;gBAAa;gBAE7E,IAAI;oBAAE,YAAY;gBAAc;gBAChC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,kBAAkB;oBAAE,WAAW;gBAAa;YAC9E;YAEA;;SAEC,GACD,eAAe,CAAC;gBACd,MAAM,oBAAoB,MAAM,UAAU;gBAC1C,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;oBAAE,eAAe;gBAAI;gBAEjE,IAAI;oBAAE,YAAY;gBAAc;gBAChC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,mBAAmB;oBAAE;gBAAI;YAC3D;YAEA,2EAA2E;YAC3E,8BAA8B;YAC9B,2EAA2E;YAE3E;;SAEC,GACD,kBAAkB,CAAC,SAAkB;gBACnC,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ;oBACZ,GAAG,SAAS;oBACZ,eAAe;oBACf,gBAAgB,WAAW;gBAC7B;gBAEA,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,sBAAsB;oBAAE;oBAAS;gBAAQ;YAC3E;YAEA;;SAEC,GACD,gBAAgB,CAAC,OAAsB;gBACrC,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ;oBACZ,GAAG,SAAS;oBACZ,aAAa;oBACb,cAAc,WAAW;gBAC3B;gBAEA,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,oBAAoB;oBAAE;oBAAO,YAAY,CAAC,CAAC;gBAAQ;YACrF;YAEA;;SAEC,GACD,kBAAkB;gBAChB,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ;oBACZ,GAAG,SAAS;oBACZ,aAAa;oBACb,cAAc;gBAChB;gBAEA,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE;YAClC;YAEA,2EAA2E;YAC3E,wBAAwB;YACxB,2EAA2E;YAE3E;;SAEC,GACD,iBAAiB,CAAC;gBAChB,MAAM,KAAK,CAAA,GAAA,yHAAA,CAAA,yBAAsB,AAAD;gBAChC,MAAM,YAAY,KAAK,GAAG;gBAC1B,MAAM,WAAW,aAAa,QAAQ,IAAI,CAAA,GAAA,yHAAA,CAAA,iCAA8B,AAAD,EAAE,aAAa,IAAI;gBAE1F,MAAM,kBAAkB;oBACtB,GAAG,YAAY;oBACf;oBACA;oBACA;gBACF;gBAEA,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,mBAAmB;uBAAI,UAAU,aAAa;oBAAE;iBAAgB;gBACtE,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,eAAe;gBAAiB;gBAE9D,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,oBAAoB;oBAAE,MAAM,aAAa,IAAI;oBAAE;gBAAG;gBAElF,0CAA0C;gBAC1C,IAAI,WAAW,GAAG;oBAChB,WAAW;wBACT,MAAM,kBAAkB,CAAC;oBAC3B,GAAG;gBACL;YACF;YAEA;;SAEC,GACD,oBAAoB,CAAC;gBACnB,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,mBAAmB,UAAU,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACtE,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,eAAe;gBAAiB;gBAE9D,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,uBAAuB;oBAAE;gBAAG;YAC9D;YAEA;;SAEC,GACD,oBAAoB;gBAClB,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,eAAe,EAAE;gBAAC;gBAEhD,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE;YAClC;YAEA,2EAA2E;YAC3E,iBAAiB;YACjB,2EAA2E;YAE3E;;SAEC,GACD,WAAW,CAAC,KAAa;gBACvB,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,YAAY;oBAChB,GAAG,UAAU,MAAM;oBACnB,CAAC,IAAI,EAAE;wBAAE,SAAS;wBAAM;oBAAK;gBAC/B;gBACA,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,QAAQ;gBAAU;gBAEhD,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,cAAc;oBAAE;oBAAK,SAAS,CAAC,CAAC;gBAAK;YACvE;YAEA;;SAEC,GACD,WAAW,CAAC;gBACV,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,YAAY;oBAChB,GAAG,UAAU,MAAM;oBACnB,CAAC,IAAI,EAAE;wBAAE,SAAS;wBAAO,MAAM;oBAAU;gBAC3C;gBACA,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,QAAQ;gBAAU;gBAEhD,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,cAAc;oBAAE;gBAAI;YACtD;YAEA;;SAEC,GACD,eAAe;gBACb,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,YAA8D,CAAC;gBAErE,2BAA2B;gBAC3B,OAAO,IAAI,CAAC,UAAU,MAAM,EAAE,OAAO,CAAC,CAAA;oBACpC,SAAS,CAAC,IAAI,GAAG;wBAAE,SAAS;wBAAO,MAAM;oBAAU;gBACrD;gBAEA,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,QAAQ;gBAAU;gBAEhD,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE;YAClC;QACF,CAAC,GACD;QACE,SAAS;YACP,MAAM,6HAAA,CAAA,eAAY,CAAC,GAAG;YACtB,SAAS,6HAAA,CAAA,iBAAc,CAAC,GAAG;YAC3B,YAAY,CAAC,QAAU,CAAC;oBACtB,OAAO,MAAM,KAAK;oBAClB,UAAU,MAAM,QAAQ;oBACxB,YAAY;wBACV,kBAAkB,MAAM,UAAU,CAAC,gBAAgB;wBACnD,eAAe,MAAM,UAAU,CAAC,aAAa;oBAC/C;gBACF,CAAC;QACH;QACA,UAAU;YACR,MAAM,6HAAA,CAAA,cAAW,CAAC,GAAG;YACrB,SAAS,oDAAyB;QACpC;IACF;AAGN;AAMO,MAAM,cAAc"}}, {"offset": {"line": 1839, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1845, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts"], "sourcesContent": ["/**\n * Application Hooks\n * Custom hooks for easy application state access\n */\n\n'use client';\n\nimport { useCallback, useEffect } from 'react';\nimport { useAppStore } from './app-store';\nimport type { ThemeConfig, AppSettings, NavigationState, UIState } from './types';\n\n// ============================================================================\n// Theme Hooks\n// ============================================================================\n\n/**\n * Hook to get current theme configuration\n */\nexport const useTheme = () => {\n  return useAppStore((state) => state.theme);\n};\n\n/**\n * Hook to get current theme mode\n */\nexport const useThemeMode = () => {\n  return useAppStore((state) => state.theme.mode);\n};\n\n/**\n * Hook to check if dark mode is active\n */\nexport const useIsDarkMode = () => {\n  return useAppStore((state) => state.theme.mode === 'dark');\n};\n\n/**\n * Hook to get theme actions\n */\nexport const useThemeActions = () => {\n  const setTheme = useAppStore((state) => state.setTheme);\n  const toggleTheme = useAppStore((state) => state.toggleTheme);\n\n  return {\n    setTheme,\n    toggleTheme,\n  };\n};\n\n// ============================================================================\n// Settings Hooks\n// ============================================================================\n\n/**\n * Hook to get application settings\n */\nexport const useAppSettings = () => {\n  return useAppStore((state) => state.settings);\n};\n\n/**\n * Hook to get settings actions\n */\nexport const useSettingsActions = () => {\n  const updateSettings = useAppStore((state) => state.updateSettings);\n  const resetSettings = useAppStore((state) => state.resetSettings);\n\n  return {\n    updateSettings,\n    resetSettings,\n  };\n};\n\n/**\n * Hook to get specific setting value\n */\nexport const useSetting = <K extends keyof AppSettings>(key: K) => {\n  return useAppStore((state) => state.settings[key]);\n};\n\n// ============================================================================\n// Navigation Hooks\n// ============================================================================\n\n/**\n * Hook to get navigation state\n */\nexport const useNavigation = () => {\n  return useAppStore((state) => state.navigation);\n};\n\n/**\n * Hook to get current path\n */\nexport const useCurrentPath = () => {\n  return useAppStore((state) => state.navigation.currentPath);\n};\n\n/**\n * Hook to get breadcrumbs\n */\nexport const useBreadcrumbs = () => {\n  return useAppStore((state) => state.navigation.breadcrumbs);\n};\n\n/**\n * Hook to get sidebar state\n */\nexport const useSidebarState = () => {\n  const collapsed = useAppStore((state) => state.navigation.sidebarCollapsed);\n  const toggleSidebar = useAppStore((state) => state.toggleSidebar);\n\n  return {\n    collapsed,\n    toggleSidebar,\n  };\n};\n\n/**\n * Hook to get active menu key\n */\nexport const useActiveMenu = () => {\n  return useAppStore((state) => state.navigation.activeMenuKey);\n};\n\n/**\n * Hook to get navigation actions\n */\nexport const useNavigationActions = () => {\n  const setCurrentPath = useAppStore((state) => state.setCurrentPath);\n  const setBreadcrumbs = useAppStore((state) => state.setBreadcrumbs);\n  const toggleSidebar = useAppStore((state) => state.toggleSidebar);\n  const setActiveMenu = useAppStore((state) => state.setActiveMenu);\n\n  return {\n    setCurrentPath,\n    setBreadcrumbs,\n    toggleSidebar,\n    setActiveMenu,\n  };\n};\n\n// ============================================================================\n// UI State Hooks\n// ============================================================================\n\n/**\n * Hook to get UI state\n */\nexport const useUIState = () => {\n  return useAppStore((state) => state.ui);\n};\n\n/**\n * Hook to get global loading state\n */\nexport const useGlobalLoading = () => {\n  const loading = useAppStore((state) => state.ui.globalLoading);\n  const message = useAppStore((state) => state.ui.loadingMessage);\n\n  return { loading, message };\n};\n\n/**\n * Hook to get global error state\n */\nexport const useGlobalError = () => {\n  const error = useAppStore((state) => state.ui.globalError);\n  const details = useAppStore((state) => state.ui.errorDetails);\n\n  return { error, details };\n};\n\n/**\n * Hook to get UI actions\n */\nexport const useUIActions = () => {\n  const setGlobalLoading = useAppStore((state) => state.setGlobalLoading);\n  const setGlobalError = useAppStore((state) => state.setGlobalError);\n  const clearGlobalError = useAppStore((state) => state.clearGlobalError);\n\n  return {\n    setGlobalLoading,\n    setGlobalError,\n    clearGlobalError,\n  };\n};\n\n// ============================================================================\n// Notifications Hooks\n// ============================================================================\n\n/**\n * Hook to get notifications\n */\nexport const useNotifications = () => {\n  return useAppStore((state) => state.ui.notifications);\n};\n\n/**\n * Hook to get notification actions\n */\nexport const useNotificationActions = () => {\n  const addNotification = useAppStore((state) => state.addNotification);\n  const removeNotification = useAppStore((state) => state.removeNotification);\n  const clearNotifications = useAppStore((state) => state.clearNotifications);\n\n  return {\n    addNotification,\n    removeNotification,\n    clearNotifications,\n  };\n};\n\n/**\n * Hook for easy notification creation\n */\nexport const useNotify = () => {\n  const addNotification = useAppStore((state) => state.addNotification);\n\n  const notify = useCallback(() => ({\n    success: (message: string, title?: string) => {\n      addNotification({\n        type: 'success',\n        title: title || 'Success',\n        message,\n      });\n    },\n    error: (message: string, title?: string) => {\n      addNotification({\n        type: 'error',\n        title: title || 'Error',\n        message,\n      });\n    },\n    warning: (message: string, title?: string) => {\n      addNotification({\n        type: 'warning',\n        title: title || 'Warning',\n        message,\n      });\n    },\n    info: (message: string, title?: string) => {\n      addNotification({\n        type: 'info',\n        title: title || 'Info',\n        message,\n      });\n    },\n  }), [addNotification]);\n\n  return notify();\n};\n\n// ============================================================================\n// Modals Hooks\n// ============================================================================\n\n/**\n * Hook to get modals state\n */\nexport const useModals = () => {\n  return useAppStore((state) => state.ui.modals);\n};\n\n/**\n * Hook to get specific modal state\n */\nexport const useModal = (key: string) => {\n  const modal = useAppStore((state) => state.ui.modals[key]);\n  const showModal = useAppStore((state) => state.showModal);\n  const hideModal = useAppStore((state) => state.hideModal);\n\n  const show = useCallback((data?: any) => {\n    showModal(key, data);\n  }, [showModal, key]);\n\n  const hide = useCallback(() => {\n    hideModal(key);\n  }, [hideModal, key]);\n\n  return {\n    visible: modal?.visible || false,\n    data: modal?.data,\n    show,\n    hide,\n  };\n};\n\n/**\n * Hook to get modal actions\n */\nexport const useModalActions = () => {\n  const showModal = useAppStore((state) => state.showModal);\n  const hideModal = useAppStore((state) => state.hideModal);\n  const hideAllModals = useAppStore((state) => state.hideAllModals);\n\n  return {\n    showModal,\n    hideModal,\n    hideAllModals,\n  };\n};\n\n// ============================================================================\n// System Info Hooks\n// ============================================================================\n\n/**\n * Hook to get application version\n */\nexport const useAppVersion = () => {\n  return useAppStore((state) => state.version);\n};\n\n/**\n * Hook to get build time\n */\nexport const useBuildTime = () => {\n  return useAppStore((state) => state.buildTime);\n};\n\n/**\n * Hook to get environment\n */\nexport const useEnvironment = () => {\n  return useAppStore((state) => state.environment);\n};\n\n/**\n * Hook to get system info\n */\nexport const useSystemInfo = () => {\n  const version = useAppVersion();\n  const buildTime = useBuildTime();\n  const environment = useEnvironment();\n\n  return {\n    version,\n    buildTime,\n    environment,\n    isDevelopment: environment === 'development',\n    isProduction: environment === 'production',\n  };\n};\n\n// ============================================================================\n// Composite Hooks\n// ============================================================================\n\n/**\n * Hook to get complete application state\n */\nexport const useApp = () => {\n  const theme = useTheme();\n  const settings = useAppSettings();\n  const navigation = useNavigation();\n  const ui = useUIState();\n  const systemInfo = useSystemInfo();\n\n  const themeActions = useThemeActions();\n  const settingsActions = useSettingsActions();\n  const navigationActions = useNavigationActions();\n  const uiActions = useUIActions();\n  const notificationActions = useNotificationActions();\n  const modalActions = useModalActions();\n\n  return {\n    // State\n    theme,\n    settings,\n    navigation,\n    ui,\n    systemInfo,\n\n    // Actions\n    ...themeActions,\n    ...settingsActions,\n    ...navigationActions,\n    ...uiActions,\n    ...notificationActions,\n    ...modalActions,\n  };\n};\n\n/**\n * Hook for responsive design utilities\n */\nexport const useResponsive = () => {\n  const sidebarCollapsed = useAppStore((state) => state.navigation.sidebarCollapsed);\n  const toggleSidebar = useAppStore((state) => state.toggleSidebar);\n\n  // Auto-collapse sidebar on mobile\n  useEffect(() => {\n    const handleResize = () => {\n      const isMobile = window.innerWidth < 768;\n      if (isMobile && !sidebarCollapsed) {\n        toggleSidebar();\n      }\n    };\n\n    window.addEventListener('resize', handleResize);\n    handleResize(); // Check on mount\n\n    return () => window.removeEventListener('resize', handleResize);\n  }, [sidebarCollapsed, toggleSidebar]);\n\n  return {\n    isMobile: typeof window !== 'undefined' ? window.innerWidth < 768 : false,\n    isTablet: typeof window !== 'undefined' ? window.innerWidth >= 768 && window.innerWidth < 1024 : false,\n    isDesktop: typeof window !== 'undefined' ? window.innerWidth >= 1024 : false,\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAID;AACA;;AAHA;;;AAaO,MAAM,WAAW;;IACtB,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;gCAAE,CAAC,QAAU,MAAM,KAAK;;AAC3C;GAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,eAAe;;IAC1B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;oCAAE,CAAC,QAAU,MAAM,KAAK,CAAC,IAAI;;AAChD;IAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,gBAAgB;;IAC3B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;qCAAE,CAAC,QAAU,MAAM,KAAK,CAAC,IAAI,KAAK;;AACrD;IAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,kBAAkB;;IAC7B,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;iDAAE,CAAC,QAAU,MAAM,QAAQ;;IACtD,MAAM,cAAc,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;oDAAE,CAAC,QAAU,MAAM,WAAW;;IAE5D,OAAO;QACL;QACA;IACF;AACF;IARa;;QACM,gIAAA,CAAA,cAAW;QACR,gIAAA,CAAA,cAAW;;;AAe1B,MAAM,iBAAiB;;IAC5B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;sCAAE,CAAC,QAAU,MAAM,QAAQ;;AAC9C;IAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,qBAAqB;;IAChC,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;0DAAE,CAAC,QAAU,MAAM,cAAc;;IAClE,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;yDAAE,CAAC,QAAU,MAAM,aAAa;;IAEhE,OAAO;QACL;QACA;IACF;AACF;IARa;;QACY,gIAAA,CAAA,cAAW;QACZ,gIAAA,CAAA,cAAW;;;AAW5B,MAAM,aAAa,CAA8B;;IACtD,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;kCAAE,CAAC,QAAU,MAAM,QAAQ,CAAC,IAAI;;AACnD;IAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAUb,MAAM,gBAAgB;;IAC3B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;qCAAE,CAAC,QAAU,MAAM,UAAU;;AAChD;IAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,iBAAiB;;IAC5B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;sCAAE,CAAC,QAAU,MAAM,UAAU,CAAC,WAAW;;AAC5D;IAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,iBAAiB;;IAC5B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;sCAAE,CAAC,QAAU,MAAM,UAAU,CAAC,WAAW;;AAC5D;IAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,kBAAkB;;IAC7B,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,QAAU,MAAM,UAAU,CAAC,gBAAgB;;IAC1E,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,QAAU,MAAM,aAAa;;IAEhE,OAAO;QACL;QACA;IACF;AACF;KARa;;QACO,gIAAA,CAAA,cAAW;QACP,gIAAA,CAAA,cAAW;;;AAW5B,MAAM,gBAAgB;;IAC3B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;qCAAE,CAAC,QAAU,MAAM,UAAU,CAAC,aAAa;;AAC9D;KAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,uBAAuB;;IAClC,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;4DAAE,CAAC,QAAU,MAAM,cAAc;;IAClE,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;4DAAE,CAAC,QAAU,MAAM,cAAc;;IAClE,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;2DAAE,CAAC,QAAU,MAAM,aAAa;;IAChE,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;2DAAE,CAAC,QAAU,MAAM,aAAa;;IAEhE,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;KAZa;;QACY,gIAAA,CAAA,cAAW;QACX,gIAAA,CAAA,cAAW;QACZ,gIAAA,CAAA,cAAW;QACX,gIAAA,CAAA,cAAW;;;AAiB5B,MAAM,aAAa;;IACxB,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;kCAAE,CAAC,QAAU,MAAM,EAAE;;AACxC;KAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,mBAAmB;;IAC9B,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;iDAAE,CAAC,QAAU,MAAM,EAAE,CAAC,aAAa;;IAC7D,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;iDAAE,CAAC,QAAU,MAAM,EAAE,CAAC,cAAc;;IAE9D,OAAO;QAAE;QAAS;IAAQ;AAC5B;KALa;;QACK,gIAAA,CAAA,cAAW;QACX,gIAAA,CAAA,cAAW;;;AAQtB,MAAM,iBAAiB;;IAC5B,MAAM,QAAQ,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;6CAAE,CAAC,QAAU,MAAM,EAAE,CAAC,WAAW;;IACzD,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;+CAAE,CAAC,QAAU,MAAM,EAAE,CAAC,YAAY;;IAE5D,OAAO;QAAE;QAAO;IAAQ;AAC1B;KALa;;QACG,gIAAA,CAAA,cAAW;QACT,gIAAA,CAAA,cAAW;;;AAQtB,MAAM,eAAe;;IAC1B,MAAM,mBAAmB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,QAAU,MAAM,gBAAgB;;IACtE,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;oDAAE,CAAC,QAAU,MAAM,cAAc;;IAClE,MAAM,mBAAmB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,QAAU,MAAM,gBAAgB;;IAEtE,OAAO;QACL;QACA;QACA;IACF;AACF;KAVa;;QACc,gIAAA,CAAA,cAAW;QACb,gIAAA,CAAA,cAAW;QACT,gIAAA,CAAA,cAAW;;;AAgB/B,MAAM,mBAAmB;;IAC9B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;wCAAE,CAAC,QAAU,MAAM,EAAE,CAAC,aAAa;;AACtD;KAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,yBAAyB;;IACpC,MAAM,kBAAkB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;+DAAE,CAAC,QAAU,MAAM,eAAe;;IACpE,MAAM,qBAAqB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;kEAAE,CAAC,QAAU,MAAM,kBAAkB;;IAC1E,MAAM,qBAAqB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;kEAAE,CAAC,QAAU,MAAM,kBAAkB;;IAE1E,OAAO;QACL;QACA;QACA;IACF;AACF;KAVa;;QACa,gIAAA,CAAA,cAAW;QACR,gIAAA,CAAA,cAAW;QACX,gIAAA,CAAA,cAAW;;;AAYjC,MAAM,YAAY;;IACvB,MAAM,kBAAkB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,QAAU,MAAM,eAAe;;IAEpE,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yCAAE,IAAM,CAAC;gBAChC,OAAO;qDAAE,CAAC,SAAiB;wBACzB,gBAAgB;4BACd,MAAM;4BACN,OAAO,SAAS;4BAChB;wBACF;oBACF;;gBACA,KAAK;qDAAE,CAAC,SAAiB;wBACvB,gBAAgB;4BACd,MAAM;4BACN,OAAO,SAAS;4BAChB;wBACF;oBACF;;gBACA,OAAO;qDAAE,CAAC,SAAiB;wBACzB,gBAAgB;4BACd,MAAM;4BACN,OAAO,SAAS;4BAChB;wBACF;oBACF;;gBACA,IAAI;qDAAE,CAAC,SAAiB;wBACtB,gBAAgB;4BACd,MAAM;4BACN,OAAO,SAAS;4BAChB;wBACF;oBACF;;YACF,CAAC;wCAAG;QAAC;KAAgB;IAErB,OAAO;AACT;KAnCa;;QACa,gIAAA,CAAA,cAAW;;;AA2C9B,MAAM,YAAY;;IACvB,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;iCAAE,CAAC,QAAU,MAAM,EAAE,CAAC,MAAM;;AAC/C;KAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,WAAW,CAAC;;IACvB,MAAM,QAAQ,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;uCAAE,CAAC,QAAU,MAAM,EAAE,CAAC,MAAM,CAAC,IAAI;;IACzD,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;2CAAE,CAAC,QAAU,MAAM,SAAS;;IACxD,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;2CAAE,CAAC,QAAU,MAAM,SAAS;;IAExD,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sCAAE,CAAC;YACxB,UAAU,KAAK;QACjB;qCAAG;QAAC;QAAW;KAAI;IAEnB,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sCAAE;YACvB,UAAU;QACZ;qCAAG;QAAC;QAAW;KAAI;IAEnB,OAAO;QACL,SAAS,OAAO,WAAW;QAC3B,MAAM,OAAO;QACb;QACA;IACF;AACF;KAnBa;;QACG,gIAAA,CAAA,cAAW;QACP,gIAAA,CAAA,cAAW;QACX,gIAAA,CAAA,cAAW;;;AAqBxB,MAAM,kBAAkB;;IAC7B,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,QAAU,MAAM,SAAS;;IACxD,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,QAAU,MAAM,SAAS;;IACxD,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,QAAU,MAAM,aAAa;;IAEhE,OAAO;QACL;QACA;QACA;IACF;AACF;KAVa;;QACO,gIAAA,CAAA,cAAW;QACX,gIAAA,CAAA,cAAW;QACP,gIAAA,CAAA,cAAW;;;AAgB5B,MAAM,gBAAgB;;IAC3B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;qCAAE,CAAC,QAAU,MAAM,OAAO;;AAC7C;KAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,eAAe;;IAC1B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;oCAAE,CAAC,QAAU,MAAM,SAAS;;AAC/C;KAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,iBAAiB;;IAC5B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;sCAAE,CAAC,QAAU,MAAM,WAAW;;AACjD;KAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,gBAAgB;;IAC3B,MAAM,UAAU;IAChB,MAAM,YAAY;IAClB,MAAM,cAAc;IAEpB,OAAO;QACL;QACA;QACA;QACA,eAAe,gBAAgB;QAC/B,cAAc,gBAAgB;IAChC;AACF;KAZa;;QACK;QACE;QACE;;;AAkBf,MAAM,SAAS;;IACpB,MAAM,QAAQ;IACd,MAAM,WAAW;IACjB,MAAM,aAAa;IACnB,MAAM,KAAK;IACX,MAAM,aAAa;IAEnB,MAAM,eAAe;IACrB,MAAM,kBAAkB;IACxB,MAAM,oBAAoB;IAC1B,MAAM,YAAY;IAClB,MAAM,sBAAsB;IAC5B,MAAM,eAAe;IAErB,OAAO;QACL,QAAQ;QACR;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV,GAAG,YAAY;QACf,GAAG,eAAe;QAClB,GAAG,iBAAiB;QACpB,GAAG,SAAS;QACZ,GAAG,mBAAmB;QACtB,GAAG,YAAY;IACjB;AACF;KA9Ba;;QACG;QACG;QACE;QACR;QACQ;QAEE;QACG;QACE;QACR;QACU;QACP;;;AAuBhB,MAAM,gBAAgB;;IAC3B,MAAM,mBAAmB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;uDAAE,CAAC,QAAU,MAAM,UAAU,CAAC,gBAAgB;;IACjF,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;oDAAE,CAAC,QAAU,MAAM,aAAa;;IAEhE,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;wDAAe;oBACnB,MAAM,WAAW,OAAO,UAAU,GAAG;oBACrC,IAAI,YAAY,CAAC,kBAAkB;wBACjC;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC,gBAAgB,iBAAiB;YAEjC;2CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;kCAAG;QAAC;QAAkB;KAAc;IAEpC,OAAO;QACL,UAAU,uCAAgC,OAAO,UAAU,GAAG;QAC9D,UAAU,uCAAgC,OAAO,UAAU,IAAI,OAAO,OAAO,UAAU,GAAG;QAC1F,WAAW,uCAAgC,OAAO,UAAU,IAAI;IAClE;AACF;KAxBa;;QACc,gIAAA,CAAA,cAAW;QACd,gIAAA,CAAA,cAAW"}}, {"offset": {"line": 2449, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2455, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/app-utils.ts"], "sourcesContent": ["/**\n * Application Utilities\n * Helper functions for application operations\n */\n\nimport type { ThemeConfig, AppSettings, NavigationState, UIState } from './types';\nimport { useAppStore } from './app-store';\n\n// ============================================================================\n// Theme Utilities\n// ============================================================================\n\n/**\n * Get current theme configuration\n */\nexport const getCurrentTheme = (): ThemeConfig => {\n  return useAppStore.getState().theme;\n};\n\n/**\n * Get current theme mode\n */\nexport const getCurrentThemeMode = (): 'light' | 'dark' => {\n  return useAppStore.getState().theme.mode;\n};\n\n/**\n * Check if dark mode is active\n */\nexport const isDarkMode = (): boolean => {\n  return getCurrentThemeMode() === 'dark';\n};\n\n/**\n * Toggle theme mode\n */\nexport const toggleTheme = (): void => {\n  useAppStore.getState().toggleTheme();\n};\n\n/**\n * Set theme mode\n */\nexport const setThemeMode = (mode: 'light' | 'dark'): void => {\n  useAppStore.getState().setTheme({ mode });\n};\n\n/**\n * Apply theme to document\n */\nexport const applyThemeToDocument = (): void => {\n  if (typeof document === 'undefined') return;\n\n  const theme = getCurrentTheme();\n  const { mode, primaryColor, borderRadius } = theme;\n\n  // Apply theme class to body\n  document.body.className = document.body.className.replace(/theme-\\w+/g, '');\n  document.body.classList.add(`theme-${mode}`);\n\n  // Apply CSS custom properties\n  const root = document.documentElement;\n  root.style.setProperty('--primary-color', primaryColor);\n  root.style.setProperty('--border-radius', `${borderRadius}px`);\n};\n\n// ============================================================================\n// Settings Utilities\n// ============================================================================\n\n/**\n * Get current application settings\n */\nexport const getCurrentSettings = (): AppSettings => {\n  return useAppStore.getState().settings;\n};\n\n/**\n * Get specific setting value\n */\nexport const getSetting = <K extends keyof AppSettings>(key: K): AppSettings[K] => {\n  return useAppStore.getState().settings[key];\n};\n\n/**\n * Update application settings\n */\nexport const updateSettings = (settings: Partial<AppSettings>): void => {\n  useAppStore.getState().updateSettings(settings);\n};\n\n/**\n * Reset settings to default\n */\nexport const resetSettings = (): void => {\n  useAppStore.getState().resetSettings();\n};\n\n// ============================================================================\n// Navigation Utilities\n// ============================================================================\n\n/**\n * Get current navigation state\n */\nexport const getCurrentNavigation = (): NavigationState => {\n  return useAppStore.getState().navigation;\n};\n\n/**\n * Get current path\n */\nexport const getCurrentPath = (): string => {\n  return useAppStore.getState().navigation.currentPath;\n};\n\n/**\n * Set current path\n */\nexport const setCurrentPath = (path: string): void => {\n  useAppStore.getState().setCurrentPath(path);\n};\n\n/**\n * Get breadcrumbs\n */\nexport const getBreadcrumbs = (): NavigationState['breadcrumbs'] => {\n  return useAppStore.getState().navigation.breadcrumbs;\n};\n\n/**\n * Set breadcrumbs\n */\nexport const setBreadcrumbs = (breadcrumbs: NavigationState['breadcrumbs']): void => {\n  useAppStore.getState().setBreadcrumbs(breadcrumbs);\n};\n\n/**\n * Check if sidebar is collapsed\n */\nexport const isSidebarCollapsed = (): boolean => {\n  return useAppStore.getState().navigation.sidebarCollapsed;\n};\n\n/**\n * Toggle sidebar\n */\nexport const toggleSidebar = (): void => {\n  useAppStore.getState().toggleSidebar();\n};\n\n/**\n * Get active menu key\n */\nexport const getActiveMenuKey = (): string => {\n  return useAppStore.getState().navigation.activeMenuKey;\n};\n\n/**\n * Set active menu key\n */\nexport const setActiveMenu = (key: string): void => {\n  useAppStore.getState().setActiveMenu(key);\n};\n\n// ============================================================================\n// UI State Utilities\n// ============================================================================\n\n/**\n * Get current UI state\n */\nexport const getCurrentUIState = (): UIState => {\n  return useAppStore.getState().ui;\n};\n\n/**\n * Check if global loading is active\n */\nexport const isGlobalLoading = (): boolean => {\n  return useAppStore.getState().ui.globalLoading;\n};\n\n/**\n * Set global loading state\n */\nexport const setGlobalLoading = (loading: boolean, message?: string): void => {\n  useAppStore.getState().setGlobalLoading(loading, message);\n};\n\n/**\n * Get global error\n */\nexport const getGlobalError = (): string | null => {\n  return useAppStore.getState().ui.globalError;\n};\n\n/**\n * Set global error\n */\nexport const setGlobalError = (error: string | null, details?: any): void => {\n  useAppStore.getState().setGlobalError(error, details);\n};\n\n/**\n * Clear global error\n */\nexport const clearGlobalError = (): void => {\n  useAppStore.getState().clearGlobalError();\n};\n\n// ============================================================================\n// Notifications Utilities\n// ============================================================================\n\n/**\n * Get current notifications\n */\nexport const getNotifications = (): UIState['notifications'] => {\n  return useAppStore.getState().ui.notifications;\n};\n\n/**\n * Add notification\n */\nexport const addNotification = (\n  notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>\n): void => {\n  useAppStore.getState().addNotification(notification);\n};\n\n/**\n * Remove notification\n */\nexport const removeNotification = (id: string): void => {\n  useAppStore.getState().removeNotification(id);\n};\n\n/**\n * Clear all notifications\n */\nexport const clearNotifications = (): void => {\n  useAppStore.getState().clearNotifications();\n};\n\n/**\n * Show success notification\n */\nexport const notifySuccess = (message: string, title?: string): void => {\n  addNotification({\n    type: 'success',\n    title: title || 'Success',\n    message,\n  });\n};\n\n/**\n * Show error notification\n */\nexport const notifyError = (message: string, title?: string): void => {\n  addNotification({\n    type: 'error',\n    title: title || 'Error',\n    message,\n  });\n};\n\n/**\n * Show warning notification\n */\nexport const notifyWarning = (message: string, title?: string): void => {\n  addNotification({\n    type: 'warning',\n    title: title || 'Warning',\n    message,\n  });\n};\n\n/**\n * Show info notification\n */\nexport const notifyInfo = (message: string, title?: string): void => {\n  addNotification({\n    type: 'info',\n    title: title || 'Info',\n    message,\n  });\n};\n\n// ============================================================================\n// Modals Utilities\n// ============================================================================\n\n/**\n * Get modals state\n */\nexport const getModalsState = (): UIState['modals'] => {\n  return useAppStore.getState().ui.modals;\n};\n\n/**\n * Check if modal is visible\n */\nexport const isModalVisible = (key: string): boolean => {\n  const modal = useAppStore.getState().ui.modals[key];\n  return modal?.visible || false;\n};\n\n/**\n * Get modal data\n */\nexport const getModalData = (key: string): any => {\n  const modal = useAppStore.getState().ui.modals[key];\n  return modal?.data;\n};\n\n/**\n * Show modal\n */\nexport const showModal = (key: string, data?: any): void => {\n  useAppStore.getState().showModal(key, data);\n};\n\n/**\n * Hide modal\n */\nexport const hideModal = (key: string): void => {\n  useAppStore.getState().hideModal(key);\n};\n\n/**\n * Hide all modals\n */\nexport const hideAllModals = (): void => {\n  useAppStore.getState().hideAllModals();\n};\n\n// ============================================================================\n// System Info Utilities\n// ============================================================================\n\n/**\n * Get application version\n */\nexport const getAppVersion = (): string => {\n  return useAppStore.getState().version;\n};\n\n/**\n * Get build time\n */\nexport const getBuildTime = (): string => {\n  return useAppStore.getState().buildTime;\n};\n\n/**\n * Get environment\n */\nexport const getEnvironment = (): 'development' | 'staging' | 'production' => {\n  return useAppStore.getState().environment;\n};\n\n/**\n * Check if development environment\n */\nexport const isDevelopment = (): boolean => {\n  return getEnvironment() === 'development';\n};\n\n/**\n * Check if production environment\n */\nexport const isProduction = (): boolean => {\n  return getEnvironment() === 'production';\n};\n\n// ============================================================================\n// Responsive Utilities\n// ============================================================================\n\n/**\n * Check if current viewport is mobile\n */\nexport const isMobile = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth < 768;\n};\n\n/**\n * Check if current viewport is tablet\n */\nexport const isTablet = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth >= 768 && window.innerWidth < 1024;\n};\n\n/**\n * Check if current viewport is desktop\n */\nexport const isDesktop = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth >= 1024;\n};\n\n/**\n * Get current breakpoint\n */\nexport const getCurrentBreakpoint = (): 'mobile' | 'tablet' | 'desktop' => {\n  if (isMobile()) return 'mobile';\n  if (isTablet()) return 'tablet';\n  return 'desktop';\n};\n\n// ============================================================================\n// Development Utilities\n// ============================================================================\n\n/**\n * Get full application state (development only)\n */\nexport const getAppState = () => {\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  return useAppStore.getState();\n};\n\n/**\n * Reset application state (development/testing only)\n */\nexport const resetAppState = (): void => {\n  if (process.env.NODE_ENV !== 'development') {\n    return;\n  }\n\n  const store = useAppStore.getState();\n  store.resetSettings();\n  store.clearNotifications();\n  store.hideAllModals();\n  store.clearGlobalError();\n  store.setGlobalLoading(false);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGD;AA+ZM;;AAtZC,MAAM,kBAAkB;IAC7B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,KAAK;AACrC;AAKO,MAAM,sBAAsB;IACjC,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI;AAC1C;AAKO,MAAM,aAAa;IACxB,OAAO,0BAA0B;AACnC;AAKO,MAAM,cAAc;IACzB,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,WAAW;AACpC;AAKO,MAAM,eAAe,CAAC;IAC3B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAAE;IAAK;AACzC;AAKO,MAAM,uBAAuB;IAClC,IAAI,OAAO,aAAa,aAAa;IAErC,MAAM,QAAQ;IACd,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG;IAE7C,4BAA4B;IAC5B,SAAS,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc;IACxE,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM;IAE3C,8BAA8B;IAC9B,MAAM,OAAO,SAAS,eAAe;IACrC,KAAK,KAAK,CAAC,WAAW,CAAC,mBAAmB;IAC1C,KAAK,KAAK,CAAC,WAAW,CAAC,mBAAmB,GAAG,aAAa,EAAE,CAAC;AAC/D;AASO,MAAM,qBAAqB;IAChC,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,QAAQ;AACxC;AAKO,MAAM,aAAa,CAA8B;IACtD,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI;AAC7C;AAKO,MAAM,iBAAiB,CAAC;IAC7B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,cAAc,CAAC;AACxC;AAKO,MAAM,gBAAgB;IAC3B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;AACtC;AASO,MAAM,uBAAuB;IAClC,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU;AAC1C;AAKO,MAAM,iBAAiB;IAC5B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,WAAW;AACtD;AAKO,MAAM,iBAAiB,CAAC;IAC7B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,cAAc,CAAC;AACxC;AAKO,MAAM,iBAAiB;IAC5B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,WAAW;AACtD;AAKO,MAAM,iBAAiB,CAAC;IAC7B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,cAAc,CAAC;AACxC;AAKO,MAAM,qBAAqB;IAChC,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,gBAAgB;AAC3D;AAKO,MAAM,gBAAgB;IAC3B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;AACtC;AAKO,MAAM,mBAAmB;IAC9B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,aAAa;AACxD;AAKO,MAAM,gBAAgB,CAAC;IAC5B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa,CAAC;AACvC;AASO,MAAM,oBAAoB;IAC/B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE;AAClC;AAKO,MAAM,kBAAkB;IAC7B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,aAAa;AAChD;AAKO,MAAM,mBAAmB,CAAC,SAAkB;IACjD,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,gBAAgB,CAAC,SAAS;AACnD;AAKO,MAAM,iBAAiB;IAC5B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,WAAW;AAC9C;AAKO,MAAM,iBAAiB,CAAC,OAAsB;IACnD,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,cAAc,CAAC,OAAO;AAC/C;AAKO,MAAM,mBAAmB;IAC9B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,gBAAgB;AACzC;AASO,MAAM,mBAAmB;IAC9B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,aAAa;AAChD;AAKO,MAAM,kBAAkB,CAC7B;IAEA,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe,CAAC;AACzC;AAKO,MAAM,qBAAqB,CAAC;IACjC,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,kBAAkB,CAAC;AAC5C;AAKO,MAAM,qBAAqB;IAChC,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,kBAAkB;AAC3C;AAKO,MAAM,gBAAgB,CAAC,SAAiB;IAC7C,gBAAgB;QACd,MAAM;QACN,OAAO,SAAS;QAChB;IACF;AACF;AAKO,MAAM,cAAc,CAAC,SAAiB;IAC3C,gBAAgB;QACd,MAAM;QACN,OAAO,SAAS;QAChB;IACF;AACF;AAKO,MAAM,gBAAgB,CAAC,SAAiB;IAC7C,gBAAgB;QACd,MAAM;QACN,OAAO,SAAS;QAChB;IACF;AACF;AAKO,MAAM,aAAa,CAAC,SAAiB;IAC1C,gBAAgB;QACd,MAAM;QACN,OAAO,SAAS;QAChB;IACF;AACF;AASO,MAAM,iBAAiB;IAC5B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,MAAM;AACzC;AAKO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,QAAQ,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI;IACnD,OAAO,OAAO,WAAW;AAC3B;AAKO,MAAM,eAAe,CAAC;IAC3B,MAAM,QAAQ,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI;IACnD,OAAO,OAAO;AAChB;AAKO,MAAM,YAAY,CAAC,KAAa;IACrC,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,SAAS,CAAC,KAAK;AACxC;AAKO,MAAM,YAAY,CAAC;IACxB,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,SAAS,CAAC;AACnC;AAKO,MAAM,gBAAgB;IAC3B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;AACtC;AASO,MAAM,gBAAgB;IAC3B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,OAAO;AACvC;AAKO,MAAM,eAAe;IAC1B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,SAAS;AACzC;AAKO,MAAM,iBAAiB;IAC5B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,WAAW;AAC3C;AAKO,MAAM,gBAAgB;IAC3B,OAAO,qBAAqB;AAC9B;AAKO,MAAM,eAAe;IAC1B,OAAO,qBAAqB;AAC9B;AASO,MAAM,WAAW;IACtB,uCAAmC;;IAAY;IAC/C,OAAO,OAAO,UAAU,GAAG;AAC7B;AAKO,MAAM,WAAW;IACtB,uCAAmC;;IAAY;IAC/C,OAAO,OAAO,UAAU,IAAI,OAAO,OAAO,UAAU,GAAG;AACzD;AAKO,MAAM,YAAY;IACvB,uCAAmC;;IAAY;IAC/C,OAAO,OAAO,UAAU,IAAI;AAC9B;AAKO,MAAM,uBAAuB;IAClC,IAAI,YAAY,OAAO;IACvB,IAAI,YAAY,OAAO;IACvB,OAAO;AACT;AASO,MAAM,cAAc;IACzB,uCAA4C;;IAE5C;IAEA,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ;AAC7B;AAKO,MAAM,gBAAgB;IAC3B,uCAA4C;;IAE5C;IAEA,MAAM,QAAQ,gIAAA,CAAA,cAAW,CAAC,QAAQ;IAClC,MAAM,aAAa;IACnB,MAAM,kBAAkB;IACxB,MAAM,aAAa;IACnB,MAAM,gBAAgB;IACtB,MAAM,gBAAgB,CAAC;AACzB"}}, {"offset": {"line": 2717, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2723, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/store-context.tsx"], "sourcesContent": ["/**\n * Store Context - React context for accessing stores\n * Provides centralized access to all Zustand stores\n */\n\n'use client';\n\nimport React, { createContext, useContext, ReactNode } from 'react';\nimport { useAuth } from './auth-hooks';\nimport { useApp } from './app-hooks';\nimport type { AuthStore, AppStore } from './types';\n\n/**\n * Store context interface\n */\ninterface StoreContextType {\n  authStore: AuthStore;\n  appStore: AppStore;\n}\n\n/**\n * Store context\n */\nconst StoreContext = createContext<StoreContextType | null>(null);\n\n/**\n * Store context provider props\n */\ninterface StoreContextProviderProps {\n  children: ReactNode;\n}\n\n/**\n * Store context provider component\n * Provides all stores to child components\n */\nexport function StoreContextProvider({ children }: StoreContextProviderProps) {\n  // Get store instances\n  const authStore = useAuth();\n  const appStore = useApp();\n\n  const contextValue: StoreContextType = {\n    authStore,\n    appStore,\n  };\n\n  return (\n    <StoreContext.Provider value={contextValue}>\n      {children}\n    </StoreContext.Provider>\n  );\n}\n\n/**\n * Hook to access store context\n * Throws error if used outside provider\n */\nexport function useStoreContext(): StoreContextType {\n  const context = useContext(StoreContext);\n\n  if (!context) {\n    throw new Error(\n      'useStoreContext must be used within a StoreContextProvider'\n    );\n  }\n\n  return context;\n}\n\n/**\n * Hook to access auth store from context\n */\nexport function useAuthStoreContext() {\n  const { authStore } = useStoreContext();\n  return authStore;\n}\n\n/**\n * Hook to access app store from context\n */\nexport function useAppStoreContext() {\n  const { appStore } = useStoreContext();\n  return appStore;\n}\n\n/**\n * Hook to check if store context is available\n */\nexport function useIsStoreContextAvailable(): boolean {\n  const context = useContext(StoreContext);\n  return context !== null;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAID;AACA;AACA;;;AAJA;;;;AAeA;;CAEC,GACD,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA2B;AAarD,SAAS,qBAAqB,EAAE,QAAQ,EAA6B;;IAC1E,sBAAsB;IACtB,MAAM,YAAY,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACxB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,SAAM,AAAD;IAEtB,MAAM,eAAiC;QACrC;QACA;IACF;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;GAfgB;;QAEI,iIAAA,CAAA,UAAO;QACR,gIAAA,CAAA,SAAM;;;KAHT;AAqBT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MACR;IAEJ;IAEA,OAAO;AACT;IAVgB;AAeT,SAAS;;IACd,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,OAAO;AACT;IAHgB;;QACQ;;;AAOjB,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG;IACrB,OAAO;AACT;IAHgB;;QACO;;;AAOhB,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,OAAO,YAAY;AACrB;IAHgB"}}, {"offset": {"line": 2811, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2817, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/store-provider.tsx"], "sourcesContent": ["/**\n * Store Provider - Main provider component for all stores\n * Wraps the application with necessary store providers\n */\n\n'use client';\n\nimport React, { ReactNode, useEffect } from 'react';\nimport { StoreContextProvider } from './store-context';\nimport { useAuth } from './auth-hooks';\nimport { useApp } from './app-hooks';\nimport { STORAGE_KEYS } from './constants';\n\n/**\n * Store provider props\n */\ninterface StoreProviderProps {\n  children: ReactNode;\n}\n\n/**\n * Store initialization component\n * Handles store initialization and hydration\n */\nfunction StoreInitializer({ children }: { children: ReactNode }) {\n  const authStore = useAuth();\n  const appStore = useApp();\n\n  useEffect(() => {\n    // Initialize stores on mount\n    const initializeStores = async () => {\n      try {\n        console.log('✅ Stores initialized successfully');\n      } catch (error) {\n        console.error('❌ Failed to initialize stores:', error);\n      }\n    };\n\n    initializeStores();\n  }, []);\n\n  return <>{children}</>;\n}\n\n/**\n * Main store provider component\n * Provides all stores and handles initialization\n */\nexport function StoreProvider({ children }: StoreProviderProps) {\n  return (\n    <StoreContextProvider>\n      <StoreInitializer>\n        {children}\n      </StoreInitializer>\n    </StoreContextProvider>\n  );\n}\n\n/**\n * HOC to wrap components with store provider\n */\nexport function withStoreProvider<P extends object>(\n  Component: React.ComponentType<P>\n) {\n  const WrappedComponent = (props: P) => (\n    <StoreProvider>\n      <Component {...props} />\n    </StoreProvider>\n  );\n\n  WrappedComponent.displayName = `withStoreProvider(${Component.displayName || Component.name})`;\n\n  return WrappedComponent;\n}\n\n/**\n * Store provider utilities\n */\nexport const StoreProviderUtils = {\n  /**\n   * Check if stores are properly initialized\n   */\n  checkStoreInitialization: () => {\n    try {\n      return {\n        auth: true,\n        app: true,\n        all: true,\n      };\n    } catch (error) {\n      console.error('Failed to check store initialization:', error);\n      return {\n        auth: false,\n        app: false,\n        all: false,\n      };\n    }\n  },\n\n  /**\n   * Reset all stores to initial state\n   */\n  resetAllStores: () => {\n    try {\n      console.log('✅ All stores reset successfully');\n    } catch (error) {\n      console.error('❌ Failed to reset stores:', error);\n    }\n  },\n\n  /**\n   * Clear all persisted store data\n   */\n  clearPersistedData: () => {\n    try {\n      Object.values(STORAGE_KEYS).forEach(key => {\n        localStorage.removeItem(key);\n      });\n\n      console.log('✅ All persisted store data cleared');\n    } catch (error) {\n      console.error('❌ Failed to clear persisted data:', error);\n    }\n  },\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAID;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAeA;;;CAGC,GACD,SAAS,iBAAiB,EAAE,QAAQ,EAA2B;;IAC7D,MAAM,YAAY,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACxB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,SAAM,AAAD;IAEtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,6BAA6B;YAC7B,MAAM;+DAAmB;oBACvB,IAAI;wBACF,QAAQ,GAAG,CAAC;oBACd,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kCAAkC;oBAClD;gBACF;;YAEA;QACF;qCAAG,EAAE;IAEL,qBAAO;kBAAG;;AACZ;GAlBS;;QACW,iIAAA,CAAA,UAAO;QACR,gIAAA,CAAA,SAAM;;;KAFhB;AAwBF,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,qBACE,6LAAC,qIAAA,CAAA,uBAAoB;kBACnB,cAAA,6LAAC;sBACE;;;;;;;;;;;AAIT;MARgB;AAaT,SAAS,kBACd,SAAiC;IAEjC,MAAM,mBAAmB,CAAC,sBACxB,6LAAC;sBACC,cAAA,6LAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,CAAC,kBAAkB,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAE9F,OAAO;AACT;AAKO,MAAM,qBAAqB;IAChC;;GAEC,GACD,0BAA0B;QACxB,IAAI;YACF,OAAO;gBACL,MAAM;gBACN,KAAK;gBACL,KAAK;YACP;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;gBACL,MAAM;gBACN,KAAK;gBACL,KAAK;YACP;QACF;IACF;IAEA;;GAEC,GACD,gBAAgB;QACd,IAAI;YACF,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA;;GAEC,GACD,oBAAoB;QAClB,IAAI;YACF,OAAO,MAAM,CAAC,6HAAA,CAAA,eAAY,EAAE,OAAO,CAAC,CAAA;gBAClC,aAAa,UAAU,CAAC;YAC1B;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;AACF"}}, {"offset": {"line": 2952, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2958, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/provider-hooks.ts"], "sourcesContent": ["/**\n * Provider Hooks - Custom hooks for using stores with providers\n * Provides convenient access to stores through context\n */\n\n'use client';\n\nimport { useCallback, useMemo } from 'react';\nimport {\n  useStoreContext,\n  useAuthStoreContext,\n  useAppStoreContext,\n  useIsStoreContextAvailable\n} from './store-context';\nimport type { SystemUser, AuthState, AppState } from './types';\n\n/**\n * Hook to use auth store with provider context\n * Provides auth state and actions\n */\nexport function useAuthProvider() {\n  const authStore = useAuthStoreContext();\n\n  const login = useCallback(async (credentials: { username: string; password: string }) => {\n    return authStore.login(credentials);\n  }, [authStore]);\n\n  const logout = useCallback(async () => {\n    return authStore.logout();\n  }, [authStore]);\n\n  const logoutAll = useCallback(async () => {\n    return authStore.logoutAll();\n  }, [authStore]);\n\n  const refreshToken = useCallback(async () => {\n    return authStore.refreshToken();\n  }, [authStore]);\n\n  const updateProfile = useCallback(async (data: Partial<SystemUser>) => {\n    return authStore.updateProfile(data);\n  }, [authStore]);\n\n  const changePassword = useCallback(async (data: { currentPassword: string; newPassword: string }) => {\n    return authStore.changePassword(data);\n  }, [authStore]);\n\n  return useMemo(() => ({\n    // State\n    user: authStore.user,\n    token: authStore.token,\n    refreshToken: authStore.refreshToken,\n    isAuthenticated: authStore.isAuthenticated,\n    isLoading: authStore.isLoading,\n    error: authStore.error,\n    isInitialized: authStore.isInitialized,\n\n    // Actions\n    login,\n    logout,\n    logoutAll,\n    refreshToken: refreshToken,\n    updateProfile,\n    changePassword,\n    clearError: authStore.clearError,\n    reset: authStore.reset,\n  }), [\n    authStore.user,\n    authStore.token,\n    authStore.refreshToken,\n    authStore.isAuthenticated,\n    authStore.isLoading,\n    authStore.error,\n    authStore.isInitialized,\n    login,\n    logout,\n    logoutAll,\n    refreshToken,\n    updateProfile,\n    changePassword,\n    authStore.clearError,\n    authStore.reset,\n  ]);\n}\n\n/**\n * Hook to use app store with provider context\n * Provides app state and actions\n */\nexport function useAppProvider() {\n  const appStore = useAppStoreContext();\n\n  const setTheme = useCallback((theme: 'light' | 'dark') => {\n    appStore.setTheme(theme);\n  }, [appStore]);\n\n  const toggleTheme = useCallback(() => {\n    appStore.toggleTheme();\n  }, [appStore]);\n\n  const setLanguage = useCallback((language: string) => {\n    appStore.setLanguage(language);\n  }, [appStore]);\n\n  const setSidebarCollapsed = useCallback((collapsed: boolean) => {\n    appStore.setSidebarCollapsed(collapsed);\n  }, [appStore]);\n\n  const toggleSidebar = useCallback(() => {\n    appStore.toggleSidebar();\n  }, [appStore]);\n\n  const setLoading = useCallback((loading: boolean) => {\n    appStore.setLoading(loading);\n  }, [appStore]);\n\n  const showNotification = useCallback((notification: { type: 'success' | 'error' | 'warning' | 'info'; message: string; description?: string }) => {\n    appStore.showNotification(notification);\n  }, [appStore]);\n\n  const hideNotification = useCallback(() => {\n    appStore.hideNotification();\n  }, [appStore]);\n\n  return useMemo(() => ({\n    // State\n    theme: appStore.theme,\n    language: appStore.language,\n    sidebarCollapsed: appStore.sidebarCollapsed,\n    isLoading: appStore.isLoading,\n    notification: appStore.notification,\n    isInitialized: appStore.isInitialized,\n\n    // Actions\n    setTheme,\n    toggleTheme,\n    setLanguage,\n    setSidebarCollapsed,\n    toggleSidebar,\n    setLoading,\n    showNotification,\n    hideNotification,\n    reset: appStore.reset,\n  }), [\n    appStore.theme,\n    appStore.language,\n    appStore.sidebarCollapsed,\n    appStore.isLoading,\n    appStore.notification,\n    appStore.isInitialized,\n    setTheme,\n    toggleTheme,\n    setLanguage,\n    setSidebarCollapsed,\n    toggleSidebar,\n    setLoading,\n    showNotification,\n    hideNotification,\n    appStore.reset,\n  ]);\n}\n\n/**\n * Hook to check if stores are available\n */\nexport function useStoreAvailability() {\n  const isAvailable = useIsStoreContextAvailable();\n\n  return useMemo(() => ({\n    isAvailable,\n    isStoreReady: isAvailable,\n  }), [isAvailable]);\n}\n\n/**\n * Hook to get all stores\n */\nexport function useStores() {\n  const context = useStoreContext();\n\n  return useMemo(() => ({\n    authStore: context.authStore,\n    appStore: context.appStore,\n  }), [context.authStore, context.appStore]);\n}\n\n/**\n * Hook for development/debugging purposes\n */\nexport function useStoreDebug() {\n  const { authStore, appStore } = useStores();\n\n  return useMemo(() => ({\n    authState: {\n      user: authStore.user,\n      isAuthenticated: authStore.isAuthenticated,\n      isLoading: authStore.isLoading,\n      error: authStore.error,\n      isInitialized: authStore.isInitialized,\n    } as AuthState,\n    appState: {\n      theme: appStore.theme,\n      language: appStore.language,\n      sidebarCollapsed: appStore.sidebarCollapsed,\n      isLoading: appStore.isLoading,\n      notification: appStore.notification,\n      isInitialized: appStore.isInitialized,\n    } as AppState,\n    actions: {\n      resetAuth: authStore.reset,\n      resetApp: appStore.reset,\n      clearAuthError: authStore.clearError,\n      hideNotification: appStore.hideNotification,\n    },\n  }), [authStore, appStore]);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAID;AACA;;AAHA;;;AAeO,SAAS;;IACd,MAAM,YAAY,CAAA,GAAA,qIAAA,CAAA,sBAAmB,AAAD;IAEpC,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,OAAO;YAC/B,OAAO,UAAU,KAAK,CAAC;QACzB;6CAAG;QAAC;KAAU;IAEd,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YACzB,OAAO,UAAU,MAAM;QACzB;8CAAG;QAAC;KAAU;IAEd,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC5B,OAAO,UAAU,SAAS;QAC5B;iDAAG;QAAC;KAAU;IAEd,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC/B,OAAO,UAAU,YAAY;QAC/B;oDAAG;QAAC;KAAU;IAEd,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,OAAO;YACvC,OAAO,UAAU,aAAa,CAAC;QACjC;qDAAG;QAAC;KAAU;IAEd,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,OAAO;YACxC,OAAO,UAAU,cAAc,CAAC;QAClC;sDAAG;QAAC;KAAU;IAEd,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mCAAE,IAAM,CAAC;gBACpB,QAAQ;gBACR,MAAM,UAAU,IAAI;gBACpB,OAAO,UAAU,KAAK;gBACtB,cAAc,UAAU,YAAY;gBACpC,iBAAiB,UAAU,eAAe;gBAC1C,WAAW,UAAU,SAAS;gBAC9B,OAAO,UAAU,KAAK;gBACtB,eAAe,UAAU,aAAa;gBAEtC,UAAU;gBACV;gBACA;gBACA;gBACA,cAAc;gBACd;gBACA;gBACA,YAAY,UAAU,UAAU;gBAChC,OAAO,UAAU,KAAK;YACxB,CAAC;kCAAG;QACF,UAAU,IAAI;QACd,UAAU,KAAK;QACf,UAAU,YAAY;QACtB,UAAU,eAAe;QACzB,UAAU,SAAS;QACnB,UAAU,KAAK;QACf,UAAU,aAAa;QACvB;QACA;QACA;QACA;QACA;QACA;QACA,UAAU,UAAU;QACpB,UAAU,KAAK;KAChB;AACH;GA/DgB;;QACI,qIAAA,CAAA,sBAAmB;;;AAoEhC,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD;IAElC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAAC;YAC5B,SAAS,QAAQ,CAAC;QACpB;+CAAG;QAAC;KAAS;IAEb,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAC9B,SAAS,WAAW;QACtB;kDAAG;QAAC;KAAS;IAEb,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YAC/B,SAAS,WAAW,CAAC;QACvB;kDAAG;QAAC;KAAS;IAEb,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YACvC,SAAS,mBAAmB,CAAC;QAC/B;0DAAG;QAAC;KAAS;IAEb,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAChC,SAAS,aAAa;QACxB;oDAAG;QAAC;KAAS;IAEb,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAC9B,SAAS,UAAU,CAAC;QACtB;iDAAG;QAAC;KAAS;IAEb,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACpC,SAAS,gBAAgB,CAAC;QAC5B;uDAAG;QAAC;KAAS;IAEb,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YACnC,SAAS,gBAAgB;QAC3B;uDAAG;QAAC;KAAS;IAEb,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kCAAE,IAAM,CAAC;gBACpB,QAAQ;gBACR,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;gBAC3B,kBAAkB,SAAS,gBAAgB;gBAC3C,WAAW,SAAS,SAAS;gBAC7B,cAAc,SAAS,YAAY;gBACnC,eAAe,SAAS,aAAa;gBAErC,UAAU;gBACV;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,OAAO,SAAS,KAAK;YACvB,CAAC;iCAAG;QACF,SAAS,KAAK;QACd,SAAS,QAAQ;QACjB,SAAS,gBAAgB;QACzB,SAAS,SAAS;QAClB,SAAS,YAAY;QACrB,SAAS,aAAa;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,SAAS,KAAK;KACf;AACH;IAvEgB;;QACG,qIAAA,CAAA,qBAAkB;;;AA2E9B,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,qIAAA,CAAA,6BAA0B,AAAD;IAE7C,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wCAAE,IAAM,CAAC;gBACpB;gBACA,cAAc;YAChB,CAAC;uCAAG;QAAC;KAAY;AACnB;IAPgB;;QACM,qIAAA,CAAA,6BAA0B;;;AAWzC,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAE9B,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6BAAE,IAAM,CAAC;gBACpB,WAAW,QAAQ,SAAS;gBAC5B,UAAU,QAAQ,QAAQ;YAC5B,CAAC;4BAAG;QAAC,QAAQ,SAAS;QAAE,QAAQ,QAAQ;KAAC;AAC3C;IAPgB;;QACE,qIAAA,CAAA,kBAAe;;;AAW1B,SAAS;;IACd,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG;IAEhC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iCAAE,IAAM,CAAC;gBACpB,WAAW;oBACT,MAAM,UAAU,IAAI;oBACpB,iBAAiB,UAAU,eAAe;oBAC1C,WAAW,UAAU,SAAS;oBAC9B,OAAO,UAAU,KAAK;oBACtB,eAAe,UAAU,aAAa;gBACxC;gBACA,UAAU;oBACR,OAAO,SAAS,KAAK;oBACrB,UAAU,SAAS,QAAQ;oBAC3B,kBAAkB,SAAS,gBAAgB;oBAC3C,WAAW,SAAS,SAAS;oBAC7B,cAAc,SAAS,YAAY;oBACnC,eAAe,SAAS,aAAa;gBACvC;gBACA,SAAS;oBACP,WAAW,UAAU,KAAK;oBAC1B,UAAU,SAAS,KAAK;oBACxB,gBAAgB,UAAU,UAAU;oBACpC,kBAAkB,SAAS,gBAAgB;gBAC7C;YACF,CAAC;gCAAG;QAAC;QAAW;KAAS;AAC3B;IA1BgB;;QACkB"}}, {"offset": {"line": 3239, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3245, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/index.ts"], "sourcesContent": ["/**\n * Store Index - Central export for all stores\n * Provides barrel exports for all store modules\n */\n\n// Store types and interfaces\nexport * from './types';\n\n// Store utilities\nexport * from './utils';\n\n// Individual stores\nexport * from './auth-store';\nexport * from './auth-hooks';\nexport * from './auth-utils';\nexport * from './app-store';\nexport * from './app-hooks';\nexport * from './app-utils';\n\n// Store providers and context\nexport * from './store-provider';\nexport * from './store-context';\nexport * from './provider-hooks';\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,6BAA6B"}}, {"offset": {"line": 3264, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3289, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/store-demo/page.tsx"], "sourcesContent": ["/**\n * Store Demo Page - Test page for Store Provider functionality\n * Demonstrates usage of stores through providers\n */\n\n'use client';\n\nimport React from 'react';\nimport { useAuthProvider, useAppProvider, useStoreAvailability, useStoreDebug } from '@/stores';\n\nexport default function StoreDemoPage() {\n  const auth = useAuthProvider();\n  const app = useAppProvider();\n  const { isAvailable } = useStoreAvailability();\n  const debug = useStoreDebug();\n\n  const handleLogin = async () => {\n    try {\n      await auth.login({\n        username: 'admin',\n        password: 'admin123456'\n      });\n    } catch (error) {\n      console.error('Login failed:', error);\n    }\n  };\n\n  const handleLogout = async () => {\n    try {\n      await auth.logout();\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n\n  const handleThemeToggle = () => {\n    app.toggleTheme();\n  };\n\n  const handleSidebarToggle = () => {\n    app.toggleSidebar();\n  };\n\n  const handleShowNotification = () => {\n    app.showNotification({\n      type: 'success',\n      message: 'Test Notification',\n      description: 'This is a test notification from the store provider demo.'\n    });\n  };\n\n  const handleLanguageChange = () => {\n    const newLanguage = app.language === 'en' ? 'vi' : 'en';\n    app.setLanguage(newLanguage);\n  };\n\n  return (\n    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>\n      <h1>Store Provider Demo</h1>\n      \n      {/* Store Availability */}\n      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>Store Availability</h2>\n        <p>Store Available: <strong>{isAvailable ? 'Yes' : 'No'}</strong></p>\n        <p>Auth Initialized: <strong>{auth.isInitialized ? 'Yes' : 'No'}</strong></p>\n        <p>App Initialized: <strong>{app.isInitialized ? 'Yes' : 'No'}</strong></p>\n      </div>\n\n      {/* Authentication State */}\n      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>Authentication State</h2>\n        <p>Authenticated: <strong>{auth.isAuthenticated ? 'Yes' : 'No'}</strong></p>\n        <p>Loading: <strong>{auth.isLoading ? 'Yes' : 'No'}</strong></p>\n        <p>User: <strong>{auth.user ? `${auth.user.username} (${auth.user.role})` : 'None'}</strong></p>\n        <p>Error: <strong>{auth.error || 'None'}</strong></p>\n        \n        <div style={{ marginTop: '10px' }}>\n          <button \n            onClick={handleLogin} \n            disabled={auth.isLoading || auth.isAuthenticated}\n            style={{ marginRight: '10px', padding: '5px 10px' }}\n          >\n            Login (Demo)\n          </button>\n          <button \n            onClick={handleLogout} \n            disabled={auth.isLoading || !auth.isAuthenticated}\n            style={{ marginRight: '10px', padding: '5px 10px' }}\n          >\n            Logout\n          </button>\n          <button \n            onClick={auth.clearError} \n            disabled={!auth.error}\n            style={{ padding: '5px 10px' }}\n          >\n            Clear Error\n          </button>\n        </div>\n      </div>\n\n      {/* Application State */}\n      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>Application State</h2>\n        <p>Theme: <strong>{app.theme}</strong></p>\n        <p>Language: <strong>{app.language}</strong></p>\n        <p>Sidebar Collapsed: <strong>{app.sidebarCollapsed ? 'Yes' : 'No'}</strong></p>\n        <p>Loading: <strong>{app.isLoading ? 'Yes' : 'No'}</strong></p>\n        <p>Notification: <strong>{app.notification ? `${app.notification.type}: ${app.notification.message}` : 'None'}</strong></p>\n        \n        <div style={{ marginTop: '10px' }}>\n          <button \n            onClick={handleThemeToggle}\n            style={{ marginRight: '10px', padding: '5px 10px' }}\n          >\n            Toggle Theme\n          </button>\n          <button \n            onClick={handleSidebarToggle}\n            style={{ marginRight: '10px', padding: '5px 10px' }}\n          >\n            Toggle Sidebar\n          </button>\n          <button \n            onClick={handleLanguageChange}\n            style={{ marginRight: '10px', padding: '5px 10px' }}\n          >\n            Change Language\n          </button>\n          <button \n            onClick={handleShowNotification}\n            style={{ marginRight: '10px', padding: '5px 10px' }}\n          >\n            Show Notification\n          </button>\n          <button \n            onClick={app.hideNotification}\n            disabled={!app.notification}\n            style={{ padding: '5px 10px' }}\n          >\n            Hide Notification\n          </button>\n        </div>\n      </div>\n\n      {/* Debug Information */}\n      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>Debug Information</h2>\n        <details>\n          <summary>Auth State (Click to expand)</summary>\n          <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>\n            {JSON.stringify(debug.authState, null, 2)}\n          </pre>\n        </details>\n        \n        <details style={{ marginTop: '10px' }}>\n          <summary>App State (Click to expand)</summary>\n          <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>\n            {JSON.stringify(debug.appState, null, 2)}\n          </pre>\n        </details>\n        \n        <div style={{ marginTop: '10px' }}>\n          <button \n            onClick={debug.actions.resetAuth}\n            style={{ marginRight: '10px', padding: '5px 10px' }}\n          >\n            Reset Auth Store\n          </button>\n          <button \n            onClick={debug.actions.resetApp}\n            style={{ padding: '5px 10px' }}\n          >\n            Reset App Store\n          </button>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div style={{ marginTop: '30px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>\n        <h2>Navigation</h2>\n        <p>This demo page shows the Store Provider functionality.</p>\n        <a href=\"/\" style={{ color: 'blue', textDecoration: 'underline' }}>\n          ← Back to Home\n        </a>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAKD;AAAA;;;AAHA;;AAKe,SAAS;;IACtB,MAAM,OAAO,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAC3B,MAAM,MAAM,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD;IACzB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,uBAAoB,AAAD;IAC3C,MAAM,QAAQ,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IAE1B,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,KAAK,KAAK,CAAC;gBACf,UAAU;gBACV,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,KAAK,MAAM;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,WAAW;IACjB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,aAAa;IACnB;IAEA,MAAM,yBAAyB;QAC7B,IAAI,gBAAgB,CAAC;YACnB,MAAM;YACN,SAAS;YACT,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB;QAC3B,MAAM,cAAc,IAAI,QAAQ,KAAK,OAAO,OAAO;QACnD,IAAI,WAAW,CAAC;IAClB;IAEA,qBACE,6LAAC;QAAI,OAAO;YAAE,SAAS;YAAQ,YAAY;QAAoB;;0BAC7D,6LAAC;0BAAG;;;;;;0BAGJ,6LAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCACjG,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;;4BAAE;0CAAiB,6LAAC;0CAAQ,cAAc,QAAQ;;;;;;;;;;;;kCACnD,6LAAC;;4BAAE;0CAAkB,6LAAC;0CAAQ,KAAK,aAAa,GAAG,QAAQ;;;;;;;;;;;;kCAC3D,6LAAC;;4BAAE;0CAAiB,6LAAC;0CAAQ,IAAI,aAAa,GAAG,QAAQ;;;;;;;;;;;;;;;;;;0BAI3D,6LAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCACjG,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;;4BAAE;0CAAe,6LAAC;0CAAQ,KAAK,eAAe,GAAG,QAAQ;;;;;;;;;;;;kCAC1D,6LAAC;;4BAAE;0CAAS,6LAAC;0CAAQ,KAAK,SAAS,GAAG,QAAQ;;;;;;;;;;;;kCAC9C,6LAAC;;4BAAE;0CAAM,6LAAC;0CAAQ,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG;;;;;;;;;;;;kCAC5E,6LAAC;;4BAAE;0CAAO,6LAAC;0CAAQ,KAAK,KAAK,IAAI;;;;;;;;;;;;kCAEjC,6LAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAO;;0CAC9B,6LAAC;gCACC,SAAS;gCACT,UAAU,KAAK,SAAS,IAAI,KAAK,eAAe;gCAChD,OAAO;oCAAE,aAAa;oCAAQ,SAAS;gCAAW;0CACnD;;;;;;0CAGD,6LAAC;gCACC,SAAS;gCACT,UAAU,KAAK,SAAS,IAAI,CAAC,KAAK,eAAe;gCACjD,OAAO;oCAAE,aAAa;oCAAQ,SAAS;gCAAW;0CACnD;;;;;;0CAGD,6LAAC;gCACC,SAAS,KAAK,UAAU;gCACxB,UAAU,CAAC,KAAK,KAAK;gCACrB,OAAO;oCAAE,SAAS;gCAAW;0CAC9B;;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCACjG,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;;4BAAE;0CAAO,6LAAC;0CAAQ,IAAI,KAAK;;;;;;;;;;;;kCAC5B,6LAAC;;4BAAE;0CAAU,6LAAC;0CAAQ,IAAI,QAAQ;;;;;;;;;;;;kCAClC,6LAAC;;4BAAE;0CAAmB,6LAAC;0CAAQ,IAAI,gBAAgB,GAAG,QAAQ;;;;;;;;;;;;kCAC9D,6LAAC;;4BAAE;0CAAS,6LAAC;0CAAQ,IAAI,SAAS,GAAG,QAAQ;;;;;;;;;;;;kCAC7C,6LAAC;;4BAAE;0CAAc,6LAAC;0CAAQ,IAAI,YAAY,GAAG,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE,GAAG;;;;;;;;;;;;kCAEvG,6LAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAO;;0CAC9B,6LAAC;gCACC,SAAS;gCACT,OAAO;oCAAE,aAAa;oCAAQ,SAAS;gCAAW;0CACnD;;;;;;0CAGD,6LAAC;gCACC,SAAS;gCACT,OAAO;oCAAE,aAAa;oCAAQ,SAAS;gCAAW;0CACnD;;;;;;0CAGD,6LAAC;gCACC,SAAS;gCACT,OAAO;oCAAE,aAAa;oCAAQ,SAAS;gCAAW;0CACnD;;;;;;0CAGD,6LAAC;gCACC,SAAS;gCACT,OAAO;oCAAE,aAAa;oCAAQ,SAAS;gCAAW;0CACnD;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAI,gBAAgB;gCAC7B,UAAU,CAAC,IAAI,YAAY;gCAC3B,OAAO;oCAAE,SAAS;gCAAW;0CAC9B;;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCACjG,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;;0CACC,6LAAC;0CAAQ;;;;;;0CACT,6LAAC;gCAAI,OAAO;oCAAE,YAAY;oCAAW,SAAS;oCAAQ,UAAU;gCAAO;0CACpE,KAAK,SAAS,CAAC,MAAM,SAAS,EAAE,MAAM;;;;;;;;;;;;kCAI3C,6LAAC;wBAAQ,OAAO;4BAAE,WAAW;wBAAO;;0CAClC,6LAAC;0CAAQ;;;;;;0CACT,6LAAC;gCAAI,OAAO;oCAAE,YAAY;oCAAW,SAAS;oCAAQ,UAAU;gCAAO;0CACpE,KAAK,SAAS,CAAC,MAAM,QAAQ,EAAE,MAAM;;;;;;;;;;;;kCAI1C,6LAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAO;;0CAC9B,6LAAC;gCACC,SAAS,MAAM,OAAO,CAAC,SAAS;gCAChC,OAAO;oCAAE,aAAa;oCAAQ,SAAS;gCAAW;0CACnD;;;;;;0CAGD,6LAAC;gCACC,SAAS,MAAM,OAAO,CAAC,QAAQ;gCAC/B,OAAO;oCAAE,SAAS;gCAAW;0CAC9B;;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,OAAO;oBAAE,WAAW;oBAAQ,SAAS;oBAAQ,QAAQ;oBAAkB,cAAc;gBAAM;;kCAC9F,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;kCAAE;;;;;;kCACH,6LAAC;wBAAE,MAAK;wBAAI,OAAO;4BAAE,OAAO;4BAAQ,gBAAgB;wBAAY;kCAAG;;;;;;;;;;;;;;;;;;AAM3E;GAlLwB;;QACT,qIAAA,CAAA,kBAAe;QAChB,qIAAA,CAAA,iBAAc;QACF,qIAAA,CAAA,uBAAoB;QAC9B,qIAAA,CAAA,gBAAa;;;KAJL"}}, {"offset": {"line": 3903, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}